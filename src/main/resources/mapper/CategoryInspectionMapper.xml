<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.mdp.compass.category.mapper.CategoryInspectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.mdp.compass.category.entity.CategoryInspection">
        <id column="inspection_id" property="inspectionId" />
        <result column="inspection_time" property="inspectionTime" />
        <result column="device_id" property="deviceId" />
        <result column="image_path" property="imagePath" />
        <result column="remarks" property="remarks" />
        <result column="log_level" property="logLevel" />
        <result column="created_at" property="createdAt" />
        <result column="add_time" property="addTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        inspection_id, inspection_time, device_id, image_path, remarks, log_level, created_at, add_time, update_time
    </sql>
    
    <!-- 根据设备ID查询巡检记录 -->
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM category_inspection
        WHERE device_id = #{deviceId}
        ORDER BY inspection_time DESC
    </select>
    
    <!-- 根据时间范围查询巡检记录 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM category_inspection
        WHERE inspection_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY inspection_time DESC
    </select>
    
    <!-- 批量插入巡检记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO category_inspection
        (inspection_time, device_id, image_path, remarks, log_level, created_at, add_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.inspectionTime}, #{item.deviceId}, #{item.imagePath}, 
            #{item.remarks}, #{item.logLevel}, #{item.createdAt}, #{item.addTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
</mapper>
