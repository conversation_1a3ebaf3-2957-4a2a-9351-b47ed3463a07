<?xml version='1.1' encoding='UTF-8'?>
<project>
    <actions/>
    <description>定时测试的job模板</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.security.AuthorizationMatrixProperty>
            <inheritanceStrategy class="org.jenkinsci.plugins.matrixauth.inheritance.InheritParentStrategy"/>
            <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Create:dongheng</permission>
            <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Update:dongheng</permission>
            <permission>com.cloudbees.plugins.credentials.CredentialsProvider.View:dongheng</permission>
            <permission>hudson.model.Item.Build:dongheng</permission>
            <permission>hudson.model.Item.Cancel:dongheng</permission>
            <permission>hudson.model.Item.Configure:dongheng</permission>
        </hudson.security.AuthorizationMatrixProperty>
        <hudson.plugins.disk__usage.DiskUsageProperty plugin="disk-usage@0.28"/>
        <com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty plugin="naginator@1.17.2">
            <optOut>false</optOut>
        </com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty>
        <com.synopsys.arc.jenkinsci.plugins.jobrestrictions.jobs.JobRestrictionProperty plugin="job-restrictions@0.8"/>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.ChoiceParameterDefinition>
                    <name>os</name>
                    <description>选择测试端</description>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>Android</string>
                            <string>iOS</string>
                        </a>
                    </choices>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>alias</name>
                    <description>业务线名称</description>
                    <defaultValue>美团</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>loginCase</name>
                    <description>用于登录测试App的脚本</description>
                    <defaultValue></defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>devicesVersion</name>
                    <description>测试设备系统</description>
                    <defaultValue>5,6,7,8,9,10</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>deviceCount</name>
                    <description>测试设备数量</description>
                    <defaultValue>1</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>maxWaitTime</name>
                    <description>设备等待时间，单位(分)</description>
                    <defaultValue>60</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>intents</name>
                    <description>设置起始页面</description>
                    <defaultValue></defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>receiver</name>
                    <description>接收Job运行结果通知人</description>
                    <defaultValue></defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>events</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;Android&lt;/font&gt;端测试需要设置每个页面的事件数</description>
                    <defaultValue>100</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>throttle</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;Android&lt;/font&gt;端测试需要设置每个事件之间的间隔，单位(ms)</description>
                    <defaultValue>200</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>runTime</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;iOS&lt;/font&gt;端测试需要设置执行时长，单位(s)</description>
                    <defaultValue>3600</defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.BooleanParameterDefinition>
                    <name>screenShot</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;iOS&lt;/font&gt;端测试选择是否需要截图</description>
                    <defaultValue>true</defaultValue>
                </hudson.model.BooleanParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>buildScenes</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;ios-normal-monkey&lt;/font&gt;测试类型</description>
                    <defaultValue></defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>misId</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;dongheng&lt;/font&gt;提交人mis号</description>
                    <defaultValue></defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>conanKey</name>
                    <description>&lt;font style=&quot;color:red&quot;&gt;18a03220-ef17-4082-ba6b-59bbe65ce781&lt;/font&gt;提交人conanKey号</description>
                    <defaultValue></defaultValue>
                    <trim>false</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <job-metadata plugin="metadata@1.1.0b">
            <values class="linked-list">
                <metadata-tree>
                    <name>job-info</name>
                    <parent class="job-metadata" reference="../../.."/>
                    <generated>true</generated>
                    <exposedToEnvironment>false</exposedToEnvironment>
                    <children class="linked-list">
                        <metadata-tree>
                            <name>last-saved</name>
                            <description></description>
                            <parent class="metadata-tree" reference="../../.."/>
                            <generated>true</generated>
                            <exposedToEnvironment>false</exposedToEnvironment>
                            <children class="linked-list">
                                <metadata-date>
                                    <name>time</name>
                                    <description></description>
                                    <parent class="metadata-tree" reference="../../.."/>
                                    <generated>true</generated>
                                    <exposedToEnvironment>false</exposedToEnvironment>
                                    <value>
                                        <time>1606281224142</time>
                                        <timezone>PRC</timezone>
                                    </value>
                                    <checked>false</checked>
                                </metadata-date>
                                <metadata-tree>
                                    <name>user</name>
                                    <parent class="metadata-tree" reference="../../.."/>
                                    <generated>true</generated>
                                    <exposedToEnvironment>false</exposedToEnvironment>
                                    <children class="linked-list">
                                        <metadata-string>
                                            <name>display-name</name>
                                            <description></description>
                                            <parent class="metadata-tree" reference="../../.."/>
                                            <generated>true</generated>
                                            <exposedToEnvironment>false</exposedToEnvironment>
                                            <value>董恒</value>
                                        </metadata-string>
                                        <metadata-string>
                                            <name>full-name</name>
                                            <description></description>
                                            <parent class="metadata-tree" reference="../../.."/>
                                            <generated>true</generated>
                                            <exposedToEnvironment>false</exposedToEnvironment>
                                            <value>董恒</value>
                                        </metadata-string>
                                    </children>
                                </metadata-tree>
                            </children>
                        </metadata-tree>
                    </children>
                </metadata-tree>
            </values>
        </job-metadata>
    </properties>
    <scm class="hudson.scm.NullSCM"/>
    <assignedNode>docker</assignedNode>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <jdk>(System)</jdk>
    <triggers>
        <hudson.triggers.TimerTrigger>
            <spec>H 0-23/1 * * *</spec>
        </hudson.triggers.TimerTrigger>
    </triggers>
    <concurrentBuild>true</concurrentBuild>
    <builders>
        <hudson.tasks.Shell>
            <command>#!/bin/bash
                currentTime=`date +&apos;%Y-%m-%d %H:%M:%S&apos;`


                echo &quot;os:&quot;$os
                echo &quot;alias:&quot;$alias
                echo &quot;loginCase:&quot;$loginCase
                echo &quot;url:&quot;$url
                echo &quot;devicesVersion:&quot;$devicesVersion
                echo &quot;deviceCount:&quot;$deviceCount
                echo &quot;maxWaitTime:&quot;$maxWaitTime
                echo &quot;intents:&quot;$intents
                echo &quot;receiver:&quot;$receiver
                echo &quot;events:&quot;$events
                echo &quot;throttle:&quot;$throttle
                echo &quot;runTime:&quot;$runTime
                echo &quot;screenShot:&quot;$screenShot
                echo &quot;buildScenes:&quot;$buildScenes
                echo &quot;conanKey:&quot;$conanKey
                echo &quot;misId:&quot;$misId
                echo &quot;monkeyResultId:&quot;$monkeyResultId

                #预处理case
                if [[ $loginCase == &quot;1&quot; ]]; then
                git clone ssh://*******************/~dongheng/monkey_logincase.git
                loginCase=monkey_logincase/com.sankuai.conan.monkey.login-1.0-SNAPSHOT-jar-with-dependencies.jar
                fi

                getJsonVal(){
                python -c &quot;import json,sys;sys.stdout.write(json.dumps(json.load(sys.stdin)$1))&quot;;
                }
                MONKEY_URL=&apos;http://qaassist.sankuai.com/compass/api/monkey&apos;
                response=`curl -X POST -d &quot;jenkinsName=$JOB_BASE_NAME&quot; &quot;$MONKEY_URL/getApkUrlAndConfigSettingId&quot;`
                url=`echo $caseResponse | getJsonVal &quot;[&apos;appurl&apos;]&quot; | sed &apos;s/\&quot;//g&apos;`
                configSettingId=`echo $caseResponse | getJsonVal &quot;[&apos;configSettingId&apos;]&quot; | sed &apos;s/\&quot;//g&apos;`

                echo &quot;---------成功触发Job----------&quot;
                startJobTime=$currentTime
                echo &quot;startJobTime:&quot;$startJobTime

                submitAndroidUrl=&quot;http://conan.sankuai.com/ci/stability/advanced-monkey&quot;
                submitiOSUrl=&quot;https://conan.sankuai.com/ci/stability/ios-normal-monkey&quot;
                jenkinsUrl=&quot;http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/&quot;$BUILD_NUMBER
                echo &quot;jenkinsUrl:&quot;$jenkinsUrl
                echo &quot;build_number:&quot;$BUILD_NUMBER
                BUILD_PATH=&apos;http://qaassist.sankuai.com/compass/api/autotest/build&apos;
                JOB_PATH=&apos;http://qaassist.sankuai.com/compass/api/autotest/job&apos;
                echo &quot;----------新增构建入库----------&quot;

                if [[ $url == &quot;&quot; ]]; then
                echo &quot;url为空&quot;
                exit 1
                fi
                buildId=`curl -X POST -d &quot;jenkinsId=$BUILD_NUMBER&amp;platform=$os&amp;buildUrl=$jenkinsUrl&amp;buildType=stability&amp;buildScenes=$buildScenes&amp;alias=$alias&quot; &quot;$BUILD_PATH/add&quot;`
                echo &quot;buildId:&quot;$buildId
                curl -X POST -d &quot;id=$buildId&amp;status=0&quot; &quot;$BUILD_PATH/update/status&quot;
                echo &quot;-----------新增入库完成-----------&quot;
                #开始插入运行数据表
                monkeyResultId=`curl -X POST -d &quot;jenkinsName=$JOB_BASE_NAME&amp;apkUrl=$url&amp;submitResult=开始执行任务&amp;status=0&amp;configSettingId=$configSettingId&quot; &quot;$MONKEY_URL/updateData&quot;`


                #上传预处理case
                upload_case(){
                caseType=case
                for i in {0..60}
                do
                caseResponse=`curl -X POST -H Content-Type:multipart/form-data -F &quot;file=@$1&quot; https://conan.sankuai.com/ci/upload\?type\=$caseType\&amp;misId\=$misId\&amp;conanKey\=$conanKey`
                caseResponseStatus=`echo $caseResponse | getJsonVal &quot;[&apos;status&apos;]&quot;`
                caseName=`echo $caseResponse | getJsonVal &quot;[&apos;fileName&apos;]&quot; | sed &apos;s/\&quot;//g&apos;`
                if [[ $caseResponseStatus -eq 1 ]]; then
                echo $caseName
                curl -X POST -d &quot;id=$monkeyResultId&amp;caseResult=1&amp;status=0&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;
                break
                else
                curl -X POST -d &quot;id=$monkeyResultId&amp;caseResult=0&amp;status=0&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;
                sleep 10
                fi
                done
                }

                #提交job：云测支持直接通过上传测试包的链接，不用再单独上传测试包
                submit_job(){
                if [[ $os == &quot;Android&quot; ]]; then
                para=&quot;apk=$url&amp;misId=$misId&amp;conanKey=$conanKey&amp;alias=$alias&amp;devicesVersion=$devicesVersion&amp;intents=$intents&amp;events=$events&amp;throttle=$throttle&amp;maxWaitTime=$maxWaitTime&amp;after=notifyResultCommon&amp;notify=http://qaassist.sankuai.com/compass/api/autotest/job/result&quot;
                submitUrl=$submitAndroidUrl
                elif [[ $os == &quot;iOS&quot; ]]; then
                para=&quot;ipa=$url&amp;misId=$misId&amp;conanKey=$conanKey&amp;alias=$alias&amp;devicesVersion=$devicesVersion&amp;intents=$intents&amp;runTime=$runTime&amp;screenShot=$screenShot&amp;maxWaitTime=$maxWaitTime&amp;after=notifyResultCommon&amp;notify=http://qaassist.sankuai.com/compass/api/autotest/job/result&quot;
                submitUrl=$submitiOSUrl
                fi

                for i in {0..60}
                do
                jobResponse=`curl -d &quot;$para&quot; $submitUrl`
                sleep 5
                responseStatus=`echo $jobResponse | getJsonVal &quot;[&apos;status&apos;]&quot;`
                jobId=`echo $jobResponse | getJsonVal &quot;[&apos;jobId&apos;]&quot;`
                msg=`echo $jobResponse | getJsonVal &quot;[&apos;msg&apos;]&quot;`
                if [[ $responseStatus -eq 1 ]]; then
                echo $jobResponse
                break
                else
                curl -X POST -d &quot;id=$monkeyResultId&amp;jobId=$jobId&amp;submitResult=$msg&amp;status=0&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;
                sleep 30
                fi
                if [[ $i == 60 ]]; then
                echo $jobResponse
                fi

                done
                }

                echo &quot;----------开始执行测试-----------&quot;
                if [[ $url == &quot;&quot; ]]; then
                echo &quot;url为空&quot;
                exit 1
                fi

                url_content=(${url//,/ })
                echo &quot;url_count:&quot;${#url_content[@]}
                echo &quot;url_content:&quot;${url_content[@]}
                for var in ${url_content[@]}; do
                url=$var

                if [ $os == &quot;Android&quot; ];then
                echo &quot;------------开始处理intents-------------&quot;
                intents_content=(${intents//,/ })
                echo &quot;intents_count:&quot;${#intents_content[@]}
                scheme_count=0
                for scheme in ${intents_content[@]}; do
                scheme_count=`expr $scheme_count + 1`
                if [[ $scheme_count -eq ${#intents_content[@]}  ]]; then
                intents_list=$intents_list&quot;scheme$scheme_count=$scheme&quot;
                else
                intents_list=$intents_list&quot;scheme$scheme_count=$scheme&quot;,
                fi
                done
                intents=$intents_list
                echo &quot;intents：&quot;$intents
                echo &quot;--------------结束处理intents--------&quot;

                if [[ $loginCase ]]; then
                #更新任务状态为：开始上传预处理case
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=开始上传预处理case&amp;status=0&quot; &quot;$MONKEY_URL/updateData&quot;
                echo &quot;----------Uploading case-----------&quot;
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startUploadCase&quot;
                caseName=`upload_case $loginCase`
                echo &quot;caseName:&quot;$caseName
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endUploadCase&quot;
                #更新任务状态为：结束上传预处理case
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=结束上传预处理case&amp;status=0&quot; &quot;$MONKEY_URL/updateData&quot;
                fi

                #更新任务状态为：开始向云测提交job
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=开始向云测提交job&amp;status=0&quot; &quot;$MONKEY_URL/updateData&quot;
                echo &quot;------------Submit Job-------------&quot;
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startSubmit&quot;
                jobResponse=`submit_job`
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endSubmit&quot;

                elif [ $os == &quot;iOS&quot; ]; then
                if [[ $loginCase ]]; then
                #更新任务状态为：开始上传预处理case
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=开始上传预处理case&amp;status=0&quot; &quot;$MONKEY_URL/updateData&quot;
                echo &quot;----------Uploading case-----------&quot;
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startUploadCase&quot;
                caseName=`upload_case $loginCase`
                echo &quot;caseName:&quot;$caseName
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endUploadCase&quot;
                #更新任务状态为：结束上传预处理case
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=结束上传预处理case&amp;status=0&quot; &quot;$MONKEY_URL/updateData&quot;
                fi
                #更新任务状态为：开始向云测提交job
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=开始向云测提交job&amp;status=0&quot; &quot;$MONKEY_URL/updateData&quot;
                echo &quot;------------Submit Job-------------&quot;
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startSubmit&quot;
                jobResponse=`submit_job`
                curl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endSubmit&quot;
                fi

                echo &quot;jobResponse:&quot;$jobResponse
                jobId=`echo $jobResponse | getJsonVal &quot;[&apos;jobId&apos;]&quot;`
                msg=`echo $jobResponse | getJsonVal &quot;[&apos;msg&apos;]&quot;`
                echo &quot;jobId:&quot;$jobId
                curl -X POST -d &quot;buildId=$buildId&amp;platform=$os&amp;alias=$alias&amp;jobId=$jobId&amp;jobType=stability&quot; &quot;$JOB_PATH/add&quot;
                if [[ $jobId -ne -1 ]]; then
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=提交job成功&amp;jobId=$jobId&amp;status=1&amp;reportUrl=https://conan.sankuai.com/v2/new-stability/report/&quot;$jobId &quot;$MONKEY_URL/updateData&quot;
                else
                curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=$msg&amp;jobId=$jobId&amp;status=1&amp;reportUrl=https://conan.sankuai.com/v2/new-stability/report/&quot;$jobId &quot;$MONKEY_URL/updateData&quot;
                fi

                echo &quot;----------Jenkins Job Finished----------&quot;
                curl -X POST -d &quot;buildUrl=$jenkinsUrl&quot; &quot;$BUILD_PATH/update/end&quot;
                curl -X POST -d &quot;buildUrl=$jenkinsUrl&amp;status=1&quot; &quot;$BUILD_PATH/update/status&quot;

                endJobTime=$currentTime
                echo &quot;endJobTime:&quot;$endJobTime
            </command>
        </hudson.tasks.Shell>
    </builders>
    <publishers>
        <hudson.plugins.postbuildtask.PostbuildTask plugin="postbuild-task@1.8">
            <tasks>
                <hudson.plugins.postbuildtask.TaskProperties>
                    <logTexts>
                        <hudson.plugins.postbuildtask.LogProperties>
                            <logText>Aborted</logText>
                            <operator>OR</operator>
                        </hudson.plugins.postbuildtask.LogProperties>
                        <hudson.plugins.postbuildtask.LogProperties>
                            <logText>aborted</logText>
                            <operator>OR</operator>
                        </hudson.plugins.postbuildtask.LogProperties>
                    </logTexts>
                    <EscalateStatus>false</EscalateStatus>
                    <RunIfJobSuccessful>false</RunIfJobSuccessful>
                    <script>curl -X POST -d &quot;buildUrl=http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/$BUILD_NUMBER&quot; &quot;http://qaassist.sankuai.com/compass/api/autotest/build/update/end&quot;&#xd;
                        curl -X POST -d &quot;buildUrl=http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/$BUILD_NUMBER&amp;status=1&quot; &quot;$http://qaassist.sankuai.com/compass/api/autotest/build/update/status&quot;&#xd;
                        &#xd;
                        monkeyResultId=`curl -X POST -d &quot;jenkinsName=$JOB_BASE_NAME&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;`&#xd;
                        &#xd;
                        curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=任务被取消&amp;status=-1&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;  </script>
                </hudson.plugins.postbuildtask.TaskProperties>
                <hudson.plugins.postbuildtask.TaskProperties>
                    <logTexts>
                        <hudson.plugins.postbuildtask.LogProperties>
                            <logText>Failure</logText>
                            <operator>OR</operator>
                        </hudson.plugins.postbuildtask.LogProperties>
                    </logTexts>
                    <EscalateStatus>false</EscalateStatus>
                    <RunIfJobSuccessful>false</RunIfJobSuccessful>
                    <script>monkeyResultId=`curl -X POST -d &quot;jenkinsName=$JOB_BASE_NAME&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;`&#xd;
                        &#xd;
                        curl -X POST -d &quot;id=$monkeyResultId&amp;submitResult=任务失败&amp;status=-1&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;</script>
                </hudson.plugins.postbuildtask.TaskProperties>
            </tasks>
        </hudson.plugins.postbuildtask.PostbuildTask>
        <com.sankuai.meituan.ep.jenkins.notify.DxNotifier plugin="dx-notify-plugin@1.1.1">
            <recipients>$misId</recipients>
            <contentFile></contentFile>
            <removeDefaultTitleIfContentIsNotEmpty>false</removeDefaultTitleIfContentIsNotEmpty>
            <notifyUsersStarted>true</notifyUsersStarted>
            <notifyLastCommitUser>false</notifyLastCommitUser>
            <notifyMap class="enum-map" enum-type="com.sankuai.meituan.ep.jenkins.notify.BuildResult">
                <entry>
                    <com.sankuai.meituan.ep.jenkins.notify.BuildResult>SUCCESS</com.sankuai.meituan.ep.jenkins.notify.BuildResult>
                    <boolean>true</boolean>
                </entry>
                <entry>
                    <com.sankuai.meituan.ep.jenkins.notify.BuildResult>FAILURE</com.sankuai.meituan.ep.jenkins.notify.BuildResult>
                    <boolean>true</boolean>
                </entry>
                <entry>
                    <com.sankuai.meituan.ep.jenkins.notify.BuildResult>UNSTABLE</com.sankuai.meituan.ep.jenkins.notify.BuildResult>
                    <boolean>true</boolean>
                </entry>
                <entry>
                    <com.sankuai.meituan.ep.jenkins.notify.BuildResult>ABORTED</com.sankuai.meituan.ep.jenkins.notify.BuildResult>
                    <boolean>true</boolean>
                </entry>
                <entry>
                    <com.sankuai.meituan.ep.jenkins.notify.BuildResult>NOT_BUILT</com.sankuai.meituan.ep.jenkins.notify.BuildResult>
                    <boolean>false</boolean>
                </entry>
                <entry>
                    <com.sankuai.meituan.ep.jenkins.notify.BuildResult>FIXED</com.sankuai.meituan.ep.jenkins.notify.BuildResult>
                    <boolean>true</boolean>
                </entry>
            </notifyMap>
            <notifySuccess>true</notifySuccess>
            <notifyFailure>true</notifyFailure>
            <notifyFixed>true</notifyFixed>
            <notifyUnstable>true</notifyUnstable>
            <notifyAborted>true</notifyAborted>
            <notifyNotBuilt>false</notifyNotBuilt>
        </com.sankuai.meituan.ep.jenkins.notify.DxNotifier>
    </publishers>
    <buildWrappers>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper plugin="build-timeout@1.19">
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>180</timeoutMinutes>
            </strategy>
            <operationList>
                <hudson.plugins.build__timeout.operations.AbortOperation/>
            </operationList>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
    </buildWrappers>
</project>