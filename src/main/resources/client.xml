<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context.xsd">
    <!-- 1.OCR -->
    <!--<bean id="horusFoodSKU" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.FoodSKU"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageScoresBrightness" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageScoresBrightness"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageScoresAesthetic" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageScoresAesthetic"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageScoresMemorability" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageScoresMemorability"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageWatermarkRemoveWm" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageWatermarkRemoveWm"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageFaceMosaic" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageFaceMosaic"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusDgWaterMark" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.itc.serving.mtthrift.venuswatermark.DgWaterMark"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusDetectWaterMark" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.itc.serving.mtthrift.venuswatermark.DetectWaterMark"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageScoresClick" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageScoresClick"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusDishName" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.DishName"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusDishNameV2" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.DishNameV2"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusScreenerFilter" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ScreenerFilter"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusQrCodeDetection" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.QrCodeDetection"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageDuplicate" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageDuplicate"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageTagsLiren" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageTagsLiren"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageTagsXiuyu" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageTagsXiuyu"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageTagsSports" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageTagsSports"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageTagsEducation" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageTagsEducation"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageTagsQinzi" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageTagsQinzi"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageTagsWedding" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageTagsWedding"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageAutoCrop" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageAutoCrop"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageFoodFeature" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageFoodFeature"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value=""/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusCaptchaRecg" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.captchaRecg.captchaRecg"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusHotelWatermark" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.HotelWatermark"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusHotelWatermarkCompetitor" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.HotelWatermarkCompetitor"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--<bean id="horusImageWatermarkDcDet" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.ImageWatermarkDcDet"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->
    <!--<bean id="horusArplat" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.arplat.Arplat"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000" />-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->
    <!--<bean id="horusVideoServiceIf" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">-->
        <!--<property name="serviceInterface" value="com.meituan.horus.service.VideoServiceIf"/> &lt;!&ndash; 接口名 &ndash;&gt;-->
        <!--<property name="appKey" value="com.sankuai.sigma.compass"/>  &lt;!&ndash; 本地appkey &ndash;&gt;-->
        <!--<property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  &lt;!&ndash; 目标 Server Appkey  &ndash;&gt;-->
        <!--<property name="timeout" value="5000"/>-->
        <!--<property name="nettyIO" value="true" />-->
        <!--<property name="remoteUniProto" value="true" />-->
    <!--</bean>-->

    <!--horus.xml-->
    <bean id="ocrServicesBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.horus.service.OCRServices"/> <!-- 接口名 -->
        <property name="remoteServerPort" value="9001"/>
        <property name="appKey" value="com.sankuai.sigma.compass"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  <!-- 目标Server Appkey  -->
        <!--<property name="async" value="true"/>-->
        <property name="async" value="false"/>
        <property name="timeout" value="20000"/>
    </bean>

    <!--玲珑-->
    <bean id="opsService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.travel.mop.ad.resops.thrift.OpsService"/>
        <property name="appKey" value="com.sankuai.sigma.compass"/>
        <property name="remoteAppkey" value="com.sankuai.travel.osg.mopresadmin"/>
        <property name="remoteServerPort" value="10706"/>
        <property name="timeout" value="5000"/>
    </bean>

    <bean id="SMSQueryMessageService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/smsService/smsQueryMessageService_1.0.0" />
        <property name="interfaceName" value="com.dianping.sms.biz.SMSQueryMessageService" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
        <property name="timeout" value="1000" />
    </bean>

    <bean id="horusClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.horus.proxy.HorusProxyService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.sigma.compass"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.horus.horus.proxy"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <property name="remoteUniProto" value="true"/>
    </bean>

</beans>