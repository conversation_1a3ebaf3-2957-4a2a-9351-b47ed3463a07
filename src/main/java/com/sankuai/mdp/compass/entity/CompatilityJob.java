package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import java.io.Serializable;

/**
 * Created by xieyongrui on 2019/11/9.
 */

@Data
@TableName("autotest_compatility_job")
public class CompatilityJob implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer jenkinsId;

    private String platform;

    //维护 iOS 包链接
    private String imeituanUrl;

    //维护 安卓 包链接
    private String apkUrl;

    private String api;

    /** 枚举值
     * -1：触发测试(排队中)
     * 0：测试中
     * 1：测试完成
     * 2：取消测试
     * 3：跳过测试
     */
    private Integer status;

    //UI测试的云测JobId
    private String report;

    //埋点测试的云测JobId
    private String eventConanId;

    private String apkVersion;

    private String devicesVersion;

    private String baseLx;

    private String basePic;

    private String business;

    private String templateName;

    private Integer templateId;

    // {"QA":"","PM":"","UI":"","RD":""}
    private String user;

    private String mockData;

    /** 枚举值
     * 0：UI
     * 1：埋点
     * 2：UI&埋点
     */
    private Integer type;

    private Integer checkStatus;

    private String mockRule;

    @TableField(exist = false)
    private String operator;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    private String createdBy;

    // 手动触发任务的人
    private String misId;

    private String description;

    private String checkBy;

    private String downloadUrl;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventFinishAt;

    @TableField(exist = false)
    private Object jsonBasePic;

    @TableField(exist = false)
    private Object jsonBaseLx;

    @TableField(exist = false)
    private Object jsonMockData;

    @TableField(exist = false)
    private String templateUrl;

    private String address;

    private String failedReason;

    /** 枚举值
     * smoke：冒烟测试
     * compatibility：兼容性测试
     */
    private String scenes;

    private Boolean autoResult;

    private String finishedId;

    private String eventFinishedId;

    private String caseIds;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstFinish;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventFirstFinish;

    private String mockIds;

    private String caseName;

    private String eventConanidIos;

    private String reportIos;

    @TableField(exist = false)
    private String testMode;

//    private String finishedIdIos;
//
//    private String eventFinishedIdIos;
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date eventFirstFinishIos;
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date firstFinishIos;
}
