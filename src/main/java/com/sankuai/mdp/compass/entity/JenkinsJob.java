package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by xieyongrui on 2019/11/21.
 */

@Data
@TableName("jenkins_job")
public class JenkinsJob implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Integer id;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date jobStartAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitFinishAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseRunAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date jobFinishAt;

    private String status;

    private String url;

    private Integer buildNumber;

//    private String name;

}
