package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("dynamic_sdk_diff_report")
public class DynamicSdkReport {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer jenkinsId;

    private String status;

    private String platform;

    private String branch;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
}