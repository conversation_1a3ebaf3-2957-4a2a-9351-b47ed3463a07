package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.function.IntBinaryOperator;

/**
 * Created by xieyongrui on 2019/11/21.
 */

@Data
@TableName("autotest_jsonpath")
public class DynamicData implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String business;

    private String templatePath;

    private String templateName;

    private Integer isList;

    private String listPath;

    private String chineseName;

    private String zipPath;

    private int mockId;

    private int groupId;

    private String api;

    private String apiData;

    private String templateData;

    private String mockRule;

    private String mockArray;

    private  String address;
}
