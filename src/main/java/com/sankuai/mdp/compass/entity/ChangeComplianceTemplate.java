package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sunkangtong on 2022/5/9.
 */

@Data
@TableName("change_compliance_template")
public class ChangeComplianceTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String templateId;

    private String templateName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTime;

    private String onesUrl;

    private String tester;

    private String problematic;

    private String processUrl;

    private String extra;

    private boolean doneTest;

    private boolean doneNotice;

    private boolean doneGrey;

    private boolean doneRatify;

    private boolean doneObserve;


}
