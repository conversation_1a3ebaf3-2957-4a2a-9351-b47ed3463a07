package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("dynamic_sdk_xml_pic")
public class DynamicSdkUiDiff {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String xmlName;

    private String platform;

    private String branch;

    private String phoneModels;

    private Double similarity;

    private String apkUrl;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    private String xmlUrl;

    private String basePic;

    private String testPic;

    private String mockData;

    private String diffUrl;

    private Integer reportId;

}
