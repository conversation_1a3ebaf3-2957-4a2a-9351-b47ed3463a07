package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("autotest_failed_reason")
public class FailedReason {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer failedNumber;

    private String failedDescription;

    private String failedField;
}
