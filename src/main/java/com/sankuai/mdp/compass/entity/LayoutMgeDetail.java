package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by xieyongrui on 2019/11/17.
 */
@Data
@TableName("autotest_compatility_mge")
public class LayoutMgeDetail {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer jobId;

    private String conanId;

    private String taskId;

    private String business;

    private String templateName;

    //埋点描述
    private String eventDesc;

    private String androidResult;

    private String iosResult;

    private String androidContent;

    private String iosContent;

    private String eventType;

    private String bid;

    private String mockData;

    private Integer mockId;

    private String pic;

    private String iosPic;

    private String androidPic;

    private String oceanMge;

    private String config;

    private String operationComponent;

    @TableField(exist = false)
    private Object jsonAndroidContent;

    @TableField(exist = false)
    private Object jsonIosContent;

    @TableField(exist = false)
    private Object jsonMockData;

    @TableField(exist = false)
    private Object jsonCfgMge;

    @TableField(exist = false)
    private String jsonOceanMge;


}