package com.sankuai.mdp.compass.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("autotest_dynamic_page")
public class DynamicPage implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    //展位名称
    private String module;

    //展位中文名称
    private String moduleCn;

    //页面名称
    private String pageName;

    //页面跳转协议
    private String imeituanUrl;

    private Integer isList;

    private String listPath;

    private String apiData;

    //请求path
    private String apiPath;
}
