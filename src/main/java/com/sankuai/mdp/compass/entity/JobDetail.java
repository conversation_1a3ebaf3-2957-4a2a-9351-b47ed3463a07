package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by xieyongrui on 2019/11/17.
 */
@Data
@TableName("autotest_compatility_task")
public class JobDetail {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer jobId;

    private String reportId;

    private String taskId;

    private Integer status;

    private String business;

    private String templateName;

    private String basePic;

    private String comparePic;

    private String resultPic;

    private String deviceModel;

    private String deviceVersion;

    private String apkVersion;

    private String compareMge;

    private String mgeType;

    private String mockData;

    private Integer mockId;

    private Double score;

    private String description;

    private Integer type;

    private Double totalTime;

    private String mockType;

    private String beforeJumpPic;

    private String clickPic;

    private String afterJumpPic;

    private String platform;

    private String mockRule;

    private String mockKey;

    private String oceanMge;

    private String operationComponent;

    private int isValid;

    private String overlapPic;

    private String exra;
    // 合并后的截图地址
    private String mergedUrl;
    //设备分辨率
    private String deviceResolution;

    @TableField(exist = false)
    private Object jsonCompareMge;

    @TableField(exist = false)
    private Object jsonMockData;

    @TableField(exist = false)
    private Object jsonCfgMge;

    @TableField(exist = false)
    private Object jsonAndr;

    @TableField(exist = false)
    private Object jsonIos;

    @TableField(exist = false)
    private Integer checkStatus;



}