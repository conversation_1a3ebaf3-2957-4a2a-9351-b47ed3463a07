package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meituan.mdp.doc.generator.anno.ElementName;
import lombok.Data;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by sunkangtong on 2021/3/9.
 */

@Data
@TableName("autotest_dynamic_case")
public class DynamicCase implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * business 业务名称
     * templateName 模板名称
     * apiData 业务数据（外层）
     * templateName 模板数据（内层）
     * mockRule mock规则
     * createdBy 维护人
     * modiftyAt 最后编辑时间
     * module 页面/展位
     */


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String business;

    private String templateName;

//    private Integer isList;

    private String listPath;

//    private String chineseName;

//    private String zipPath;

//    private int mockId;

//    private int groupId;

    private String api;

    private String apiData;

    private String templateData;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modiftyAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    private String createdBy;

    private String description;

    private String mockRule;

//    private String mockArray;

    private  String dataSource;

    private String module;
}
