package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/21 10:22 上午
 */

@Data
@TableName("autotest_dynamic_template_publish")
public class DynamicTemplatePublishDetail {
    private static final long serialVersionUID = 1L;
    /**
     * templateName 模板名称
     * templateId 模板id
     * templateVersion 模板的版本信息
     * templateUrl 模板链接
     * templateStatus 模板状态，枚举值 灰度：gray 全量：publish
     * misId 操作人mis号
     */

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String templateName;

    private String templateId;

    private String templateVersion;

    private String templateUrl;

    private String templateStatus;

    private String misId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;
}
