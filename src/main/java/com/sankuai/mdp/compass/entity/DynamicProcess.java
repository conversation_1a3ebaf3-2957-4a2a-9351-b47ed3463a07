package com.sankuai.mdp.compass.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sunkangtong on 2021/3/9.
 */

@Data
@TableName("autotest_dynamic_process")
public class DynamicProcess implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String business;

    private String templateName;

    private  String createdBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishAt;

    private String testBy;

    private int processCondition;

    private String replenishUiStatus;

    private  String uiAcceptStatus;

    private String thirdStatus;

    private String smokeStatus;

    private String jobidTeam;

    private String step;


    private String onesUrl;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date smokeTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uiTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date replenishTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastTime;

    private String smokeUser;

    private String uiUser;

    private String replenishUser;

    private String lastUser;

    private String bugId;

    private String cancleReason;

}
