package com.sankuai.mdp.compass.aiTestRecord.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.aiTestRecord.entity.AiTestRecord;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AiTestRecord服务接口
 * 定义针对AiTestRecord实体的业务操作
 */
public interface AiTestRecordService extends IService<AiTestRecord> {

    /**
     * 添加测试记录
     * 
     * @param record 测试记录实体
     * @return 是否添加成功
     */
    boolean addRecord(AiTestRecord record);

    /**
     * 根据ID查询测试记录
     * 
     * @param id 记录ID
     * @return 测试记录实体
     */
    AiTestRecord getRecordById(Long id);

    /**
     * 根据设备ID查询最新的测试记录
     * 
     * @param deviceId 设备ID
     * @return 最新的测试记录，如果不存在则返回null
     */
    AiTestRecord getLatestByDeviceId(String deviceId);

    /**
     * 根据MIS ID查询测试记录列表
     * 
     * @param misId MIS ID
     * @return 符合条件的测试记录列表
     */
    List<AiTestRecord> getByMisId(String misId);

    /**
     * 根据进程ID查询测试记录列表
     * 
     * @param processId 进程ID
     * @return 符合条件的测试记录列表
     */
    List<AiTestRecord> getByProcessId(String processId);

    /**
     * 根据时间范围查询测试记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的测试记录列表
     */
    List<AiTestRecord> getByTimeRange(Date startTime, Date endTime);

    /**
     * 分页查询测试记录
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @return 分页结果
     */
    IPage<AiTestRecord> pageRecords(Page<AiTestRecord> page, String deviceId);

    /**
     * 分页查询测试记录，支持多条件筛选
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param misId MIS ID（可选）
     * @param processId 进程ID（可选）
     * @return 分页结果
     */
    IPage<AiTestRecord> pageRecords(Page<AiTestRecord> page, String deviceId, String misId, String processId);

    /**
     * 更新测试记录
     * 
     * @param record 测试记录实体
     * @return 是否更新成功
     */
    boolean updateRecord(AiTestRecord record);

    /**
     * 根据ID删除测试记录
     * 
     * @param id 记录ID
     * @return 是否删除成功
     */
    boolean deleteRecord(Long id);

    /**
     * 根据设备ID删除测试记录
     * 
     * @param deviceId 设备ID
     * @return 删除的记录数
     */
    int deleteByDeviceId(String deviceId);

    /**
     * 根据MIS ID删除测试记录
     * 
     * @param misId MIS ID
     * @return 删除的记录数
     */
    int deleteByMisId(String misId);

    /**
     * 根据进程ID删除测试记录
     * 
     * @param processId 进程ID
     * @return 删除的记录数
     */
    int deleteByProcessId(String processId);

    /**
     * 获取指定日期之后有测试记录的所有设备
     *
     * @param startDate 开始日期
     * @return 设备列表，每个设备包含设备ID和最后测试时间
     */
    List<Map<String, Object>> getRecentDevices(Date startDate);

    /**
     * 获取指定日期之后有测试记录的所有MIS ID
     *
     * @param startDate 开始日期
     * @return MIS ID列表，每个MIS ID包含MIS ID和最后测试时间
     */
    List<Map<String, Object>> getRecentMisIds(Date startDate);

    /**
     * 获取指定设备的测试轮次统计
     *
     * @param deviceId 设备ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 轮次统计信息
     */
    Map<String, Object> getDeviceRoundStats(String deviceId, Date startDate, Date endDate);

    /**
     * 高级查询接口，支持多条件组合查询
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param misId MIS ID（可选）
     * @param processId 进程ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param promptText 提示文本模糊查询（可选）
     * @param sortAsc 是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    IPage<AiTestRecord> advancedQueryRecords(Page<AiTestRecord> page, String deviceId, String misId, 
        String processId, Date startTime, Date endTime, String promptText, Boolean sortAsc);

    /**
     * 批量条件删除测试记录
     * 支持按设备ID、MIS ID、进程ID、时间范围等条件组合删除
     *
     * @param deviceId 设备ID（可选）
     * @param misId MIS ID（可选）
     * @param processId 进程ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 删除记录数限制，防止大量删除
     * @param skipSafetyCheck 是否跳过安全检查
     * @return 删除的记录数
     */
    int batchDeleteRecords(String deviceId, String misId, String processId, Date startTime, Date endTime, 
        Integer limit, Boolean skipSafetyCheck);

    /**
     * 清理测试记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @return 删除的记录数
     */
    int cleanupRecords(int keepRecords);

    /**
     * 向指定设备发送消息
     * 向目标IP和端口发送POST请求，请求体格式为 {"prompt": "消息内容"}
     *
     * @param targetIp 目标IP地址
     * @param port 目标端口
     * @param message 要发送的消息内容
     * @return 发送结果，包含响应信息
     */
    Map<String, Object> sendMessageToDevice(String targetIp, Integer port, String message);

    /**
     * 向指定设备发送确认消息
     * 调用设备的 /confirm 接口来确认设备是否为有效的Agent服务器
     *
     * @param targetIp 目标IP地址
     * @param port 目标端口
     * @return 确认结果
     */
    Map<String, Object> confirmDevice(String targetIp, Integer port);

    /**
     * 向指定设备提交任务
     * 调用设备的 /submit_task 接口来提交任务
     *
     * @param targetIp 目标IP地址
     * @param port 目标端口
     * @param taskDescription 任务描述
     * @param misId MIS ID
     * @return 提交结果，包含轮次号
     */
    Map<String, Object> submitTaskToDevice(String targetIp, Integer port, String taskDescription, String misId);

    /**
     * 查找可用的设备IP
     * 从 *********** 到 *********** 逐个查找可用的设备
     *
     * @param port 目标端口
     * @return 第一个可用的设备IP，如果没有找到则返回null
     */
    String findAvailableDeviceIp(Integer port);

    /**
     * 根据轮次号查询提示信息
     * 
     * @param roundNum 轮次号
     * @return 符合条件的测试记录列表
     */
    List<AiTestRecord> getByRoundNum(Integer roundNum);
} 