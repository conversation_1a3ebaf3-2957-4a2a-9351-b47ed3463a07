package com.sankuai.mdp.compass.aiTestRecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.aiTestRecord.entity.AiTestRecord;
import com.sankuai.mdp.compass.aiTestRecord.mapper.AiTestRecordMapper;
import com.sankuai.mdp.compass.aiTestRecord.service.AiTestRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * AiTestRecordService实现类
 * 实现对ai_test_record表的业务操作
 */
@Service
public class AiTestRecordServiceImpl extends ServiceImpl<AiTestRecordMapper, AiTestRecord> implements AiTestRecordService {

    private static final Logger logger = LoggerFactory.getLogger(AiTestRecordServiceImpl.class);

    @Autowired
    private AiTestRecordMapper baseMapper;

    /**
     * 添加测试记录
     * 
     * @param record 测试记录实体
     * @return 是否添加成功
     */
    @Override
    public boolean addRecord(AiTestRecord record) {
        // 设置默认创建时间
        if (record.getCreatedAt() == null) {
            record.setCreatedAt(new Date());
        }
        return save(record);
    }

    /**
     * 根据ID查询测试记录
     * 
     * @param id 记录ID
     * @return 测试记录实体
     */
    @Override
    public AiTestRecord getRecordById(Long id) {
        return getById(id);
    }

    /**
     * 根据设备ID查询最新的测试记录
     * 
     * @param deviceId 设备ID
     * @return 最新的测试记录，如果不存在则返回null
     */
    @Override
    public AiTestRecord getLatestByDeviceId(String deviceId) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getDeviceId, deviceId)
                    .orderByDesc(AiTestRecord::getCreatedAt)
                    .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据MIS ID查询测试记录列表
     * 
     * @param misId MIS ID
     * @return 符合条件的测试记录列表
     */
    @Override
    public List<AiTestRecord> getByMisId(String misId) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getMisId, misId)
                    .orderByDesc(AiTestRecord::getCreatedAt);
        return list(queryWrapper);
    }

    /**
     * 根据进程ID查询测试记录列表
     * 
     * @param processId 进程ID
     * @return 符合条件的测试记录列表
     */
    @Override
    public List<AiTestRecord> getByProcessId(String processId) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getProcessId, processId)
                    .orderByDesc(AiTestRecord::getCreatedAt);
        return list(queryWrapper);
    }

    /**
     * 根据时间范围查询测试记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的测试记录列表
     */
    @Override
    public List<AiTestRecord> getByTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(startTime != null, AiTestRecord::getCreatedAt, startTime)
                    .le(endTime != null, AiTestRecord::getCreatedAt, endTime)
                    .orderByDesc(AiTestRecord::getCreatedAt);
        return list(queryWrapper);
    }

    /**
     * 分页查询测试记录
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @return 分页结果
     */
    @Override
    public IPage<AiTestRecord> pageRecords(Page<AiTestRecord> page, String deviceId) {
        QueryWrapper<AiTestRecord> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_Id", deviceId);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc("Created_At");
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 分页查询测试记录，支持多条件筛选
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param misId MIS ID（可选）
     * @param processId 进程ID（可选）
     * @return 分页结果
     */
    @Override
    public IPage<AiTestRecord> pageRecords(Page<AiTestRecord> page, String deviceId, String misId, String processId) {
        QueryWrapper<AiTestRecord> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_Id", deviceId);
        }
        
        // 如果指定了MIS ID，添加条件
        if (misId != null && !misId.isEmpty()) {
            queryWrapper.eq("Mis_Id", misId);
        }
        
        // 如果指定了进程ID，添加条件
        if (processId != null && !processId.isEmpty()) {
            queryWrapper.eq("Process_Id", processId);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc("Created_At");
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 更新测试记录
     * 
     * @param record 测试记录实体
     * @return 是否更新成功
     */
    @Override
    public boolean updateRecord(AiTestRecord record) {
        return updateById(record);
    }

    /**
     * 根据ID删除测试记录
     * 
     * @param id 记录ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteRecord(Long id) {
        return removeById(id);
    }

    /**
     * 根据设备ID删除测试记录
     * 
     * @param deviceId 设备ID
     * @return 删除的记录数
     */
    @Override
    public int deleteByDeviceId(String deviceId) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getDeviceId, deviceId);
        return count(queryWrapper) > 0 ? baseMapper.delete(queryWrapper) : 0;
    }

    /**
     * 根据MIS ID删除测试记录
     * 
     * @param misId MIS ID
     * @return 删除的记录数
     */
    @Override
    public int deleteByMisId(String misId) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getMisId, misId);
        return count(queryWrapper) > 0 ? baseMapper.delete(queryWrapper) : 0;
    }

    /**
     * 根据进程ID删除测试记录
     * 
     * @param processId 进程ID
     * @return 删除的记录数
     */
    @Override
    public int deleteByProcessId(String processId) {
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getProcessId, processId);
        return count(queryWrapper) > 0 ? baseMapper.delete(queryWrapper) : 0;
    }

    /**
     * 获取指定日期之后有测试记录的所有设备
     *
     * @param startDate 开始日期
     * @return 设备列表，每个设备包含设备ID和最后测试时间
     */
    @Override
    public List<Map<String, Object>> getRecentDevices(Date startDate) {
        // 创建查询条件，筛选创建时间大于等于startDate的记录
        QueryWrapper<AiTestRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("Created_At", startDate);
        queryWrapper.orderByDesc("Created_At");
        queryWrapper.select("Device_Id", "MAX(Created_At) as lastTestTime");
        queryWrapper.groupBy("Device_Id");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> device : result) {
            // 将数据库字段名转换为驼峰命名
            if (device.containsKey("Device_Id")) {
                device.put("deviceId", device.get("Device_Id"));
                device.remove("Device_Id");
            }
            if (device.containsKey("lastTestTime")) {
                device.put("lastTestTime", device.get("lastTestTime"));
            }
        }
        
        return result;
    }

    /**
     * 获取指定日期之后有测试记录的所有MIS ID
     *
     * @param startDate 开始日期
     * @return MIS ID列表，每个MIS ID包含MIS ID和最后测试时间
     */
    @Override
    public List<Map<String, Object>> getRecentMisIds(Date startDate) {
        // 创建查询条件，筛选创建时间大于等于startDate的记录
        QueryWrapper<AiTestRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("Created_At", startDate);
        queryWrapper.orderByDesc("Created_At");
        queryWrapper.select("Mis_Id", "MAX(Created_At) as lastTestTime");
        queryWrapper.groupBy("Mis_Id");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> misRecord : result) {
            // 将数据库字段名转换为驼峰命名
            if (misRecord.containsKey("Mis_Id")) {
                misRecord.put("misId", misRecord.get("Mis_Id"));
                misRecord.remove("Mis_Id");
            }
            if (misRecord.containsKey("lastTestTime")) {
                misRecord.put("lastTestTime", misRecord.get("lastTestTime"));
            }
        }
        
        return result;
    }

    /**
     * 获取指定设备的测试轮次统计
     *
     * @param deviceId 设备ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 轮次统计信息
     */
    @Override
    public Map<String, Object> getDeviceRoundStats(String deviceId, Date startDate, Date endDate) {
        Map<String, Object> result = new HashMap<>();
        
        // 查询指定设备和时间范围内的记录
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getDeviceId, deviceId)
                   .ge(startDate != null, AiTestRecord::getCreatedAt, startDate)
                   .le(endDate != null, AiTestRecord::getCreatedAt, endDate);
        
        List<AiTestRecord> records = list(queryWrapper);
        
        // 统计总记录数
        result.put("totalRecords", records.size());
        
        // 统计轮次信息
        if (!records.isEmpty()) {
            int maxRound = records.stream().mapToInt(AiTestRecord::getRoundNum).max().orElse(0);
            int minRound = records.stream().mapToInt(AiTestRecord::getRoundNum).min().orElse(0);
            
            result.put("maxRound", maxRound);
            result.put("minRound", minRound);
            result.put("roundRange", maxRound - minRound + 1);
        } else {
            result.put("maxRound", 0);
            result.put("minRound", 0);
            result.put("roundRange", 0);
        }
        
        result.put("deviceId", deviceId);
        result.put("startDate", startDate);
        result.put("endDate", endDate);
        
        return result;
    }

    /**
     * 高级查询接口，支持多条件组合查询
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param misId MIS ID（可选）
     * @param processId 进程ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param promptText 提示文本模糊查询（可选）
     * @param sortAsc 是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @Override
    public IPage<AiTestRecord> advancedQueryRecords(Page<AiTestRecord> page, String deviceId, String misId, 
            String processId, Date startTime, Date endTime, String promptText, Boolean sortAsc) {
        QueryWrapper<AiTestRecord> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_Id", deviceId);
        }
        
        // 如果指定了MIS ID，添加条件
        if (misId != null && !misId.isEmpty()) {
            queryWrapper.eq("Mis_Id", misId);
        }
        
        // 如果指定了进程ID，添加条件
        if (processId != null && !processId.isEmpty()) {
            queryWrapper.eq("Process_Id", processId);
        }
        
        // 添加时间范围条件
        if (startTime != null) {
            queryWrapper.ge("Created_At", startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le("Created_At", endTime);
        }
        
        // 添加提示文本模糊查询条件
        if (promptText != null && !promptText.isEmpty()) {
            queryWrapper.like("Prompt_Text", promptText);
        }
        
        // 根据sortAsc参数决定排序方式
        if (sortAsc != null && sortAsc) {
            queryWrapper.orderByAsc("Created_At"); // 正序排序
        } else {
            queryWrapper.orderByDesc("Created_At"); // 默认倒序排序
        }
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 批量条件删除测试记录
     * 支持按设备ID、MIS ID、进程ID、时间范围等条件组合删除
     *
     * @param deviceId 设备ID（可选）
     * @param misId MIS ID（可选）
     * @param processId 进程ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 删除记录数限制，防止大量删除
     * @param skipSafetyCheck 是否跳过安全检查
     * @return 删除的记录数
     */
    @Override
    @Transactional
    public int batchDeleteRecords(String deviceId, String misId, String processId, Date startTime, Date endTime, 
                                 Integer limit, Boolean skipSafetyCheck) {
        
        logger.info("开始批量删除操作，条件：deviceId={}, misId={}, processId={}, " +
                   "startTime={}, endTime={}, limit={}, skipSafetyCheck={}", 
                   deviceId, misId, processId, startTime, endTime, limit, skipSafetyCheck);
        
        // 构建条件查询器
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加各种条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq(AiTestRecord::getDeviceId, deviceId);
        }
        
        if (misId != null && !misId.isEmpty()) {
            queryWrapper.eq(AiTestRecord::getMisId, misId);
        }
        
        if (processId != null && !processId.isEmpty()) {
            queryWrapper.eq(AiTestRecord::getProcessId, processId);
        }
        
        // 处理时间范围
        if (startTime != null) {
            queryWrapper.ge(AiTestRecord::getCreatedAt, startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le(AiTestRecord::getCreatedAt, endTime);
        }
        
        // 如果设置了限制数量
        if (limit != null && limit > 0) {
            // 首先查询符合条件的记录总数
            long totalCount = count(queryWrapper);
            
            // 检查是否超过了限制
            if (totalCount > limit && !Boolean.TRUE.equals(skipSafetyCheck)) {
                logger.warn("符合删除条件的记录数({})超过了限制({}), 由于未跳过安全检查，删除操作已取消", totalCount, limit);
                throw new IllegalArgumentException("符合删除条件的记录数(" + totalCount + ")超过了限制(" + limit + "), 删除操作已取消。如需继续删除，请设置skipSafetyCheck=true");
            }
            
            // 如果记录数超过限制但跳过了安全检查，记录警告日志
            if (totalCount > limit) {
                logger.warn("符合删除条件的记录数({})超过了限制({}), 但已跳过安全检查，继续执行删除操作", totalCount, limit);
            }
            
            // 按照创建时间降序排序，保留最新的记录
            if (totalCount > limit && Boolean.TRUE.equals(skipSafetyCheck)) {
                // 查找出要保留的记录的最早时间
                Page<AiTestRecord> page = new Page<>(1, limit);
                page.setDesc("Created_At"); // 按时间降序排序，保留最新的记录
                
                Page<AiTestRecord> latestRecords = baseMapper.selectPage(page, queryWrapper);
                if (latestRecords.getRecords().size() > 0) {
                    // 获取符合条件的limit条最新记录中的最早时间
                    Date earliestTimeToKeep = latestRecords.getRecords()
                                                       .get(latestRecords.getRecords().size() - 1)
                                                       .getCreatedAt();
                    
                    // 修改查询条件，只删除早于该时间的记录
                    queryWrapper.lt(AiTestRecord::getCreatedAt, earliestTimeToKeep);
                }
            }
        }
        
        // 执行删除操作，并返回删除的记录数
        int deletedCount = baseMapper.delete(queryWrapper);
        logger.info("批量删除操作完成，共删除{}条记录", deletedCount);
        
        return deletedCount;
    }

    /**
     * 清理测试记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @return 删除的记录数
     */
    @Override
    @Async
    @Transactional
    public int cleanupRecords(int keepRecords) {
        // 获取总记录数
        long totalCount = count();

        // 如果总记录数小于等于保留数量，不需要清理
        if (totalCount <= keepRecords) {
            return 0;
        }

        // 计算需要删除的记录数
        long deleteCount = totalCount - keepRecords;

        // 查询要保留的最小ID（按创建时间排序，保留最新的记录）
        QueryWrapper<AiTestRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("MIN(Id) as minId")
                   .orderByDesc("Created_At")
                   .last("LIMIT " + keepRecords);
        
        Map<String, Object> result = getMap(queryWrapper);
        if (result == null || !result.containsKey("minId")) {
            return 0;
        }

        Long minIdToKeep = Long.valueOf(result.get("minId").toString());
        
        // 删除ID小于minIdToKeep的记录
        LambdaQueryWrapper<AiTestRecord> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.lt(AiTestRecord::getId, minIdToKeep);
        
        // 执行删除操作
        int deletedCount = baseMapper.delete(deleteWrapper);
        
        // 返回删除的记录数
        return deletedCount;
    }

    /**
     * 向指定设备发送消息
     * 向目标IP和端口发送POST请求，请求体格式为 {"prompt": "消息内容"}
     *
     * @param targetIp 目标IP地址
     * @param port 目标端口
     * @param message 要发送的消息内容
     * @return 发送结果，包含响应信息
     */
    @Override
    public Map<String, Object> sendMessageToDevice(String targetIp, Integer port, String message) {
        Map<String, Object> result = new HashMap<>();
        RestTemplate restTemplate = new RestTemplate();
        
        try {
            // 构建目标URL
            String targetUrl = "http://" + targetIp + ":" + port;
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 构建请求体，格式为 {"prompt": "消息内容"}
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("prompt", message);
            
            // 创建HTTP请求实体
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            logger.info("向设备发送消息: URL={}, 消息={}", targetUrl, message);
            
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(targetUrl, requestEntity, String.class);
            
            // 处理响应
            result.put("success", true);
            result.put("statusCode", response.getStatusCodeValue());
            result.put("responseBody", response.getBody());
            result.put("targetUrl", targetUrl);
            result.put("sentMessage", message);
            
            logger.info("消息发送成功: URL={}, 状态码={}, 响应={}", 
                       targetUrl, response.getStatusCodeValue(), response.getBody());
            
        } catch (RestClientException e) {
            logger.error("向设备发送消息失败: URL=http://{}:{}, 消息={}, 异常={}", 
                        targetIp, port, message, e.getMessage(), e);
            
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("targetUrl", "http://" + targetIp + ":" + port);
            result.put("sentMessage", message);
        } catch (Exception e) {
            logger.error("发送消息时发生未知异常: URL=http://{}:{}, 消息={}, 异常={}", 
                        targetIp, port, message, e.getMessage(), e);
            
            result.put("success", false);
            result.put("error", "未知异常: " + e.getMessage());
            result.put("targetUrl", "http://" + targetIp + ":" + port);
            result.put("sentMessage", message);
        }
        
        return result;
    }

    /**
     * 向指定设备发送确认消息
     * 调用设备的 /confirm 接口来确认设备是否为有效的Agent服务器
     *
     * @param targetIp 目标IP地址
     * @param port 目标端口
     * @return 确认结果
     */
    @Override
    public Map<String, Object> confirmDevice(String targetIp, Integer port) {
        Map<String, Object> result = new HashMap<>();
        RestTemplate restTemplate = new RestTemplate();
        
        try {
            // 构建目标URL
            String targetUrl = "http://" + targetIp + ":" + port + "/confirm";
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 构建请求体，格式为 {"message": "aitest confirm"}
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("message", "aitest confirm");
            
            // 创建HTTP请求实体
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            logger.info("向设备发送确认消息: URL={}", targetUrl);
            
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(targetUrl, requestEntity, String.class);
            
            // 处理响应
            result.put("success", true);
            result.put("statusCode", response.getStatusCodeValue());
            result.put("responseBody", response.getBody());
            result.put("targetUrl", targetUrl);
            result.put("targetIp", targetIp);
            
            // 检查响应是否为 "confirmed"
            String responseBody = response.getBody();
            if (responseBody != null && responseBody.contains("confirmed")) {
                result.put("confirmed", true);
                logger.info("设备确认成功: IP={}, 响应={}", targetIp, responseBody);
            } else {
                result.put("confirmed", false);
                logger.warn("设备确认失败: IP={}, 响应={}", targetIp, responseBody);
            }
            
        } catch (RestClientException e) {
            logger.error("向设备发送确认消息失败: IP={}, 异常={}", targetIp, e.getMessage());
            
            result.put("success", false);
            result.put("confirmed", false);
            result.put("error", e.getMessage());
            result.put("targetUrl", "http://" + targetIp + ":" + port + "/confirm");
            result.put("targetIp", targetIp);
        } catch (Exception e) {
            logger.error("发送确认消息时发生未知异常: IP={}, 异常={}", targetIp, e.getMessage());
            
            result.put("success", false);
            result.put("confirmed", false);
            result.put("error", "未知异常: " + e.getMessage());
            result.put("targetUrl", "http://" + targetIp + ":" + port + "/confirm");
            result.put("targetIp", targetIp);
        }
        
        return result;
    }

    /**
     * 向指定设备提交任务
     * 调用设备的 /submit_task 接口来提交任务
     *
     * @param targetIp 目标IP地址
     * @param port 目标端口
     * @param taskDescription 任务描述
     * @param misId MIS ID
     * @return 提交结果，包含轮次号
     */
    @Override
    public Map<String, Object> submitTaskToDevice(String targetIp, Integer port, String taskDescription, String misId) {
        Map<String, Object> result = new HashMap<>();
        RestTemplate restTemplate = new RestTemplate();
        
        try {
            // 构建目标URL
            String targetUrl = "http://" + targetIp + ":" + port + "/submit_task";
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 构建请求体，格式为 {"task_description": "任务描述", "mis_id": "MIS ID"}
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("task_description", taskDescription);
            requestBody.put("mis_id", misId);
            
            // 创建HTTP请求实体
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            logger.info("向设备提交任务: URL={}, 任务描述={}, MIS ID={}", targetUrl, taskDescription, misId);
            
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(targetUrl, requestEntity, String.class);
            
            // 处理响应
            result.put("success", true);
            result.put("statusCode", response.getStatusCodeValue());
            result.put("responseBody", response.getBody());
            result.put("targetUrl", targetUrl);
            result.put("targetIp", targetIp);
            result.put("taskDescription", taskDescription);
            result.put("misId", misId);
            
            // 解析响应获取轮次号
            String responseBody = response.getBody();
            if (responseBody != null && !responseBody.isEmpty()) {
                try {
                    // 解析JSON响应
                    com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    Map<String, Object> responseJson = objectMapper.readValue(responseBody, Map.class);
                    
                    // 提取轮次号
                    String roundId = (String) responseJson.get("round_id");
                    if (roundId != null) {
                        result.put("roundId", roundId);
                        logger.info("任务提交成功: IP={}, 轮次号={}, MIS ID={}", targetIp, roundId, misId);
                    } else {
                        logger.warn("任务提交响应中未找到轮次号: IP={}, 响应={}", targetIp, responseBody);
                    }
                } catch (Exception e) {
                    logger.error("解析任务提交响应失败: IP={}, 响应={}, MIS ID={}, 异常={}", targetIp, responseBody, misId, e.getMessage());
                }
            }
            
        } catch (RestClientException e) {
            logger.error("向设备提交任务失败: IP={}, 任务描述={}, MIS ID={}, 异常={}", targetIp, taskDescription, misId, e.getMessage());
            
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("targetUrl", "http://" + targetIp + ":" + port + "/submit_task");
            result.put("targetIp", targetIp);
            result.put("taskDescription", taskDescription);
            result.put("misId", misId);
        } catch (Exception e) {
            logger.error("提交任务时发生未知异常: IP={}, 任务描述={}, MIS ID={}, 异常={}", targetIp, taskDescription, misId, e.getMessage());
            
            result.put("success", false);
            result.put("error", "未知异常: " + e.getMessage());
            result.put("targetUrl", "http://" + targetIp + ":" + port + "/submit_task");
            result.put("targetIp", targetIp);
            result.put("taskDescription", taskDescription);
            result.put("misId", misId);
        }
        
        return result;
    }

    /**
     * 查找可用的设备IP
     * 从 *********** 到 ************* 逐个查找可用的设备
     *
     * @param port 目标端口
     * @return 第一个可用的设备IP，如果没有找到则返回null
     */
    @Override
    public String findAvailableDeviceIp(Integer port) {
        logger.info("开始查找可用设备IP，端口: {}", port);
        
        // 从 *********** 到 ************* 逐个查找
        for (int segment = 74; segment <= 77; segment++) {
            for (int host = 1; host <= 254; host++) {
                String targetIp = "172.18." + segment + "." + host;
                
                logger.debug("正在检查设备: {}", targetIp);
                
                // 调用确认接口
                Map<String, Object> confirmResult = confirmDevice(targetIp, port);
                
                // 如果确认成功，返回该IP
                if (Boolean.TRUE.equals(confirmResult.get("confirmed"))) {
                    logger.info("找到可用设备: {}", targetIp);
                    return targetIp;
                }
            }
        }
        
        logger.warn("未找到可用设备");
        return null;
    }

    /**
     * 根据轮次号查询提示信息
     * 
     * @param roundNum 轮次号
     * @return 符合条件的测试记录列表
     */
    @Override
    public List<AiTestRecord> getByRoundNum(Integer roundNum) {
        logger.debug("根据轮次号查询提示信息: {}", roundNum);
        
        if (roundNum == null) {
            logger.warn("轮次号不能为空");
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<AiTestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestRecord::getRoundNum, roundNum);
        queryWrapper.orderByDesc(AiTestRecord::getCreatedAt);
        
        List<AiTestRecord> records = baseMapper.selectList(queryWrapper);
        logger.debug("根据轮次号 {} 查询到 {} 条记录", roundNum, records.size());
        
        return records;
    }
} 