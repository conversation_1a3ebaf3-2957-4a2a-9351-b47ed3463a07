package com.sankuai.mdp.compass.aiTestRecord.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sankuai.mdp.compass.aiTestRecord.entity.AiTestRecord;
import com.sankuai.mdp.compass.aiTestRecord.service.AiTestRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;

/**
 * AiTestRecord控制器
 * 提供RESTful API接口，用于对AiTestRecord数据的CRUD操作
 */
@RestController
@RequestMapping("compass/api/ai-test-record")
public class AiTestRecordController {

    private static final Logger logger = LoggerFactory.getLogger(AiTestRecordController.class);

    // 数据库字段长度常量
    private static final int MAX_MIS_ID_LENGTH = 50;
    private static final int MAX_DEVICE_ID_LENGTH = 100;
    private static final int MAX_PROMPT_TEXT_LENGTH = 1000;
    private static final int MAX_REMARK_LENGTH = 255;

    @Autowired
    private AiTestRecordService aiTestRecordService;

    /**
     * 创建AI测试记录
     *
     * @param record AI测试记录实体
     * @return 创建结果
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> createRecord(@RequestBody AiTestRecord record) {
        Map<String, Object> result = new HashMap<>();

        // 数据校验
        Map<String, String> validationErrors = validateRecordData(record);
        if (!validationErrors.isEmpty()) {
            result.put("success", false);
            result.put("message", "数据校验失败");
            result.put("errors", validationErrors);
            logger.warn("AI测试记录数据校验失败: {}, 数据: {}", validationErrors, record);
            return ResponseEntity.badRequest().body(result);
        }

        // 设置创建时间
        if (record.getCreatedAt() == null) {
            record.setCreatedAt(new Date());
        }

        boolean success = false;
        try {
            success = aiTestRecordService.addRecord(record);
        } catch (Exception e) {
            logger.error("添加AI测试记录失败，数据: {}, 异常: {}", record, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "添加AI测试记录失败: " + e.getMessage());
            result.put("failedData", record);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }

        if (success) {
            logger.info("添加AI测试记录成功, ID: {}, 设备ID: {}", record.getId(), record.getDeviceId());
            result.put("success", true);
            result.put("message", "添加AI测试记录成功");
            result.put("data", record);
            return ResponseEntity.ok(result);
        } else {
            logger.error("添加AI测试记录失败，数据: {}", record);
            result.put("success", false);
            result.put("message", "添加AI测试记录失败");
            result.put("failedData", record);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 校验AI测试记录数据
     * 
     * @param record AI测试记录实体
     * @return 校验错误信息，如果没有错误则返回空Map
     */
    private Map<String, String> validateRecordData(AiTestRecord record) {
        Map<String, String> errors = new HashMap<>();

        // 检查必填字段
        if (record.getMisId() == null || record.getMisId().trim().isEmpty()) {
            errors.put("misId", "MIS ID不能为空");
        } else if (record.getMisId().length() > MAX_MIS_ID_LENGTH) {
            errors.put("misId", "MIS ID长度不能超过" + MAX_MIS_ID_LENGTH + "个字符");
        }

        if (record.getProcessId() == null) {
            errors.put("processId", "进程ID不能为空");
        }

        if (record.getPromptText() == null || record.getPromptText().trim().isEmpty()) {
            errors.put("promptText", "提示文本不能为空");
        } else if (record.getPromptText().length() > MAX_PROMPT_TEXT_LENGTH) {
            errors.put("promptText", "提示文本长度不能超过" + MAX_PROMPT_TEXT_LENGTH + "个字符");
        }

        // 设备ID可以为空，但如果不为空则检查长度
        if (record.getDeviceId() != null && record.getDeviceId().length() > MAX_DEVICE_ID_LENGTH) {
            errors.put("deviceId", "设备ID长度不能超过" + MAX_DEVICE_ID_LENGTH + "个字符");
        }

        if (record.getRoundNum() == null) {
            errors.put("roundNum", "轮次号不能为空");
        } else if (record.getRoundNum() < 0) {
            errors.put("roundNum", "轮次号不能为负数");
        }

        // 检查可选字段长度
        if (record.getRemark() != null && record.getRemark().length() > MAX_REMARK_LENGTH) {
            errors.put("remark", "备注信息长度不能超过" + MAX_REMARK_LENGTH + "个字符");
        }

        return errors;
    }

    /**
     * 根据ID查询AI测试记录
     *
     * @param id 记录ID
     * @return AI测试记录
     */
    @GetMapping("/find/{id}")
    public ResponseEntity<Map<String, Object>> getRecordById(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        AiTestRecord record = aiTestRecordService.getRecordById(id);

        if (record != null) {
            result.put("success", true);
            result.put("data", record);
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "未找到ID为" + id + "的AI测试记录");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 根据设备ID查询最新的AI测试记录
     *
     * @param deviceId 设备ID
     * @return 最新的AI测试记录
     */
    @GetMapping("/device/latest")
    public ResponseEntity<Map<String, Object>> getLatestByDeviceId(@RequestParam String deviceId) {
        Map<String, Object> result = new HashMap<>();
        AiTestRecord record = aiTestRecordService.getLatestByDeviceId(deviceId);

        if (record != null) {
            result.put("success", true);
            result.put("data", record);
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "未找到设备ID为" + deviceId + "的AI测试记录");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 根据MIS ID查询AI测试记录列表
     *
     * @param misId MIS ID
     * @return AI测试记录列表
     */
    @GetMapping("/mis/{misId}")
    public ResponseEntity<Map<String, Object>> getByMisId(@PathVariable String misId) {
        Map<String, Object> result = new HashMap<>();
        List<AiTestRecord> records = aiTestRecordService.getByMisId(misId);

        result.put("success", true);
        result.put("data", records);
        result.put("count", records.size());
        result.put("message", "成功获取MIS ID为" + misId + "的AI测试记录");
        return ResponseEntity.ok(result);
    }

    /**
     * 根据进程ID查询AI测试记录列表
     *
     * @param processId 进程ID
     * @return AI测试记录列表
     */
    @GetMapping("/process/{processId}")
    public ResponseEntity<Map<String, Object>> getByProcessId(@PathVariable String processId) {
        Map<String, Object> result = new HashMap<>();
        List<AiTestRecord> records = aiTestRecordService.getByProcessId(processId);

        result.put("success", true);
        result.put("data", records);
        result.put("count", records.size());
        result.put("message", "成功获取进程ID为" + processId + "的AI测试记录");
        return ResponseEntity.ok(result);
    }

    /**
     * 根据轮次号查询AI测试记录列表
     *
     * @param roundNum 轮次号
     * @return AI测试记录列表
     */
    @GetMapping("/round/{roundNum}")
    public ResponseEntity<Map<String, Object>> getByRoundNum(@PathVariable Integer roundNum) {
        Map<String, Object> result = new HashMap<>();
        
        // 参数验证
        if (roundNum == null) {
            result.put("success", false);
            result.put("message", "轮次号不能为空");
            return ResponseEntity.badRequest().body(result);
        }
        
        List<AiTestRecord> records = aiTestRecordService.getByRoundNum(roundNum);

        result.put("success", true);
        result.put("data", records);
        result.put("count", records.size());
        result.put("message", "成功获取轮次号为" + roundNum + "的AI测试记录");
        return ResponseEntity.ok(result);
    }

    /**
     * 根据时间范围查询AI测试记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return AI测试记录列表
     */
    @GetMapping("/time-range")
    public ResponseEntity<Map<String, Object>> getByTimeRange(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        Map<String, Object> result = new HashMap<>();
        List<AiTestRecord> records = aiTestRecordService.getByTimeRange(startTime, endTime);

        result.put("success", true);
        result.put("data", records);
        result.put("count", records.size());
        result.put("message", "成功获取指定时间范围内的AI测试记录");
        return ResponseEntity.ok(result);
    }

    /**
     * 获取近n天内有AI测试记录的所有设备列表
     *
     * @param days 查询的天数范围，默认为3天
     * @return 设备列表，包含设备ID和最后测试时间
     */
    @GetMapping("/recent-devices")
    public ResponseEntity<Map<String, Object>> getRecentDevices(
            @RequestParam(required = false, defaultValue = "3") Integer days) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        // 计算n天前的时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.add(java.util.Calendar.DAY_OF_MONTH, -days);
        Date daysAgo = calendar.getTime();

        // 获取近n天的设备列表
        List<Map<String, Object>> devices = aiTestRecordService.getRecentDevices(daysAgo);

        result.put("success", true);
        result.put("data", devices);
        result.put("count", devices.size());
        result.put("days", days);
        result.put("message", "成功获取近" + days + "天内的设备列表");

        return ResponseEntity.ok(result);
    }

    /**
     * 获取近n天内有AI测试记录的所有MIS ID列表
     *
     * @param days 查询的天数范围，默认为3天
     * @return MIS ID列表，包含MIS ID和最后测试时间
     */
    @GetMapping("/recent-mis-ids")
    public ResponseEntity<Map<String, Object>> getRecentMisIds(
            @RequestParam(required = false, defaultValue = "3") Integer days) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        // 计算n天前的时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.add(java.util.Calendar.DAY_OF_MONTH, -days);
        Date daysAgo = calendar.getTime();

        // 获取近n天的MIS ID列表
        List<Map<String, Object>> misIds = aiTestRecordService.getRecentMisIds(daysAgo);

        result.put("success", true);
        result.put("data", misIds);
        result.put("count", misIds.size());
        result.put("days", days);
        result.put("message", "成功获取近" + days + "天内的MIS ID列表");

        return ResponseEntity.ok(result);
    }

    /**
     * 获取指定设备的测试轮次统计
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 轮次统计信息
     */
    @GetMapping("/device/round-stats")
    public ResponseEntity<Map<String, Object>> getDeviceRoundStats(
            @RequestParam String deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        Map<String, Object> result = new HashMap<>();
        Map<String, Object> stats = aiTestRecordService.getDeviceRoundStats(deviceId, startTime, endTime);

        result.put("success", true);
        result.put("data", stats);
        result.put("message", "成功获取设备轮次统计信息");
        return ResponseEntity.ok(result);
    }

    /**
     * 分页查询AI测试记录
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param deviceId 设备ID（可选）
     * @return 分页结果
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> pageRecords(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String deviceId) {

        Map<String, Object> result = new HashMap<>();
        Page<AiTestRecord> page = new Page<>(pageNum, pageSize);

        IPage<AiTestRecord> pageResult = aiTestRecordService.pageRecords(page, deviceId);

        result.put("success", true);
        result.put("data", pageResult);
        return ResponseEntity.ok(result);
    }

    /**
     * 更新AI测试记录
     *
     * @param id     记录ID
     * @param record AI测试记录实体
     * @return 更新结果
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<Map<String, Object>> updateRecord(
            @PathVariable Long id,
            @RequestBody AiTestRecord record) {

        Map<String, Object> result = new HashMap<>();
        record.setId(id);

        boolean success = aiTestRecordService.updateRecord(record);

        if (success) {
            result.put("success", true);
            result.put("message", "更新AI测试记录成功");
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "更新AI测试记录失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 根据轮次ID更新设备ID
     * 查找具有指定轮次ID且设备ID为"temporary_Id"的记录，然后更新设备ID
     *
     * @param updateParams 包含轮次ID和新设备ID的参数对象
     * @return 更新结果
     */
    @PostMapping("/update-device-by-round")
    public ResponseEntity<Map<String, Object>> updateDeviceIdByRoundId(@RequestBody Map<String, Object> updateParams) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从参数中获取轮次ID和新的设备ID
            String roundId = (String) updateParams.get("roundId");
            String newDeviceId = (String) updateParams.get("deviceId");

            // 参数验证
            if (roundId == null || roundId.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "轮次ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (newDeviceId == null || newDeviceId.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "设备ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            logger.info("开始根据轮次ID更新设备ID: roundId={}, newDeviceId={}", roundId, newDeviceId);

            // 根据轮次ID查找记录
            List<AiTestRecord> records = aiTestRecordService.getByProcessId(roundId);

            if (records.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到轮次ID为 " + roundId + " 的记录");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // 查找设备ID为"temporary_Id"的记录
            AiTestRecord targetRecord = null;
            for (AiTestRecord record : records) {
                if ("temporary_Id".equals(record.getDeviceId())) {
                    targetRecord = record;
                    break;
                }
            }

            if (targetRecord == null) {
                result.put("success", false);
                result.put("message", "未找到轮次ID为 " + roundId + " 且设备ID为 'temporary_Id' 的记录");
                result.put("foundRecords", records.size());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // 更新设备ID
            targetRecord.setDeviceId(newDeviceId);
            boolean updateSuccess = aiTestRecordService.updateRecord(targetRecord);

            if (updateSuccess) {
                logger.info("设备ID更新成功: recordId={}, roundId={}, oldDeviceId=temporary_Id, newDeviceId={}", 
                           targetRecord.getId(), roundId, newDeviceId);
                result.put("success", true);
                result.put("message", "设备ID更新成功");
                result.put("recordId", targetRecord.getId());
                result.put("roundId", roundId);
                result.put("oldDeviceId", "temporary_Id");
                result.put("newDeviceId", newDeviceId);
                return ResponseEntity.ok(result);
            } else {
                logger.error("设备ID更新失败: recordId={}, roundId={}", targetRecord.getId(), roundId);
                result.put("success", false);
                result.put("message", "设备ID更新失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }

        } catch (Exception e) {
            logger.error("根据轮次ID更新设备ID失败: {}, 参数: {}", e.getMessage(), updateParams, e);
            result.put("success", false);
            result.put("message", "更新设备ID失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 根据ID删除AI测试记录
     *
     * @param id 记录ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Map<String, Object>> deleteRecord(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        boolean success = aiTestRecordService.deleteRecord(id);

        if (success) {
            result.put("success", true);
            result.put("message", "删除AI测试记录成功");
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "删除AI测试记录失败，可能记录不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 根据设备ID删除AI测试记录
     *
     * @param deviceId 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> deleteByDeviceId(@PathVariable String deviceId) {
        Map<String, Object> result = new HashMap<>();
        int count = aiTestRecordService.deleteByDeviceId(deviceId);

        result.put("success", true);
        result.put("message", "成功删除" + count + "条AI测试记录");
        result.put("count", count);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据MIS ID删除AI测试记录
     *
     * @param misId MIS ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/mis/{misId}")
    public ResponseEntity<Map<String, Object>> deleteByMisId(@PathVariable String misId) {
        Map<String, Object> result = new HashMap<>();
        int count = aiTestRecordService.deleteByMisId(misId);

        result.put("success", true);
        result.put("message", "成功删除" + count + "条AI测试记录");
        result.put("count", count);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据进程ID删除AI测试记录
     *
     * @param processId 进程ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/process/{processId}")
    public ResponseEntity<Map<String, Object>> deleteByProcessId(@PathVariable String processId) {
        Map<String, Object> result = new HashMap<>();
        int count = aiTestRecordService.deleteByProcessId(processId);

        result.put("success", true);
        result.put("message", "成功删除" + count + "条AI测试记录");
        result.put("count", count);
        return ResponseEntity.ok(result);
    }

    /**
     * 高级查询接口
     * 支持多条件组合查询：设备ID、MIS ID、进程ID、时间范围、提示文本模糊匹配
     * 支持分页和排序控制
     *
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @param deviceId   设备ID（可选）
     * @param misId      MIS ID（可选）
     * @param processId  进程ID（可选）
     * @param startTime  开始时间（可选）
     * @param endTime    结束时间（可选）
     * @param promptText 提示文本模糊查询（可选）
     * @param sortAsc    是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @GetMapping("/query")
    public ResponseEntity<Map<String, Object>> queryRecords(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String misId,
            @RequestParam(required = false) String processId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String promptText,
            @RequestParam(required = false, defaultValue = "false") Boolean sortAsc) {

        Map<String, Object> result = new HashMap<>();
        Page<AiTestRecord> page = new Page<>(pageNum, pageSize);

        // 调用服务层方法，传入所有查询参数
        IPage<AiTestRecord> pageResult = aiTestRecordService.advancedQueryRecords(
                page, deviceId, misId, processId, startTime, endTime, promptText, sortAsc);

        result.put("success", true);
        result.put("data", pageResult);

        // 添加查询参数到结果中，方便前端了解当前查询条件
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("pageNum", pageNum);
        queryParams.put("pageSize", pageSize);
        queryParams.put("deviceId", deviceId);
        queryParams.put("misId", misId);
        queryParams.put("processId", processId);
        queryParams.put("startTime", startTime);
        queryParams.put("endTime", endTime);
        queryParams.put("promptText", promptText);
        queryParams.put("sortAsc", sortAsc);
        result.put("queryParams", queryParams);

        return ResponseEntity.ok(result);
    }

    /**
     * 清理AI测试记录，只保留最新的10万条记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @return 清理操作的结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupRecords() {
        Map<String, Object> result = new HashMap<>();

        // 保留的记录数量
        final int KEEP_RECORDS = 100000;

        // 获取总记录数
        long totalCount = aiTestRecordService.count();

        if (totalCount <= KEEP_RECORDS) {
            result.put("success", true);
            result.put("message", "当前记录数(" + totalCount + ")未超过保留阈值(" + KEEP_RECORDS + ")，无需清理");
            result.put("totalCount", totalCount);
            result.put("keepRecords", KEEP_RECORDS);
            result.put("deletedCount", 0);
            return ResponseEntity.ok(result);
        }

        // 异步执行清理操作
        int deletedCount = aiTestRecordService.cleanupRecords(KEEP_RECORDS);

        result.put("success", true);
        result.put("message", "清理操作已启动，将异步删除旧记录，保留最新的" + KEEP_RECORDS + "条记录");
        result.put("totalCount", totalCount);
        result.put("keepRecords", KEEP_RECORDS);
        result.put("estimatedDeleteCount", totalCount - KEEP_RECORDS);

        return ResponseEntity.ok(result);
    }

    /**
     * 批量条件删除AI测试记录
     * 支持按设备ID、MIS ID、进程ID、时间范围等条件组合删除
     *
     * @param deleteParams 包含删除条件的参数对象
     * @return 删除结果
     */
    @PostMapping("/batch-delete")
    public ResponseEntity<Map<String, Object>> batchDeleteRecords(@RequestBody Map<String, Object> deleteParams) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从参数中获取各种条件
            String deviceId = (String) deleteParams.get("deviceId");
            String misId = (String) deleteParams.get("misId");
            String processId = (String) deleteParams.get("processId");
            Date startTime = null;
            Date endTime = null;

            // 处理日期参数
            if (deleteParams.containsKey("startTime") && deleteParams.get("startTime") != null) {
                if (deleteParams.get("startTime") instanceof Date) {
                    startTime = (Date) deleteParams.get("startTime");
                } else {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        startTime = sdf.parse(deleteParams.get("startTime").toString());
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "无效的开始时间格式，请使用 yyyy-MM-dd HH:mm:ss");
                        return ResponseEntity.badRequest().body(result);
                    }
                }
            }

            if (deleteParams.containsKey("endTime") && deleteParams.get("endTime") != null) {
                if (deleteParams.get("endTime") instanceof Date) {
                    endTime = (Date) deleteParams.get("endTime");
                } else {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        endTime = sdf.parse(deleteParams.get("endTime").toString());
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "无效的结束时间格式，请使用 yyyy-MM-dd HH:mm:ss");
                        return ResponseEntity.badRequest().body(result);
                    }
                }
            }

            // 参数校验，至少需要一个删除条件
            if (deviceId == null && misId == null && processId == null && startTime == null && endTime == null) {
                result.put("success", false);
                result.put("message", "至少需要提供一个删除条件");
                return ResponseEntity.badRequest().body(result);
            }

            // 添加安全限制，避免误删除大量数据
            Boolean skipSafetyCheck = (Boolean) deleteParams.getOrDefault("skipSafetyCheck", false);
            Integer limit = (Integer) deleteParams.getOrDefault("limit", 1000);

            if (limit > 10000 && !skipSafetyCheck) {
                result.put("success", false);
                result.put("message", "为保护数据安全，单次批量删除记录数不能超过10000条。如需删除更多记录，请分批操作或设置skipSafetyCheck=true");
                return ResponseEntity.badRequest().body(result);
            }

            // 调用服务层批量删除方法
            int deletedCount = aiTestRecordService.batchDeleteRecords(
                    deviceId, misId, processId, startTime, endTime, limit, skipSafetyCheck);

            result.put("success", true);
            result.put("message", "批量删除成功，共删除" + deletedCount + "条记录");
            result.put("count", deletedCount);

            // 添加删除条件到结果
            Map<String, Object> conditions = new HashMap<>(deleteParams);
            conditions.remove("skipSafetyCheck");
            result.put("conditions", conditions);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("批量删除AI测试记录失败: {}, 条件: {}", e.getMessage(), deleteParams, e);
            result.put("success", false);
            result.put("message", "批量删除AI测试记录失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 向AI测试设备发送消息并保存测试记录
     * 支持两种模式：
     * 1. 指定targetIp：直接使用指定的IP地址
     * 2. 不指定targetIp：自动查找可用的设备IP（从***********到*************）
     * 然后通过/submit_task接口提交任务，成功后将轮次号保存到数据库
     *
     * @param messageParams 包含消息内容、MIS ID和可选的目标IP的参数对象
     * @return 发送结果
     */
    @PostMapping("/send-message")
    public ResponseEntity<Map<String, Object>> sendMessage(@RequestBody Map<String, Object> messageParams) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从参数中获取各种参数，端口固定为5630
            Integer port = 5630;
            String message = (String) messageParams.get("message");
            String misId = (String) messageParams.get("misId");
            String targetIp = (String) messageParams.get("targetIp");

            // 参数验证
            if (message == null || message.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "消息内容不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            if (misId == null || misId.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "MIS ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            String availableIp;
            
            // 如果指定了目标IP，直接使用；否则自动查找
            if (targetIp != null && !targetIp.trim().isEmpty()) {
                availableIp = targetIp.trim();
                logger.info("使用指定的目标IP: {}, 消息内容: {}, MIS ID: {}", availableIp, message, misId);
                
                // 验证指定的IP是否可用
                Map<String, Object> confirmResult = aiTestRecordService.confirmDevice(availableIp, port);
                if (!Boolean.TRUE.equals(confirmResult.get("confirmed"))) {
                    result.put("success", false);
                    result.put("message", "指定的IP地址不可用或无法确认: " + availableIp);
                    result.put("confirmResult", confirmResult);
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
                }
            } else {
                logger.info("开始自动查找可用设备, 消息内容: {}, MIS ID: {}", message, misId);
                
                // 查找可用的设备IP
                availableIp = aiTestRecordService.findAvailableDeviceIp(port);
                if (availableIp == null) {
                    result.put("success", false);
                    result.put("message", "未找到可用的设备IP (从***********到*************)");
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
                }
            }

            logger.info("使用设备IP: {}, 准备提交任务", availableIp);

            // 向可用设备提交任务
            Map<String, Object> submitResult = aiTestRecordService.submitTaskToDevice(availableIp, port, message, misId);

            result.put("success", true);
            result.put("message", "消息发送完成");
            result.put("targetIp", availableIp);
            result.put("port", port);
            result.put("sentMessage", message);
            result.put("misId", misId);
            result.put("response", submitResult);

            // 处理任务提交结果
            if (Boolean.TRUE.equals(submitResult.get("success"))) {
                try {
                    String roundId = (String) submitResult.get("roundId");
                    if (roundId != null && !roundId.trim().isEmpty()) {
                        
                        // 检查是否被拒绝（round_id 为 "0"）
                        if ("0".equals(roundId)) {
                            // 任务被拒绝，但仍然保存记录
                            String responseBody = (String) submitResult.get("responseBody");
                            String rejectMessage = "";
                            
                            // 从响应中提取拒绝原因
                            if (responseBody != null && !responseBody.isEmpty()) {
                                try {
                                    com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
                                    Map<String, Object> responseJson = objectMapper.readValue(responseBody, Map.class);
                                    rejectMessage = (String) responseJson.get("message");
                                } catch (Exception e) {
                                    logger.warn("解析拒绝响应失败: {}", e.getMessage());
                                    rejectMessage = "任务被拒绝，无法解析具体原因";
                                }
                            }
                            
                            logger.warn("任务提交被拒绝: {}", rejectMessage);
                            
                            // 更新返回结果，标明任务被拒绝
                            result.put("success", false);
                            result.put("message", "任务提交被拒绝");
                            result.put("rejectReason", rejectMessage);
                            
                            // 创建测试记录对象，记录被拒绝的任务
                            AiTestRecord record = new AiTestRecord();
                            record.setMisId(misId);
                            record.setProcessId("REJECTED_" + System.currentTimeMillis());  // 使用时间戳作为标识
                            record.setPromptText(message);
                            record.setDeviceId("temporary_Id");  // 临时设备ID
                            record.setRoundNum(0);  // 轮次号为0表示被拒绝
                            record.setRemark("提交被拒绝，拒绝原因：" + rejectMessage);  // 记录拒绝原因
                            record.setCreatedAt(new Date());
                            
                            // 保存被拒绝的记录
                            boolean saveSuccess = aiTestRecordService.addRecord(record);
                            if (saveSuccess) {
                                logger.info("被拒绝任务记录保存成功: ID={}, MIS ID={}", record.getId(), misId);
                                result.put("recordSaved", true);
                                result.put("recordId", record.getId());
                                result.put("recordType", "rejected");
                            } else {
                                logger.error("被拒绝任务记录保存失败");
                                result.put("recordSaved", false);
                                result.put("recordError", "保存被拒绝任务记录失败");
                            }
                            
                            return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
                        }
                        
                        // 正常处理成功的任务
                        // 创建测试记录对象
                        AiTestRecord record = new AiTestRecord();
                        record.setMisId(misId);
                        record.setProcessId(roundId);  // 使用轮次号作为进程ID
                        record.setPromptText(message);
                        record.setDeviceId("temporary_Id");  // 临时设备ID，后续通过其他逻辑更新
                        
                        // 解析轮次号为整数
                        Integer roundNum = null;
                        try {
                            roundNum = Integer.valueOf(roundId);
                        } catch (NumberFormatException e) {
                            logger.warn("轮次号格式不正确，无法转换为整数: {}", roundId);
                            roundNum = null;
                        }
                        record.setRoundNum(roundNum);
                        
                        record.setRemark(null);  // 备注信息为空
                        record.setCreatedAt(new Date());  // 设置当前时间
                        
                        // 数据校验
                        Map<String, String> validationErrors = validateRecordData(record);
                        if (!validationErrors.isEmpty()) {
                            logger.error("AI测试记录数据校验失败: {}", validationErrors);
                            result.put("recordSaved", false);
                            result.put("recordError", "数据校验失败: " + validationErrors);
                        } else {
                            // 保存记录到数据库
                            boolean saveSuccess = aiTestRecordService.addRecord(record);
                            if (saveSuccess) {
                                logger.info("AI测试记录保存成功: ID={}, 轮次号={}, MIS ID={}", 
                                           record.getId(), roundId, misId);
                                result.put("recordSaved", true);
                                result.put("recordId", record.getId());
                                Map<String, Object> extractedData = new HashMap<>();
                                extractedData.put("createdAt", record.getCreatedAt());
                                extractedData.put("roundId", roundId);
                                extractedData.put("roundNum", roundNum);
                                extractedData.put("misId", misId);
                                extractedData.put("promptText", message);
                                result.put("extractedData", extractedData);
                            } else {
                                logger.error("AI测试记录保存失败");
                                result.put("recordSaved", false);
                                result.put("recordError", "保存记录到数据库失败");
                            }
                        }
                    } else {
                        logger.warn("任务提交响应中未找到轮次号");
                        result.put("recordSaved", false);
                        result.put("recordError", "任务提交响应中未找到轮次号");
                    }
                } catch (Exception e) {
                    logger.error("解析响应或保存记录失败: {}", e.getMessage(), e);
                    result.put("recordSaved", false);
                    result.put("recordError", "解析响应或保存记录失败: " + e.getMessage());
                }
            } else {
                result.put("recordSaved", false);
                result.put("recordError", "任务提交失败，未保存记录");
            }

            logger.info("消息发送成功: {}", availableIp);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("发送消息失败: {}, 参数: {}", e.getMessage(), messageParams, e);
            result.put("success", false);
            result.put("message", "发送消息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

} 