package com.sankuai.mdp.compass.aiTestRecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.mdp.compass.category.util.MultiFormatDateDeserializer;

import java.io.Serializable;
import java.util.Date;

/**
 * AiTestRecord实体类
 * 对应云端数据库表ai_test_record
 */
@TableName("ai_test_record")
public class AiTestRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Long id;

    /**
     * MIS ID
     */
    @TableField("Mis_Id")
    private String misId;

    /**
     * 进程ID
     */
    @TableField("Process_Id")
    private String processId;

    /**
     * 提示文本
     */
    @TableField("Prompt_Text")
    private String promptText;

    /**
     * 设备ID
     */
    @TableField("Device_Id")
    private String deviceId;

    /**
     * 轮次号
     */
    @TableField("Round_Num")
    private Integer roundNum;

    /**
     * 备注信息
     */
    @TableField("Remark")
    private String remark;

    /**
     * 创建时间戳
     */
    @TableField("Created_At")
    @JsonDeserialize(using = MultiFormatDateDeserializer.class)
    private Date createdAt;

    // Getter and Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMisId() {
        return misId;
    }

    public void setMisId(String misId) {
        this.misId = misId;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getPromptText() {
        return promptText;
    }

    public void setPromptText(String promptText) {
        this.promptText = promptText;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(Integer roundNum) {
        this.roundNum = roundNum;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "AiTestRecord{" +
                "id=" + id +
                ", misId='" + misId + '\'' +
                ", processId='" + processId + '\'' +
                ", promptText='" + promptText + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", roundNum=" + roundNum +
                ", remark='" + remark + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
} 