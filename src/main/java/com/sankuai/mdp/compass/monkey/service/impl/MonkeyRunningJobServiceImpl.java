package com.sankuai.mdp.compass.monkey.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.monkey.entity.MonkeyResult;
import com.sankuai.mdp.compass.monkey.entity.MonkeySetting;
import com.sankuai.mdp.compass.monkey.mapper.MonkeyResultMapper;
import com.sankuai.mdp.compass.monkey.mapper.MonkeySettingMapper;
import com.sankuai.mdp.compass.monkey.service.CreateOrTriggerJenkinsService;
import com.sankuai.mdp.compass.monkey.service.MonkeyRunningJobService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.python.antlr.ast.Str;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dongheng on 2020/11/18
 */
@Slf4j
@Service
public class MonkeyRunningJobServiceImpl implements MonkeyRunningJobService {
    @Autowired
    MonkeyResultMapper monkeyResultMapper;

    @Autowired
    MonkeySettingMapper monkeySettingMapper;
    @Override
    public String updateRunningData(JSONObject monkeyRunningJob) {
        log.info("monkeyRuningJob is :"+monkeyRunningJob.toString());
        MonkeyResult monkeyResult = new MonkeyResult();
        if(monkeyRunningJob.has("id")){
            monkeyResult.setId(monkeyRunningJob.getInt("id"));
        }
        if (monkeyRunningJob.has("jenkinsName")){
            monkeyResult.setJenkinsName(monkeyRunningJob.getString("jenkinsName"));
        }
        if (monkeyRunningJob.has("buildId")){
            monkeyResult.setBuildId(monkeyRunningJob.getString("buildId"));
        }
        if (monkeyRunningJob.has("jenkinsUrl")){
            monkeyResult.setJenkinsUrl(monkeyRunningJob.getString("jenkinsUrl"));
        }
        if(monkeyRunningJob.has("submitResult")){
            monkeyResult.setSubmitResult(monkeyRunningJob.getString("submitResult"));
        }
        if (monkeyRunningJob.has("reportUrl")){
            monkeyResult.setReportUrl(monkeyRunningJob.getString("reportUrl"));
        }
        if (monkeyRunningJob.has("jobId")){
            monkeyResult.setJobId(monkeyRunningJob.getString("jobId"));
        }
        if (monkeyRunningJob.has("reportResult")){
            monkeyResult.setReportResult(monkeyRunningJob.getString("reportResult"));
        }
        if (monkeyRunningJob.has("extra")){
            monkeyResult.setExtra(monkeyRunningJob.getString("extra"));
        }
        if (monkeyRunningJob.has("status")){
            monkeyResult.setStatus(monkeyRunningJob.getInt("status"));
        }
        if (monkeyRunningJob.has("caseResult")){
            monkeyResult.setCaseResult(monkeyRunningJob.getInt("caseResult"));
        }
        if (monkeyRunningJob.has("configSettingId")){
            monkeyResult.setConfigSettingId(monkeyRunningJob.getInt("configSettingId"));
        }
        if (monkeyRunningJob.has("apkUrl")){
            monkeyResult.setApkUrl(monkeyRunningJob.getString("apkUrl"));
        }
        return updateRunningData(monkeyResult);
    }

    public String updateRunningData(MonkeyResult monkeyResult){
        QueryWrapper<MonkeyResult> queryMonkeyWrapper = new QueryWrapper<>();
        if (monkeyResult.getId()!=null){
            queryMonkeyWrapper.eq("id", monkeyResult.getId());
            int update = monkeyResultMapper.update(monkeyResult, queryMonkeyWrapper);
            if (update!=0) {
                return monkeyResult.getId().toString();
            }
        }else if (StringUtils.isNotBlank(monkeyResult.getBuildId())){
            queryMonkeyWrapper.eq("build_id", monkeyResult.getBuildId());
            queryMonkeyWrapper.eq("jenkins_name", monkeyResult.getJenkinsName());
            int update = monkeyResultMapper.update(monkeyResult, queryMonkeyWrapper);
            if (update!=0) {
                List<MonkeyResult> monkeyResults = monkeyResultMapper.selectList(queryMonkeyWrapper);
                monkeyResult = monkeyResults.get(0);
                return monkeyResult.getId().toString();
            }
        }
        return "-1";
    }

    @Override
    public String insertRunningData(JSONObject monkeyRunningJob) {
        MonkeyResult monkeyResult = new MonkeyResult();
        if (monkeyRunningJob.has("jenkinsName")){
            monkeyResult.setJenkinsName(monkeyRunningJob.getString("jenkinsName"));
        }
        if (monkeyRunningJob.has("buildId")){
            monkeyResult.setBuildId(monkeyRunningJob.getString("buildId"));
        }
        if (monkeyRunningJob.has("jenkinsUrl")){
            monkeyResult.setJenkinsUrl(monkeyRunningJob.getString("jenkinsUrl"));
        }
        if(monkeyRunningJob.has("submitResult")){
            monkeyResult.setSubmitResult(monkeyRunningJob.getString("submitResult"));
        }
        if (monkeyRunningJob.has("status")){
            monkeyResult.setStatus(monkeyRunningJob.getInt("status"));
        }
        if (monkeyRunningJob.has("apkUrl")){
            monkeyResult.setApkUrl(monkeyRunningJob.getString("apkUrl"));
        }
        //查询配置项id
        QueryWrapper<MonkeySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("jenkins_name", monkeyResult.getJenkinsName());
        List<MonkeySetting> list = monkeySettingMapper.selectList(queryWrapper);
        if (list.size()==0){
            return "-1";
        }
        MonkeySetting monkeySetting = list.get(0);
        monkeyResult.setConfigSettingId(monkeySetting.getId());

        int insert = monkeyResultMapper.insert(monkeyResult);
        if (insert!=0){
            return monkeyResult.getId().toString();
        }
        return "-1";
    }

    @Override
    public JsonObject backMonkeyLastData(Integer configSettingId){
        QueryWrapper<MonkeyResult> queryMonkeyWrapper = new QueryWrapper<>();
        queryMonkeyWrapper.eq("config_setting_id", configSettingId);
        List<MonkeyResult> monkeyRunningResult = monkeyResultMapper.selectList(queryMonkeyWrapper);
        MonkeyResult monkeyResult = monkeyRunningResult.get(monkeyRunningResult.size()-1);
        return backRunningMonkeyData(monkeyResult);
    }

    @Override
    public JsonObject backMonkeyTargetedData(Integer id) {
        QueryWrapper<MonkeyResult> queryMonkeyWrapper = new QueryWrapper<>();
        queryMonkeyWrapper.eq("id", id);
        List<MonkeyResult> monkeyRunningResult = monkeyResultMapper.selectList(queryMonkeyWrapper);
        MonkeyResult monkeyResult = monkeyRunningResult.get(0);
        return backRunningMonkeyData(monkeyResult);
    }

    public JsonObject backRunningMonkeyData(MonkeyResult monkeyResult){
        JsonObject runningMonkeyData = new JsonObject();
        if (monkeyResult==null){
            runningMonkeyData.addProperty("status", -1);
            runningMonkeyData.addProperty("msg", "未查询到相关数据");
        }
        if (monkeyResult.getStatus()==0){
            runningMonkeyData.addProperty("status", 0);
            runningMonkeyData.addProperty("msg", "monkey检测正在执行");
            JsonObject data = new JsonObject();
            if (monkeyResult.getCaseResult()!=null){
                data.addProperty("caseResult", monkeyResult.getStatus());
            }
            if (monkeyResult.getJobId()!=null){
                data.addProperty("jobId", monkeyResult.getJobId());
            }
            data.addProperty("info", monkeyResult.getSubmitResult());
            runningMonkeyData.add("data", data);
        }

        if (monkeyResult.getStatus()==1){
            runningMonkeyData.addProperty("status", 1);
            runningMonkeyData.addProperty("msg", "monkey检测完成");
            JsonObject data = new JsonObject();
            if (monkeyResult.getCaseResult()!=null){
                data.addProperty("caseResult", monkeyResult.getStatus());
            }
            if (monkeyResult.getJobId()!=null){
                data.addProperty("jobId", monkeyResult.getJobId());
            }
            data.addProperty("info", monkeyResult.getSubmitResult());
            data.addProperty("reportUrl", monkeyResult.getReportUrl());
            runningMonkeyData.add("data", data);
        }
        if (monkeyResult.getStatus()==-1){
            runningMonkeyData.addProperty("status", -1);
            runningMonkeyData.addProperty("msg", "monkey检测失败");
            JsonObject data = new JsonObject();
            if (monkeyResult.getCaseResult()!=null){
                data.addProperty("caseResult", monkeyResult.getStatus());
            }
            if (monkeyResult.getJobId()!=null){
                data.addProperty("jobId", monkeyResult.getJobId());
            }
            if (monkeyResult.getJobId().equals("-1")) {
                data.addProperty("info", monkeyResult.getSubmitResult());
            }else {
                data.addProperty("info", "脚本原因导致任务失败");
            }
            runningMonkeyData.add("data", data);
        }
        return runningMonkeyData;
    }
}
