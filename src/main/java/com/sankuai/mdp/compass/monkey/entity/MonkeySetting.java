package com.sankuai.mdp.compass.monkey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("monkey_setting")
public class MonkeySetting {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer  id;

    private String  describes;
    private String  platform;
    private String  project;
    private String  devices;
    private String  deviceCount;
    private String  numbers;
    private String  events;
    private String  throttle;
    private String  runTime;
    private String  notify;
    private String  login;
    private String  apkGetMethod;
    private String  apkCondition;
    private String  apkUrl;
    private String  apkKey;
    private String  timer;
    private String  scheme;
    private String  jenkinsName;
    private String  maxWaitTime;
    //向云测提交任务的mis号
    private String misId;
    //向云测提交任务的密钥
    private String conanKey;
    private String configTime;
}
