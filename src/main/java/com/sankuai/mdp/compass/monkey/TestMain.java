package com.sankuai.mdp.compass.monkey;

/**
 * Created by dongheng on 2020/10/29
 */
public class TestMain {

    public static void main(String[] args) {
//        MonkeySetting monkeySetting = new MonkeySetting();
//        monkeySetting.setNumbers("1234");
//        monkeySetting.setDescribes("dongheng test");
//        MonkeySettingServiceImpl monkeySettingService = new MonkeySettingServiceImpl();
//        String s = monkeySettingService.saveSetting(monkeySetting);
//        System.out.println("返回值是:" +s);
//        FileUtil.downloadAndReadFile("https://apptest.sankuai.com/download/aimeituan-release_11.4.200-106414.apk", "", "dongheng");
        //下载文件
//        FileUtils.saveUrlAs("https://apptest.sankuai.com/download/aimeituan-release_11.4.200-106414.apk", "/Users/<USER>/Desktop/platform_compass/src/main/java/com/sankuai/mdp/compass/monkey/Utils", "GET");
        //上传文件

    }
}
