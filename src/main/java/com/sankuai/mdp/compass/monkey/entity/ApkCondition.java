package com.sankuai.mdp.compass.monkey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by dongheng on 2020/10/30
 */
@Data
@TableName("apk_condition")
public class ApkCondition {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer  id;
    private String jenkinsName;
    private String os;
    private String appName;//包英文名
    private String componentName;
    private String buildTypeName;//分组,比如:Release 打包、Debug 打包
    private String buildNum;
    private String branch;
    private String version;
    private String flavor;
    private Integer virtual;//模拟器打包
    private String channel;//渠道号
    private Integer currentBranch;//是否当前测试分支
    private Integer sourceIPA;//是否获取其同源内测包
}
