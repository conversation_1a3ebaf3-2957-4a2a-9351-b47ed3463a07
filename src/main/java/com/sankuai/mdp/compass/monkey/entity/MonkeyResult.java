package com.sankuai.mdp.compass.monkey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by dongheng on 2020/11/17
 */
@Data
@TableName("monkey_results")
public class MonkeyResult {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer  id;
    //生成的monkeyid用于唯一标识每条任务,返回给thanos或者用户
//    private String monkeyId;
    private Integer configSettingId;
    //jenkins的每条任务build号
    private String buildId;
    private String jenkinsUrl;
    private String jenkinsName;
    //云测jobId
    private String jobId;
    private String apkUrl;
    private String submitResult;
    private String reportUrl;
    private String reportResult;
    private String extra;
    //记录上传预处理case成功或失败，成功：1，失败：0
    private Integer caseResult;
    //记录任务执行状态，正在检测0，检测完成1，检测失败-1
    private Integer status;
}


