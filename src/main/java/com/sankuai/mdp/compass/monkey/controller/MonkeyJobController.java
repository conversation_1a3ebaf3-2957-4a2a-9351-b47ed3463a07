package com.sankuai.mdp.compass.monkey.controller;

import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.monkey.service.CreateOrTriggerJenkinsService;
import com.sankuai.mdp.compass.monkey.service.MonkeyRunningJobService;
import com.sankuai.mdp.compass.monkey.service.MonkeySettingService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/compass/api/monkey")
public class MonkeyJobController {
    private static final Logger logger = LoggerFactory.getLogger(MonkeyJobController.class);

    @Autowired
    MonkeySettingService monkeySettingService;
    @Autowired
    CreateOrTriggerJenkinsService createOrTriggerJenkins;
    @Autowired
    MonkeyRunningJobService monkeyRunningJobService;

    /**
     * 创建保存配置
     * @param body 配置body
     * @return     配置项数据库id
     */
    //保存配置信息
    @PostMapping("/createOrUpdateConfig")
    public String saveSettings(@RequestBody JSONObject body) {
        return monkeySettingService.saveSetting(body);
    }

    /**
     * 创建jenkins Job
     * @param id 配置项数据库id
     * @return    返回是否创建成功，值success、fail
     */
    @PostMapping("/createJenkinsJob")
    public String createJenkinsJob(Integer id){
        return createOrTriggerJenkins.createJenkins(id).toString();
    }

    /**
     * 触发jenkins job
     * @param id      配置项数据库id
     * @param appUrl   测试包url
     * @return       触发任务的数据库id
     */
    @PostMapping("/triggerJenkinsJob")
    public String triggerJenkinsJob(Integer id,String appUrl){
        return createOrTriggerJenkins.triggerJenkins(id,appUrl).toString();
    }

    /**
     * 更新任务执行过程中的信息
     * @param body  任务执行过程中的参数
     * @return      任务的数据库id
     */
    @PostMapping("/updateData")
    public String updateRunningMonkeyData(@RequestBody JSONObject body){
        return monkeyRunningJobService.updateRunningData(body);
    }

    /**
     * 向数据插入每条任务过程状态信息
     * @param body   每条任务的过程状态信息
     * @return      返回每条任务的数据库存储id
     */
    @PostMapping("/insertRunningData")
    public String insertRunningMonkeyData(@RequestBody JSONObject body){
        return monkeyRunningJobService.insertRunningData(body);
    }

    /**
     * 查询任务的执行状态
     * @param monkeyResultId   每条任务的数据库存储id
     * @param configSettingId  配置项id
     * @return
     */
    @PostMapping("/queryMonkeyResult")
    public String backData(@RequestParam(value="monkeyResultId",required = false) Integer monkeyResultId, @RequestParam(value="configSettingId",required = false) Integer configSettingId){
        if (monkeyResultId!=null){
            return monkeyRunningJobService.backMonkeyTargetedData(monkeyResultId).toString();
        }else if (configSettingId!=null) {
            return monkeyRunningJobService.backMonkeyLastData(configSettingId).toString();
        }
        JsonObject runningMonkeyData = new JsonObject();
        runningMonkeyData.addProperty("status", -1);
        runningMonkeyData.addProperty("msg", "查询失败，请输入正确的查询参数");
        return runningMonkeyData.toString();
    }

    /**
     * 获取配置项里的url和配置项id
     * @param jenkinsName
     * @return   返回要测试的测试包url与配置项id
     */
    @PostMapping("/getApkUrlAndConfigSettingId")
    public String getApkUrl(String jenkinsName){
        return monkeySettingService.getApkUrlAndConfigSettingId(jenkinsName).toString();
    }

    @PostMapping("/stopJob")
    public String deleteJenkinsJob(Integer monkeySettingId){

        return "";
    }

    @PostMapping("/deleteMonkeySetting")
    public String deleteMonkeySetting(@RequestParam(value = "id") Integer id){
        return monkeySettingService.deleteSetting(id);
    }


}
