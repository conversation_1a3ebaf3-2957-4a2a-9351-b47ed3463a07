package com.sankuai.mdp.compass.monkey.service;

import com.google.gson.JsonObject;
import net.sf.json.JSONObject;

/**
 * Created by dongheng on 2020/11/18
 */
public interface MonkeyRunningJobService {
    String updateRunningData(JSONObject monkeyRunningJob);
    JsonObject backMonkeyTargetedData(Integer runningId);
    JsonObject backMonkeyLastData(Integer configSettingId);
    String insertRunningData(JSONObject monkeyRunningJob);
}
