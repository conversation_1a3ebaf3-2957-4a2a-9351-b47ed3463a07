package com.sankuai.mdp.compass.monkey.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.monkey.entity.MonkeySetting;
import com.sankuai.mdp.compass.monkey.mapper.MonkeySettingMapper;
import com.sankuai.mdp.compass.monkey.service.CreateOrTriggerJenkinsService;
import com.sankuai.mdp.compass.monkey.service.MonkeySettingService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

//import net.minidev.json.JSONObject;

@Slf4j
@Service
public class MonkeySettingServiceImpl extends ServiceImpl<MonkeySettingMapper,MonkeySetting> implements MonkeySettingService{

    @Autowired
    MonkeySettingMapper monkeySettingMapper;

    @Autowired
    CreateOrTriggerJenkinsService createOrTriggerJenkinsService;

    @Override
    public String saveSetting(JSONObject settingBody) {
        log.info("settingBody ："+settingBody.toString());
        MonkeySetting monkeySetting = new MonkeySetting();
        if (settingBody.has("describes")){
            monkeySetting.setDescribes(settingBody.get("describes").toString());
        }
        if (settingBody.has("project")){
            monkeySetting.setProject(settingBody.get("project").toString());
        }
        if (settingBody.has("platform")){
            monkeySetting.setPlatform(settingBody.get("platform").toString());
        }
        if (settingBody.has("devices")){
            monkeySetting.setDevices(settingBody.getString("devices"));
        }
        if (settingBody.has("numbers")){
            monkeySetting.setNumbers(settingBody.getString("numbers"));
        }
        if (settingBody.has("events")){
            monkeySetting.setEvents(settingBody.getString("events"));
        }
        if (settingBody.has("throttle")){
            monkeySetting.setThrottle(settingBody.getString("throttle"));
        }
        if (settingBody.has("runTime")){
            monkeySetting.setRunTime(settingBody.getString("runTime"));
        }
        if (settingBody.has("notify")){
            monkeySetting.setNotify(settingBody.getString("notify"));
        }
        if (settingBody.has("login")){
            monkeySetting.setLogin(settingBody.getString("login"));
        }
        if (settingBody.has("apkGetMethod")){
            monkeySetting.setApkGetMethod(settingBody.getString("apkGetMethod"));
        }
        if (settingBody.has("apkCondition")){
            monkeySetting.setApkCondition(settingBody.getString("apkCondition"));
        }
        if (settingBody.has("apkUrl")){
            monkeySetting.setApkUrl(settingBody.getString("apkUrl"));
        }
        if (settingBody.has("apkKey")){
            monkeySetting.setApkKey(settingBody.getString("apkKey"));
        }
        if (settingBody.has("timer")){
            monkeySetting.setTimer(settingBody.getString("timer"));
        }
        if (settingBody.has("deviceCount")){
            monkeySetting.setDeviceCount(settingBody.getString("deviceCount"));
        }
        if (settingBody.has("scheme")){
            monkeySetting.setScheme(settingBody.getString("scheme"));
        }
        if (settingBody.has("maxWaitTime")){
            monkeySetting.setMaxWaitTime(settingBody.getString("maxWaitTime"));
        }
        if (settingBody.has("misId")){
            monkeySetting.setMisId(settingBody.getString("misId"));
        }else {
            monkeySetting.setMisId("lizhen39");
        }
        if (settingBody.has("conanKey")){
            monkeySetting.setConanKey(settingBody.getString("conanKey"));
        }else {
            monkeySetting.setConanKey("70a9ae35-dc42-431f-ac9b-50f0e45627bf");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            //设置配置时间
            Date currentDate = new Date();
            String configTime = sdf.format(currentDate.getTime());
            monkeySetting.setConfigTime(configTime);
        }catch (Exception e){
            e.printStackTrace();
        }
        if (settingBody.has("id")){
            monkeySetting.setId(settingBody.getInt("id"));
            //如果有id，则走更新配置方法
            return updateSetting(monkeySetting).toString();
        }
        return saveSettingToDB(monkeySetting);
    }
    public String saveSettingToDB(MonkeySetting monkeySetting) {
        Integer monkeySettingId = -1;
        int insert = monkeySettingMapper.insert(monkeySetting);
        if (insert==1){
            monkeySettingId = monkeySetting.getId();

        }
        return monkeySettingId.toString();
    }

    @Override
    public JsonObject getApkUrlAndConfigSettingId(String jenkinsName) {
        JsonObject results = new JsonObject();
        QueryWrapper<MonkeySetting> monkeySettingQueryWrapper = new QueryWrapper<>();
        monkeySettingQueryWrapper.eq("jenkins_name", jenkinsName);
        List<MonkeySetting> monkeySettings = monkeySettingMapper.selectList(monkeySettingQueryWrapper);
        if (null==monkeySettings||monkeySettings.isEmpty()){
            return results;
        }
        MonkeySetting monkeySetting = monkeySettings.get(0);
        String apkUrl = createOrTriggerJenkinsService.getApkUrl(monkeySetting);
        results.addProperty("appUrl", apkUrl);
        results.addProperty("configSettingId", monkeySetting.getId());
        return results;
    }

    public JsonObject updateSetting(MonkeySetting monkeySetting){
        log.info("updateSetting:     "+monkeySetting.toString());
        JsonObject jsonObject = new JsonObject();
        QueryWrapper<MonkeySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", monkeySetting.getId());
        int update = monkeySettingMapper.update(monkeySetting, queryWrapper);
        if (update==1){
            jsonObject.addProperty("msg", "success");
            return jsonObject;
        }
        jsonObject.addProperty("msg", "fail");
        return jsonObject;
    }

    public String deleteSetting(Integer id){
        int i = monkeySettingMapper.deleteById(id);
        if (i==1){
            return "success";
        }
        return "fail";
    }
}
