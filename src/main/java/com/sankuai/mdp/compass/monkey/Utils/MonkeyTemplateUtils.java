package com.sankuai.mdp.compass.monkey.Utils;

import com.sankuai.mdp.compass.monkey.entity.MonkeySetting;

/**
 * Created by dongheng on 2020/11/23
 */

/**
 * job的模板
 * 需要拼接出测试模板，需要拼接的地方有：
 * 1.测试job的描述，取值：describes
 * 2.测试job的定时时间规则，取值：timer
 * 3.测试job的配置项id，取值：配置项id
 */
public class MonkeyTemplateUtils {
    public String getTimerTemplateXML(MonkeySetting monkeySetting){
        String templatXML="<?xml version='1.1' encoding='UTF-8'?>\n" +
                "<project>\n" +
                "  <actions/>\n" +
                "  <description>"+monkeySetting.getDescribes()+"</description>\n" +
                "  <keepDependencies>false</keepDependencies>\n" +
                "  <properties>\n" +
                "    <hudson.security.AuthorizationMatrixProperty>\n" +
                "      <inheritanceStrategy class=\"org.jenkinsci.plugins.matrixauth.inheritance.InheritParentStrategy\"/>\n" +
                "      <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Create:dongheng</permission>\n" +
                "      <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Update:dongheng</permission>\n" +
                "      <permission>com.cloudbees.plugins.credentials.CredentialsProvider.View:dongheng</permission>\n" +
                "      <permission>hudson.model.Item.Build:dongheng</permission>\n" +
                "      <permission>hudson.model.Item.Cancel:dongheng</permission>\n" +
                "      <permission>hudson.model.Item.Configure:dongheng</permission>\n" +
                "    </hudson.security.AuthorizationMatrixProperty>\n" +
                "    <hudson.plugins.disk__usage.DiskUsageProperty plugin=\"disk-usage@0.28\"/>\n" +
                "    <com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty plugin=\"naginator@1.17.2\">\n" +
                "      <optOut>false</optOut>\n" +
                "    </com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty>\n" +
                "    <com.synopsys.arc.jenkinsci.plugins.jobrestrictions.jobs.JobRestrictionProperty plugin=\"job-restrictions@0.8\"/>\n" +
                "    <hudson.model.ParametersDefinitionProperty>\n" +
                "      <parameterDefinitions>\n" +
                "        <hudson.model.ChoiceParameterDefinition>\n" +
                "          <name>os</name>\n" +
                "          <description>选择测试端</description>\n" +
                "          <choices class=\"java.util.Arrays$ArrayList\">\n" +
                "            <a class=\"string-array\">\n" +
                "              <string>"+monkeySetting.getPlatform()+"</string>\n" +
                "              <string>"+monkeySetting.getPlatform()+"</string>\n" +
                "            </a>\n" +
                "          </choices>\n" +
                "        </hudson.model.ChoiceParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>alias</name>\n" +
                "          <description>业务线名称</description>\n" +
                "          <defaultValue>美团</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>loginCase</name>\n" +
                "          <description>用于登录测试App的脚本</description>\n" +
                "          <defaultValue>"+monkeySetting.getLogin()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>devicesVersion</name>\n" +
                "          <description>测试设备系统</description>\n" +
                "          <defaultValue>"+monkeySetting.getDevices()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>deviceCount</name>\n" +
                "          <description>测试设备数量</description>\n" +
                "          <defaultValue>"+monkeySetting.getDeviceCount()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>maxWaitTime</name>\n" +
                "          <description>设备等待时间，单位(分)</description>\n" +
                "          <defaultValue>"+monkeySetting.getMaxWaitTime()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>intents</name>\n" +
                "          <description>设置起始页面</description>\n" +
                "          <defaultValue>"+URLEncoderUtils.transUrl(monkeySetting.getScheme())+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>receiver</name>\n" +
                "          <description>接收Job运行结果通知人</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>events</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;Android&lt;/font&gt;端测试需要设置每个页面的事件数</description>\n" +
                "          <defaultValue>"+monkeySetting.getEvents()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>throttle</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;Android&lt;/font&gt;端测试需要设置每个事件之间的间隔，单位(ms)</description>\n" +
                "          <defaultValue>"+monkeySetting.getThrottle()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>runTime</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;iOS&lt;/font&gt;端测试需要设置执行时长，单位(s)</description>\n" +
                "          <defaultValue>"+monkeySetting.getRunTime()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.BooleanParameterDefinition>\n" +
                "          <name>screenShot</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;iOS&lt;/font&gt;端测试选择是否需要截图</description>\n" +
                "          <defaultValue>true</defaultValue>\n" +
                "        </hudson.model.BooleanParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>buildScenes</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;ios-normal-monkey&lt;/font&gt;测试类型</description>\n" +
                "          <defaultValue>"+monkeySetting.getDescribes()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>misId</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;dongheng&lt;/font&gt;提交人mis号</description>\n" +
                "          <defaultValue>"+monkeySetting.getMisId()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>conanKey</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;18a03220-ef17-4082-ba6b-59bbe65ce781&lt;/font&gt;提交人conanKey号</description>\n" +
                "          <defaultValue>"+monkeySetting.getConanKey()+"</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "      </parameterDefinitions>\n" +
                "    </hudson.model.ParametersDefinitionProperty>\n" +
                "    <job-metadata plugin=\"metadata@1.1.0b\">\n" +
                "      <values class=\"linked-list\">\n" +
                "        <metadata-tree>\n" +
                "          <name>job-info</name>\n" +
                "          <parent class=\"job-metadata\" reference=\"../../..\"/>\n" +
                "          <generated>true</generated>\n" +
                "          <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "          <children class=\"linked-list\">\n" +
                "            <metadata-tree>\n" +
                "              <name>last-saved</name>\n" +
                "              <description></description>\n" +
                "              <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "              <generated>true</generated>\n" +
                "              <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "              <children class=\"linked-list\">\n" +
                "                <metadata-date>\n" +
                "                  <name>time</name>\n" +
                "                  <description></description>\n" +
                "                  <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                  <generated>true</generated>\n" +
                "                  <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                  <value>\n" +
                "                    <time>1606401501335</time>\n" +
                "                    <timezone>PRC</timezone>\n" +
                "                  </value>\n" +
                "                  <checked>false</checked>\n" +
                "                </metadata-date>\n" +
                "                <metadata-tree>\n" +
                "                  <name>user</name>\n" +
                "                  <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                  <generated>true</generated>\n" +
                "                  <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                  <children class=\"linked-list\">\n" +
                "                    <metadata-string>\n" +
                "                      <name>display-name</name>\n" +
                "                      <description></description>\n" +
                "                      <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                      <generated>true</generated>\n" +
                "                      <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                      <value>董恒</value>\n" +
                "                    </metadata-string>\n" +
                "                    <metadata-string>\n" +
                "                      <name>full-name</name>\n" +
                "                      <description></description>\n" +
                "                      <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                      <generated>true</generated>\n" +
                "                      <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                      <value>董恒</value>\n" +
                "                    </metadata-string>\n" +
                "                  </children>\n" +
                "                </metadata-tree>\n" +
                "              </children>\n" +
                "            </metadata-tree>\n" +
                "          </children>\n" +
                "        </metadata-tree>\n" +
                "      </values>\n" +
                "    </job-metadata>\n" +
                "  </properties>\n" +
                "  <scm class=\"hudson.scm.NullSCM\"/>\n" +
                "  <assignedNode>docker</assignedNode>\n" +
                "  <canRoam>false</canRoam>\n" +
                "  <disabled>false</disabled>\n" +
                "  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>\n" +
                "  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>\n" +
                "  <jdk>(System)</jdk>\n" +
                "  <triggers>\n" +
                "    <hudson.triggers.TimerTrigger>\n" +
                "      <spec>"+monkeySetting.getTimer()+"</spec>\n" +
                "    </hudson.triggers.TimerTrigger>\n" +
                "  </triggers>\n" +
                "  <concurrentBuild>true</concurrentBuild>\n" +
                "  <builders>\n" +
                "    <hudson.tasks.Shell>\n" +
                "      <command>#!/bin/bash\n" +
                "currentTime=`date +&apos;%Y-%m-%d %H:%M:%S&apos;`\n" +
                "\n" +
                "\n" +
                "echo &quot;os:&quot;$os\n" +
                "echo &quot;alias:&quot;$alias\n" +
                "echo &quot;loginCase:&quot;$loginCase\n" +
                "echo &quot;url:&quot;$url\n" +
                "echo &quot;devicesVersion:&quot;$devicesVersion\n" +
                "echo &quot;deviceCount:&quot;$deviceCount\n" +
                "echo &quot;maxWaitTime:&quot;$maxWaitTime\n" +
                "echo &quot;intents:&quot;$intents\n" +
                "echo &quot;receiver:&quot;$receiver\n" +
                "echo &quot;events:&quot;$events\n" +
                "echo &quot;throttle:&quot;$throttle\n" +
                "echo &quot;runTime:&quot;$runTime\n" +
                "echo &quot;screenShot:&quot;$screenShot\n" +
                "echo &quot;buildScenes:&quot;$buildScenes\n" +
                "echo &quot;conanKey:&quot;$conanKey\n" +
                "echo &quot;misId:&quot;$misId\n" +
                "\n" +
                "getJsonVal(){\n" +
                "\tpython -c &quot;import json,sys;sys.stdout.write(json.dumps(json.load(sys.stdin)$1))&quot;;\n" +
                "}\n" +
                "MONKEY_URL=&apos;http://qaassist.sankuai.com/compass/api/monkey&apos;\n" +
                "response=`curl -X POST -d &quot;jenkinsName=$JOB_BASE_NAME&quot; &quot;$MONKEY_URL/getApkUrlAndConfigSettingId&quot;`\n" +
                "url=`echo $response | getJsonVal &quot;[&apos;appUrl&apos;]&quot; | sed &apos;s/\\&quot;//g&apos;`\n" +
                "configSettingId=`echo $response | getJsonVal &quot;[&apos;configSettingId&apos;]&quot; | sed &apos;s/\\&quot;//g&apos;`\n" +
                "\n" +
                "echo &quot;---------成功触发Job----------&quot;\n" +
                "startJobTime=$currentTime\n" +
                "echo &quot;startJobTime:&quot;$startJobTime\n" +
                "\n" +
                "submitAndroidUrl=&quot;http://conan.sankuai.com/ci/stability/advanced-monkey&quot;\n" +
                "submitiOSUrl=&quot;https://conan.sankuai.com/ci/stability/ios-normal-monkey&quot;\n" +
                "jenkinsUrl=&quot;http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/&quot;$BUILD_NUMBER\n" +
                "echo &quot;jenkinsUrl:&quot;$jenkinsUrl\n" +
                "echo &quot;build_number:&quot;$BUILD_NUMBER\n" +
                "BUILD_PATH=&apos;http://qaassist.sankuai.com/compass/api/autotest/build&apos;\n" +
                "JOB_PATH=&apos;http://qaassist.sankuai.com/compass/api/autotest/job&apos;\n" +
                "echo &quot;----------新增构建入库----------&quot;\n" +
                "\n" +
                "if [[ $url == &quot;&quot; ]]; then\n" +
                "\techo &quot;url为空&quot;\n" +
                "\texit 1\n" +
                "fi\n" +
                "buildId=`curl -X POST -d &quot;jenkinsId=$BUILD_NUMBER&amp;platform=$os&amp;buildUrl=$jenkinsUrl&amp;buildType=stability&amp;buildScenes=$buildScenes&amp;alias=$alias&quot; &quot;$BUILD_PATH/add&quot;`\n" +
                "echo &quot;buildId:&quot;$buildId\n" +
                "curl -X POST -d &quot;id=$buildId&amp;status=0&quot; &quot;$BUILD_PATH/update/status&quot;\n" +
                "echo &quot;-----------新增入库完成-----------&quot;\n" +
                "echo &quot;---------开始插入运行数据表------&quot;\n" +
                "monkeyResultId=`curl -H &quot;Content-Type:application/json&quot; -X POST -d &quot;{\\&quot;jenkinsName\\&quot;: \\&quot;$JOB_BASE_NAME\\&quot;, \\&quot;buildId\\&quot;: \\&quot;$BUILD_NUMBER\\&quot;, \\&quot;apkUrl\\&quot;: \\&quot;$url\\&quot;, \\&quot;jenkinsUrl\\&quot;: \\&quot;$jenkinsUrl\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始执行monkey检测任务\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;$MONKEY_URL/insertRunningData&quot;`\n" +
                "\n" +
                "echo &quot;-------下载预处理case-------&quot;\n" +
                "if [[ $loginCase == &quot;1&quot; ]]; then\n" +
                "\tgit clone ssh://*******************/~dongheng/monkey_logincase.git\n" +
                "    loginCase=monkey_logincase/com.sankuai.conan.monkey.login-1.0-SNAPSHOT-jar-with-dependencies.jar\n" +
                "fi\n" +
                "\n" +
                "\n" +
                "#上传预处理case\n" +
                "upload_case(){\n" +
                "\tcaseType=case\n" +
                "\tfor i in {0..60}\n" +
                "\tdo\n" +
                "\t\tcaseResponse=`curl -X POST -H Content-Type:multipart/form-data -F &quot;file=@$1&quot; https://conan.sankuai.com/ci/upload\\?type\\=$caseType\\&amp;misId\\=$misId\\&amp;conanKey\\=$conanKey`\n" +
                "        caseResponseStatus=`echo $caseResponse | getJsonVal &quot;[&apos;status&apos;]&quot;`\n" +
                "\t\tcaseName=`echo $caseResponse | getJsonVal &quot;[&apos;fileName&apos;]&quot; | sed &apos;s/\\&quot;//g&apos;`\n" +
                "\t\tif [[ $caseResponseStatus -eq 1 ]]; then\n" +
                "\t\t\techo $caseName\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;caseResult\\&quot;: \\&quot;1\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;\n" +
                "\t\t\tbreak\n" +
                "\t\telse\n" +
                "        \tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;caseResult\\&quot;: \\&quot;0\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;\n" +
                "\t\t\tsleep 10\n" +
                "\t\tfi \n" +
                "\tdone\n" +
                "}\n" +
                "\n" +
                "#提交job：云测支持直接通过上传测试包的链接，不用再单独上传测试包\n" +
                "submit_job(){\n" +
                "\tif [[ $os == &quot;Android&quot; ]]; then\n" +
                "\t\tpara=&quot;apk=$url&amp;misId=$misId&amp;conanKey=$conanKey&amp;alias=$alias&amp;devicesVersion=$devicesVersion&amp;intents=$intents&amp;events=$events&amp;throttle=$throttle&amp;maxWaitTime=$maxWaitTime&amp;after=notifyResultCommon&amp;notify=http://qaassist.sankuai.com/compass/api/autotest/job/result&quot;\n" +
                "        submitUrl=$submitAndroidUrl\n" +
                "\telif [[ $os == &quot;iOS&quot; ]]; then\n" +
                "        para=&quot;ipa=$url&amp;misId=$misId&amp;conanKey=$conanKey&amp;alias=$alias&amp;devicesVersion=$devicesVersion&amp;intents=$intents&amp;runTime=$runTime&amp;screenShot=$screenShot&amp;maxWaitTime=$maxWaitTime&amp;after=notifyResultCommon&amp;notify=http://qaassist.sankuai.com/compass/api/autotest/job/result&quot;\n" +
                "\t\tsubmitUrl=$submitiOSUrl\n" +
                "\tfi\n" +
                "\t\n" +
                "\tfor i in {0..60}\n" +
                "\tdo\n" +
                "\t\tjobResponse=`curl -d &quot;$para&quot; $submitUrl`\n" +
                "\t\tsleep 5\n" +
                "\t\tresponseStatus=`echo $jobResponse | getJsonVal &quot;[&apos;status&apos;]&quot;`\n" +
                "\t\tjobId=`echo $jobResponse | getJsonVal &quot;[&apos;jobId&apos;]&quot;`\n" +
                "        msg=`echo $jobResponse | getJsonVal &quot;[&apos;msg&apos;]&quot;`\n" +
                "\t\tif [[ $responseStatus -eq 1 ]]; then\n" +
                "\t\t\techo $jobResponse\n" +
                "\t\t\tbreak\n" +
                "\t\telse\n" +
                "        \tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;jobId\\&quot;: \\&quot;$jobId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;$msg\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;\n" +
                "\t\t\tsleep 30\n" +
                "\t\tfi\n" +
                "\t\tif [[ $i == 60 ]]; then\n" +
                "\t\t\techo $jobResponse\n" +
                "\t\tfi\n" +
                "        \n" +
                "\tdone\n" +
                "}\n" +
                "\n" +
                "echo &quot;----------开始执行测试-----------&quot;\n" +
                "if [[ $url == &quot;&quot; ]]; then\n" +
                "\techo &quot;url为空&quot;\n" +
                "\texit 1\n" +
                "fi\n" +
                "\n" +
                "url_content=(${url//,/ })\n" +
                "echo &quot;url_count:&quot;${#url_content[@]}\n" +
                "echo &quot;url_content:&quot;${url_content[@]}\n" +
                "for var in ${url_content[@]}; do\n" +
                "\turl=$var\n" +
                "\n" +
                "\tif [ $os == &quot;Android&quot; ];then\n" +
                "\t\techo &quot;------------开始处理intents-------------&quot;\n" +
                "\t\tintents_content=(${intents//,/ })\n" +
                "\t\techo &quot;intents_count:&quot;${#intents_content[@]}\n" +
                "\t\tscheme_count=0\n" +
                "\t\tfor scheme in ${intents_content[@]}; do\n" +
                "\t\t\tscheme_count=`expr $scheme_count + 1`\n" +
                "\t\t\tif [[ $scheme_count -eq ${#intents_content[@]}  ]]; then\n" +
                "\t\t\t\tintents_list=$intents_list&quot;scheme$scheme_count=$scheme&quot;\n" +
                "\t\t\telse\n" +
                "\t\t\t\tintents_list=$intents_list&quot;scheme$scheme_count=$scheme&quot;,\n" +
                "\t\t\tfi\n" +
                "\t\tdone\n" +
                "\t\tintents=$intents_list\n" +
                "\t\techo &quot;intents：&quot;$intents\n" +
                "\t\techo &quot;--------------结束处理intents--------&quot;\n" +
                "\n" +
                "\t\tif [[ $loginCase ]]; then\n" +
                "        \t#更新任务状态为：开始上传预处理case\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "            echo &quot;----------Uploading case-----------&quot;\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startUploadCase&quot;\n" +
                "\t\t\tcaseName=`upload_case $loginCase`\n" +
                "\t\t\techo &quot;caseName:&quot;$caseName\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endUploadCase&quot;\n" +
                "            #更新任务状态为：结束上传预处理case\n" +
                "\t\t\tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;结束上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "\t\tfi\n" +
                "        \n" +
                "        #更新任务状态为：开始向云测提交job\n" +
                "\t\tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始向云测提交job\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "        echo &quot;------------Submit Job-------------&quot;\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startSubmit&quot;\n" +
                "\t\tjobResponse=`submit_job`\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endSubmit&quot;\n" +
                "\n" +
                "\telif [ $os == &quot;iOS&quot; ]; then\n" +
                "        if [[ $loginCase ]]; then\n" +
                "        \t#更新任务状态为：开始上传预处理case\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "\t\t\techo &quot;----------Uploading case-----------&quot;\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startUploadCase&quot;\n" +
                "\t\t\tcaseName=`upload_case $loginCase`\n" +
                "\t\t\techo &quot;caseName:&quot;$caseName\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endUploadCase&quot;\n" +
                "            #更新任务状态为：结束上传预处理case\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;结束上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "\t\tfi\n" +
                "        echo &quot;-------更新任务状态为：开始向云测提交job------&quot;\n" +
                "\t\tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始向云测提交job\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "        echo &quot;------------Submit Job-------------&quot;\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startSubmit&quot;\n" +
                "\t\tjobResponse=`submit_job`\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endSubmit&quot; \n" +
                "\tfi\n" +
                "\n" +
                "\techo &quot;jobResponse:&quot;$jobResponse\n" +
                "\tjobId=`echo $jobResponse | getJsonVal &quot;[&apos;jobId&apos;]&quot;`\n" +
                "    msg=`echo $jobResponse | getJsonVal &quot;[&apos;msg&apos;]&quot;`\n" +
                "\techo &quot;jobId:&quot;$jobId\n" +
                "\tcurl -X POST -d &quot;buildId=$buildId&amp;platform=$os&amp;alias=$alias&amp;jobId=$jobId&amp;jobType=stability&quot; &quot;$JOB_PATH/add&quot;\n" +
                "    if [[ $jobId -ne -1 ]]; then\n" +
                "        curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;提交job成功\\&quot;, \\&quot;status\\&quot;: \\&quot;1\\&quot;, \\&quot;jobId\\&quot;: \\&quot;$jobId\\&quot;, \\&quot;reportUrl\\&quot;: \\&quot;https://conan.sankuai.com/v2/new-stability/report/$jobId\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "    else\n" +
                "    \tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;$msg\\&quot;, \\&quot;status\\&quot;: \\&quot;-1\\&quot;, \\&quot;jobId\\&quot;: \\&quot;$jobId\\&quot;, \\&quot;reportUrl\\&quot;: \\&quot;https://conan.sankuai.com/v2/new-stability/report/$jobId\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "    fi\n" +
                "done  \n" +
                "\n" +
                "echo &quot;----------Jenkins Job Finished----------&quot;\n" +
                "curl -X POST -d &quot;buildUrl=$jenkinsUrl&quot; &quot;$BUILD_PATH/update/end&quot;\n" +
                "curl -X POST -d &quot;buildUrl=$jenkinsUrl&amp;status=1&quot; &quot;$BUILD_PATH/update/status&quot;\n" +
                "\n" +
                "endJobTime=$currentTime\n" +
                "echo &quot;endJobTime:&quot;$endJobTime\n" +
                "</command>\n" +
                "    </hudson.tasks.Shell>\n" +
                "  </builders>\n" +
                "  <publishers>\n" +
                "    <hudson.plugins.postbuildtask.PostbuildTask plugin=\"postbuild-task@1.8\">\n" +
                "      <tasks>\n" +
                "        <hudson.plugins.postbuildtask.TaskProperties>\n" +
                "          <logTexts>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>Aborted</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>aborted</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "          </logTexts>\n" +
                "          <EscalateStatus>false</EscalateStatus>\n" +
                "          <RunIfJobSuccessful>false</RunIfJobSuccessful>\n" +
                "          <script>curl -X POST -d &quot;buildUrl=http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/$BUILD_NUMBER&quot; &quot;http://qaassist.sankuai.com/compass/api/autotest/build/update/end&quot;&#xd;\n" +
                "curl -X POST -d &quot;buildUrl=http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/$BUILD_NUMBER&amp;status=1&quot; &quot;$http://qaassist.sankuai.com/compass/api/autotest/build/update/status&quot;&#xd;\n" +
                "&#xd;\n" +
                "&#xd;\n" +
                "curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;buildId\\&quot;: \\&quot;$BUILD_NUMBER\\&quot;,\\&quot;jenkinsName\\&quot;: \\&quot;$JOB_BASE_NAME\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;任务被取消\\&quot;, \\&quot;status\\&quot;: \\&quot;-1\\&quot;}&quot;  &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;&#xd;\n" +
                "</script>\n" +
                "        </hudson.plugins.postbuildtask.TaskProperties>\n" +
                "        <hudson.plugins.postbuildtask.TaskProperties>\n" +
                "          <logTexts>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>Failure</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>failure</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "          </logTexts>\n" +
                "          <EscalateStatus>false</EscalateStatus>\n" +
                "          <RunIfJobSuccessful>false</RunIfJobSuccessful>\n" +
                "          <script>&#xd;\n" +
                "curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;buildId\\&quot;: \\&quot;$BUILD_NUMBER\\&quot;,\\&quot;jenkinsName\\&quot;: \\&quot;$JOB_BASE_NAME\\&quot;,  \\&quot;status\\&quot;: \\&quot;-1\\&quot;}&quot;  &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;</script>\n" +
                "        </hudson.plugins.postbuildtask.TaskProperties>\n" +
                "      </tasks>\n" +
                "    </hudson.plugins.postbuildtask.PostbuildTask>\n" +
                "    <com.sankuai.meituan.ep.jenkins.notify.DxNotifier plugin=\"dx-notify-plugin@1.1.1\">\n" +
                "      <recipients>$misId</recipients>\n" +
                "      <contentFile></contentFile>\n" +
                "      <removeDefaultTitleIfContentIsNotEmpty>false</removeDefaultTitleIfContentIsNotEmpty>\n" +
                "      <notifyUsersStarted>true</notifyUsersStarted>\n" +
                "      <notifyLastCommitUser>false</notifyLastCommitUser>\n" +
                "      <notifyMap class=\"enum-map\" enum-type=\"com.sankuai.meituan.ep.jenkins.notify.BuildResult\">\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>SUCCESS</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>FAILURE</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>UNSTABLE</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>ABORTED</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>NOT_BUILT</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>false</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>FIXED</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "      </notifyMap>\n" +
                "      <notifySuccess>true</notifySuccess>\n" +
                "      <notifyFailure>true</notifyFailure>\n" +
                "      <notifyFixed>true</notifyFixed>\n" +
                "      <notifyUnstable>true</notifyUnstable>\n" +
                "      <notifyAborted>true</notifyAborted>\n" +
                "      <notifyNotBuilt>false</notifyNotBuilt>\n" +
                "    </com.sankuai.meituan.ep.jenkins.notify.DxNotifier>\n" +
                "  </publishers>\n" +
                "  <buildWrappers>\n" +
                "    <hudson.plugins.build__timeout.BuildTimeoutWrapper plugin=\"build-timeout@1.19\">\n" +
                "      <strategy class=\"hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy\">\n" +
                "        <timeoutMinutes>180</timeoutMinutes>\n" +
                "      </strategy>\n" +
                "      <operationList>\n" +
                "        <hudson.plugins.build__timeout.operations.AbortOperation/>\n" +
                "      </operationList>\n" +
                "    </hudson.plugins.build__timeout.BuildTimeoutWrapper>\n" +
                "  </buildWrappers>\n" +
                "</project>";
        return templatXML;
    }
    public String getCommonTemplateXML(MonkeySetting monkeySetting){
        String templateXML ="<?xml version='1.1' encoding='UTF-8'?>\n" +
                "<project>\n" +
                "  <actions/>\n" +
                "  <description>"+monkeySetting.getDescribes()+"</description>\n" +
                "  <keepDependencies>false</keepDependencies>\n" +
                "  <properties>\n" +
                "    <hudson.security.AuthorizationMatrixProperty>\n" +
                "      <inheritanceStrategy class=\"org.jenkinsci.plugins.matrixauth.inheritance.InheritParentStrategy\"/>\n" +
                "      <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Create:dongheng</permission>\n" +
                "      <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Update:dongheng</permission>\n" +
                "      <permission>com.cloudbees.plugins.credentials.CredentialsProvider.View:dongheng</permission>\n" +
                "      <permission>hudson.model.Item.Build:dongheng</permission>\n" +
                "      <permission>hudson.model.Item.Cancel:dongheng</permission>\n" +
                "      <permission>hudson.model.Item.Configure:dongheng</permission>\n" +
                "    </hudson.security.AuthorizationMatrixProperty>\n" +
                "    <hudson.plugins.disk__usage.DiskUsageProperty plugin=\"disk-usage@0.28\"/>\n" +
                "    <com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty plugin=\"naginator@1.17.2\">\n" +
                "      <optOut>false</optOut>\n" +
                "    </com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty>\n" +
                "    <com.synopsys.arc.jenkinsci.plugins.jobrestrictions.jobs.JobRestrictionProperty plugin=\"job-restrictions@0.8\"/>\n" +
                "    <hudson.model.ParametersDefinitionProperty>\n" +
                "      <parameterDefinitions>\n" +
                "        <hudson.model.ChoiceParameterDefinition>\n" +
                "          <name>os</name>\n" +
                "          <description>选择测试端</description>\n" +
                "          <choices class=\"java.util.Arrays$ArrayList\">\n" +
                "            <a class=\"string-array\">\n" +
                "              <string>Android</string>\n" +
                "              <string>iOS</string>\n" +
                "            </a>\n" +
                "          </choices>\n" +
                "        </hudson.model.ChoiceParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>alias</name>\n" +
                "          <description>业务线名称</description>\n" +
                "          <defaultValue>美团</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>loginCase</name>\n" +
                "          <description>用于登录测试App的脚本</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>url</name>\n" +
                "          <description>测试App链接</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>devicesVersion</name>\n" +
                "          <description>测试设备系统</description>\n" +
                "          <defaultValue>5,6,7,8,9,10</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>deviceCount</name>\n" +
                "          <description>测试设备数量</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>maxWaitTime</name>\n" +
                "          <description>设备等待时间，单位(分)</description>\n" +
                "          <defaultValue>90</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>intents</name>\n" +
                "          <description>设置起始页面</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>receiver</name>\n" +
                "          <description>接收Job运行结果通知人</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>events</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;Android&lt;/font&gt;端测试需要设置每个页面的事件数</description>\n" +
                "          <defaultValue>100</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>throttle</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;Android&lt;/font&gt;端测试需要设置每个事件之间的间隔，单位(ms)</description>\n" +
                "          <defaultValue>200</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>runTime</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;iOS&lt;/font&gt;端测试需要设置执行时长，单位(s)</description>\n" +
                "          <defaultValue>3600</defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.BooleanParameterDefinition>\n" +
                "          <name>screenShot</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;iOS&lt;/font&gt;端测试选择是否需要截图</description>\n" +
                "          <defaultValue>true</defaultValue>\n" +
                "        </hudson.model.BooleanParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>buildScenes</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;ios-normal-monkey&lt;/font&gt;测试类型</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>misId</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;dongheng&lt;/font&gt;提交人mis号</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>conanKey</name>\n" +
                "          <description>&lt;font style=&quot;color:red&quot;&gt;18a03220-ef17-4082-ba6b-59bbe65ce781&lt;/font&gt;提交人conanKey号</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "        <hudson.model.StringParameterDefinition>\n" +
                "          <name>monkeyResultId</name>\n" +
                "          <description>每条任务数据库id</description>\n" +
                "          <defaultValue></defaultValue>\n" +
                "          <trim>false</trim>\n" +
                "        </hudson.model.StringParameterDefinition>\n" +
                "      </parameterDefinitions>\n" +
                "    </hudson.model.ParametersDefinitionProperty>\n" +
                "    <job-metadata plugin=\"metadata@1.1.0b\">\n" +
                "      <values class=\"linked-list\">\n" +
                "        <metadata-tree>\n" +
                "          <name>job-info</name>\n" +
                "          <parent class=\"job-metadata\" reference=\"../../..\"/>\n" +
                "          <generated>true</generated>\n" +
                "          <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "          <children class=\"linked-list\">\n" +
                "            <metadata-tree>\n" +
                "              <name>last-saved</name>\n" +
                "              <description></description>\n" +
                "              <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "              <generated>true</generated>\n" +
                "              <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "              <children class=\"linked-list\">\n" +
                "                <metadata-date>\n" +
                "                  <name>time</name>\n" +
                "                  <description></description>\n" +
                "                  <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                  <generated>true</generated>\n" +
                "                  <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                  <value>\n" +
                "                    <time>1606401000294</time>\n" +
                "                    <timezone>PRC</timezone>\n" +
                "                  </value>\n" +
                "                  <checked>false</checked>\n" +
                "                </metadata-date>\n" +
                "                <metadata-tree>\n" +
                "                  <name>user</name>\n" +
                "                  <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                  <generated>true</generated>\n" +
                "                  <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                  <children class=\"linked-list\">\n" +
                "                    <metadata-string>\n" +
                "                      <name>display-name</name>\n" +
                "                      <description></description>\n" +
                "                      <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                      <generated>true</generated>\n" +
                "                      <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                      <value>董恒</value>\n" +
                "                    </metadata-string>\n" +
                "                    <metadata-string>\n" +
                "                      <name>full-name</name>\n" +
                "                      <description></description>\n" +
                "                      <parent class=\"metadata-tree\" reference=\"../../..\"/>\n" +
                "                      <generated>true</generated>\n" +
                "                      <exposedToEnvironment>false</exposedToEnvironment>\n" +
                "                      <value>董恒</value>\n" +
                "                    </metadata-string>\n" +
                "                  </children>\n" +
                "                </metadata-tree>\n" +
                "              </children>\n" +
                "            </metadata-tree>\n" +
                "          </children>\n" +
                "        </metadata-tree>\n" +
                "      </values>\n" +
                "    </job-metadata>\n" +
                "  </properties>\n" +
                "  <scm class=\"hudson.scm.NullSCM\"/>\n" +
                "  <assignedNode>docker</assignedNode>\n" +
                "  <canRoam>false</canRoam>\n" +
                "  <disabled>false</disabled>\n" +
                "  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>\n" +
                "  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>\n" +
                "  <jdk>(System)</jdk>\n" +
                "  <triggers/>\n" +
                "  <concurrentBuild>true</concurrentBuild>\n" +
                "  <builders>\n" +
                "    <hudson.tasks.Shell>\n" +
                "      <command>#!/bin/bash\n" +
                "currentTime=`date +&apos;%Y-%m-%d %H:%M:%S&apos;`\n" +
                "\n" +
                "\n" +
                "echo &quot;os:&quot;$os\n" +
                "echo &quot;alias:&quot;$alias\n" +
                "echo &quot;loginCase:&quot;$loginCase\n" +
                "echo &quot;url:&quot;$url\n" +
                "echo &quot;devicesVersion:&quot;$devicesVersion\n" +
                "echo &quot;deviceCount:&quot;$deviceCount\n" +
                "echo &quot;maxWaitTime:&quot;$maxWaitTime\n" +
                "echo &quot;intents:&quot;$intents\n" +
                "echo &quot;receiver:&quot;$receiver\n" +
                "echo &quot;events:&quot;$events\n" +
                "echo &quot;throttle:&quot;$throttle\n" +
                "echo &quot;runTime:&quot;$runTime\n" +
                "echo &quot;screenShot:&quot;$screenShot\n" +
                "echo &quot;buildScenes:&quot;$buildScenes\n" +
                "echo &quot;conanKey:&quot;$conanKey\n" +
                "echo &quot;misId:&quot;$misId\n" +
                "echo &quot;monkeyResultId:&quot;$monkeyResultId\n" +
                "\n" +
                "#预处理case\n" +
                "if [[ $loginCase == &quot;1&quot; ]]; then\n" +
                "\tgit clone ssh://*******************/~dongheng/monkey_logincase.git\n" +
                "    loginCase=monkey_logincase/com.sankuai.conan.monkey.login-1.0-SNAPSHOT-jar-with-dependencies.jar\n" +
                "fi\n" +
                "\n" +
                "MONKEY_URL=&quot;http://qaassist.sankuai.com/compass/api/monkey&quot;\n" +
                "echo &quot;---------成功触发Job----------&quot;\n" +
                "startJobTime=$currentTime\n" +
                "echo &quot;startJobTime:&quot;$startJobTime\n" +
                "\n" +
                "submitAndroidUrl=&quot;http://conan.sankuai.com/ci/stability/advanced-monkey&quot;\n" +
                "submitiOSUrl=&quot;https://conan.sankuai.com/ci/stability/ios-normal-monkey&quot;\n" +
                "jenkinsUrl=&quot;http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/&quot;$BUILD_NUMBER\n" +
                "echo &quot;jenkinsUrl:&quot;$jenkinsUrl\n" +
                "echo &quot;build_number:&quot;$BUILD_NUMBER\n" +
                "BUILD_PATH=&apos;http://qaassist.sankuai.com/compass/api/autotest/build&apos;\n" +
                "JOB_PATH=&apos;http://qaassist.sankuai.com/compass/api/autotest/job&apos;\n" +
                "echo &quot;----------新增构建入库----------&quot;\n" +
                "#更新任务状态为：开始执行任务\n" +
                "resp=`curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;jenkinsName\\&quot;: \\&quot;$JOB_BASE_NAME\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始执行monkey检测任务\\&quot;, \\&quot;apkUrl\\&quot;: \\&quot;$url\\&quot;, \\&quot;buildId\\&quot;: \\&quot;$BUILD_NUMBER\\&quot;, \\&quot;jenkinsUrl\\&quot;: \\&quot;$jenkinsUrl\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;$MONKEY_URL/updateData&quot;`\n" +
                "echo &quot;-------$resp---------&quot;\n" +
                "\n" +
                "if [[ $url == &quot;&quot; ]]; then\n" +
                "\techo &quot;url为空&quot;\n" +
                "\texit 1\n" +
                "fi\n" +
                "buildId=`curl -X POST -d &quot;jenkinsId=$BUILD_NUMBER&amp;platform=$os&amp;buildUrl=$jenkinsUrl&amp;buildType=stability&amp;buildScenes=$buildScenes&amp;alias=$alias&quot; &quot;$BUILD_PATH/add&quot;`\n" +
                "echo &quot;buildId:&quot;$buildId\n" +
                "curl -X POST -d &quot;id=$buildId&amp;status=0&quot; &quot;$BUILD_PATH/update/status&quot;\n" +
                "echo &quot;-----------新增入库完成-----------&quot;\n" +
                "\n" +
                "getJsonVal(){\n" +
                "\tpython -c &quot;import json,sys;sys.stdout.write(json.dumps(json.load(sys.stdin)$1))&quot;;\n" +
                "}\n" +
                "\n" +
                "#上传预处理case\n" +
                "upload_case(){\n" +
                "\tcaseType=case\n" +
                "\tfor i in {0..60}\n" +
                "\tdo\n" +
                "\t\tcaseResponse=`curl -X POST -H Content-Type:multipart/form-data -F &quot;file=@$1&quot; https://conan.sankuai.com/ci/upload\\?type\\=$caseType\\&amp;misId\\=$misId\\&amp;conanKey\\=$conanKey`\n" +
                "        caseResponseStatus=`echo $caseResponse | getJsonVal &quot;[&apos;status&apos;]&quot;`\n" +
                "\t\tcaseName=`echo $caseResponse | getJsonVal &quot;[&apos;fileName&apos;]&quot; | sed &apos;s/\\&quot;//g&apos;`\n" +
                "\t\tif [[ $caseResponseStatus -eq 1 ]]; then\n" +
                "\t\t\techo $caseName\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;caseResult\\&quot;: \\&quot;1\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;\n" +
                "\t\t\tbreak\n" +
                "\t\telse\n" +
                "        \tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;caseResult\\&quot;: \\&quot;0\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;\n" +
                "\t\t\tsleep 10\n" +
                "\t\tfi \n" +
                "\tdone\n" +
                "}\n" +
                "\n" +
                "#提交job：云测支持直接通过上传测试包的链接，不用再单独上传测试包\n" +
                "submit_job(){\n" +
                "\tif [[ $os == &quot;Android&quot; ]]; then\n" +
                "\t\tpara=&quot;apk=$url&amp;misId=$misId&amp;conanKey=$conanKey&amp;alias=$alias&amp;devicesVersion=$devicesVersion&amp;intents=$intents&amp;events=$events&amp;throttle=$throttle&amp;maxWaitTime=$maxWaitTime&amp;after=notifyResultCommon&amp;notify=http://qaassist.sankuai.com/compass/api/autotest/job/result&quot;\n" +
                "        submitUrl=$submitAndroidUrl\n" +
                "\telif [[ $os == &quot;iOS&quot; ]]; then\n" +
                "        para=&quot;ipa=$url&amp;misId=$misId&amp;conanKey=$conanKey&amp;alias=$alias&amp;devicesVersion=$devicesVersion&amp;intents=$intents&amp;runTime=$runTime&amp;screenShot=$screenShot&amp;maxWaitTime=$maxWaitTime&amp;after=notifyResultCommon&amp;notify=http://qaassist.sankuai.com/compass/api/autotest/job/result&quot;\n" +
                "        submitUrl=$submitiOSUrl\n" +
                "\tfi\n" +
                "\t\n" +
                "\tfor i in {0..60}\n" +
                "\tdo\n" +
                "\t\tjobResponse=`curl -d &quot;$para&quot; $submitUrl`\n" +
                "\t\tsleep 5\n" +
                "\t\tresponseStatus=`echo $jobResponse | getJsonVal &quot;[&apos;status&apos;]&quot;`\n" +
                "\t\tjobId=`echo $jobResponse | getJsonVal &quot;[&apos;jobId&apos;]&quot;`\n" +
                "        msg=`echo $jobResponse | getJsonVal &quot;[&apos;msg&apos;]&quot;`\n" +
                "\t\tif [[ $responseStatus -eq 1 ]]; then\n" +
                "\t\t\techo $jobResponse\n" +
                "\t\t\tbreak\n" +
                "\t\telse\n" +
                "        \tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;jobId\\&quot;: \\&quot;$jobId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;$msg\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot; &quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;\n" +
                "\t\t\tsleep 30\n" +
                "\t\tfi\n" +
                "\t\tif [[ $i == 60 ]]; then\n" +
                "\t\t\techo $jobResponse\n" +
                "\t\tfi\n" +
                "        \n" +
                "\tdone\n" +
                "}\n" +
                "\n" +
                "echo &quot;----------开始执行测试-----------&quot;\n" +
                "if [[ $url == &quot;&quot; ]]; then\n" +
                "\techo &quot;url为空&quot;\n" +
                "\texit 1\n" +
                "fi\n" +
                "\n" +
                "url_content=(${url//,/ })\n" +
                "echo &quot;url_count:&quot;${#url_content[@]}\n" +
                "echo &quot;url_content:&quot;${url_content[@]}\n" +
                "for var in ${url_content[@]}; do\n" +
                "    echo &quot;---------走到遍历url--------&quot;\n" +
                "\turl=$var\n" +
                "\n" +
                "\tif [ $os == &quot;Android&quot; ];then\n" +
                "\t\techo &quot;------------开始处理intents-------------&quot;\n" +
                "\t\tintents_content=(${intents//,/ })\n" +
                "\t\techo &quot;intents_count:&quot;${#intents_content[@]}\n" +
                "\t\tscheme_count=0\n" +
                "\t\tfor scheme in ${intents_content[@]}; do\n" +
                "\t\t\tscheme_count=`expr $scheme_count + 1`\n" +
                "\t\t\tif [[ $scheme_count -eq ${#intents_content[@]}  ]]; then\n" +
                "\t\t\t\tintents_list=$intents_list&quot;scheme$scheme_count=$scheme&quot;\n" +
                "\t\t\telse\n" +
                "\t\t\t\tintents_list=$intents_list&quot;scheme$scheme_count=$scheme&quot;,\n" +
                "\t\t\tfi\n" +
                "\t\tdone\n" +
                "\t\tintents=$intents_list\n" +
                "\t\techo &quot;intents：&quot;$intents\n" +
                "\t\techo &quot;--------------结束处理intents--------&quot;\n" +
                "\n" +
                "\t\tif [[ $loginCase ]]; then\n" +
                "        \t#更新任务状态为：开始上传预处理case\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "            echo &quot;----------Uploading case-----------&quot;\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startUploadCase&quot;\n" +
                "\t\t\tcaseName=`upload_case $loginCase`\n" +
                "\t\t\techo &quot;caseName:&quot;$caseName\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endUploadCase&quot;\n" +
                "            #更新任务状态为：结束上传预处理case\n" +
                "\t\t\tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;结束上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "\t\tfi\n" +
                "        \n" +
                "        #更新任务状态为：开始向云测提交job\n" +
                "\t\tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始向云测提交job\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "        echo &quot;------------Submit Job-------------&quot;\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startSubmit&quot;\n" +
                "\t\tjobResponse=`submit_job`\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endSubmit&quot;\n" +
                "\n" +
                "\telif [ $os == &quot;iOS&quot; ]; then\n" +
                "        if [[ $loginCase ]]; then\n" +
                "        \t#更新任务状态为：开始上传预处理case\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "\t\t\techo &quot;----------Uploading case-----------&quot;\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startUploadCase&quot;\n" +
                "\t\t\tcaseName=`upload_case $loginCase`\n" +
                "\t\t\techo &quot;caseName:&quot;$caseName\n" +
                "\t\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endUploadCase&quot;\n" +
                "            #更新任务状态为：结束上传预处理case\n" +
                "            curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;结束上传预处理case\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "\t\tfi\n" +
                "        echo &quot;-------更新任务状态为：开始向云测提交job------&quot;\n" +
                "\t\tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;开始向云测提交job\\&quot;, \\&quot;status\\&quot;: \\&quot;0\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "        echo &quot;------------Submit Job-------------&quot;\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/startSubmit&quot;\n" +
                "\t\tjobResponse=`submit_job`\n" +
                "\t\tcurl -X POST -d &quot;id=$buildId&quot; &quot;$BUILD_PATH/update/endSubmit&quot;\n" +
                "\tfi\n" +
                "\n" +
                "\techo &quot;jobResponse:&quot;$jobResponse\n" +
                "\tjobId=`echo $jobResponse | getJsonVal &quot;[&apos;jobId&apos;]&quot;`\n" +
                "    msg=`echo $jobResponse | getJsonVal &quot;[&apos;msg&apos;]&quot;`\n" +
                "\techo &quot;jobId:&quot;$jobId\n" +
                "\tcurl -X POST -d &quot;buildId=$buildId&amp;platform=$os&amp;alias=$alias&amp;jobId=$jobId&amp;jobType=stability&quot; &quot;$JOB_PATH/add&quot;\n" +
                "    if [[ $jobId -ne -1 ]]; then\n" +
                "        curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;提交job成功\\&quot;, \\&quot;status\\&quot;: \\&quot;1\\&quot;, \\&quot;jobId\\&quot;: \\&quot;$jobId\\&quot;, \\&quot;reportUrl\\&quot;: \\&quot;https://conan.sankuai.com/v2/new-stability/report/$jobId\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "    else\n" +
                "    \tcurl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;$msg\\&quot;, \\&quot;status\\&quot;: \\&quot;-1\\&quot;, \\&quot;jobId\\&quot;: \\&quot;$jobId\\&quot;, \\&quot;reportUrl\\&quot;: \\&quot;https://conan.sankuai.com/v2/new-stability/report/$jobId\\&quot;}&quot;  &quot;$MONKEY_URL/updateData&quot;\n" +
                "    fi\n" +
                "done    \n" +
                "\n" +
                "    \n" +
                "echo &quot;----------Jenkins Job Finished----------&quot;\n" +
                "curl -X POST -d &quot;buildUrl=$jenkinsUrl&quot; &quot;$BUILD_PATH/update/end&quot;\n" +
                "curl -X POST -d &quot;buildUrl=$jenkinsUrl&amp;status=1&quot; &quot;$BUILD_PATH/update/status&quot;\n" +
                "\n" +
                "endJobTime=$currentTime\n" +
                "echo &quot;endJobTime:&quot;$endJobTime</command>\n" +
                "    </hudson.tasks.Shell>\n" +
                "  </builders>\n" +
                "  <publishers>\n" +
                "    <hudson.plugins.postbuildtask.PostbuildTask plugin=\"postbuild-task@1.8\">\n" +
                "      <tasks>\n" +
                "        <hudson.plugins.postbuildtask.TaskProperties>\n" +
                "          <logTexts>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>aborted</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>Aborted</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "          </logTexts>\n" +
                "          <EscalateStatus>false</EscalateStatus>\n" +
                "          <RunIfJobSuccessful>false</RunIfJobSuccessful>\n" +
                "          <script>curl -X POST -d &quot;buildUrl=http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/$BUILD_NUMBER&quot; &quot;http://qaassist.sankuai.com/compass/api/autotest/build/update/end&quot;&#xd;\n" +
                "curl -X POST -d &quot;buildUrl=http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/$JOB_BASE_NAME/$BUILD_NUMBER&amp;status=1&quot; &quot;$http://qaassist.sankuai.com/compass/api/autotest/build/update/status&quot;&#xd;\n" +
                "&#xd;\n" +
                "curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;submitResult\\&quot;: \\&quot;任务被取消\\&quot;, \\&quot;status\\&quot;: \\&quot;-1\\&quot;}&quot; &#xd;\n" +
                "&quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;  </script>\n" +
                "        </hudson.plugins.postbuildtask.TaskProperties>\n" +
                "        <hudson.plugins.postbuildtask.TaskProperties>\n" +
                "          <logTexts>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>Failure</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "            <hudson.plugins.postbuildtask.LogProperties>\n" +
                "              <logText>failure</logText>\n" +
                "              <operator>OR</operator>\n" +
                "            </hudson.plugins.postbuildtask.LogProperties>\n" +
                "          </logTexts>\n" +
                "          <EscalateStatus>false</EscalateStatus>\n" +
                "          <RunIfJobSuccessful>false</RunIfJobSuccessful>\n" +
                "          <script>curl -H &quot;Content-Type:application/json&quot; -X POST --data &quot;{\\&quot;id\\&quot;: \\&quot;$monkeyResultId\\&quot;, \\&quot;status\\&quot;: \\&quot;-1\\&quot;}&quot; &#xd;\n" +
                "&quot;http://qaassist.sankuai.com/compass/api/monkey/updateData&quot;  </script>\n" +
                "        </hudson.plugins.postbuildtask.TaskProperties>\n" +
                "      </tasks>\n" +
                "    </hudson.plugins.postbuildtask.PostbuildTask>\n" +
                "    <com.sankuai.meituan.ep.jenkins.notify.DxNotifier plugin=\"dx-notify-plugin@1.1.1\">\n" +
                "      <recipients>$misId</recipients>\n" +
                "      <contentFile></contentFile>\n" +
                "      <removeDefaultTitleIfContentIsNotEmpty>false</removeDefaultTitleIfContentIsNotEmpty>\n" +
                "      <notifyUsersStarted>true</notifyUsersStarted>\n" +
                "      <notifyLastCommitUser>false</notifyLastCommitUser>\n" +
                "      <notifyMap class=\"enum-map\" enum-type=\"com.sankuai.meituan.ep.jenkins.notify.BuildResult\">\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>SUCCESS</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>FAILURE</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>UNSTABLE</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>ABORTED</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>NOT_BUILT</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>false</boolean>\n" +
                "        </entry>\n" +
                "        <entry>\n" +
                "          <com.sankuai.meituan.ep.jenkins.notify.BuildResult>FIXED</com.sankuai.meituan.ep.jenkins.notify.BuildResult>\n" +
                "          <boolean>true</boolean>\n" +
                "        </entry>\n" +
                "      </notifyMap>\n" +
                "      <notifySuccess>true</notifySuccess>\n" +
                "      <notifyFailure>true</notifyFailure>\n" +
                "      <notifyFixed>true</notifyFixed>\n" +
                "      <notifyUnstable>true</notifyUnstable>\n" +
                "      <notifyAborted>true</notifyAborted>\n" +
                "      <notifyNotBuilt>false</notifyNotBuilt>\n" +
                "    </com.sankuai.meituan.ep.jenkins.notify.DxNotifier>\n" +
                "  </publishers>\n" +
                "  <buildWrappers>\n" +
                "    <hudson.plugins.build__timeout.BuildTimeoutWrapper plugin=\"build-timeout@1.19\">\n" +
                "      <strategy class=\"hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy\">\n" +
                "        <timeoutMinutes>180</timeoutMinutes>\n" +
                "      </strategy>\n" +
                "      <operationList>\n" +
                "        <hudson.plugins.build__timeout.operations.AbortOperation/>\n" +
                "      </operationList>\n" +
                "    </hudson.plugins.build__timeout.BuildTimeoutWrapper>\n" +
                "  </buildWrappers>\n" +
                "</project>";
        return templateXML;
    }
}
