package com.sankuai.mdp.compass.monkey.Utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.offbytwo.jenkins.JenkinsServer;
import com.offbytwo.jenkins.model.Job;
import com.offbytwo.jenkins.model.JobWithDetails;
import com.offbytwo.jenkins.model.QueueReference;
import com.sankuai.mdp.compass.common.utils.TokenUtil;
import com.sankuai.mdp.compass.mge.service.impl.MgeTestJobServiceImpl;
import com.sankuai.mdp.compass.monkey.entity.MonkeySetting;
import net.sf.json.JSONObject;

import java.io.IOException;
import java.net.URI;
import java.util.Map;

/**
 * Created by dongheng on 2020/10/29
 */
public class JenkinsConfigUtils{
    public static JenkinsServer jenkinsServer = null;
    public static Map<String,Job> jobs = null;

    static {
        try {
            String jenkinsUrl = "http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/";
            String misId = "ptmobiletest";
            URI uri = new URI(jenkinsUrl);
            jenkinsServer = new JenkinsServer(uri,misId, TokenUtil.getToken("ptmobiletest"));
            //jenkinsServer = new JenkinsServer(uri);
            jobs=jenkinsServer.getJobs();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public boolean createJenkinsJob(MonkeySetting monkeySetting) throws Exception{
        StringBuilder jobXml = new StringBuilder();
        MonkeyTemplateUtils monkeyTemplateUtils = new MonkeyTemplateUtils();
        if (monkeySetting.getTimer()!=null){
            jobXml.append(monkeyTemplateUtils.getTimerTemplateXML(monkeySetting));
        }else {
            jobXml.append(monkeyTemplateUtils.getCommonTemplateXML(monkeySetting));
        }
        jenkinsServer.createJob(monkeySetting.getJenkinsName(), jobXml.toString(),true);
        if (hasJobs(monkeySetting.getJenkinsName())==true){
            return true;
        }
        return false;
    }

    public String triggerJenkinsJob(String jobName,Map jobParams){
        try{
            if(jenkinsServer.getJob(jobName)==null){
                return null;
            }
            JobWithDetails job1 = jenkinsServer.getJob(jobName);
            QueueReference build = job1.build(jobParams, true);
            return build.getQueueItemUrlPart();

        }catch (IOException e){
            e.printStackTrace();
        }
        return null;
    }

    public Boolean hasJobs(String jenkinsName){
        try {
            JobWithDetails job = jenkinsServer.getJob(jenkinsName);
            if (job==null){
                return false;
            }
        }catch (IOException e){
            e.printStackTrace();
        }
        return true;
    }

    public Integer getJenkinsBuildNumber(String jenkinsName){
        try {
            JobWithDetails job = jenkinsServer.getJob(jenkinsName);
            return job.getNextBuildNumber();
        }catch (IOException e){
            e.printStackTrace();
        }
        return 0;
    }

    public boolean stopJenkins(String jenkinsName){
        try {
            jenkinsServer.disableJob(jenkinsName);
            JobWithDetails job = jenkinsServer.getJob(jenkinsName);
            job.isInQueue();
        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 更新jenkins
     * @param jenkinsName 需要更新的jenkinsName
     * @param jobXML      新的jenkins描述xml
     */
    public void updateJenkins(String jenkinsName,String jobXML){
        try {
           jenkinsServer.updateJob(jenkinsName, jobXML, true);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws IOException{
        String jobXml = jenkinsServer.getJobXml("donghengmeituanssAndroid32");
        System.out.println(jobXml);
//        System.out.println("-----------");
//        System.out.println(jobXml.length());
//        JenkinsConfigUtils jenkinsConfigUtils = new JenkinsConfigUtils();
//        URL url = new ClassPathResource("monkeyTemplate/monkeyTimerTemplate.xml").getURL();
////        System.out.println(url.getPath());
//        System.out.println(jenkinsConfigUtils.readXML(url.getPath()));
    }


}
