package com.sankuai.mdp.compass.monkey.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import com.sankuai.mdp.compass.getAPK.service.IGetApkDataService;
import com.sankuai.mdp.compass.monkey.Utils.JenkinsConfigUtils;
import com.sankuai.mdp.compass.monkey.Utils.URLEncoderUtils;
import com.sankuai.mdp.compass.monkey.entity.ApkCondition;
import com.sankuai.mdp.compass.monkey.entity.MonkeyResult;
import com.sankuai.mdp.compass.monkey.entity.MonkeySetting;
import com.sankuai.mdp.compass.monkey.mapper.ApkConditionMapper;
import com.sankuai.mdp.compass.monkey.mapper.MonkeyResultMapper;
import com.sankuai.mdp.compass.monkey.mapper.MonkeySettingMapper;
import com.sankuai.mdp.compass.monkey.service.CreateOrTriggerJenkinsService;
import jnr.ffi.annotations.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.python.antlr.ast.Str;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sound.midi.Soundbank;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by dongheng on 2020/10/29
 */
@Slf4j
@Service
public class CreateOrTriggerJenkinsServiceImpl implements CreateOrTriggerJenkinsService {
    @Autowired
    IGetApkDataService getApkDataService;
    @Autowired
    MonkeySettingMapper monkeySettingMapper;
    @Autowired
    ApkConditionMapper apkConditionMapper;
    @Autowired
    MonkeyResultMapper monkeyResultMapper;

    JenkinsConfigUtils jenkinsConfigUtils = new JenkinsConfigUtils();

    @Override
    public JsonObject createJenkins(Integer id){
        JsonObject createResult = new JsonObject();
        QueryWrapper<MonkeySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        List<MonkeySetting> list = monkeySettingMapper.selectList(queryWrapper);
        MonkeySetting monkeySetting = list.get(0);
        try {
            StringBuilder jenkinsName=new StringBuilder();
            if(StringUtils.isNotBlank(monkeySetting.getDescribes())){
                jenkinsName.append(monkeySetting.getDescribes());
            }
            if(StringUtils.isNotBlank(monkeySetting.getProject())){
                jenkinsName.append(monkeySetting.getProject());
            }
            if(StringUtils.isNotBlank(monkeySetting.getPlatform())){
                jenkinsName.append(monkeySetting.getPlatform());
            }
            monkeySetting.setJenkinsName(jenkinsName.append(monkeySetting.getId()).toString());
            //更新jenkinsName
            int update = monkeySettingMapper.update(monkeySetting, queryWrapper);
            boolean hasJob = jenkinsConfigUtils.createJenkinsJob(monkeySetting);
            //如果创建job成功，并且数据库中更新了jenkinsName
            if (update==1&&hasJob){
                createResult.addProperty("msg", "success");
                return createResult;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        createResult.addProperty("msg", "fail");
        return createResult;
    }

    public String updateJenkins(){
        return null;
    }

    @Override
    public JsonObject triggerJenkins(Integer id,String appUrl) {
        JsonObject triggerResult = new JsonObject();
        QueryWrapper<MonkeySetting> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        List<MonkeySetting> list = monkeySettingMapper.selectList(queryWrapper);
        MonkeySetting monkeySetting = list.get(0);
        if (monkeySetting.getJenkinsName()==null){
            triggerResult.addProperty("status", "-1");
            triggerResult.addProperty("msg", "还未创建，请先创建job！");
            return triggerResult;
        }
        Boolean hasJob = jenkinsConfigUtils.hasJobs(monkeySetting.getJenkinsName());
        if(!hasJob){
            triggerResult.addProperty("status", "-1");
            triggerResult.addProperty("msg", "aci job被删除，请再次创建job！");
            return triggerResult;
        }
        //插入数据库
        MonkeyResult monkeyResult = new MonkeyResult();
        monkeyResult.setConfigSettingId(monkeySetting.getId());
        monkeyResult.setJenkinsName(monkeySetting.getJenkinsName());
        int insertMonkeyResult = monkeyResultMapper.insert(monkeyResult);

        String jenkinsName = monkeySetting.getJenkinsName();
        //标记是否触发成功
        boolean triggerJob = false;
        Map<String,String> jobParams = getJobParams(monkeySetting, appUrl,monkeyResult.getId());
        try {
            if (hasJob){
                for(int i=0;i<5;i++){
                    String jenkinsJob = jenkinsConfigUtils.triggerJenkinsJob(jenkinsName, jobParams);
                    if (jenkinsJob==null){
                        Thread.sleep(1000);
                    }else {
                        triggerJob = true;
                        break;
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        if (triggerJob){
            if (insertMonkeyResult==1){
                //如果触发成功,返回插入任务id
                triggerResult.addProperty("status", "1");
                triggerResult.addProperty("monkeyResultId", monkeyResult.getId());
                return triggerResult;
            }
        }else {
            //触发失败，删除该条任务记录
            if (insertMonkeyResult==1) {
                monkeySettingMapper.deleteById(monkeyResult.getId());
            }
        }
        triggerResult.addProperty("status", "-1");
        triggerResult.addProperty("msg", "因网络原因触发失败，请尝试再次触发~");
        return triggerResult;
    }

    public String getApkUrl(MonkeySetting monkeySetting){
        String apkGetMethod = monkeySetting.getApkGetMethod();
        if (apkGetMethod.equals("0")){
            return monkeySetting.getApkUrl();
        }
        //取指定条件包
        if(apkGetMethod.equals("1")){
            ApkInfo apkInfo = new ApkInfo();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonApkCondition = jsonParser.parse(monkeySetting.getApkCondition()).getAsJsonObject();
            if (jsonApkCondition.has("os")){
                apkInfo.setOs(jsonApkCondition.get("os").getAsString());
            }
            if (jsonApkCondition.has("appName")){
                apkInfo.setAppName(jsonApkCondition.get("appName").getAsString());
            }
            if (jsonApkCondition.has("buildTypeName")){
                apkInfo.setBuildTypeName(jsonApkCondition.get("buildTypeName").getAsString());
            }
            if (jsonApkCondition.has("currentBranch")){
                apkInfo.setCurrentBranch(jsonApkCondition.get("currentBranch").getAsInt());
            }
            JsonObject jsonApkUrl = getApkDataService.getApkUrl(apkInfo);
            if (jsonApkUrl!=null&&jsonApkUrl.has("appUrl")){
                String appUrl = jsonApkUrl.get("appUrl").getAsString();
                return appUrl;
            }
        }
        return "";
    }

    public Map getJobParams(MonkeySetting monkeySetting,String appUrl,Integer monkeyResultId){
        Map<String,String> jobParams = new HashMap<>();
        log.info("monkeysetting:"+monkeySetting.toString());
        jobParams.put("os", monkeySetting.getPlatform());
        jobParams.put("alias", "美团");
        if (StringUtils.isNotBlank(monkeySetting.getLogin())){
            jobParams.put("loginCase", monkeySetting.getLogin());
        }
        if(StringUtils.isNotBlank(appUrl)){
            jobParams.put("url", appUrl);
        }else {
            appUrl = getApkUrl(monkeySetting);
            jobParams.put("url", appUrl);
        }
        if(StringUtils.isNotBlank(monkeySetting.getDevices())){
            jobParams.put("devicesVersion", monkeySetting.getDevices());
        }
        if (StringUtils.isNotBlank(monkeySetting.getDeviceCount())){
            jobParams.put("deviceCount", monkeySetting.getDeviceCount());
        }
        if (StringUtils.isNotBlank(monkeySetting.getMaxWaitTime())){
            jobParams.put("maxWaitTime", monkeySetting.getMaxWaitTime());
        }
        if (StringUtils.isNotBlank(monkeySetting.getScheme())){
            jobParams.put("intents", URLEncoderUtils.transUrl(monkeySetting.getScheme()));
        }
        if (StringUtils.isNotBlank(monkeySetting.getNotify())){
            jobParams.put("receiver", monkeySetting.getNotify());
        }
        if (StringUtils.isNotBlank(monkeySetting.getEvents())){
            jobParams.put("events", monkeySetting.getEvents());
        }
        if (StringUtils.isNotBlank(monkeySetting.getThrottle())){
            jobParams.put("throttle", monkeySetting.getThrottle());
        }
        if (StringUtils.isNotBlank(monkeySetting.getRunTime())){
            jobParams.put("runTime", monkeySetting.getRunTime());
        }
        jobParams.put("buildScenes", monkeySetting.getDescribes());
        if (StringUtils.isNotBlank(monkeySetting.getMisId())){
            jobParams.put("misId", monkeySetting.getMisId());
        }
        if (StringUtils.isNotBlank(monkeySetting.getConanKey())){
            jobParams.put("conanKey", monkeySetting.getConanKey());
        }
        //传递monkeyResultId
        jobParams.put("monkeyResultId", monkeyResultId.toString());
        return  jobParams;
    }
}
