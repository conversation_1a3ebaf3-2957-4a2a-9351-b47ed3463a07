package com.sankuai.mdp.compass.monkey.Utils;

import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * Created by dongheng on 2020/11/27
 */
public class URLEncoderUtils {
    public static String transUrl(String schemes){
        StringBuilder sb=new StringBuilder();
        try {
            schemes = URLDecoder.decode(schemes, "utf-8");
            String[] split = schemes.split(",");
            for(int i=0;i<split.length-1;i++){
                String encode = URLEncoder.encode(split[i], "utf-8");
                sb.append(encode);
                sb.append(",");
            }
            sb.append(URLEncoder.encode(split[split.length-1], "utf-8"));
        }catch (Exception e){
            e.printStackTrace();
        }
        return sb.toString();
    }
}
