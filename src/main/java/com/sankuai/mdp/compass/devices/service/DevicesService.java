package com.sankuai.mdp.compass.devices.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.devices.entity.Device;

import java.util.List;

/**
 * Created by lizhen39 on 2021/07/16.
 */
public interface DevicesService {
    Resp update(Device device);
    Resp delete(Device device);
    List<String> getAllManager();
    List<String> getAllVersion();
    List<String> getAllCompany();

    List<String> getAllCity();

    List<String> getAllNumber();

    List<String> getAllType();
    void readExcelToData(String filepath);
    IPage<Device> list(QueryRequest request, Device devices);

    Resp add(Device device);
}
