package com.sankuai.mdp.compass.devices.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by lizhen39 on 2021/07/16.
 */
@Data
@TableName("devices")
public class Device{
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String deviceCompany;

    private String deviceType;

    private String deviceVersion;

    private String deviceNumber;

    private String deviceManager;

    private String deviceCity;

}
