package com.sankuai.mdp.compass.devices.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.InterfaceCase;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.devices.entity.Device;
import com.sankuai.mdp.compass.devices.service.DevicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Created by lizhen39 on 2021/07/16.
 */
@RestController
@RequestMapping("/compass/api/devices")

public class DevicesManagerController extends BaseController {
    @Autowired
    DevicesService devicesService;

    @PostMapping("/add")
    public Resp add(Device device) { return devicesService.add(device); }

    @PostMapping("/delete")
    public Resp delete(Device device) {
        return devicesService.delete(device);
    }

    @PostMapping("/update")
    public Resp update(Device device) {
        return devicesService.update(device);
    }

    @GetMapping("/getAllManager")
    public List<String> getAllManager() {
        return devicesService.getAllManager();
    }

    @GetMapping("/getAllVersion")
    public List<String> getAllVersion() {
        return devicesService.getAllVersion();
    }

    @GetMapping("/getAllCompany")
    public List<String> getAllCompany() {
        return devicesService.getAllCompany();
    }

    @GetMapping("/getAllCity")
    public List<String> getAllCity() {
        return devicesService.getAllCity();
    }

    @GetMapping("/getAllNumber")
    public List<String> getAllNumber() {
        return devicesService.getAllNumber();
    }

    @GetMapping("/getAllType")
    public List<String> getAllType() {
        return devicesService.getAllType();
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, Device device) {
        IPage<Device> CompatilityJobIPage = this.devicesService.list(request, device);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/submitExcel")
    public void readExcelToData(String filepath){
        devicesService.readExcelToData(filepath);
    }

}
