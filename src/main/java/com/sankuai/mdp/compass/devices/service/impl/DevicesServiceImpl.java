package com.sankuai.mdp.compass.devices.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.base.DBUtil_druid;
import com.sankuai.mdp.compass.base.ReadExcelUtils;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.devices.entity.Device;
import com.sankuai.mdp.compass.devices.mapper.DevicesMapper;
import com.sankuai.mdp.compass.devices.service.DevicesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by lizhen39 on 2021/07/16.
 */
@Slf4j
@Service
public class DevicesServiceImpl extends ServiceImpl<DevicesMapper, Device> implements DevicesService {
    @Autowired
    DevicesMapper devicesMapper;



    @Override
    public Resp add(Device device) {
        Resp resp = new Resp();
        log.info("插入的设备数据是：" + device.toString());
        devicesMapper.insert(device);
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Resp update(Device device) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        Resp resp = new Resp();
        devicesMapper.update(device, queryWrapper
                .eq("id", device.getId()));
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Resp delete(Device device) {
        Resp resp = new Resp();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", device.getId());
        devicesMapper.delete(queryWrapper);
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public List<String> getAllManager() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<Device> devicelist = devicesMapper.selectList(queryWrapper.groupBy("device_manager"));
        for (Device device : devicelist) {
            list.add(device.getDeviceManager());
        }
        return list;
    }

    @Override
    public List<String> getAllVersion() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<Device> devicelist = devicesMapper.selectList(queryWrapper.groupBy("device_version"));
        for (Device device : devicelist) {
            list.add(device.getDeviceVersion());
        }
        return list;
    }

    @Override
    public List<String> getAllCompany() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<Device> devicelist = devicesMapper.selectList(queryWrapper.groupBy("device_company"));
        for (Device device : devicelist) {
            list.add(device.getDeviceCompany());
        }
        return list;
    }
    @Override
    public List<String> getAllCity() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<Device> devicelist = devicesMapper.selectList(queryWrapper.groupBy("device_city"));
        for (Device device : devicelist) {
            list.add(device.getDeviceCity());
        }
        return list;
    }

    @Override
    public List<String> getAllNumber() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<Device> devicelist = devicesMapper.selectList(queryWrapper.groupBy("device_number"));
        for (Device device : devicelist) {
            list.add(device.getDeviceNumber());
        }
        return list;
    }

    @Override
    public List<String> getAllType() {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<Device> devicelist = devicesMapper.selectList(queryWrapper.groupBy("device_type"));
        for (Device device : devicelist) {
            list.add(device.getDeviceType());
        }
        return list;
    }

    @Override
    public IPage<Device> list(QueryRequest request, Device device) {
        try {
            LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();

            if (null != device.getDeviceVersion()) {
                queryWrapper.like(Device::getDeviceVersion, device.getDeviceVersion());
            }
            if (null != device.getDeviceCompany()) {
                queryWrapper.eq(Device::getDeviceCompany, device.getDeviceCompany());
            }

            if (null != device.getDeviceManager()) {
                queryWrapper.eq(Device::getDeviceManager, device.getDeviceManager());
            }

            if (null != device.getDeviceType()) {
                queryWrapper.like(Device::getDeviceType, device.getDeviceType());
            }

            if (null != device.getDeviceNumber()) {
                queryWrapper.like(Device::getDeviceNumber, device.getDeviceNumber());
            }

            if (null != device.getDeviceCity()) {
                queryWrapper.like(Device::getDeviceCity, device.getDeviceCity());
            }
            Page<Device> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public void readExcelToData(String filepath) {

        Map<Integer, Map<Integer, Object>> map = null;

        try {

            ReadExcelUtils excelReader = new ReadExcelUtils(filepath);

            map = excelReader.readExcelContent();

        } catch (FileNotFoundException e) {

            System.out.println("未找到指定路径的文件!");

            e.printStackTrace();

        } catch (Exception e) {

            e.printStackTrace();

        }
        List<Device> deviceList = new ArrayList<>();
        for (int i = 1; i <= map.size(); i++) {
            Device device = new Device();
            device.setDeviceCompany((String) map.get(i).get(0));
            device.setDeviceType((String) map.get(i).get(1));
            device.setDeviceVersion(String.valueOf(map.get(i).get(2)));
            device.setDeviceNumber((String) map.get(i).get(3));
            device.setDeviceManager((String) map.get(i).get(4));
            deviceList.add(device);
        }
        //this.saveBatch(deviceList);
        for (Device device : deviceList) {
            toData(device);
        }

    }

    public void toData(Device device) {
        PreparedStatement ps = null;
        Connection connection = DBUtil_druid.getConnection();
        try {
            String sql = "insert into devices (device_company,device_type,device_version,device_number,device_manager) values(?,?,?,?,?)";
            ps = connection.prepareStatement(sql);
            ps.setString(1,device.getDeviceCompany());
            ps.setString(2,device.getDeviceType());
            ps.setString(3,device.getDeviceVersion());
            ps.setString(4,device.getDeviceNumber());
            ps.setString(5,device.getDeviceManager());
            ps.executeUpdate();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }finally {
            try {
                if (ps != null) {
                    ps.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
}
