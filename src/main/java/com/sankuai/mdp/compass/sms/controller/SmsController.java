package com.sankuai.mdp.compass.sms.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.sms.biz.SMSQueryMessageService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.sms.entity.SmsInfo;
import com.sankuai.mdp.compass.sms.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.dianping.sms.dto.SMSQueryMessageDTO;
import org.mortbay.log.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.caucho.hessian.io.HessianInputFactory.log;

@RestController
@RequestMapping("/compass/api/sms")
public class SmsController extends BaseController {

    @Autowired
    private SMSQueryMessageService smsQueryMessageService;

    @Autowired
    private SmsService smsService;

    @GetMapping("/getSms")
    public Resp getSms(@RequestParam String mobileNo,
                       @RequestParam String beginDate,
                       @RequestParam String endDate) throws ParseException {
        Log.info(beginDate.toString());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8")); // 设置时区，根据需要调整

        Date beginDateTime = sdf.parse(beginDate);
        Date endDateTime = sdf.parse(endDate);

        long beginTimestamp = beginDateTime.getTime();
        long endTimestamp = endDateTime.getTime();

        List<SMSQueryMessageDTO> messages = smsQueryMessageService.queryMessage(mobileNo, new Date(beginTimestamp), new Date(endTimestamp));
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(messages.get(0).getMessage());

        if (matcher.find()) {
            System.out.println("匹配到的数字是: " + matcher.group());
            return Resp.success(matcher.group());
        } else {
            return Resp.error();
        }

    }

    @PostMapping("/setSms")
    public Resp setSms(SmsInfo smsInfo)  {
        return smsService.setSms(smsInfo);
    }

    @PostMapping("/updateSms")
    public Resp updateSms(@RequestBody SmsInfo smsInfo)  {
        return smsService.updateSms(smsInfo);
    }

    @GetMapping("/getSmsOnline")
    public Resp getSmsOnline(SmsInfo smsInfo)  {
        return smsService.getSmsOnline(smsInfo);
    }
}
