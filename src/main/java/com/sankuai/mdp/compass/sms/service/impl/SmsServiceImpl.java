package com.sankuai.mdp.compass.sms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.sms.entity.SmsInfo;
import com.sankuai.mdp.compass.sms.mapper.SmsInfoMapper;
import com.sankuai.mdp.compass.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SmsServiceImpl extends ServiceImpl<SmsInfoMapper, SmsInfo> implements SmsService {

    @Autowired
    SmsInfoMapper smsInfoMapper;

    @Override
    public Resp setSms(SmsInfo smsInfo) {
        log.info(String.valueOf(smsInfo));
        int result = smsInfoMapper.insert(smsInfo);
        // 根据插入结果返回相应的响应
        if (result > 0) {
            return Resp.success("数据插入成功"); // 假设Resp.success()是成功时的返回方法
        } else {
            return Resp.error(); //
        }
    }

    @Override
    public Resp updateSms(SmsInfo smsInfo) {
        UpdateWrapper<SmsInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("phone_number", smsInfo.getPhoneNumber());
        SmsInfo smsInfo1 = smsInfoMapper.selectOne(updateWrapper);

        if (smsInfo1 != null){
            smsInfo1.setSmsCode(smsInfo.getSmsCode());
            smsInfo1.setSmsSendTime(smsInfo.getSmsSendTime());

            smsInfoMapper.updateById(smsInfo1);
            return Resp.success("短信信息更新成功");
        }
        else return Resp.error();
    }

    @Override
    public Resp getSmsOnline(SmsInfo smsInfo) {
        QueryWrapper<SmsInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("phone_number", smsInfo.getPhoneNumber());
        // 执行查询操作
        SmsInfo result = smsInfoMapper.selectOne(queryWrapper);

        // 检查查询结果
        if (result != null) {
            // 如果找到了匹配的记录，返回成功响应和smsCode
            return Resp.success(result.getSmsCode());
        } else {
            // 如果没有找到匹配的记录，返回失败响应
            return Resp.error();
        }
    }
}
