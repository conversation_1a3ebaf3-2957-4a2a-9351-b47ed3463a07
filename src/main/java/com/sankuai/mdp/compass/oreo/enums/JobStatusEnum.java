package com.sankuai.mdp.compass.oreo.enums;
import lombok.Getter;
@Getter
public enum JobStatusEnum {
    /*
    *
        * 0等待，
        * 1执行中，
        * 2执行成功，
        * 3执行失败，
        * 4超时
    *
    * */
    WAIT(0,"等待"),
    PROCESS(1, "执行中"),
    SUCCESS(2, "成功"),
    FAIL(3,"失败"),
    TIMEOUT(4, "超时");
    int code;
    String text;

    JobStatusEnum(){
    }

    JobStatusEnum(int code, String text){
        this.code = code;
        this.text = text;
    }
    public static String getTextByCode(int code) {
        for (JobStatusEnum status : JobStatusEnum.values()) {
            if (status.getCode() == code) {
                return status.getText();
            }
        }
        return "执行中";
    }

}
