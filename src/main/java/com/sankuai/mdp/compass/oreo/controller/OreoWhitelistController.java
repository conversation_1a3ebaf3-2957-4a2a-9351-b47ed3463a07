package com.sankuai.mdp.compass.oreo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoWhitelist;
import com.sankuai.mdp.compass.oreo.service.OreoWhitelistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/compass/api/oreoWhitelist")
public class OreoWhitelistController extends BaseController {
    @Autowired
    OreoWhitelistService oreoWhitelistService;

    @PostMapping("/add")
    public Resp add(@RequestBody OreoWhitelist oreoWhitelist) {
        return oreoWhitelistService.add(oreoWhitelist);
    }

    @PostMapping("/delete")
    public Resp delete(@RequestBody OreoWhitelist oreoWhitelist) {
        return oreoWhitelistService.delete(oreoWhitelist);
    }

    @PostMapping("/update")
    public Resp update(@RequestBody OreoWhitelist oreoWhitelist) {
        return oreoWhitelistService.updateWhitelist(oreoWhitelist);
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, OreoWhitelist oreoWhitelist) {
        IPage<OreoWhitelist> oreoWhitelistIPage = this.oreoWhitelistService.list(request, oreoWhitelist);
        if (oreoWhitelistIPage != null) {
            return getDataTable(oreoWhitelistIPage);
        } else {
            return null;
        }
    }

    // 通过策略key获取记录接口
    @GetMapping("/getAllStrategyKey")
    public List<String> getAllStrategyKey(){
        return oreoWhitelistService.getAllStrategyKey();
    }

    // 通过策略描述获取记录接口
    @GetMapping("/getAllStrategyName")
    public List<String> getAllStrategyName(){
        return oreoWhitelistService.getAllStrategyName();
    }

    // 通过account获取记录接口
    @GetMapping("/getAllAccount")
    public List<String> getAllAccount(){
        return oreoWhitelistService.getAllAccount();
    }

    @GetMapping("/listAll")
    public List<OreoWhitelist> listAll(@RequestBody OreoWhitelist oreoWhitelist) {
        return oreoWhitelistService.getByOreoWhitelist(oreoWhitelist);
    }


}

