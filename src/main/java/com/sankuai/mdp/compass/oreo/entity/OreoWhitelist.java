package com.sankuai.mdp.compass.oreo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


@Data
@TableName("autotest_oreo_whitelist")
public class OreoWhitelist implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String strategyKey;
    private String strategyName;
    private String account;
    private String password;
    private Boolean smallCity;
    private String createBy;
    private String createTime;
}
