package com.sankuai.mdp.compass.oreo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("autotest_oreo_detail")
public class OreoDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private int buildId;
    private long oreoId;
    private int conanJobId;
    private String strategyId;
    private int conanTaskId;
    private int conanCaseId;
    private int caseResult;
    private int crashResult;
    private String strategyName;
    private String message;
    private String caseName;
    private String picName;
    private String platform;
    private String appVersion;
    private String picUrl;

    private String crashInfo;
    private String crashHash;
    private String crashUuid;
    private String crashVersion;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;
    private float totalTime;
    private String appName;
}
