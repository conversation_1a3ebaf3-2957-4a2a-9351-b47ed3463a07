package com.sankuai.mdp.compass.oreo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoWhitelist;
import com.sankuai.mdp.compass.oreo.mapper.OreoWhitelistMapper;
import com.sankuai.mdp.compass.oreo.service.OreoWhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OreoWhitelistServiceImpl extends ServiceImpl<OreoWhitelistMapper, OreoWhitelist> implements OreoWhitelistService {

    @Autowired
    OreoWhitelistMapper oreoWhitelistMapper;
    @Override
    public Resp add(OreoWhitelist oreoWhitelist) {
        Resp resp = new Resp();
        try {
            oreoWhitelistMapper.insert(oreoWhitelist);
            resp.setCode(200);
            resp.setMsg("success");
        } catch (Exception e) {
            resp.setCode(500);
            resp.setMsg("error");
        }
        return resp;
    }

    @Override
    public Resp delete(OreoWhitelist oreoWhitelist) {
        Resp resp = new Resp();
        try {
            QueryWrapper<OreoWhitelist> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", oreoWhitelist.getId());
            oreoWhitelistMapper.delete(queryWrapper);
            resp.setCode(200);
            resp.setMsg("success");
        } catch (Exception e) {
            resp.setCode(500);
            resp.setMsg("error");
        }
        return resp;
    }

    @Override
    public Resp updateWhitelist(OreoWhitelist oreoWhitelist) {
    Resp resp = new Resp();
    try {
        oreoWhitelistMapper.updateById(oreoWhitelist);
        resp.setCode(200);
        resp.setMsg("success");
    } catch (Exception e) {
        resp.setCode(500);
        resp.setMsg("error");
    }
    return resp;
}

@Override
public IPage<OreoWhitelist> list(QueryRequest request, OreoWhitelist oreoWhitelist) {
    try {
        LambdaQueryWrapper<OreoWhitelist> queryWrapper = new LambdaQueryWrapper<>();

        if (null != oreoWhitelist.getStrategyKey()) {
            queryWrapper.like(OreoWhitelist::getStrategyKey, oreoWhitelist.getStrategyKey());
        }
        if (null != oreoWhitelist.getStrategyName()) {
            queryWrapper.eq(OreoWhitelist::getStrategyName, oreoWhitelist.getStrategyName());
        }
        if (null != oreoWhitelist.getAccount()) {
            queryWrapper.eq(OreoWhitelist::getAccount, oreoWhitelist.getAccount());
        }
        // 添加其他字段的查询条件...

        Page<OreoWhitelist> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);

    } catch (Exception e) {
        log.error("获取列表失败", e);
        return null;
    }
}


@Override
public List<OreoWhitelist> getByStrategyKey(String strategyKey) {
    QueryWrapper<OreoWhitelist> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("strategy_key", strategyKey);
    return oreoWhitelistMapper.selectList(queryWrapper);
}

@Override
public List<OreoWhitelist> getByStrategyDescription(String strategyDescription) {
    QueryWrapper<OreoWhitelist> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("strategy_description", strategyDescription);
    return oreoWhitelistMapper.selectList(queryWrapper);
}

@Override
public List<OreoWhitelist> getByAccount(String account) {
    QueryWrapper<OreoWhitelist> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("account", account);
    return oreoWhitelistMapper.selectList(queryWrapper);
}
    @Override
    public List<String> getAllAccount() {
    QueryWrapper<OreoWhitelist> queryWrapper = new QueryWrapper<>();
    List<String> list = new ArrayList<>();
    List<OreoWhitelist> whitelistList = oreoWhitelistMapper.selectList(queryWrapper.groupBy("account"));
    for (OreoWhitelist whitelist : whitelistList) {
        list.add(whitelist.getAccount());
    }
    return list;
    }
    @Override
    public List<String> getAllStrategyKey() {
        QueryWrapper <OreoWhitelist> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<>();
        List<OreoWhitelist> whitelistList = oreoWhitelistMapper.selectList(queryWrapper.groupBy("strategy_key"));
        for (OreoWhitelist whitelist : whitelistList) {
            list.add(whitelist.getStrategyKey());
        }
        return list;
    }


    @Override
    public List<String> getAllStrategyName() {
        QueryWrapper <OreoWhitelist> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<>();
        List<OreoWhitelist> whitelistList = oreoWhitelistMapper.selectList(queryWrapper.groupBy("strategy_name"));
        for (OreoWhitelist whitelist : whitelistList) {
            list.add(whitelist.getStrategyName());
        }
        return list;
    }

    @Override
    public List<OreoWhitelist> getByOreoWhitelist(OreoWhitelist oreoWhitelist) {
        LambdaQueryWrapper<OreoWhitelist> queryWrapper = new LambdaQueryWrapper<>();

        if (null != oreoWhitelist.getId()) {
            queryWrapper.eq(OreoWhitelist::getId, oreoWhitelist.getId());
        }
        if (null != oreoWhitelist.getStrategyKey()) {
            queryWrapper.like(OreoWhitelist::getStrategyKey, oreoWhitelist.getStrategyKey());
        }
        if (null != oreoWhitelist.getStrategyName()) {
            queryWrapper.eq(OreoWhitelist::getStrategyName, oreoWhitelist.getStrategyName());
        }
        if (null != oreoWhitelist.getAccount()) {
            queryWrapper.eq(OreoWhitelist::getAccount, oreoWhitelist.getAccount());
        }
        if (null != oreoWhitelist.getPassword()) {
            queryWrapper.eq(OreoWhitelist::getPassword, oreoWhitelist.getPassword());
        }
        if (null != oreoWhitelist.getSmallCity()) {
            queryWrapper.eq(OreoWhitelist::getSmallCity, oreoWhitelist.getSmallCity());
        }
        if (null != oreoWhitelist.getCreateBy()) {
            queryWrapper.eq(OreoWhitelist::getCreateBy, oreoWhitelist.getCreateBy());
        }
        if (null != oreoWhitelist.getCreateTime()) {
            queryWrapper.eq(OreoWhitelist::getCreateTime, oreoWhitelist.getCreateTime());
        }

        return oreoWhitelistMapper.selectList(queryWrapper);
    }


}
