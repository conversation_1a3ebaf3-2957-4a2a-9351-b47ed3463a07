package com.sankuai.mdp.compass.oreo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("autotest_oreo_job")
public class OreoJob implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer buildId;

    private String buildUrl;

    private Long oreoId;

    private String oreoUuid;

    private String oreoDesc;

    private String platform;

    private String apkUrl;

    private String conanTask;

    private Integer jobStatus;

    private String reportId;
    private String reportUrl;
    private String finishedId;
    private String finishedUrl;
    private String creator;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

}
