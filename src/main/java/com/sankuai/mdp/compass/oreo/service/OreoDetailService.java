package com.sankuai.mdp.compass.oreo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonArray;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoDetail;

import java.util.List;

public interface OreoDetailService extends IService<OreoDetail> {

    IPage<OreoDetail> list(QueryRequest request, OreoDetail oreoDetail);

    Resp updateBatch(List<OreoDetail> oreoDetailList);
    Resp updateOne(OreoDetail oreoDetail);

    Integer add(OreoDetail oreoDetail);
}
