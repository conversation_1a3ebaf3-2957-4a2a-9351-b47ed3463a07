package com.sankuai.mdp.compass.oreo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.oreo.entity.OreoWhitelist;
import net.minidev.json.JSONObject;

import java.util.List;

public interface OreoWhitelistService extends IService<OreoWhitelist> {


    Resp add(OreoWhitelist oreoWhitelist);

    Resp delete(OreoWhitelist oreoWhitelist);

    Resp updateWhitelist(OreoWhitelist oreoWhitelist);

    IPage<OreoWhitelist> list(QueryRequest request, OreoWhitelist oreoWhitelist);

    List<OreoWhitelist> getByStrategyKey(String strategyKey);

    List<OreoWhitelist> getByStrategyDescription(String strategyDescription);

    List<OreoWhitelist> getByAccount(String account);

    List<String> getAllAccount();

    List<String> getAllStrategyKey();

    List<String> getAllStrategyName();

    List<OreoWhitelist> getByOreoWhitelist(OreoWhitelist oreoWhitelist);
}
