package com.sankuai.mdp.compass.oreo.enums;

import lombok.Getter;


@Getter
public enum CaseResultEnum {
    /*
    *
        -1 未执行
        0 成功
        1失败
        2 崩溃
        3 待确认
        4 人工判断成功(3->4)
        5 人工判断失败(3->5)
        * 100：UI自动化判断结果失败
        * 200：UI自动化判断结果成功
    *
    * */
    SUCCESS(0,"成功"),
    FAIL(1, "失败"),
    CRASH(2, "崩溃"),
    UNCONFIRMED(3,"待确认"),
    CHECK_SUCCESS(4,"人工判断成功"),
    CHECK_FAIL(5, "人工判断失败"),
    UNEXECUTED(-1, "未执行"),
    NOACCOUNT(10,"录制数据失败，用例未执行");



    int code;
    String text;

    CaseResultEnum(){
    }

    CaseResultEnum(int code, String text){
        this.code = code;
        this.text = text;
    }

}
