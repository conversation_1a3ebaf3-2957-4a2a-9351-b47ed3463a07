package com.sankuai.mdp.compass.oreo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.*;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.oreo.entity.OreoWhitelist;
import com.sankuai.mdp.compass.oreo.service.OreoJobService;
import com.sankuai.mdp.compass.oreo.service.OreoWhitelistService;
import com.sankuai.mdp.compass.oreo.utils.Common;
import net.minidev.json.JSONObject;
import org.mortbay.log.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/compass/api/oreoJob")
public class OreoJobController extends BaseController {
    @Autowired
    OreoJobService oreoJobService;
    @Autowired
    OreoWhitelistService oreoWhitelistService;

    Common common = new Common();

    @PostMapping("/result")
    public Resp result(@RequestBody JSONObject conanBody) throws Exception {
        return oreoJobService.result(conanBody);
    }

    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, OreoJob oreoJob)  {
        try {
            IPage<OreoJob> OreoJobIPage = this.oreoJobService.list(request, oreoJob);
            if (OreoJobIPage != null) {
                return getDataTable(OreoJobIPage);
            } else {
                return null;
            }}
        catch (Exception e) {
            return null;
        }
    }

    @PostMapping("/add")
    public Integer create(OreoJob oreoJob) {
        return oreoJobService.add(oreoJob);
    }

    @PostMapping("/updateStatusSuccess")
    public Resp updateSuccess(OreoJob oreoJob) {
        return oreoJobService.updateSuccess(oreoJob);
    }

    @PostMapping("/updateStatusProcess")
    public Resp updateProcess(OreoJob oreoJob) {
        return oreoJobService.updateProcess(oreoJob);
    }

    @PostMapping("/updateReportId")
    public Resp updateReportId(OreoJob oreoJob) {
        return oreoJobService.updateReportId(oreoJob);
    }

    @PostMapping("/newTest")
    public Resp newBuild(@RequestBody String body) throws Exception {
        Gson gson = new Gson();
        JsonObject responseBody = new JsonParser().parse(body).getAsJsonObject();
        Map<String, JsonElement> elementsToModify = new HashMap<>();

        for (Map.Entry<String, JsonElement> entry : responseBody.entrySet()) {
            if (entry.getValue().isJsonArray()) {
                JsonArray jsonArray = entry.getValue().getAsJsonArray();
                // 如果数组为空，则跳过处理
                if (jsonArray.size() == 0) {
                    continue;
                }
                List<String> elements = new ArrayList<>();
                for (JsonElement element : jsonArray) {
                    JsonObject jsonObject = element.getAsJsonObject();
                    String strategyString = common.buildStrategyString(jsonObject);
                    elements.add(strategyString);
                }
                String joinedString = String.join(",", elements);
                elementsToModify.put("strategyListForString", new JsonPrimitive(joinedString));
            }
        }
        responseBody.addProperty("strategyListForString",elementsToModify.get("strategyListForString").getAsString());
        OreoJob oreoJob = gson.fromJson(responseBody, OreoJob.class);
        return oreoJobService.newBuild(oreoJob, responseBody.get("strategyListForString").getAsString());
    }
}
