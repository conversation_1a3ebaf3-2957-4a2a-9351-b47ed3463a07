package com.sankuai.mdp.compass.oreo.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoDetail;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.oreo.enums.CaseResultEnum;
import com.sankuai.mdp.compass.oreo.mapper.OreoDetailMapper;
import com.sankuai.mdp.compass.oreo.service.OreoDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OreoDetailServiceImpl extends ServiceImpl<OreoDetailMapper, OreoDetail> implements OreoDetailService {

    @Autowired
    OreoDetailMapper oreoDetailMapper;

    @Override
    public IPage<OreoDetail> list(QueryRequest request, OreoDetail oreoDetail) {
        // 创建一个Page对象，用来存储分页信息
        Page<OreoDetail> page = new Page<>(request.getPageNum(), request.getPageSize());

        // 获取oreoId和platform
        Long oreoId = oreoDetail.getOreoId();
        String platform = oreoDetail.getPlatform();

        // 创建查询条件
        LambdaQueryWrapper<OreoDetail> queryWrapper = new LambdaQueryWrapper<>();

        // 如果oreoId不为空，则添加oreoId的查询条件
        queryWrapper.eq(OreoDetail::getOreoId, oreoId);

        // 如果platform不为空，则添加platform的查询条件
        if (platform != null) {
        queryWrapper.eq(OreoDetail::getPlatform, platform);
    }

    // 调用mapper的查询方法，传入查询条件
    return this.page(page, queryWrapper);
}


    @Override
    public Resp updateBatch(List<OreoDetail> oreoDetailList) {
        int result = 0;
        for (OreoDetail oreoDetail : oreoDetailList) {
            result += oreoDetailMapper.updateById(oreoDetail);
        }
        if (result > 0) {
            return Resp.success();
        } else {
            return Resp.error();
        }
    }

    @Override
    public Integer add(OreoDetail oreoDetail) {
        Date date = new Date();
        oreoDetail.setStartTime(date);
        oreoDetailMapper.insert(oreoDetail);
        return oreoDetail.getId();
    }

    @Override
    public Resp updateOne(OreoDetail oreoDetail) {
        int result = oreoDetailMapper.updateById(oreoDetail);
        if (result > 0) {
            return Resp.success();
        } else {
            return Resp.error();
    }
}

}
