package com.sankuai.mdp.compass.oreo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.enums.ConanCaseResultEnums;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.oreo.entity.OreoDetail;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.oreo.enums.JobStatusEnum;
import com.sankuai.mdp.compass.oreo.enums.CaseResultEnum;
import com.sankuai.mdp.compass.oreo.mapper.OreoDetailMapper;
import com.sankuai.mdp.compass.oreo.mapper.OreoJobMapper;
import com.sankuai.mdp.compass.oreo.service.OreoDetailService;
import com.sankuai.mdp.compass.oreo.service.OreoJobService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class OreoJobServiceImpl extends ServiceImpl<OreoJobMapper, OreoJob> implements OreoJobService {
    @Autowired
    OreoDetailService oreoDetailService;

    @Autowired
    ConanJobService  conanJobService;

    @Autowired
    OreoJobMapper oreoJobMapper;

    @Autowired
    OreoDetailMapper oreoDetailMapper;


    ComConstant comConstant = new ComConstant();

    ConanUtil conanUtil = new ConanUtil();

    JenkinsUtil jenkinsUtil = new JenkinsUtil();

    private String fileDir = "hyperJump";

    FileUtil fileUtil = new FileUtil();

    VenusUtil venusUtil = new VenusUtil();

    DxUtil dxUtil = new DxUtil();

    /**
     * <AUTHOR>
     * @description  该方法用于处理云测回调结果
     * @Date 2024-01-17
     * */

    @Override
    public Resp result(JSONObject conanBody) {
        conanJobService.collectResult(conanBody);
        Resp resp = new Resp();
        try {
            log.info(conanBody.toString());
            JsonParser jsonParser = new JsonParser();

            // 解析云测回调参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject timeData = jsonParser.parse(conanBody.get("timedata").toString()).getAsJsonObject();
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            Date startTime = sdf.parse(timeData.get("startTime").getAsString());
            String conanId = jobData.get("id").getAsString();
            log.info("云测id：" + conanId);

            // 根据云测id找到对应的测试job
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.like("report_id", conanId);
            OreoJob oreoJob = oreoJobMapper.selectOne(queryWrapper);
            if (oreoJob == null) {
                log.info("未找到对应的测试job");
                resp.setMsg("未找到对应的测试job");
                resp.setCode(-1);
                return resp;
            }
            log.info("找到对应的测试job");
            Integer id = oreoJob.getId();
            Long oreoId = oreoJob.getOreoId();

            // 文件下载完成后存放的目录
            String dirPath = ComConstant.OUT_PUT + fileDir + id;

            try {
                JsonObject conanJobInfo = conanUtil.getJobInfo(conanId);
                if (conanJobInfo != null) {
                    JsonArray taskArray = conanJobInfo.getAsJsonArray("taskList");
                    String appVersion = conanJobInfo.get("appVersion").getAsString();
                    String platform = conanJobInfo.get("platform").getAsString();
                    for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
                        JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
                        String taskId = task.get("id").getAsString();
                        QueryWrapper<OreoDetail> detailQueryWrapper = new QueryWrapper<OreoDetail>().eq("conan_task_id",
                                taskId);
                        int detailUpdateFlag = oreoDetailMapper.selectCount(detailQueryWrapper);
                        if (detailUpdateFlag > 0) {
                            log.info("id为 " + taskId + " 的任务已校验下载入库完成过了，跳过");
                            continue;
                        }
                        /**
                         * 1.获取文件下载链接
                         */
                        String deviceModel = task.get("deviceModel").getAsString();
                        String deviceVersion = task.get("deviceVersion").getAsString();
                        String deviceResolution = task.get("resolution").getAsString();
                        float totalTime = (float)task.get("totalTime").getAsDouble();
                        String downloadURL = conanUtil.getDownloadURL(taskId);

                        if (null != downloadURL) {
                            downloadURL = downloadURL.replace("s3-img", "s3");
                            /**
                             * 2.下载解压文件
                             */
                            FileUtil.downloadAndReadFile(downloadURL, dirPath, taskId);

                            /**
                             * 4.获取图片文件路径
                             * 将本地图片上传到服务器
                             * 将图片链接存入数据库
                             */
                            ArrayList<String> fileList = new ArrayList<>();
                            String picPath = dirPath + "/" + taskId + "/autoScreenshot/";
                            FileUtil.getFileList(picPath, "", fileList);
                            List<String> txtNames = new ArrayList<>();
                            for (String fileName : fileList) {
                                if (!fileName.contains(".txt")) {
                                    continue;
                                }
                                String caseName = fileName.split(".txt")[0];
                                txtNames.add(caseName);
                                List<List<Object>> casePics = FileUtil.readFileByLines(picPath + fileName, "\\|");
                                for (int i = 0; i < casePics.size(); i++) {
                                    List<Object> casePic = casePics.get(i);
                                    if (casePic.size() < 3) {
                                        log.error("数据格式错误，跳过此行数据: " + casePic);
                                        continue;
                                    }
                                    String strategyId = casePic.get(0).toString();
                                    String picName = casePic.get(1).toString();
                                    String picUrl = casePic.get(2).toString();
                                    // String message = casePic.get(3).toString();
                                    // 通过oreoId，caseName和strategyId查询已有记录
                                    QueryWrapper<OreoDetail> detailQueryWrapper1 = new QueryWrapper<OreoDetail>()
                                            .eq("oreo_id", oreoId).eq("case_name", caseName)
                                            .eq("strategy_id", strategyId).eq("platform",platform);
                                    List<OreoDetail> oreoDetails = oreoDetailMapper.selectList(detailQueryWrapper1);
                                    OreoDetail oreoDetail;
                                    if (!oreoDetails.isEmpty()) {
                                        oreoDetail = oreoDetails.get(0);
                                    } else {
                                        // 如果查询结果为空，创建新的OreoDetail对象，准备插入数据库
                                        oreoDetail = new OreoDetail();
                                        oreoDetail.setOreoId(oreoId);
                                        oreoDetail.setCaseName(caseName);
                                        oreoDetail.setStrategyId(strategyId);
                                        oreoDetail.setConanJobId(Integer.parseInt(conanId));
                                        oreoDetail.setCaseName(caseName);
                                        //oreoDetail.setStrategyName(strategyName);
                                        // 插入新的记录
                                        oreoDetailMapper.insert(oreoDetail);
                                    }
                                    if (i < oreoDetails.size()) {
                                        oreoDetail = oreoDetails.get(i);
                                    } else {
                                        // 如果图片数量超过查询得到的记录数量，复制最后一条记录
                                        OreoDetail lastOreoDetail = oreoDetails.get(oreoDetails.size() - 1);
                                        oreoDetail = new OreoDetail();
                                        // 复制需要的属性
                                        oreoDetail.setOreoId(lastOreoDetail.getOreoId());
                                        oreoDetail.setCaseName(lastOreoDetail.getCaseName());
                                        oreoDetail.setStrategyId(lastOreoDetail.getStrategyId());
                                    }
                                    Date endTime = sdf.parse(timeData.get("endTime").getAsString());
                                    oreoDetail.setPicName(picName);
                                    oreoDetail.setPicUrl(picUrl);
                                    oreoDetail.setFinishTime(endTime);
                                    oreoDetail.setTotalTime(totalTime);
                                    // oreoDetail.setMessage(message);
                                    if (isCaseNameExtFail(caseName, conanId)) {
                                        log.info("case执行失败，进入兜底的task判断");
                                        oreoDetail.setMessage("云测自动化执行失败");
                                        oreoDetail.setCaseResult(CaseResultEnum.FAIL.getCode());
                                    } else {
                                        oreoDetail.setCaseResult(CaseResultEnum.SUCCESS.getCode());
                                    }
                                    if (i < oreoDetails.size()) {
                                        oreoDetailMapper.updateById(oreoDetail);
                                    } else {
                                        oreoDetailMapper.insert(oreoDetail);
                                    }
                                }
                            }

                            // 处理完所有图片后，再次查询数据库中的所有OreoDetail记录
                            List<OreoDetail> allOreoDetails = oreoDetailMapper
                                    .selectList(new QueryWrapper<OreoDetail>().eq("conan_job_id", conanId));
                            // 遍历查询结果，如果数据库中的caseName在txt文件名集合中不存在，将对应记录的caseResult设置为失败
                            for (OreoDetail oreoDetail : allOreoDetails) {
                                if (!txtNames.contains(oreoDetail.getCaseName())) {
                                    oreoDetail.setCaseResult(CaseResultEnum.FAIL.getCode());
                                    oreoDetailMapper.updateById(oreoDetail);
                                }
                            }

                        }
                    }
                }
            } catch (Exception e) {
                log.error("get output err", e);
            } finally {
                // 使用 jobId 作为查询条件，从数据库中查询出对应的 OreoJob 对象
                QueryWrapper<OreoJob> queryWrapper1 = new QueryWrapper<>();
                queryWrapper1.like("report_id", conanId);
                OreoJob oreoJob1 = oreoJobMapper.selectOne(queryWrapper1);

                boolean flag = false; // 标记任务是否完成

                String finishedId = oreoJob.getFinishedId();
                if (finishedId == null || !finishedId.contains(conanId)) {
                    finishedId = finishedId == null ? conanId : finishedId + "_" + conanId;
                    oreoJob.setFinishedId(finishedId);
                }
                oreoJob.setJobStatus(JobStatusEnum.PROCESS.getCode());
                Date endTime = sdf.parse(timeData.get("endTime").getAsString());

                // 检查 OreoJob 对象的 reportId 和 finishedId 字段，如果这两个字段的值相等，那么表示任务已经完成
                if (oreoJob.getReportId().length() == oreoJob.getFinishedId().length()) {
                    // 任务已完成，更新状态
                    if (conanUtil.getPassrate(finishedId).get("pass").getAsFloat() == 1){
                        oreoJob.setJobStatus(JobStatusEnum.SUCCESS.getCode());
                    }
                    else {
                        oreoJob.setJobStatus(JobStatusEnum.FAIL.getCode());
                    }
                    oreoJob.setStartTime(startTime);
                    oreoJob.setFinishTime(endTime);
                    flag = true; // 标记任务已完成
                }
                // 更新 OreoJob 对象
                oreoJobMapper.update(oreoJob, queryWrapper1);

                if (flag) {
                    JsonArray userList = new JsonArray();
                    userList.add("lizhen39");
                    String repUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/oreoDetail?oreoId=";
                    String mcmUrl = "https://mcm.mws.sankuai.com/#/process-center/detail/";
                    if (EnvUtil.isOnline()) {
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("※ Oreo策略变更自动化测试完成 ※：\n" + "【Oreo策略变更】："
                                    + oreoJob.getOreoDesc() + "\n" + "【开始时间】：" + startTime + "\n" + "【完成时间】："
                                    + sdf.format(new Date()) + "\n" + "【执行状态】：✅ 已完成\n" + "【测试结果】："
                                    + JobStatusEnum.getTextByCode(oreoJob.getJobStatus()) + "\n" + "【查看报告】：[查看报告|" + repUrl + oreoJob.getOreoId()
                                    + "]\n" + "【审批链接】：[MCM审批｜" + mcmUrl + oreoJob.getOreoId() + "/info" + "如有问题请联系lizhen39", user);
                        }
                        dxUtil.sendToGroupByCompass("※ Oreo策略变更自动化测试完成 ※：\n" + "【Oreo策略变更】：" + oreoJob.getOreoDesc()
                                + "\n" + "【开始时间】：" + startTime + "\n" + "【完成时间】：" + sdf.format(new Date()) + "\n"
                                + "【执行状态】：✅ 已完成\n" + "【测试结果】：" + JobStatusEnum.getTextByCode(oreoJob.getJobStatus()) + "\n" + "【查看报告】：[查看报告|"
                                + repUrl + oreoJob.getOreoId() + "]\n" + "【审批链接】：[MCM审批｜" + mcmUrl + oreoJob.getOreoId() +"/info"
                                + "如有问题请联系lizhen39",67372750596L);
                    }
                }
                FileUtil.delFolder(ComConstant.OUT_PUT);
            }
        } catch (Exception e) {
            log.info(e.toString());
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
        return Resp.success();
    }

    @Override
    public IPage<OreoJob> list(QueryRequest request, OreoJob oreoJob) {
        try {
            Page<OreoJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            LambdaQueryWrapper<OreoJob> queryWrapper = new LambdaQueryWrapper<>();
            if (oreoJob != null) {
                if (oreoJob.getOreoDesc() != null) {
                    queryWrapper.like(OreoJob::getOreoDesc, oreoJob.getOreoDesc());
                }
                if (oreoJob.getStartTime() != null) {
                    queryWrapper.ge(OreoJob::getStartTime, oreoJob.getStartTime());
                }

                if (oreoJob.getPlatform() != null) {
                    queryWrapper.eq(OreoJob::getPlatform, oreoJob.getPlatform());
                }
            }
            queryWrapper.orderByDesc(OreoJob::getId);
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("查询失败", e);
            return null;
        }
    }

    boolean isCaseNameExtFail(String caseName, String jobId) {
        JsonArray caseJsonInfo = conanUtil.getAllCaseInfo(jobId);
        if (caseJsonInfo == null) {
            log.error("getAllCaseInfo返回null");
            return true;
        }
        for (JsonElement caseItem : caseJsonInfo) {
            JsonObject caseJson = caseItem.getAsJsonObject();
            if (caseJson == null) {
                log.error("getAsJsonObject返回null");
                continue;
            }
            if (caseJson.get("methodName").getAsString().equals(caseName)) {
                if (caseJson.get("status").getAsInt() == ConanCaseResultEnums.PASS.getCode()) {
                    log.info("task中包含指定用例:"+caseName+caseJson.get("taskId").getAsString());
                    return false;
                }
            }
        }
        log.info("task中不包含指定用例:"+caseName+ jobId);
        return true;
    }

    @Override
    public Resp newBuild(OreoJob oreoJob, String StrategyMessage) throws Exception {
        Resp resp = new Resp();
        String creator = oreoJob.getCreator();
        Long oreoid = oreoJob.getOreoId();
        // EnvUtil.isOnline()
        if (EnvUtil.isOnline()) {
            jenkinsUtil.newUfoJob(oreoJob,StrategyMessage);
            JsonArray users = new JsonArray();
            if (creator != null) {
                users.add(creator);
            }
            users.add("zhangyuchi02");
            users.add("fengenci");
            users.add("lizhen39");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                for (JsonElement user : users) {
                    String u = user.getAsString();
                    dxUtil.sendToPersionByCompass("※ Oreo策略变更自动化测试开始 ※：\n" +
                                    "【Oreo策略变更】: " + oreoid + "\n" +
                                    "【开始时间】：" + sdf.format(new Date()) + "\n"+
                                    "【执行状态】：✅ 任务开始 \n" +
                                    "如有问题请联系lizhen39", u);
                }
                dxUtil.sendToGroupByCompass("※ Oreo策略变更自动化测试开始 ※：\n" +
                        "【Oreo策略变更】："  + oreoid + "\n" +
                        "【开始时间】：" + sdf.format(new Date()) + "\n" +
                        "【执行状态】：✅ 任务开始\n" +
                        "如有问题请联系lizhen39",67756764307L);
            } catch (Exception e) {
                log.error("发送消息失败：" + e.getMessage());
            }
            resp.setMsg("success");
            resp.setCode(1);
        } else {
            resp.setCode(0);
            resp.setMsg("非线上环境");
        }
        resp.setData(oreoJob);
        return resp;
    }

    @Override
    public Integer add(OreoJob oreoJob) {
        Date date = new Date();
        oreoJob.setStartTime(date);
        oreoJob.setJobStatus((JobStatusEnum.WAIT.getCode()));
        oreoJobMapper.insert(oreoJob);
        return oreoJob.getId();
    }

    public Resp updateProcess(OreoJob oreoJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", oreoJob.getId());
        OreoJob oreoJob1 = oreoJobMapper.selectOne(queryWrapper);
        if (null != oreoJob1) {
            oreoJob1.setJobStatus(JobStatusEnum.PROCESS.getCode());
            oreoJobMapper.updateById(oreoJob1);
        }
        else {
            oreoJob1.setJobStatus(JobStatusEnum.FAIL.getCode());
            oreoJobMapper.updateById(oreoJob1);
        }
        return Resp.success();
    }

    public Resp updateSuccess(OreoJob oreoJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", oreoJob.getId());
        OreoJob oreoJob1 = oreoJobMapper.selectOne(queryWrapper);
        if (null != oreoJob1) {
            oreoJob1.setJobStatus(JobStatusEnum.SUCCESS.getCode());
            oreoJobMapper.updateById(oreoJob1);
        }
        else {
            oreoJob1.setJobStatus(JobStatusEnum.FAIL.getCode());
            oreoJobMapper.updateById(oreoJob1);
        }
        return Resp.success();
    }


    public Resp updateReportId(OreoJob oreoJob) {
        Integer id = oreoJob.getId();
        OreoJob oreoJob1 = oreoJobMapper.selectById(id);
        String oldReportId = oreoJob1.getReportId();
        String oldReportUrl= oreoJob1.getReportUrl();
        String newReportId = oreoJob.getReportId();
        if (oldReportId != null && !oldReportId.isEmpty()) {
            oldReportUrl = oldReportUrl+ "_" + newReportId;
            newReportId = oldReportId + "_" + newReportId;
        }else {
            oldReportUrl = oldReportUrl + newReportId;
        }
        // 如果新的reportId以"_"结尾，去掉末尾的"_"
        if (newReportId.endsWith("_")) {
            newReportId = newReportId.substring(0, newReportId.length() - 1);
            oldReportUrl = oldReportUrl.substring(0, oldReportUrl.length() - 1);
        }
        oreoJob1.setReportId(newReportId);
        oreoJob1.setReportUrl(oldReportUrl);
        int result = oreoJobMapper.updateById(oreoJob1);
        if (result <= 0) {
            log.error("更新OreoJob失败");
            throw new RuntimeException("更新OreoJob失败");
        }
        return Resp.success();
    }
}
