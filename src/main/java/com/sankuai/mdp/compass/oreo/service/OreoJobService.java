package com.sankuai.mdp.compass.oreo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonElement;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import net.minidev.json.JSONObject;

public interface OreoJobService extends IService<OreoJob> {
    Resp result(JSONObject conanBody);

    IPage<OreoJob> list(QueryRequest request, OreoJob oreoJob);

    Resp newBuild(OreoJob oreoJob, String StrategyMessage) throws Exception;

    Integer add(OreoJob oreoJob);

    Resp updateProcess(OreoJob oreoJob);

    Resp updateSuccess(OreoJob oreoJob);

    Resp updateReportId(OreoJob oreoJob);

}
