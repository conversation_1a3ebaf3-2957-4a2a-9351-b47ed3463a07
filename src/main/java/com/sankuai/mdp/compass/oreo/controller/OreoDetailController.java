package com.sankuai.mdp.compass.oreo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.jump.entity.JumpDetail;
import com.sankuai.mdp.compass.oreo.entity.OreoDetail;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.oreo.service.OreoDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/compass/api/oreoDetail")
public class OreoDetailController extends BaseController {
    @Autowired
    OreoDetailService oreoDetailService;

    @GetMapping("/list")
    public Map<String, Object> detailList(QueryRequest request, OreoDetail oreoDetail)  {
        IPage<OreoDetail> OreoDetailIPage = this.oreoDetailService.list(request, oreoDetail);
        if (OreoDetailIPage != null) {
            return getDataTable(OreoDetailIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/update")
    public Resp updateOne(OreoDetail oreoDetail) {
        return Resp.success(oreoDetailService.updateOne(oreoDetail));
    }

    @PostMapping("/updateBatch")
    public Resp updateBatch(List<OreoDetail> oreoDetailList) {
    return Resp.success(oreoDetailService.updateBatch(oreoDetailList));
}



    @PostMapping("/add")
    public Integer create(OreoDetail oreoDetail) {
        return oreoDetailService.add(oreoDetail);
    }
}
