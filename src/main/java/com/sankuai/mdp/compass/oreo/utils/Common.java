package com.sankuai.mdp.compass.oreo.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.HttpUtil;
import com.sankuai.mdp.compass.common.utils.MockUtil;
import org.mortbay.log.Log;

import java.io.IOException;

public class Common {

    String displayMockUrl = "https://oreo-autotest.vip.sankuai.com/autotest/homepage/display";
    String displayMockUrlTest = "http://hushengwen02-oibzx-sl-pt-api.wpt.test.sankuai.com/autotest/homepage/display";
    MockUtil mockUtil = new MockUtil();
    public String createMockIdByStrategyId(String strategyId) throws IOException {
        String params = "accuracy=35&addressStyle=2&addressType=1&allowToTrack=1&ci=532&ciLatitude=33.7737808&ciLongitude=120.2574463&clearTimeStamp=-1&coldBootLoad=0¤tScene=phfDefault&firstPageAbtest=default&ipCi=1&language=zh-CN&latitude=33.7737808&latlng=40.008451%2C116.487304&locatedCi=1&locationEnable=1&longitude=120.2574463&mtPtLawSettings=%7B%22contentSwitch%22%3Atrue%2C%22adSwitch%22%3Atrue%7D&planKey=homepage&userid=1589295871&utm_campaign=AgroupBgroupD100H0&utm_medium=iphone&utm_source=Alpha&version_name=12.19.400&strategyId=";
        Integer mockId = 0;
        String resp;
        try {
            resp = HttpUtil.sendGet(displayMockUrl, params + strategyId);
        } catch (IOException e) {
            return mockId.toString();
        }
        JsonObject jsonObject = new JsonParser().parse(resp).getAsJsonObject();
        JsonObject mockResult = mockUtil.create("/groupapi/homepage/display", jsonObject);
        if (null != mockResult && mockResult.has("data") && mockResult.getAsJsonObject("data").has("mockId")) {
            mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
        }
        Log.info("mockId:" + mockId);
        return mockId.toString();
    }

    public String buildStrategyString(JsonObject jsonObject) throws IOException {
        String strategyId = jsonObject.get("strategyid").getAsString();
        String strategyName = jsonObject.get("strategyName").getAsString();
        String mockId = createMockIdByStrategyId(strategyId);
        if (mockId.equals("0")){
            return String.format("strategyId=%s&strategyName=%s&mockId=noMockId",
                    strategyId, strategyName);
        }
        return String.format("strategyId=%s&strategyName=%s&mockId=%s",
                strategyId, strategyName, mockId);
    }
}
