package com.sankuai.mdp.compass.searchPerformance.service;

import com.sankuai.mdp.compass.searchPerformance.entity.SearchPerformance;
import net.minidev.json.JSONObject;

import java.io.IOException;

/**
 * Created by guanxin on 2020/4/9.
 */
public interface SearchPerformanceService {
    void insertJob(SearchPerformance searchPerformance) throws Exception;
    void insertResult(JSONObject body) throws IOException;
}
