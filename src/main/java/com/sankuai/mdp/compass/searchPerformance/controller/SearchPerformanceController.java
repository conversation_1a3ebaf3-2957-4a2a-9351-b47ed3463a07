package com.sankuai.mdp.compass.searchPerformance.controller;

import com.sankuai.mdp.compass.searchPerformance.entity.SearchPerformance;
import com.sankuai.mdp.compass.searchPerformance.service.SearchPerformanceService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * Created by guanxin on 2020/4/9.
 */
@RestController
@RequestMapping("/compass/api/searchPerformance")
public class SearchPerformanceController {
    @Autowired
    SearchPerformanceService searchPerformanceService;

    //插入job
    @PostMapping("/insertJob")
    public void insertJob(SearchPerformance searchPerformance) throws Exception {searchPerformanceService.insertJob(searchPerformance);}

    //插入结果数据
    @PostMapping("/insertResult")
    public void insertResult(@RequestBody JSONObject body) throws IOException {
        searchPerformanceService.insertResult(body);
    }
}
