package com.sankuai.mdp.compass.searchPerformance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.utils.ConanUtil;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.FileUtil;
import com.sankuai.mdp.compass.searchPerformance.entity.SearchPerformance;
import com.sankuai.mdp.compass.searchPerformance.mapper.SearchPerformanceMapper;
import com.sankuai.mdp.compass.searchPerformance.service.SearchPerformanceService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by guanxin on 2020/4/9.
 */
@Slf4j
@Service
public class SearchPerformanceImpl extends ServiceImpl<SearchPerformanceMapper,SearchPerformance> implements SearchPerformanceService
{
    ComConstant comConstant = new ComConstant();
    FileUtil fileUtil = new FileUtil();
    DxUtil dxUtil = new DxUtil();

    @Autowired
    SearchPerformanceMapper searchPerformanceMapper;
    JsonParser jsonParser = new JsonParser();

    @Override
    public void insertJob(SearchPerformance searchPerformance) throws Exception {
        searchPerformance.setType("job");
        searchPerformanceMapper.insert(searchPerformance);
        String report_url = searchPerformance.getReportUrl();
        String  dx_result = dxUtil.sendToPersionByCompass("【搜索性能测试】job已创建！[云测报告链接|"+report_url+"]","liujiao11");
//        System.out.println("done");
    }
    @Override
    public void insertResult(JSONObject body) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SearchPerformance searchPerformance = new SearchPerformance();
        ConanUtil conanUtil = new ConanUtil();
        String conan_report_url = "https://conan.sankuai.com/auto-function/report/";
//        searchPerformanceService.collectResult(body);
        JsonObject timeData = jsonParser.parse(body.get("timedata").toString()).getAsJsonObject();
        JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
        JsonArray devicelist = jsonParser.parse(body.get("devicelist").toString()).getAsJsonArray();
        String jobId = jobData.get("id").toString();
        RandomAccessFile rf = null;
        try {
            Date startTime = sdf.parse(timeData.get("startTime").toString());
            Date endTime = sdf.parse(timeData.get("endTime").toString());
            Float totalTime = timeData.get("totalTime").getAsFloat();
            String reportUrl = jobData.get("report").getAsString();
            String jobType = jobData.get("jobType").getAsString();
            searchPerformance.setJobId(jobId);
            searchPerformance.setStartTime(startTime);
            searchPerformance.setEndTime(endTime);
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("job_id", jobId);
            SearchPerformance searchPerformanceJob = searchPerformanceMapper.selectOne(queryWrapper);
            String apkUrl = searchPerformanceJob.getApkurl();
            String platform = searchPerformanceJob.getPlatform();
            String version = searchPerformanceJob.getVersion();
            searchPerformance.setApkurl(apkUrl);
            searchPerformance.setPlatform(platform);
            searchPerformance.setVersion(version);
//        String cmd = "sh 111.sh "+jobId;
//        Process process = null;
//        process = Runtime.getRuntime().exec(cmd);

            String taskId = conanUtil.getTaskInfo(jobId).get(0).getAsJsonObject().get("id").getAsString();
            String log_url = conanUtil.getDownloadURL(taskId);
            System.out.print("logurl: "+log_url);
            if (null != log_url) {
                fileUtil.downloadAndReadFile(log_url,comConstant.OUT_PUT,taskId);
                String time_all;
                String path = comConstant.OUT_PUT + "/" + taskId+ "/";
                String T3 = "";
                String time_details = "";
                String fps = "";
                File file = new File(path);
                File[] tempFile = file.listFiles();

                for(int i = 0; i < tempFile.length; i++){
                    if(tempFile[i].getName().indexOf("search_performance_time_result")!=-1){
                        rf = new RandomAccessFile(path+tempFile[i].getName(), "r");
                        rf.seek(0);
                        time_all=rf.readLine();
                        if (time_all != null) {
                            time_all = new String(time_all.getBytes("ISO-8859-1"), "utf-8");
                            if (time_all.split("fengefenge").length>1){
                                T3 = time_all.split("fengefenge")[0];
                                time_details = time_all.split("fengefenge")[1];
                                System.out.print(T3+'\n');
                                System.out.print(time_details+'\n');
                                searchPerformance.setType("time");
                                searchPerformance.setValue(Float.parseFloat(T3));
                                searchPerformance.setDetails(time_details);
                                searchPerformanceMapper.insert(searchPerformance);
                            }
                        }
                    }else if(tempFile[i].getName().indexOf("search_performance_fps_result")!=-1){
                        rf = new RandomAccessFile(path+tempFile[i].getName(), "r");
                        rf.seek(0);
                        fps=rf.readLine();
                        if (fps != null) {
                            fps = new String(fps.getBytes("ISO-8859-1"), "utf-8");
                            System.out.print(fps+'\n');
                            searchPerformance.setType("fps");
                            searchPerformance.setValue(Float.parseFloat(fps));
                            searchPerformance.setDetails(null);
                            searchPerformanceMapper.insert(searchPerformance);
                        }
                    }
                }
                String dx_result = dxUtil.sendToPersionByCompass("【搜索性能测试】收到云测回调！[云测报告链接|"+reportUrl+"]","liujiao11");
            }
        } catch (Exception e) {
            log.error("get output err",e);
        } finally {
            rf.close();

            fileUtil.delFolder(comConstant.OUT_PUT);
        }

    }

}
