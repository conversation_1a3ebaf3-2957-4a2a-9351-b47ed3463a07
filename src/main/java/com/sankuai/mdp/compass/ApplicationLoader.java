package com.sankuai.mdp.compass;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.meituan.mdp.boot.starter.mapping.processor.shaded.freemarker.core.Environment;
import com.sankuai.mdp.compass.config.DataSourceConfig;
import com.sankuai.mdp.compass.config.MybatisPlusConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.zebra.ZebraAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;


@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,DynamicDataSourceAutoConfiguration.class})
//@MapperScan("com.sankuai.mdp.compass.mapper")
@EnableScheduling
@EnableAsync
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        ConfigurableEnvironment env =  application.run(args).getEnvironment();
        System.out.println("当前端口："+env.getProperty("server.port"));
    }
}