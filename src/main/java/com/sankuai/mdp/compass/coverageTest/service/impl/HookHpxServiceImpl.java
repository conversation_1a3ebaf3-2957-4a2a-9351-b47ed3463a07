package com.sankuai.mdp.compass.coverageTest.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.HyperloopXUtils;
import com.sankuai.mdp.compass.common.utils.LionUtil;
import com.sankuai.mdp.compass.coverageTest.entity.HookInfo;
import com.sankuai.mdp.compass.coverageTest.mapper.HookInfoMapper;
import com.sankuai.mdp.compass.coverageTest.service.HookHpxService;
import com.sankuai.mdp.compass.hpxInfo.mapper.BuInfoMapper;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by skt on 2022/11/2.
 */

@Slf4j
@Service
public class HookHpxServiceImpl implements HookHpxService {

    @Autowired
    HookInfoMapper hookInfoMapper;

    DxUtil dxUtil;

    @Value("${hyperloopxBaseUrl}")
    private String hpxBaseUrl;

    @Autowired
    private HyperloopXUtils hyperloopXUtils;

    @Override
    public String hookFunctionalBuild(JsonObject hpxCallbackData) {
        String buildNum = hpxCallbackData.get("buildNum").getAsString();
        JsonArray messageReceivers = hpxCallbackData.get("messageReceivers").getAsJsonArray();
        String branch = hpxCallbackData.get("branch").getAsString();
        String buildTypeName = hpxCallbackData.get("buildTypeName").getAsString();
        String appName = hpxCallbackData.get("appName").getAsString();
        String misId = hpxCallbackData.get("misId").getAsString();
        if ("it_nano".equals(misId))return "";
        String os = hpxCallbackData.get("os").getAsString();
        if (!os.equals("android")) return "";
        JsonArray integrationList = hpxCallbackData.get("paramInfo").getAsJsonObject().get("integrationList").getAsJsonArray();
        ArrayList<String> messageReceiverList = new ArrayList();
        for (JsonElement messageReceiver : messageReceivers) {
            messageReceiverList.add(messageReceiver.getAsString());
        }
        //1 判断是不是需要的业务，需要的才往下走，否则直接return
        boolean flag = false;
        List<String> whitePackages = new LionUtil().getListValue("useCoveragePackage");
        for1:
        for (JsonElement jsonElement : integrationList) {
            if (flag) break;
            String packageName = jsonElement.getAsJsonObject().get("name").toString();
            for (String singlePackage : whitePackages) {
                if (packageName.contains(singlePackage)) {
                    flag = true;
                    break for1;
                }
            }
        }
        if (!flag) return "";
        //2处理需要存储的信息，并落库
        HookInfo hookInfo = new HookInfo();
        hookInfo.setMessageReceivers(messageReceiverList.toString());
        hookInfo.setIntegrationList(integrationList.toString());
        hookInfo.setMisId(misId);
        hookInfo.setBranch(branch);
        hookInfo.setBuildTypeName(buildTypeName);
        hookInfo.setBuildNum(buildNum);
        hookInfo.setPlatform(os);
        hookInfo.setAppName(appName);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        hookInfo.setAddTime(new Date());
        hookInfoMapper.insert(hookInfo);
        if (new LionUtil().getBooleanValue("buildCoverPackage")) {
            hyperloopXUtils.buildCoveragePackage(hookInfo);
        }
        return "";
    }

    @Override
    public void buildCallBack(JsonObject buildCallbackResponse) {

        int coverBuildNum = buildCallbackResponse.get("buildNum").getAsInt();
        int status = buildCallbackResponse.get("status").getAsInt();
        //如果莫名奇妙失败了，重试一次;状态码wiki https://km.sankuai.com/page/183674476#id-4.4%E6%9F%A5%E8%AF%A2%E6%9F%90%E4%B8%AA%E5%BA%94%E7%94%A8%E7%9A%84%E6%89%93%E5%8C%85%E4%BF%A1%E6%81%AF%E8%AF%A6%E6%83%85
        if (status == 3) {
            LambdaQueryWrapper<HookInfo> hookInfoWapper = new LambdaQueryWrapper<>();
            hookInfoWapper.eq(HookInfo::getCoverBuildNum, coverBuildNum);
            HookInfo hookInfo = hookInfoMapper.selectOne(hookInfoWapper);
            if (null != hookInfo) {
                int retrytime = hookInfo.getRetryBuildTime();
                if (retrytime < 2) {
                    hyperloopXUtils.buildCoveragePackage(hookInfo);
                } else {
                    dxUtil.sendMessageByCoverageHelper("重试次数超过阈值", "sunkangtong");
                }
            }
        }
    }


}
