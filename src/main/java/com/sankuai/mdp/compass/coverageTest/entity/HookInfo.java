package com.sankuai.mdp.compass.coverageTest.entity;
/* *************
 * @author: sunkangtong
 * @date: 2022/11/2
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@TableName("coverage_hook_info")
public class HookInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String platform;

    private String branch;

    private String misId;

    private String buildNum;

    private String buildTypeName;

    private String messageReceivers;

    private String integrationList;

    private String appName;

    private String coverageTaskDetailUrl;

    private Integer coverBuildNum;

    private Integer retryBuildTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
