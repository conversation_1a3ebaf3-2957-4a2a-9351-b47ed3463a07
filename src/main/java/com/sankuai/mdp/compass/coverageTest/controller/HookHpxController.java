package com.sankuai.mdp.compass.coverageTest.controller;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sankuai.mdp.compass.coverageTest.service.HookHpxService;

/**
 * Created by skt on 2022/11/1.
 */
@RestController
@RequestMapping("/compass/api/autoJob")
public class HookHpxController extends BaseController {
    @Autowired
    HookHpxService hookHpxService;

    @PostMapping("taskCallBack")
    public Resp callBack(@RequestBody String body) {
        if (body != null && !"".equals(body)) {
            hookHpxService.hookFunctionalBuild(new JsonParser().parse(body).getAsJsonObject());
        }
        Resp resp = new Resp();
        return resp;
    }

    @PostMapping("buildCallBack")
    public Resp buildCallBack(@RequestBody String body) {
        new DxUtil().sendMessageByCoverageHelper("收到了回调，回调内容为： "+body,"sunkangtong");
        if (body != null && !"".equals(body)){
            hookHpxService.buildCallBack(new JsonParser().parse(body).getAsJsonObject());
        }
        Resp resp = new Resp();
        return resp;
    }


}
