package com.sankuai.mdp.compass.coverageTest.entity;

import com.google.gson.JsonArray;
import lombok.Data;

import java.util.List;

/**
 * Created by skt on 2022/11/3.
 */

@Data
public class BuildRequestBody {

    private String os;

    private String appName;

    private String buildTypeName;

    private String branch;

    private String build_type;

    private Boolean is_component;

    private String probe_type;

    private String compared_branch;

    private String aar;

    private String version_name;

    private JsonArray integrationList;

    private JsonArray messageReceivers;

    private String callbackUrl;
    
}
