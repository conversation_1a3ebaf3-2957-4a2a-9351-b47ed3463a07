package com.sankuai.mdp.compass.dynamicSDK.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.dynamicSDK.service.UIDiffService;
import com.sankuai.mdp.compass.getAPK.controller.GetApkDataController;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: wanghaojie07
 * @date: 2023/1/10
 * @time: 11:06 上午
 * Copyright (C) 2015 Meituan
 * All rights reserved
 */
@RestController
@RequestMapping("/compass/api/dynamicLayout")
public class DynamicLayoutCIController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(DynamicLayoutCIController.class);
    private final int successValue = 2;
    private DxUtil messageUtil = new DxUtil();


    @Autowired
    UIDiffService uiDiffService;
    @Autowired
    GetApkDataController getApkDataController;

    @PostMapping("/package")
    public String packageDemoApp(@RequestBody JSONObject eventParam){
        String jsonVersion = eventParam.getString("versionNumber");
        if(jsonVersion == null || "".equals(jsonVersion)){
            logger.error("event did not pass the versionNumber param");
            return "Error, please put versionNumber parameter";
        }
        return uiDiffService.packageApp(jsonVersion);
    }

    @PostMapping("/startMTSDKTest")
    public String mtAppTest(@RequestBody JSONObject eventParam){
        ApkInfo apkInfo = new ApkInfo();
        apkInfo.setOs("Android");
        apkInfo.setAppName("com.sankuai.meituan");
        apkInfo.setBuildTypeName("Release 打包");
        apkInfo.setCurrentBranch(1);
        String response = getApkDataController.getApkData(apkInfo);
        String apk_url = "";
        if (response.contains("appUrl")) {
            apk_url = new JsonParser().parse(response).getAsJsonObject().get("appUrl").getAsString();
        }
        if("".equals(apk_url)){
            return "Get apk_url failed";
        }
        return uiDiffService.startAndroidMTTest(apk_url);
    }

    @PostMapping("/startTest")
    public String startTest(@RequestBody JSONObject callBackParam){
        /**
         * 字段：status
         * 取值：未开始 0、执行中 1、成功 2、失败 3、取消 4、未知 5、超时 6、排队中 7、等待三方回执 8
         */
        System.out.println(callBackParam);
        int packageStatus = callBackParam.getIntValue("status");
        if(packageStatus != successValue){
            logger.error("Package failed");
            return "Package failed";
        }
        String os = callBackParam.getString("os");
        String packageUrl = callBackParam.getJSONObject("artifactInfo").getString("app_download_url");
        if(os == null || "".equals(os)){
            return "Get operation system type failed";
        }
        if(packageUrl == null || "".equals(packageUrl)){
            return "Get package download url failed";
        }
        String result = uiDiffService.startTest(os, packageUrl);
        try {
            messageUtil.sendToIndividualByCompassAuto(String.format("%s 回归任务已经启动，启动信息为%s", os , result), "wanghaojie07");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("send dx message failed");
        }
        return result;
    }
}
