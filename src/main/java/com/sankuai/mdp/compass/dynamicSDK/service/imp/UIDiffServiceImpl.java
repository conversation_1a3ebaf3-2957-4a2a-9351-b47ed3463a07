package com.sankuai.mdp.compass.dynamicSDK.service.imp;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.dynamicSDK.service.UIDiffService;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;

/**
 * @description:
 * @author: wanghaojie07
 * @date: 2023/1/16
 * @time: 11:18 上午
 * Copyright (C) 2015 Meituan
 * All rights reserved
 */
@Service
public class UIDiffServiceImpl implements UIDiffService {
    /**
     *  hpx开放接口地址，包括打包地址和查询地址
     */
    private final String packageAppUrl = "https://hpx.sankuai.com/api/open/build";
    private final String packageStatusCheckUrl = "https://hpx.sankuai.com/api/open/getBranchNewestPackageInfoPlus";

    private final String iosJenkinsUrl = "http://*************:8080/job/dynamicLayoutSDK_uidiff_iOS/buildWithParameters";
    private final String androidJenkinsUrl = "http://*************:8080/job/dynamicLayoutSDK_uidiff_android/buildWithParameters";
    private final String androidMTJenkinsUrl = "http://*************:8080/job/dynamicLayoutSDK_uidiff_android_meituanApp/buildWithParameters";

    private static final Logger logger = LoggerFactory.getLogger(UIDiffService.class);
    private DxUtil messageUtil = new DxUtil();

    @Value("${compassUrl}")
    String hostName;

    @Override
    public String startTest(String os, String packageUrl) {
        try{
            if("ios".equals(os)){
                URIBuilder uriBuilder = new URIBuilder(iosJenkinsUrl);
                uriBuilder.addParameter("token", "qatest");
                uriBuilder.addParameter("ipa_url", packageUrl);
                URI uri = uriBuilder.build();
                logger.info("ios jenkins request url: " + uri.toString());
                HttpClient client = new HttpClient();
                GetMethod getMethod = new GetMethod(uri.toString());
                client.executeMethod(getMethod);
            }else{
                URIBuilder uriBuilder = new URIBuilder(androidJenkinsUrl);
                uriBuilder.addParameter("token", "qatest");
                uriBuilder.addParameter("apk_url", packageUrl);
                URI uri = uriBuilder.build();
                logger.info("android jenkins request url: " + uri.toString());
                HttpClient client = new HttpClient();
                GetMethod getMethod = new GetMethod(uri.toString());
                client.executeMethod(getMethod);
            }
            return "success";
        }catch (Exception e){
            logger.error("start test failed");
            return "Start Test Failed";
        }
    }

    @Override
    public String packageApp(String demoVersion) {
        boolean ios = packageIOS(demoVersion);
        // Android 使用团App，不需要打包，直接获取线上包，暂时保留Android打包代码，以便后续需要demo测试时使用
        boolean android = true;
        if(ios && android){
            return "双端成功发送打包请求";
        }else if (ios){
            return "Android端发送打包请求失败";
        }else{
            return "iOS端发送打包请求失败";
        }
    }
    private boolean packageIOS(String demoVersion){
        String branch = "release/" + demoVersion.substring(0, 4);
        if(checkIOSPackage(branch)){
            logger.info("该版本已经有测试包，无需重复打包测试");
            try {
                messageUtil.sendToIndividualByCompassAuto(branch + "分支已经完成过打包，如需测试，请手动执行jenkins任务：\nhttp://*************:8080/job/dynamicLayoutSDK_uidiff_iOS/build", "wanghaojie07");
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("send dx message failed");
            }
            // 有该分支测试包默认认为已经完成过测试，推送大象消息，如需测试可手动打包测试。
            return false;
        }
        // 拼接打包请求参数
        JSONObject data = new JSONObject();
        data.put("os", "ios");
        data.put("appName", "MTFlexboxDemoApp");
        data.put("branch", branch);
        data.put("integratable", "false");
        data.put("description", "iOS UI diff package");
        data.put("callbackUrl", String.format("%s/compass/api/dynamicLayout/startTest", hostName));
        data.put("buildTypeName", "功能测试打包");

        String result = postJson(data, packageAppUrl);
        JSONObject response = JSONObject.parseObject(result);
        try{
            int status = response.getIntValue("status");
            if(status == 1){
                logger.info(response.getJSONObject("data").toString());
                return true;
            }else{
                logger.error(response.getJSONObject("error").toString());
                return false;
            }
        }catch (Exception e){
            logger.error("Get package status failed");
            return false;
        }
    }
    private boolean checkIOSPackage(String branch){
        JSONObject data = new JSONObject();
        data.put("os", "ios");
        data.put("appName", "MTFlexboxDemoApp");
        data.put("branch", "release/12.7");
        data.put("templateComponentConfigList", new int[]{10, 11, 12});

        String result = postJson(data, packageStatusCheckUrl);
        JSONObject response = JSONObject.parseObject(result);

        try{
            int status = response.getIntValue("status");
            if(status == 1){
                logger.info(response.getJSONObject("data").toString());
                // 已经有打包成功的测试包
                return true;
            }else{
                logger.error(response.getJSONObject("error").toString());
                return false;
            }
        }catch (Exception e){
            logger.error("Get package status failed");
            return false;
        }
    }
    private boolean packageAndroid(String demoVersion){
        String branch = "release/" + demoVersion;
        // 拼接打包参数
        JSONObject data = new JSONObject();
        data.put("os", "android");
        data.put("componentName", "com.meituan.android.dynamiclayout:library");
        data.put("branch", branch);
        data.put("integratable", "false");
        data.put("description", "Android UI diff package");
        data.put("build_type", "debug");
        data.put("callbackUrl", String.format("%s/compass/api/dynamicLayout/startTest", hostName));
        data.put("buildTypeName", "Demo App打包");
        String result = postJson(data, packageAppUrl);
        JSONObject response = JSONObject.parseObject(result);
        try{
            int status = response.getIntValue("status");
            if(status == 1){
                logger.info(response.getJSONObject("data").toString());
                return true;
            }else{
                logger.error(response.getJSONObject("error").toString());
                return false;
            }
        }catch (Exception e){
            logger.error("Get package status failed");
            return false;
        }
    }

    @Override
    public String startAndroidMTTest(String apk_url){
        try {
            URIBuilder uriBuilder = new URIBuilder(androidMTJenkinsUrl);
            uriBuilder.addParameter("token", "qatest");
            uriBuilder.addParameter("apk_url", apk_url);
            URI uri = uriBuilder.build();
            logger.info("android jenkins request url: " + uri.toString());
            HttpClient client = new HttpClient();
            GetMethod getMethod = new GetMethod(uri.toString());
            client.executeMethod(getMethod);
        }catch (Exception e){
            logger.error("start android meituan app jenkins test fail");
            return "failed to start jenkins job";
        }
        return "Success";
    }


    private String postJson(JSONObject data, String url) {
        HttpClient client = new HttpClient();
        // 设置请求链接和headers
        PostMethod postMethod = new PostMethod(url);
        postMethod.addRequestHeader("Content-Type", "application/json");
        postMethod.addRequestHeader("Authorization", "035684F902088BC4EE6BEAB733F725BF");

        InputStream input = new ByteArrayInputStream(data.toString().getBytes());
        RequestEntity requestEntity = new InputStreamRequestEntity(input, "application/json");
        postMethod.setRequestEntity(requestEntity);
        try {
            client.executeMethod(postMethod);
            String response = postMethod.getResponseBodyAsString();
            logger.info("Hpx response: " + response);
            return response;
        } catch (IOException e) {
            return "post request to hpx failed";
        }
    }


}
