package com.sankuai.mdp.compass.dynamicSDK.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @description:
 * @author: wanghaojie07
 * @date: 2023/2/13
 * @time: 5:16 下午
 * Copyright (C) 2015 Meituan
 * All rights reserved
 */
@Component
@Aspect
public class FilterForSecurityScan {
    private static final Logger logger = LoggerFactory.getLogger(FilterForSecurityScan.class);

    @Pointcut("execution(* com.sankuai.mdp.compass.dynamicSDK.controller.DynamicLayoutCIController.*(..))")
    public void pointCutTest(){
    }
    @Around("pointCutTest()")
    public String skipSecurityScan(final ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String header = request.getHeader("Cat-Page-Type");
        if(header==null || "".equals(header)){
            String result = (String)joinPoint.proceed();
            logger.info("normal request, proceed method");
            return result;
        }else{
            logger.info("security scan request, we don't need to resolve this request");
            return "security scan request, we don't need to resolve this request";
        }
    }

}
