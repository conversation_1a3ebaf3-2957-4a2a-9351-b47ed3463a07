package com.sankuai.mdp.compass.category.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.category.entity.CategoryInspection;
import com.sankuai.mdp.compass.category.mapper.CategoryInspectionMapper;
import com.sankuai.mdp.compass.category.service.CategoryInspectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.Calendar;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.HashMap;
import java.util.Set;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.Collections;

/**
 * CategoryInspectionService实现类
 * 实现对category_inspection表的业务操作
 */
@Service
public class CategoryInspectionServiceImpl extends ServiceImpl<CategoryInspectionMapper, CategoryInspection> implements CategoryInspectionService {

    private static final Logger logger = LoggerFactory.getLogger(CategoryInspectionServiceImpl.class);

    @Autowired
    private CategoryInspectionMapper baseMapper; // Assume baseMapper injection if needed for other methods

    /**
     * 根据设备ID查询最新的检查记录
     * 
     * @param deviceId 设备ID
     * @return 最新的检查记录，如果不存在则返回null
     */
    @Override
    public CategoryInspection getLatestByDeviceId(String deviceId) {
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                    .orderByDesc(CategoryInspection::getInspectionTime)
                    .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据时间范围查询检查记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的检查记录列表
     */
    @Override
    public List<CategoryInspection> getByTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(startTime != null, CategoryInspection::getInspectionTime, startTime)
                    .le(endTime != null, CategoryInspection::getInspectionTime, endTime)
                    .orderByDesc(CategoryInspection::getInspectionTime);
        return list(queryWrapper);
    }
    
    /**
     * 添加检查记录
     * 
     * @param inspection 检查记录实体
     * @return 是否添加成功
     */
    @Override
    public boolean addInspection(CategoryInspection inspection) {
        // 设置默认创建时间
        if (inspection.getCreatedAt() == null) {
            inspection.setCreatedAt(new Date());
        }
        if (inspection.getAddTime() == null) {
            inspection.setAddTime(new Date());
        }
        if (inspection.getUpdateTime() == null) {
            inspection.setUpdateTime(new Date());
        }
        return save(inspection);
    }
    
    /**
     * 根据ID查询检查记录
     * 
     * @param inspectionId 检查记录ID
     * @return 检查记录实体
     */
    @Override
    public CategoryInspection getInspectionById(Long inspectionId) {
        return getById(inspectionId);
    }
    
    /**
     * 分页查询检查记录
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param excludeLogLevel 要排除的日志级别（可选）
     * @return 分页结果
     */
    @Override
    public IPage<CategoryInspection> pageInspections(Page<CategoryInspection> page, String deviceId, String excludeLogLevel) {
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_ID", deviceId);
        }
        
        // 如果指定了要排除的日志级别，添加条件
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            queryWrapper.ne("LOG_LEVEL", excludeLogLevel);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc("Created_At");
        
        return baseMapper.selectPage(page, queryWrapper);
    }
    
    /**
     * 保留原有的方法以保持兼容性
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @return 分页结果
     */
    @Override
    public IPage<CategoryInspection> pageInspections(Page<CategoryInspection> page, String deviceId) {
        // 调用新方法，默认不排除任何日志级别
        return pageInspections(page, deviceId, null);
    }
    
    /**
     * 更新检查记录
     * 
     * @param inspection 检查记录实体
     * @return 是否更新成功
     */
    @Override
    public boolean updateInspection(CategoryInspection inspection) {
        // 设置更新时间
        inspection.setUpdateTime(new Date());
        return updateById(inspection);
    }
    
    /**
     * 根据ID删除检查记录
     * 
     * @param inspectionId 检查记录ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteInspection(Long inspectionId) {
        return removeById(inspectionId);
    }
    
    /**
     * 根据设备ID删除检查记录
     * 
     * @param deviceId 设备ID
     * @return 删除的记录数
     */
    @Override
    public int deleteByDeviceId(String deviceId) {
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryInspection::getDeviceId, deviceId);
        return count(queryWrapper) > 0 ? baseMapper.delete(queryWrapper) : 0;
    }
    
    @Override
    public Integer newInspection(CategoryInspection inspection) {
        baseMapper.insert(inspection);
        return inspection.getInspectionId().intValue();
    }

    @Override
    public List<Map<String, Object>> getRecentDevices(Date startDate) {
        // 创建查询条件，筛选创建时间大于等于startDate的记录
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("Created_At", startDate);
        queryWrapper.orderByDesc("Created_At");
        queryWrapper.select("Device_ID", "MAX(Created_At) as lastInspectionTime");
        queryWrapper.groupBy("Device_ID");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> device : result) {
            // 将数据库字段名转换为驼峰命名
            if (device.containsKey("Device_ID")) {
                device.put("deviceId", device.get("Device_ID"));
                device.remove("Device_ID");
            }
            if (device.containsKey("lastInspectionTime")) {
                device.put("lastInspectionTime", device.get("lastInspectionTime"));
            }
        }
        
        return result;
    }

    /**
     * 获取指定日期之后有检查记录的所有设备，排除指定日志级别
     *
     * @param startDate 开始日期
     * @param excludeLogLevel 要排除的日志级别
     * @return 设备列表，每个设备包含设备ID和最后检查时间
     */
    @Override
    public List<Map<String, Object>> getRecentDevices(Date startDate, String excludeLogLevel) {
        // 创建查询条件，筛选创建时间大于等于startDate的记录，排除指定日志级别
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("Created_At", startDate);
        
        // 如果指定了要排除的日志级别，添加条件
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            queryWrapper.ne("LOG_LEVEL", excludeLogLevel);
        }
        
        queryWrapper.orderByDesc("Created_At");
        queryWrapper.select("Device_ID", "MAX(Created_At) as lastInspectionTime");
        queryWrapper.groupBy("Device_ID");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> device : result) {
            // 将数据库字段名转换为驼峰命名
            if (device.containsKey("Device_ID")) {
                device.put("deviceId", device.get("Device_ID"));
                device.remove("Device_ID");
            }
            if (device.containsKey("lastInspectionTime")) {
                device.put("lastInspectionTime", device.get("lastInspectionTime"));
            }
        }
        
        return result;
    }

    /**
     * 获取所有设备的最新心跳日志
     *
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志
     * @return 心跳日志列表，每个设备的最新心跳日志
     */
    @Override
    public List<Map<String, Object>> getLatestHeartbeatLogs(boolean onlyDevice2250) {
        // 创建查询条件，筛选日志级别为"HEART"的记录
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("LOG_LEVEL", "HEART");
        queryWrapper.orderByDesc("Created_At");
        
        // 选择需要的字段
        queryWrapper.select("Device_ID", "Remarks", "Created_At", "Inspection_Time");
        
        // 使用子查询获取每个设备的最新心跳日志
        String subQuery = "SELECT t.* FROM (SELECT Device_ID, MAX(Created_At) as max_created_at FROM category_inspection " +
                          "WHERE LOG_LEVEL = 'HEART' GROUP BY Device_ID) as sub " +
                          "JOIN category_inspection t ON t.Device_ID = sub.Device_ID AND t.Created_At = sub.max_created_at " +
                          "WHERE t.LOG_LEVEL = 'HEART'";
        
        // 执行原生SQL查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> log : result) {
            // 将数据库字段名转换为驼峰命名
            if (log.containsKey("Device_ID")) {
                log.put("deviceId", log.get("Device_ID"));
                log.remove("Device_ID");
            }
            if (log.containsKey("Remarks")) {
                log.put("remarks", log.get("Remarks"));
                log.remove("Remarks");
            }
            if (log.containsKey("Created_At")) {
                log.put("createdAt", log.get("Created_At"));
                log.remove("Created_At");
            }
            if (log.containsKey("Inspection_Time")) {
                log.put("inspectionTime", log.get("Inspection_Time"));
                log.remove("Inspection_Time");
            }
        }

        // 如果 onlyDevice2250 为 true，过滤出以"【MBP-PVFCQ0LQ62-2250】"开头的 HEART 日志
        if (onlyDevice2250) {
            List<Map<String, Object>> filteredResult = new ArrayList<>();
            for (Map<String, Object> log : result) {
                String remarks = (String) log.get("remarks");
                if (remarks != null && remarks.contains("【MBP-PVFCQ0LQ62-2250】")) {
                    filteredResult.add(log);
                }
            }
            return filteredResult;
        }

        return result;
    }

    /**
     * 清理检查记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @return 删除的记录数
     */
    @Override
    @Async
    @Transactional
    public int cleanupInspectionLogs(int keepRecords) {
        // 默认排除HEART级别的日志
        return cleanupInspectionLogs(keepRecords, "HEART");
    }
    
    /**
     * 清理检查记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @param excludeLogLevel 要排除的日志级别，不会被清理
     * @return 删除的记录数
     */
    @Override
    @Async
    @Transactional
    public int cleanupInspectionLogs(int keepRecords, String excludeLogLevel) {
        // 获取总记录数（排除指定日志级别）
        long totalCount;

        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
        LambdaQueryWrapper<CategoryInspection> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.ne(CategoryInspection::getLogLevel, excludeLogLevel);
            totalCount = count(countWrapper);
        } else {
            totalCount = count();
        }

        // 如果总记录数小于等于保留数量，不需要清理
        if (totalCount <= keepRecords) {
            return 0;
        }

        // 计算需要删除的记录数
        long deleteCount = totalCount - keepRecords;

        // 查询要保留的最小ID（按创建时间排序，保留最新的记录）
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();

        // 排除指定日志级别
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            queryWrapper.ne("LOG_LEVEL", excludeLogLevel);
        }
        
        queryWrapper.select("MIN(Inspection_ID) as minId")
                   .orderByDesc("Created_At")
                   .last("LIMIT " + keepRecords);
        
        Map<String, Object> result = getMap(queryWrapper);
        if (result == null || !result.containsKey("minId")) {
            return 0;
        }

        Long minIdToKeep = Long.valueOf(result.get("minId").toString());
        
        // 删除ID小于minIdToKeep的记录，并排除指定日志级别
        LambdaQueryWrapper<CategoryInspection> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.lt(CategoryInspection::getInspectionId, minIdToKeep);
        
        // 排除指定日志级别
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            deleteWrapper.ne(CategoryInspection::getLogLevel, excludeLogLevel);
        }
        
        // 执行删除操作
        int deletedCount = baseMapper.delete(deleteWrapper);
        
        // 返回删除的记录数
        return deletedCount;
    }

    /**
     * 获取设备在指定天数内的应用错误集合
     * 从HEART级别的日志中提取应用问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId 设备ID
     * @param days 查询的天数范围，如7表示最近7天
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志
     * @return 应用错误集合，包含时间、详情和图片URL
     */
    @Override
    public List<Map<String, Object>> getDeviceAppErrors(String deviceId, int days, boolean mergeShortTimeErrors, int timeIntervalHours, boolean onlyDevice2250) {
        // Calculate start and end dates
        Calendar calendar = Calendar.getInstance();
        Date endDate = new Date(); // Use current time as end time
        calendar.setTime(endDate);
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startDate = calendar.getTime();

        return getDeviceAppErrors(deviceId, startDate, endDate, mergeShortTimeErrors, timeIntervalHours, onlyDevice2250);
    }
    
    /**
     * 获取设备在指定时间范围内的应用错误集合
     * 从HEART级别的日志中提取应用问题和测试问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId 设备ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志
     * @return 应用错误和测试错误集合，包含时间、详情和图片URL
     */
    @Override
    public List<Map<String, Object>> getDeviceAppErrors(String deviceId, Date startDate, Date endDate, boolean mergeShortTimeErrors, int timeIntervalHours, boolean onlyDevice2250) {
        logger.info("开始查询设备[{}]的错误信息(CategoryInspectionService)，参数: 开始时间={}, 结束时间={}, 合并短时间错误={}, 时间间隔={}小时, 仅2250设备={}",
                   deviceId, startDate, endDate, mergeShortTimeErrors, timeIntervalHours, onlyDevice2250);
        
        // 创建查询条件，筛选指定设备的HEART级别日志
        // 注意：此处查询的是HEART日志的创建时间，但我们需要根据问题记录中的实际时间进行过滤
        // 为了确保不遗漏问题，我们查询的时间范围要稍大一些
        Calendar extendedCalendar = Calendar.getInstance();
        extendedCalendar.setTime(startDate);
        extendedCalendar.add(Calendar.DAY_OF_MONTH, -7); // 向前多查询7天
        Date extendedStartDate = extendedCalendar.getTime();
        
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                    .eq(CategoryInspection::getLogLevel, "HEART")
                    .ge(CategoryInspection::getCreatedAt, extendedStartDate) // 使用扩展的时间范围
                    .orderByDesc(CategoryInspection::getCreatedAt);
        
        // 查询符合条件的日志记录
        List<CategoryInspection> inspections = list(queryWrapper);
        logger.info("查询到设备[{}]的HEART日志{}条", deviceId, inspections.size());
        
        // 用于存储提取的应用错误和测试错误信息
        List<Map<String, Object>> allErrors = new ArrayList<>();
        
        // 遍历日志记录，提取应用问题和测试问题信息
        for (CategoryInspection inspection : inspections) {
            String remarks = inspection.getRemarks();
            if (remarks == null || remarks.isEmpty()) {
                continue;
            }
            
            // 解析remarks字段，提取应用问题和测试问题信息，并传入是否只处理设备2250的参数
            List<Map<String, Object>> errors = extractAppErrors(remarks, inspection.getCreatedAt(), onlyDevice2250);
            
            // 添加错误到结果集
            allErrors.addAll(errors);
        }
        
        // 按问题发生的时间排序（从新到旧）
        allErrors.sort((a, b) -> {
            Date timeA = (Date) a.get("time");
            Date timeB = (Date) b.get("time");
            return timeB.compareTo(timeA);
        });
        
        // 根据问题的实际时间过滤
        List<Map<String, Object>> filteredErrors = new ArrayList<>();
        for (Map<String, Object> error : allErrors) {
            Date errorTime = (Date) error.get("time");
            if (errorTime.after(startDate) && errorTime.before(endDate) || errorTime.equals(startDate) || errorTime.equals(endDate)) {
                filteredErrors.add(error);
            }
        }
        
        // 过滤掉detail为空的记录
        filteredErrors.removeIf(error -> !error.containsKey("detail") || error.get("detail") == null);
        
        // 去重相同时间戳的问题
        List<Map<String, Object>> deduplicatedErrors = deduplicateSameTimestamp(filteredErrors);
        
        // 如果需要，合并短时间内出现的相同问题
        List<Map<String, Object>> result;
        if (mergeShortTimeErrors) {
            result = mergeShortTimeErrors(deduplicatedErrors, timeIntervalHours);
            
            logger.info("设备[{}]时间间隔合并后，最终返回{}个错误", deviceId, result.size());
        } else {
            result = deduplicatedErrors;
            logger.info("设备[{}]跳过时间间隔合并，保持{}个错误", deviceId, result.size());
        }
        
        return result;
    }
    
    /**
     * 去除时间戳完全相同的重复错误
     * 对于时间戳和详情都相同的错误，只保留一条
     * 这个步骤是必须的，不受参数控制
     *
     * @param errors 原始错误集合
     * @return 去重后的错误集合
     */
    private List<Map<String, Object>> deduplicateSameTimestamp(List<Map<String, Object>> errors) {
        if (errors == null || errors.isEmpty()) {
            return errors;
        }
        
        logger.info("开始时间戳去重，原始错误数量: {}", errors.size());
        
        // 用于存储去重后的结果
        List<Map<String, Object>> result = new ArrayList<>();
        // 用于记录已处理的错误详情、类型和时间
        Set<String> processedErrors = new HashSet<>();
        
        // 统计「休闲玩乐」相关错误
        int playErrorCount = 0;
        int duplicatePlayErrorCount = 0;
        
        for (Map<String, Object> error : errors) {
            String detail = (String) error.get("detail");
            String type = (String) error.get("type");
            Object timeObj = error.get("time");
            
            boolean isPlayRelated = "应用问题".equals(type) && detail != null && detail.contains("休闲玩乐");
            
            if (detail == null || timeObj == null || type == null) {
                // 如果缺少必要信息，直接添加到结果中
                result.add(error);
                logger.info("缺少必要信息的错误，直接添加: {}", error);
                continue;
            }
            
            String timeStr;
            // 处理Date类型的时间字段
            if (timeObj instanceof Date) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                timeStr = sdf.format((Date) timeObj);
            } else {
                timeStr = timeObj.toString();
            }
            
            // 创建组合键：时间戳 + 类型 + 详情
            String key = timeStr + "|" + type + "|" + detail;
            
            // 检查是否已处理过相同时间戳、类型和详情的错误
            if (!processedErrors.contains(key)) {
                result.add(error);
                processedErrors.add(key);
                if (isPlayRelated) {
                    playErrorCount++;
                    logger.info("添加了「休闲玩乐」错误: {} - 时间: {}", detail, timeStr);
                }
            } else {
                if (isPlayRelated) {
                    duplicatePlayErrorCount++;
                    logger.info("跳过重复的「休闲玩乐」错误: {} - 时间: {}", detail, timeStr);
                }
            }
        }
        
        logger.info("时间戳去重完成，原始数量: {}, 去重后数量: {}, 「休闲玩乐」错误: {}, 重复的「休闲玩乐」错误: {}", 
                  errors.size(), result.size(), playErrorCount, duplicatePlayErrorCount);
        
        return result;
    }
    
    /**
     * 合并短时间内出现的相同问题
     * 对于在指定时间间隔内出现的相同类型和描述的问题，只保留最早的一条
     *
     * @param errors 原始错误集合
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @return 合并后的错误集合
     */
    private List<Map<String, Object>> mergeShortTimeErrors(List<Map<String, Object>> errors, int timeIntervalHours) {
        if (errors == null || errors.isEmpty()) {
            return errors;
        }
        
        logger.info("开始时间间隔合并，原始错误数量: {}, 时间间隔: {}小时", errors.size(), timeIntervalHours);
        
        // 按时间排序
        errors.sort((a, b) -> {
            Date timeA = (Date) a.get("time");
            Date timeB = (Date) b.get("time");
             return timeA.compareTo(timeB);
        });
        
        // 用于存储合并后的结果
        List<Map<String, Object>> result = new ArrayList<>();
        // 用于记录已处理的问题类型和描述
        Map<String, Date> processedErrors = new HashMap<>();
        
        // 统计「休闲玩乐」相关错误
        int playErrorCount = 0;
        int mergedPlayErrorCount = 0;
        
        for (Map<String, Object> error : errors) {
            String detail = (String) error.get("detail");
            String type = (String) error.get("type");
            Date errorTime = (Date) error.get("time");
            
            boolean isPlayRelated = "应用问题".equals(type) && detail != null && detail.contains("休闲玩乐");
            
            if (detail == null || errorTime == null || type == null) {
                // 如果缺少必要信息，直接添加到结果中
                result.add(error);
                logger.info("缺少必要信息的错误，直接添加: {}", error);
                continue;
            }
            
            // 创建问题标识：类型 + 详情
            String errorKey = type + "|" + detail;
            
            // 检查是否已处理过相同类型和描述的问题
            Date lastProcessedTime = processedErrors.get(errorKey);
            
            if (lastProcessedTime == null) {
                // 如果是第一次遇到这个问题，直接添加并记录时间
                result.add(error);
                processedErrors.put(errorKey, errorTime);
                if (isPlayRelated) {
                    playErrorCount++;
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    logger.info("添加了首次出现的「休闲玩乐」错误: {} - 时间: {}", detail, sdf.format(errorTime));
                }
            } else {
                // 计算与上次处理时间的时间差（小时）
                long timeDiffHours = (errorTime.getTime() - lastProcessedTime.getTime()) / (60 * 60 * 1000);
                
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 如果时间差超过指定间隔，添加这个新问题
                if (timeDiffHours >= timeIntervalHours) {
                     result.add(error);
                     processedErrors.put(errorKey, errorTime);
                    if (isPlayRelated) {
                        playErrorCount++;
                        logger.info("添加了间隔{}小时后的「休闲玩乐」错误: {} - 时间: {}, 上次时间: {}", 
                                   timeDiffHours, detail, sdf.format(errorTime), sdf.format(lastProcessedTime));
                    }
                } else {
                    if (isPlayRelated) {
                        mergedPlayErrorCount++;
                        logger.info("合并了间隔{}小时内的「休闲玩乐」错误: {} - 时间: {}, 上次时间: {}", 
                                   timeDiffHours, detail, sdf.format(errorTime), sdf.format(lastProcessedTime));
                    }
                }
            }
        }
        
        logger.info("时间间隔合并完成，原始数量: {}, 合并后数量: {}, 「休闲玩乐」错误: {}, 被合并的「休闲玩乐」错误: {}", 
                  errors.size(), result.size(), playErrorCount, mergedPlayErrorCount);
        
        return result;
    }
    
    /**
     * 从remarks中提取应用问题和测试问题信息
     * 解析各种格式的问题记录，提取时间、详情、图片URL、问题类型和轮次号
     *
     * @param remarks HEART日志的remarks字段
     * @param createdAt HEART日志的创建时间
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志
     * @return 问题列表，包含应用问题和测试问题
     */
    private List<Map<String, Object>> extractAppErrors(String remarks, Date createdAt, boolean onlyDevice2250) {
        List<Map<String, Object>> errors = new ArrayList<>();
        
        // 检查remarks是否为空
        if (remarks == null || remarks.isEmpty()) {
            return errors;
        }
        
        // 【日志点1】: 检查设备ID过滤条件
        boolean containsTargetDevice = remarks.contains("【MBP-PVFCQ0LQ62-2250】");
        boolean containsActualDevice = remarks.contains("【MBP-D95QHDYH5K-2250】");
        logger.info("检查设备ID: onlyDevice2250={}, 包含目标ID={}, 包含实际ID={}, remarks长度={}",
               onlyDevice2250, containsTargetDevice, containsActualDevice, remarks.length());
        
        // 如果 onlyDevice2250 为 true，检查 remarks 是否包含 "【MBP-PVFCQ0LQ62-2250】"
        if (onlyDevice2250 && !containsTargetDevice) {
            logger.info("由于onlyDevice2250=true且不包含目标设备ID，跳过此日志");
            return errors;
        }
        
        // 将remarks中的"|"替换为换行符，便于按行解析
        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        
        boolean inRecentProblemSection = false;
        Map<String, Object> currentError = null;
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            
            // 检测是否进入"最近问题记录"部分
            if (line.contains("最近问题记录：") || line.contains("最近问题记录:")) {
                inRecentProblemSection = true;
                logger.info("开始解析最近问题记录部分");
                continue;
            }
            
            // 在"最近问题记录"部分中查找应用问题和测试问题
            if (inRecentProblemSection) {
                // 检测多种问题行格式
                boolean isProblemLine = false;
                String errorTime = null;
                String problemType = null;
                
                // 格式1: "3. 应用问题 2025-03-18 17:44:40"
                if (line.matches("\\d+\\. 应用问题 .*")) {
                    isProblemLine = true;
                    errorTime = line.substring(line.indexOf("应用问题") + 4).trim();
                    problemType = "应用问题";
                }
                // 格式2: "[应用问题] 2025-03-18 17:44:40"
                else if (line.contains("[应用问题]")) {
                    isProblemLine = true;
                    errorTime = line.substring(line.indexOf("[应用问题]") + 6).trim();
                    problemType = "应用问题";
                }
                // 格式3: "应用问题 2025-03-18 17:44:40"
                else if (line.startsWith("应用问题 ")) {
                        isProblemLine = true;
                        errorTime = line.substring(4).trim();
                        problemType = "应用问题";
                }
                // 格式4: "1. 测试问题 2025-03-23 10:54:30"
                else if (line.matches("\\d+\\. 测试问题 .*")) {
                    isProblemLine = true;
                    errorTime = line.substring(line.indexOf("测试问题") + 4).trim();
                    problemType = "测试问题";
                }
                // 格式5: "[测试问题] 2025-03-23 10:54:30"
                else if (line.contains("[测试问题]")) {
                    isProblemLine = true;
                    errorTime = line.substring(line.indexOf("[测试问题]") + 6).trim();
                    problemType = "测试问题";
                }
                // 格式6: "测试问题 2025-03-23 10:54:30"
                else if (line.startsWith("测试问题 ")) {
                        isProblemLine = true;
                        errorTime = line.substring(4).trim();
                        problemType = "测试问题";
                }

                // 如果是问题行，开始新的错误记录
                if (isProblemLine) {
                    logger.info("发现问题行: {} - {}", problemType, errorTime);

                    // 如果之前有未完成的错误记录，且包含详情，则添加到列表
                    if (currentError != null && currentError.containsKey("detail")) {
                        errors.add(currentError);
                        logger.info("添加已解析的错误: {} - {}", currentError.get("type"), currentError.get("detail"));
                    }

                    // 创建新的错误记录
                    currentError = new HashMap<>();
                    // 设置问题类型
                    currentError.put("type", problemType);

                    // 尝试从文本中解析出日期时间
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        // 提取日期时间部分
                        String dateTimeStr = extractDateTime(errorTime);
                        if (dateTimeStr != null) {
                            Date parsedDate = sdf.parse(dateTimeStr);
                            currentError.put("time", parsedDate);
                            logger.info("成功解析时间戳: {}", dateTimeStr);
                        } else {
                            // 如果无法解析，使用日志创建时间作为默认值
                            currentError.put("time", createdAt);
                            logger.info("无法从'{}' 解析时间戳，使用日志创建时间: {}", errorTime, sdf.format(createdAt));
                        }
                    } catch (Exception e) {
                        // 如果解析失败，使用日志创建时间作为默认值
                        currentError.put("time", createdAt);
                        logger.info("解析时间戳异常，使用日志创建时间: {}", e.getMessage());
                    }

                    continue;
                }

                // 如果当前正在处理一个问题，检查后续行获取详情和图片URL
                if (currentError != null) {
                    // 检查是否包含轮次信息行（如：轮次：12）
                    if (line.matches("轮次：\\d+.*") || line.matches("轮次:\\d+.*")) {
                        try {
                            // 提取轮次号
                            String roundNumberStr = line.replaceAll("轮次[：:]", "").trim();
                            // 如果轮次信息包含其他内容（例如"轮次：12345 | 页面：xxx"），提取数字部分
                            if (roundNumberStr.contains(" ") || roundNumberStr.contains("|")) {
                                roundNumberStr = roundNumberStr.split("\\s+")[0].split("\\|")[0].trim();
                            }
                            int roundNumber = Integer.parseInt(roundNumberStr);
                            currentError.put("roundNumber", roundNumber);
                            logger.info("解析到轮次号: {}", roundNumber);
                        } catch (NumberFormatException e) {
                            logger.error("解析轮次号失败: {}", line);
                        }
                        continue;
                    }
                    
                    // 检查是否包含详情标识
                    if (line.contains("详情：") || line.contains("详情:")) {
                        String prefix = line.contains("详情：") ? "详情：" : "详情:";
                        String detail = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                        currentError.put("detail", detail);
                        logger.info("解析到错误详情: {}", detail);
                        continue;
                    }
                    
                    // 检查是否包含图片URL（通常在单独一行）
                    if (line.startsWith("http://") || line.startsWith("https://") || line.startsWith("ttp://")) {
                        currentError.put("imageUrl", line);
                        logger.info("解析到图片URL: {}", line);
                        continue;
                    }
                    
                    // 如果遇到空行或新的问题记录编号行，当前问题记录结束
                    if (line.isEmpty() || line.matches("\\d+\\..*")) {
                        if (currentError.containsKey("detail")) {
                            errors.add(currentError);
                            logger.info("遇到分隔符，添加错误: {} - {}", currentError.get("type"), currentError.get("detail"));
                        }
                        currentError = null;
                    } 
                    // 否则将内容追加到详情中（可能是多行详情）
                    else if (!line.contains("最近问题记录") && !line.isEmpty()) {
                        String detail = (String) currentError.getOrDefault("detail", "");
                        if (!detail.isEmpty()) {
                            detail += "\n";
                        }
                        detail += line;
                        currentError.put("detail", detail);
                        logger.info("追加到错误详情: {}", line);
                    }
                }
            }
        }
        
        // 最后检查是否还有未处理的错误记录
        if (currentError != null && currentError.containsKey("detail")) {
            errors.add(currentError);
            logger.info("添加最后一个解析的错误: {} - {}", currentError.get("type"), currentError.get("detail"));
        }
        
        // 【日志点2】: 打印提取结果摘要
        logger.info("从日志中总共提取了{}个错误", errors.size());
        
        return errors;
    }
    
    /**
     * 从错误时间字符串中提取标准格式的日期时间
     * 
     * @param timeStr 包含日期时间的字符串
     * @return 标准格式的日期时间字符串，如无法提取则返回null
     */
    private String extractDateTime(String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) {
            return null;
        }
        
        // 尝试匹配标准的日期时间格式：yyyy-MM-dd HH:mm:ss
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2})");
        java.util.regex.Matcher matcher = pattern.matcher(timeStr);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }

    /**
     * 高级分页查询检查记录
     * 支持根据日志级别筛选和排序控制
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param sortAsc 是否按创建时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @Override
    public IPage<CategoryInspection> advancedPageInspections(Page<CategoryInspection> page, String deviceId, String logLevel, Boolean sortAsc) {
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_ID", deviceId);
        }
        
        // 如果指定了日志级别，添加条件
        if (logLevel != null && !logLevel.isEmpty()) {
            queryWrapper.eq("LOG_LEVEL", logLevel);
        }
        
        // 根据sortAsc参数决定排序方式
        if (sortAsc != null && sortAsc) {
            queryWrapper.orderByAsc("Created_At"); // 正序排序
        } else {
            queryWrapper.orderByDesc("Created_At"); // 默认倒序排序
        }
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 带时间范围的高级分页查询检查记录
     * 支持根据时间范围、设备ID、日志级别进行筛选和排序控制
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param sortAsc 是否按创建时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @Override
    public IPage<CategoryInspection> timeRangePageInspections(Page<CategoryInspection> page, String deviceId, 
            String logLevel, Date startTime, Date endTime, Boolean sortAsc) {
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_ID", deviceId);
        }
        
        // 如果指定了日志级别，添加条件
        if (logLevel != null && !logLevel.isEmpty()) {
            queryWrapper.eq("LOG_LEVEL", logLevel);
        }
        
        // 添加时间范围条件
        if (startTime != null) {
            queryWrapper.ge("Inspection_Time", startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le("Inspection_Time", endTime);
        }
        
        // 根据sortAsc参数决定排序方式
        if (sortAsc != null && sortAsc) {
            queryWrapper.orderByAsc("Created_At"); // 正序排序
        } else {
            queryWrapper.orderByDesc("Created_At"); // 默认倒序排序
        }
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 高级查询接口，支持多条件组合查询
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param remarks 备注内容模糊查询（可选）
     * @param sortAsc 是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @Override
    public IPage<CategoryInspection> advancedQueryInspections(Page<CategoryInspection> page, String deviceId, 
            String logLevel, Date startTime, Date endTime, String remarks, Boolean sortAsc) {
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_ID", deviceId);
        }
        
        // 如果指定了日志级别，添加条件
        if (logLevel != null && !logLevel.isEmpty()) {
            queryWrapper.eq("LOG_LEVEL", logLevel);
        }
        
        // 添加时间范围条件
        if (startTime != null) {
            queryWrapper.ge("Inspection_Time", startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le("Inspection_Time", endTime);
        }
        
        // 添加备注模糊查询条件
        if (remarks != null && !remarks.isEmpty()) {
            queryWrapper.like("Remarks", remarks);
        }
        
        // 根据sortAsc参数决定排序方式
        if (sortAsc != null && sortAsc) {
            queryWrapper.orderByAsc("Inspection_Time"); // 正序排序
        } else {
            queryWrapper.orderByDesc("Inspection_Time"); // 默认倒序排序
        }
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据图片路径查询记录及其设备前N条INFO级别日志
     * 
     * @param imagePath 图片路径
     * @param infoLogCount 要获取的INFO级别日志数量
     * @return 包含图片记录和对应设备的INFO日志的结果集
     */
    @Override
    public Map<String, Object> getImageRelatedRecords(String imagePath, int infoLogCount) {
        Map<String, Object> result = new HashMap<>();
        // 查询包含此图片路径的所有记录，按 createAt 降序排列，取最新一条
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryInspection::getImagePath, imagePath)
                    .orderByDesc(CategoryInspection::getCreatedAt)
                    .last("LIMIT 1");
        CategoryInspection imageRecord = getOne(queryWrapper);
        result.put("imageRecord", imageRecord);
        // 如果找不到记录，直接返回
        if (imageRecord == null) {
            result.put("infoLogs", Collections.emptyList());
            return result;
        }
        // 获取该记录的设备ID
        String deviceId = imageRecord.getDeviceId();
        result.put("deviceId", deviceId);
        // 获取创建时间
        Date createdAt = imageRecord.getCreatedAt();
        // 查询此设备在该记录之前的N条INFO级别日志
        LambdaQueryWrapper<CategoryInspection> infoLogsWrapper = new LambdaQueryWrapper<>();
        infoLogsWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                       .eq(CategoryInspection::getLogLevel, "INFO")
                       .lt(createdAt != null, CategoryInspection::getCreatedAt, createdAt)
                       .orderByDesc(CategoryInspection::getCreatedAt)
                       .last("LIMIT " + infoLogCount);
        List<CategoryInspection> infoLogs = list(infoLogsWrapper);
        // 将结果按时间升序排序
        Collections.sort(infoLogs, Comparator.comparing(CategoryInspection::getCreatedAt));
        result.put("infoLogs", infoLogs);
        result.put("count", infoLogs.size());
        result.put("requestedCount", infoLogCount);
        return result;
    }

    /**
     * 执行自定义SQL查询
     * 这个方法用于执行安全过滤后的SQL查询
     * 
     * @param sql 要执行的SQL语句
     * @param params SQL参数，用于防止SQL注入
     * @return 查询结果列表
     */
    @Override
    public List<Map<String, Object>> executeSafeSql(String sql, Map<String, Object> params) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }
        
        // 安全检查：不允许执行非查询语句
        String normalizedSql = sql.trim().toLowerCase();
        if (!normalizedSql.startsWith("select")) {
            throw new IllegalArgumentException("只允许执行SELECT查询语句");
        }
        
        // 禁止执行的危险关键字
        String[] forbiddenKeywords = {
            "delete", "update", "insert", "drop", "alter", "truncate", 
            "create", "exec", "execute", "xp_", "sp_", "syscolumns", 
            "--", "/*", "*/", ";--", "';", "char(", "convert("
        };
        
        // 检查SQL是否包含危险关键字
        for (String keyword : forbiddenKeywords) {
            if (normalizedSql.contains(keyword)) {
                throw new IllegalArgumentException("SQL语句包含不允许的关键字：" + keyword);
            }
        }
        
        // 如果查询包含limit和offset，则检查其合理性
        if (normalizedSql.contains("limit")) {
            // 确保limit后面的值是合理的，不超过1000
            try {
                // 简单检查，实际实现可能需要更复杂的解析
                int limitPos = normalizedSql.lastIndexOf("limit");
                if (limitPos > 0) {
                    String limitPart = normalizedSql.substring(limitPos + 5).trim();
                    // 提取数字部分
                    String[] parts = limitPart.split("\\s+");
                    if (parts.length > 0) {
                        int limit = Integer.parseInt(parts[0]);
                        if (limit > 1000) {
                            throw new IllegalArgumentException("查询结果数量限制不能超过1000");
                        }
                    }
                }
            } catch (NumberFormatException e) {
                // 无法解析数字，可能是参数化的，暂不处理
            }
        } else {
            // 如果没有指定limit，则自动添加，避免返回过多数据
            sql += " LIMIT 1000";
        }
        
        // 使用MyBatis-Plus的自定义SQL执行功能
        QueryWrapper<CategoryInspection> queryWrapper = new QueryWrapper<>();
        if (params != null && !params.isEmpty()) {
            // 将参数转换为MyBatis-Plus的条件
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                queryWrapper.eq(entry.getKey(), entry.getValue());
            }
        }
        
        // 执行自定义SQL查询（注意：这里使用了MyBatis-Plus的selectMaps方法，实际中可能需要更复杂的处理）
        // 通过selectMaps方法执行自定义SQL会更安全，因为它会自动处理参数化查询
        // 这里简化处理，假设params是查询条件而不是SQL参数
        
        // 实际执行方法，这里需要根据具体情况调整
        // 警告：这里的sql参数需要谨慎处理，实际项目中建议使用参数化查询
        try {
            return baseMapper.selectMaps(queryWrapper.apply(sql));
        } catch (Exception e) {
            logger.error("执行自定义SQL查询失败: {}, SQL: {}", e.getMessage(), sql, e);
            throw new RuntimeException("执行SQL查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量条件删除检查记录
     * 支持按日志级别、图片路径、设备ID、时间范围等条件组合删除
     *
     * @param logLevel 日志级别（可选）
     * @param deviceId 设备ID（可选）
     * @param hasImagePath 是否有图片路径（可选）
     * @param emptyImagePath 图片路径是否为空（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 删除记录数限制，防止大量删除
     * @param skipSafetyCheck 是否跳过安全检查
     * @return 删除的记录数
     */
    @Override
    @Transactional
    public int batchDeleteInspections(String logLevel, String deviceId, Boolean hasImagePath, 
                                     Boolean emptyImagePath, Date startTime, Date endTime, 
                                     Integer limit, Boolean skipSafetyCheck) {
        
        logger.info("开始批量删除操作，条件：logLevel={}, deviceId={}, hasImagePath={}, emptyImagePath={}, " +
                   "startTime={}, endTime={}, limit={}, skipSafetyCheck={}", 
                   logLevel, deviceId, hasImagePath, emptyImagePath, startTime, endTime, limit, skipSafetyCheck);
        
        // 构建条件查询器
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加各种条件
        if (logLevel != null && !logLevel.isEmpty()) {
            queryWrapper.eq(CategoryInspection::getLogLevel, logLevel);
        }
        
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq(CategoryInspection::getDeviceId, deviceId);
        }
        
        // 处理图片路径条件
        if (Boolean.TRUE.equals(hasImagePath)) {
            queryWrapper.isNotNull(CategoryInspection::getImagePath)
                       .ne(CategoryInspection::getImagePath, "");
        }
        
        if (Boolean.TRUE.equals(emptyImagePath)) {
            queryWrapper.and(wrapper -> wrapper
                             .isNull(CategoryInspection::getImagePath)
                             .or()
                             .eq(CategoryInspection::getImagePath, ""));
        }
        
        // 处理时间范围
        if (startTime != null) {
            queryWrapper.ge(CategoryInspection::getCreatedAt, startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le(CategoryInspection::getCreatedAt, endTime);
        }
        
        // 如果设置了限制数量
        if (limit != null && limit > 0) {
            // 首先查询符合条件的记录总数
            long totalCount = count(queryWrapper);
            
            // 检查是否超过了限制
            if (totalCount > limit && !Boolean.TRUE.equals(skipSafetyCheck)) {
                logger.warn("符合删除条件的记录数({})超过了限制({}), 由于未跳过安全检查，删除操作已取消", totalCount, limit);
                throw new IllegalArgumentException("符合删除条件的记录数(" + totalCount + ")超过了限制(" + limit + "), 删除操作已取消。如需继续删除，请设置skipSafetyCheck=true");
            }
            
            // 如果记录数超过限制但跳过了安全检查，记录警告日志
            if (totalCount > limit) {
                logger.warn("符合删除条件的记录数({})超过了限制({}), 但已跳过安全检查，继续执行删除操作", totalCount, limit);
            }
            
            // 按照创建时间降序排序，保留最新的记录
            if (totalCount > limit && Boolean.TRUE.equals(skipSafetyCheck)) {
                // 查找出要保留的记录的最早时间，通过查询结果并排序
                Page<CategoryInspection> page = new Page<>(1, limit);
                page.setDesc("Created_At"); // 按时间降序排序，保留最新的记录
                
                Page<CategoryInspection> latestRecords = baseMapper.selectPage(page, queryWrapper);
                if (latestRecords.getRecords().size() > 0) {
                    // 获取符合条件的limit条最新记录中的最早时间
                    Date earliestTimeToKeep = latestRecords.getRecords()
                                                       .get(latestRecords.getRecords().size() - 1)
                                                       .getCreatedAt();
                    
                    // 修改查询条件，只删除早于该时间的记录
                    queryWrapper.lt(CategoryInspection::getCreatedAt, earliestTimeToKeep);
                }
            }
        }
        
        // 执行删除操作，并返回删除的记录数
        int deletedCount = baseMapper.delete(queryWrapper);
        logger.info("批量删除操作完成，共删除{}条记录", deletedCount);
        
        return deletedCount;
    }

    /**
     * 从错误详情中提取问题名称
     * 去除图片URL等后缀信息，只保留问题的核心描述
     * 
     * @param detail 完整的错误详情
     * @return 提取的问题名称
     */
    private String extractProblemName(String detail) {
        if (detail == null || detail.isEmpty()) {
            return "";
        }
        
        // 处理包含"图片URL"标记的情况
        int urlIndex = -1;
        if (detail.contains("图片URL：") || detail.contains("图片URL:")) {
            int labelIdx = detail.indexOf("图片URL");
            if (labelIdx != -1) {
                String label = detail.contains("图片URL：") ? "图片URL：" : "图片URL:";
                urlIndex = labelIdx;
                // 纯文本描述是URL之前的部分
                if (urlIndex > 0) {
                    detail = detail.substring(0, urlIndex).trim();
                }
            }
        }
        
        // 如果没有"图片URL"标记，尝试查找HTTP URL
        if (urlIndex == -1) {
            int httpIdx = detail.indexOf("http://");
            int httpsIdx = detail.indexOf("https://");
            if (httpIdx != -1 || httpsIdx != -1) {
                // 找到URL的起始位置
                if (httpIdx != -1 && httpsIdx != -1) {
                    urlIndex = Math.min(httpIdx, httpsIdx);
                } else if (httpIdx != -1) {
                    urlIndex = httpIdx;
                } else {
                    urlIndex = httpsIdx;
                }
                
                // 纯文本描述是URL之前的部分
                if (urlIndex > 0) {
                    detail = detail.substring(0, urlIndex).trim();
                }
            }
        }
        
        // 如果有换行符，取第一行作为主要描述
        if (detail.contains("\n")) {
            detail = detail.split("\n")[0].trim();
        }
        
        // 去除末尾的标点符号或多余的空格
        detail = detail.replaceAll("[，。；,;.\\s]+$", "");
        
        return detail;
    }

    // 新增提取图片URL的方法
    /**
     * 从错误详情中提取图片URL
     * 
     * @param detail 完整的错误详情
     * @return 提取的图片URL，如果没有则返回null
     */
    private String extractImageUrl(String detail) {
        if (detail == null || detail.isEmpty()) {
            return null;
        }
        
        String url = null;
        
        // 处理包含"图片URL"标记的情况
        if (detail.contains("图片URL：") || detail.contains("图片URL:")) {
            int labelIdx = detail.indexOf("图片URL");
            if (labelIdx != -1) {
                String label = detail.contains("图片URL：") ? "图片URL：" : "图片URL:";
                int urlIndex = detail.indexOf(label) + label.length();
                // 从这个位置开始提取URL
                String urlPart = detail.substring(urlIndex).trim();
                // 如果URL后面有其他内容，可能需要查找换行符或空格作为终止
                int endIndex = urlPart.indexOf('\n');
                if (endIndex != -1) {
                    url = urlPart.substring(0, endIndex).trim();
                } else {
                    url = urlPart.trim();
                }
            }
        }
        
        // 如果找不到"图片URL"标记，尝试直接识别URL
        if (url == null) {
            int httpIdx = detail.indexOf("http://");
            int httpsIdx = detail.indexOf("https://");
            if (httpIdx != -1 || httpsIdx != -1) {
                // 找到URL的起始位置
                int urlIndex;
                if (httpIdx != -1 && httpsIdx != -1) {
                    urlIndex = Math.min(httpIdx, httpsIdx);
                } else if (httpIdx != -1) {
                    urlIndex = httpIdx;
                } else {
                    urlIndex = httpsIdx;
                }
                
                // 提取URL（假设URL在一行的末尾或单独一行）
                String urlPart = detail.substring(urlIndex);
                
                // 尝试各种可能的URL终止符
                int endIndex = -1;
                for (char c : new char[]{'\n', ' ', '|'}) {
                    int idx = urlPart.indexOf(c);
                    if (idx != -1 && (endIndex == -1 || idx < endIndex)) {
                        endIndex = idx;
                    }
                }
                
                if (endIndex != -1) {
                    url = urlPart.substring(0, endIndex).trim();
                } else {
                    url = urlPart.trim();
                }
            }
        }
        
        // 清理URL尾部可能存在的"|"字符和其他不需要的符号
        if (url != null) {
            // 去除URL尾部的"|"符号
            url = url.replaceAll("\\s*\\|\\s*$", "");
            // 去除可能的多余空格
            url = url.trim();
        }
        
        return url;
    }

    /**
     * 标记或取消标记指定时间范围内特定设备和备注内容的ERROR日志为误报(FP - False Positive)
     * 仅处理 imagePath 不为空的应用问题日志
     *
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param remarksKeyword 备注中的关键字
     * @param isFalsePositive 是否标记为误报 (true: 标记, false: 取消标记)
     * @return 更新的记录数
     */
    @Override
    @Transactional
    public int markFalsePositive(String deviceId, Date startTime, Date endTime, String remarksKeyword, boolean isFalsePositive) {
        logger.info("开始{}误报标记操作：设备={}, 时间范围=[{} - {}], 关键字={}, 标记={}", 
                   (isFalsePositive ? "添加" : "移除"), deviceId, startTime, endTime, remarksKeyword, isFalsePositive);
        
        // 构建基础查询条件
        LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                    .eq(CategoryInspection::getLogLevel, "ERROR") // 只处理ERROR级别的日志
                    .isNotNull(CategoryInspection::getImagePath) // 只处理应用问题（有图片路径）
                    .ne(CategoryInspection::getImagePath, "")      // 图片路径不为空字符串
                    .ge(CategoryInspection::getCreatedAt, startTime)
                    .le(CategoryInspection::getCreatedAt, endTime)
                    .like(CategoryInspection::getRemarks, remarksKeyword); // 备注包含关键字
        
        int updatedCount = 0;
        String fpPrefix = "FP "; // 误报标记前缀
        
        if (isFalsePositive) {
            // 标记为误报：在备注前面添加 "FP "
            // 1. 查询出所有符合条件且备注不以 "FP " 开头的记录
            queryWrapper.notLike(CategoryInspection::getRemarks, fpPrefix + "%" ); // 使用 not like 'FP %'
            List<CategoryInspection> recordsToMark = baseMapper.selectList(queryWrapper);
            
            if (recordsToMark.isEmpty()) {
                logger.info("没有需要标记为误报的记录");
                return 0;
            }
            
            logger.info("查询到 {} 条需要标记为误报的记录", recordsToMark.size());
            
            // 2. 逐条更新备注信息（MyBatis Plus 批量更新 UpdateWrapper 在这种场景下较复杂，逐条更新更清晰）
            for (CategoryInspection record : recordsToMark) {
                String originalRemarks = record.getRemarks();
                record.setRemarks(fpPrefix + originalRemarks);
                record.setUpdateTime(new Date()); // 更新修改时间
                if (baseMapper.updateById(record) > 0) {
                    updatedCount++;
                }
            }
            logger.info("成功标记 {} 条记录为误报", updatedCount);
            
        } else {
            // 取消标记误报：移除备注前面的 "FP "
            // 1. 查询出所有符合条件且备注以 "FP " 开头的记录
            queryWrapper.likeRight(CategoryInspection::getRemarks, fpPrefix); // 使用 like 'FP %'
            List<CategoryInspection> recordsToUnmark = baseMapper.selectList(queryWrapper);
            
            if (recordsToUnmark.isEmpty()) {
                logger.info("没有需要取消标记误报的记录");
                return 0;
            }
            
            logger.info("查询到 {} 条需要取消标记误报的记录", recordsToUnmark.size());
            
            // 2. 逐条更新备注信息
            for (CategoryInspection record : recordsToUnmark) {
                String originalRemarks = record.getRemarks();
                // 移除前缀 "FP "
                record.setRemarks(originalRemarks.substring(fpPrefix.length()));
                record.setUpdateTime(new Date()); // 更新修改时间
                if (baseMapper.updateById(record) > 0) {
                    updatedCount++;
                }
            }
            logger.info("成功取消标记 {} 条记录的误报状态", updatedCount);
        }
        
        return updatedCount;
    }
} 