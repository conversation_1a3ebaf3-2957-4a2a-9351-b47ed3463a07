package com.sankuai.mdp.compass.category.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.category.entity.CategoryInspection;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * CategoryInspection服务接口
 * 定义针对CategoryInspection实体的业务操作
 */
public interface CategoryInspectionService extends IService<CategoryInspection> {
    
    /**
     * 根据设备ID查询最新的检查记录
     * 
     * @param deviceId 设备ID
     * @return 最新的检查记录，如果不存在则返回null
     */
    CategoryInspection getLatestByDeviceId(String deviceId);
    
    /**
     * 根据时间范围查询检查记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的检查记录列表
     */
    List<CategoryInspection> getByTimeRange(Date startTime, Date endTime);
    
    /**
     * 添加检查记录
     * 
     * @param inspection 检查记录实体
     * @return 是否添加成功
     */
    boolean addInspection(CategoryInspection inspection);
    
    /**
     * 根据ID查询检查记录
     * 
     * @param inspectionId 检查记录ID
     * @return 检查记录实体
     */
    CategoryInspection getInspectionById(Long inspectionId);
    
    /**
     * 分页查询检查记录
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param excludeLogLevel 要排除的日志级别（可选）
     * @return 分页结果
     */
    IPage<CategoryInspection> pageInspections(Page<CategoryInspection> page, String deviceId, String excludeLogLevel);

    IPage<CategoryInspection> pageInspections(Page<CategoryInspection> page, String deviceId);

    /**
     * 更新检查记录
     * 
     * @param inspection 检查记录实体
     * @return 是否更新成功
     */
    boolean updateInspection(CategoryInspection inspection);
    
    /**
     * 根据ID删除检查记录
     * 
     * @param inspectionId 检查记录ID
     * @return 是否删除成功
     */
    boolean deleteInspection(Long inspectionId);
    
    /**
     * 根据设备ID删除检查记录
     * 
     * @param deviceId 设备ID
     * @return 删除的记录数
     */
    int deleteByDeviceId(String deviceId);
    
    /**
     * 直接新增一条检查记录到数据库
     * 用于测试目的
     * 
     * @param inspection 检查记录实体
     * @return 新增记录的ID
     */
    Integer newInspection(CategoryInspection inspection);
    
    /**
     * 获取指定日期之后有检查记录的所有设备
     *
     * @param startDate 开始日期
     * @return 设备列表，每个设备包含设备ID和最后检查时间
     */
    List<Map<String, Object>> getRecentDevices(Date startDate);
    
    /**
     * 获取指定日期之后有检查记录的所有设备，排除指定日志级别
     *
     * @param startDate 开始日期
     * @param excludeLogLevel 要排除的日志级别
     * @return 设备列表，每个设备包含设备ID和最后检查时间
     */
    List<Map<String, Object>> getRecentDevices(Date startDate, String excludeLogLevel);
    
    /**
     * 获取所有设备的最新心跳日志
     *
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志，默认为true
     * @return 心跳日志列表，每个设备的最新心跳日志
     */
    List<Map<String, Object>> getLatestHeartbeatLogs(boolean onlyDevice2250);
    
    /**
     * 获取所有设备的最新心跳日志
     *
     * @return 心跳日志列表，每个设备的最新心跳日志
     */
    default List<Map<String, Object>> getLatestHeartbeatLogs() {
        return getLatestHeartbeatLogs(true);
    }
    
    /**
     * 清理检查记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @return 删除的记录数
     */
    int cleanupInspectionLogs(int keepRecords);
    
    /**
     * 清理检查记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @param excludeLogLevel 要排除的日志级别，不会被清理
     * @return 删除的记录数
     */
    int cleanupInspectionLogs(int keepRecords, String excludeLogLevel);
    
    /**
     * 获取设备在指定天数内的应用错误集合
     * 从HEART级别的日志中提取应用问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId 设备ID
     * @param days 查询的天数范围，如7表示最近7天
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志，默认为true
     * @return 应用错误集合，包含时间、详情和图片URL
     */
    List<Map<String, Object>> getDeviceAppErrors(String deviceId, int days, boolean mergeShortTimeErrors, int timeIntervalHours, boolean onlyDevice2250);
    
    /**
     * 获取设备在指定天数内的应用错误集合
     * 从HEART级别的日志中提取应用问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId 设备ID
     * @param days 查询的天数范围，如7表示最近7天
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @return 应用错误集合，包含时间、详情和图片URL
     */
    default List<Map<String, Object>> getDeviceAppErrors(String deviceId, int days, boolean mergeShortTimeErrors, int timeIntervalHours) {
        return getDeviceAppErrors(deviceId, days, mergeShortTimeErrors, timeIntervalHours, true);
    }
    
    /**
     * 获取设备在指定时间范围内的应用错误集合
     * 从HEART级别的日志中提取应用问题和测试问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId 设备ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志，默认为true
     * @return 应用错误和测试错误集合，包含时间、详情和图片URL
     */
    List<Map<String, Object>> getDeviceAppErrors(String deviceId, Date startDate, Date endDate, boolean mergeShortTimeErrors, int timeIntervalHours, boolean onlyDevice2250);
    
    /**
     * 获取设备在指定时间范围内的应用错误集合
     * 从HEART级别的日志中提取应用问题和测试问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId 设备ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours 合并的时间间隔（小时）
     * @return 应用错误和测试错误集合，包含时间、详情和图片URL
     */
    default List<Map<String, Object>> getDeviceAppErrors(String deviceId, Date startDate, Date endDate, boolean mergeShortTimeErrors, int timeIntervalHours) {
        return getDeviceAppErrors(deviceId, startDate, endDate, mergeShortTimeErrors, timeIntervalHours, true);
    }
    
    /**
     * 高级分页查询检查记录
     * 支持根据日志级别筛选和排序控制
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param sortAsc 是否按创建时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    IPage<CategoryInspection> advancedPageInspections(Page<CategoryInspection> page, String deviceId, String logLevel, Boolean sortAsc);

    /**
     * 带时间范围的高级分页查询检查记录
     * 支持根据时间范围、设备ID、日志级别进行筛选和排序控制
     *
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param sortAsc 是否按创建时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    IPage<CategoryInspection> timeRangePageInspections(Page<CategoryInspection> page, String deviceId, 
        String logLevel, Date startTime, Date endTime, Boolean sortAsc);

    /**
     * 高级查询接口，支持多条件组合查询
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param remarks 备注内容模糊查询（可选）
     * @param sortAsc 是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    IPage<CategoryInspection> advancedQueryInspections(Page<CategoryInspection> page, String deviceId, 
        String logLevel, Date startTime, Date endTime, String remarks, Boolean sortAsc);

    /**
     * 根据图片路径查询记录及其设备前N条INFO级别日志
     * 
     * @param imagePath 图片路径
     * @param count 要获取的INFO级别日志数量，默认为5条
     * @return 包含图片记录和对应设备的INFO日志的结果集
     */
    Map<String, Object> getImageRelatedRecords(String imagePath, int count);
    
    /**
     * 根据图片路径查询记录及其设备前5条INFO级别日志
     * 
     * @param imagePath 图片路径
     * @return 包含图片记录和对应设备的INFO日志的结果集
     */
    default Map<String, Object> getImageRelatedRecords(String imagePath) {
        return getImageRelatedRecords(imagePath, 5);
    }

    /**
     * 执行自定义SQL查询
     * 这个方法用于执行安全过滤后的SQL查询
     * 
     * @param sql 要执行的SQL语句
     * @param params SQL参数，用于防止SQL注入
     * @return 查询结果列表
     */
    List<Map<String, Object>> executeSafeSql(String sql, Map<String, Object> params);
    
    /**
     * 批量条件删除检查记录
     * 支持按日志级别、图片路径、设备ID、时间范围等条件组合删除
     *
     * @param logLevel 日志级别（可选）
     * @param deviceId 设备ID（可选）
     * @param hasImagePath 是否有图片路径（可选）
     * @param emptyImagePath 图片路径是否为空（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 删除记录数限制，防止大量删除
     * @param skipSafetyCheck 是否跳过安全检查
     * @return 删除的记录数
     */
    int batchDeleteInspections(String logLevel, String deviceId, Boolean hasImagePath, Boolean emptyImagePath, Date startTime, Date endTime, Integer limit, Boolean skipSafetyCheck);

    /**
     * 标记或取消标记指定时间范围内特定设备和备注内容的ERROR日志为误报(FP - False Positive)
     * 仅处理 imagePath 不为空的应用问题日志
     *
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param remarksKeyword 备注中的关键字
     * @param isFalsePositive 是否标记为误报 (true: 标记, false: 取消标记)
     * @return 更新的记录数
     */
    int markFalsePositive(String deviceId, Date startTime, Date endTime, String remarksKeyword, boolean isFalsePositive);
} 