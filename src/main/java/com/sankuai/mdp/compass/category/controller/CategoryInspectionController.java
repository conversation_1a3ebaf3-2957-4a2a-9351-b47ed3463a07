package com.sankuai.mdp.compass.category.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sankuai.mdp.compass.category.entity.CategoryInspection;
import com.sankuai.mdp.compass.category.service.CategoryInspectionService;
import com.sankuai.mdp.compass.category.service.WeekReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.mdp.compass.category.util.MultiFormatDateDeserializer;

/**
 * CategoryInspection控制器
 * 提供RESTful API接口，用于对CategoryInspection数据的CRUD操作
 * 同时提供设备统计相关接口
 */
@RestController
@RequestMapping("compass/api/category/inspection")
public class CategoryInspectionController {

    private static final Logger logger = LoggerFactory.getLogger(CategoryInspectionController.class);

    // 数据库字段长度常量
    private static final int MAX_DEVICE_ID_LENGTH = 100;
    private static final int MAX_IMAGE_PATH_LENGTH = 500;
    private static final int MAX_REMARKS_LENGTH = 2000;
    private static final int MAX_LOG_LEVEL_LENGTH = 10;

    @Autowired
    private CategoryInspectionService categoryInspectionService;

    @Autowired
    private WeekReportService weekReportService;

    /**
     * 时区修正工具方法
     * 修复数据库查询的时区偏移问题（加8小时）
     * 
     * @param originalTime 原始时间
     * @return 修正后的时间（加8小时以匹配数据库的UTC时间）
     */
    private Date fixTimezone(Date originalTime) {
        if (originalTime == null) {
            return null;
        }
        
        // 修复数据库查询的时区偏移：数据库存储为UTC时间，但查询时需要加8小时
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(originalTime.getTime())); // 创建副本
        calendar.add(Calendar.HOUR_OF_DAY, 8);
        return calendar.getTime();
    }

    /**
     * 创建检查记录
     *
     * @param inspection 检查记录实体
     * @return 创建结果
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> createInspection(@RequestBody CategoryInspection inspection) {
        Map<String, Object> result = new HashMap<>();

        // 数据校验
        Map<String, String> validationErrors = validateInspectionData(inspection);
        if (!validationErrors.isEmpty()) {
            result.put("success", false);
            result.put("message", "数据校验失败");
            result.put("errors", validationErrors);
            logger.warn("检查记录数据校验失败: {}, 数据: {}", validationErrors, inspection);
            return ResponseEntity.badRequest().body(result);
        }

        // 设置创建时间
        Date date = new Date();
        if (inspection.getCreatedAt() == null) {
            inspection.setCreatedAt(date);
        }
        if (inspection.getAddTime() == null) {
            inspection.setAddTime(date);
        }
        if (inspection.getUpdateTime() == null) {
            inspection.setUpdateTime(date);
        }
        if (inspection.getInspectionTime() == null) {
            inspection.setInspectionTime(date);
        }

        boolean success = false;
        try {
            success = categoryInspectionService.addInspection(inspection);
        } catch (Exception e) {
            logger.error("添加检查记录失败，数据: {}, 异常: {}", inspection, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "添加检查记录失败: " + e.getMessage());
            result.put("failedData", inspection);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }

        if (success) {
            logger.info("添加检查记录成功, ID: {}, 设备ID: {}", inspection.getInspectionId(), inspection.getDeviceId());
            result.put("success", true);
            result.put("message", "添加检查记录成功");
            result.put("data", inspection);
            return ResponseEntity.ok(result);
        } else {
            logger.error("添加检查记录失败，数据: {}", inspection);
            result.put("success", false);
            result.put("message", "添加检查记录失败");
            result.put("failedData", inspection);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 校验检查记录数据
     * 
     * @param inspection 检查记录实体
     * @return 校验错误信息，如果没有错误则返回空Map
     */
    private Map<String, String> validateInspectionData(CategoryInspection inspection) {
        Map<String, String> errors = new HashMap<>();

        // 检查必填字段
        if (inspection.getDeviceId() == null || inspection.getDeviceId().trim().isEmpty()) {
            errors.put("deviceId", "设备ID不能为空");
        } else if (inspection.getDeviceId().length() > MAX_DEVICE_ID_LENGTH) {
            errors.put("deviceId", "设备ID长度不能超过" + MAX_DEVICE_ID_LENGTH + "个字符");
        }

        // 图片路径可以为空，但如果不为空需要检查长度
        if (inspection.getImagePath() != null && !inspection.getImagePath().trim().isEmpty()
                && inspection.getImagePath().length() > MAX_IMAGE_PATH_LENGTH) {
            errors.put("imagePath", "图片路径长度不能超过" + MAX_IMAGE_PATH_LENGTH + "个字符");
        }

        // 检查可选字段长度
        if (inspection.getRemarks() != null && inspection.getRemarks().length() > MAX_REMARKS_LENGTH) {
            errors.put("remarks", "备注信息长度不能超过" + MAX_REMARKS_LENGTH + "个字符");
        }

        if (inspection.getLogLevel() != null && inspection.getLogLevel().length() > MAX_LOG_LEVEL_LENGTH) {
            errors.put("logLevel", "日志级别长度不能超过" + MAX_LOG_LEVEL_LENGTH + "个字符");
        }

        return errors;
    }

    /**
     * 根据ID查询检查记录
     *
     * @param inspectionId 检查记录ID
     * @return 检查记录
     */
    @GetMapping("/find/{inspectionId}")
    public ResponseEntity<Map<String, Object>> getInspectionById(@PathVariable Long inspectionId) {
        Map<String, Object> result = new HashMap<>();
        CategoryInspection inspection = categoryInspectionService.getInspectionById(inspectionId);

        if (inspection != null) {
            result.put("success", true);
            result.put("data", inspection);
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "未找到ID为" + inspectionId + "的检查记录");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }


    /**
     * 根据设备ID查询最新的检查记录
     *
     * @param deviceId 设备ID
     * @return 最新的检查记录
     */
    @GetMapping("/device/latest")
    public ResponseEntity<Map<String, Object>> getLatestByDeviceId(@RequestParam String deviceId) {
        Map<String, Object> result = new HashMap<>();
        CategoryInspection inspection = categoryInspectionService.getLatestByDeviceId(deviceId);

        if (inspection != null) {
            result.put("success", true);
            result.put("data", inspection);
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "未找到设备ID为" + deviceId + "的检查记录");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }



    /**
     * 更新检查记录
     *
     * @param inspectionId 检查记录ID
     * @param inspection   检查记录实体
     * @return 更新结果
     */
    @PutMapping("/update/{inspectionId}")
    public ResponseEntity<Map<String, Object>> updateInspection(
            @PathVariable Long inspectionId,
            @RequestBody CategoryInspection inspection) {

        Map<String, Object> result = new HashMap<>();
        inspection.setInspectionId(inspectionId);

        // 设置更新时间
        inspection.setUpdateTime(new Date());

        boolean success = categoryInspectionService.updateInspection(inspection);

        if (success) {
            result.put("success", true);
            result.put("message", "更新检查记录成功");
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "更新检查记录失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 根据ID删除检查记录
     *
     * @param inspectionId 检查记录ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{inspectionId}")
    public ResponseEntity<Map<String, Object>> deleteInspection(@PathVariable Long inspectionId) {
        Map<String, Object> result = new HashMap<>();
        boolean success = categoryInspectionService.deleteInspection(inspectionId);

        if (success) {
            result.put("success", true);
            result.put("message", "删除检查记录成功");
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "删除检查记录失败，可能记录不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 根据设备ID删除检查记录
     *
     * @param deviceId 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> deleteByDeviceId(@PathVariable String deviceId) {
        Map<String, Object> result = new HashMap<>();
        int count = categoryInspectionService.deleteByDeviceId(deviceId);

        result.put("success", true);
        result.put("message", "成功删除" + count + "条检查记录");
        result.put("count", count);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取近n天内有检查记录的所有设备列表
     * 通过Created_At时间戳筛选近n天的记录，提取并去重设备ID
     * 排除特殊设备ID "global"和日志级别为"HEART"的记录
     *
     * @param days 查询的天数范围，默认为3天
     * @return 设备列表，包含设备ID和最后检查时间
     */
    @GetMapping("/recent-devices")
    public ResponseEntity<Map<String, Object>> getRecentDevices(
            @RequestParam(required = false, defaultValue = "3") Integer days) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        // 计算n天前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date daysAgo = calendar.getTime();

        // 获取近n天的设备列表，排除"global"设备和"HEART"级别的日志
        List<Map<String, Object>> devices = categoryInspectionService.getRecentDevices(daysAgo, "HEART");

        // 过滤掉deviceId为"global"的记录
        devices.removeIf(device -> "global".equals(device.get("deviceId")));

        result.put("success", true);
        result.put("data", devices);
        result.put("count", devices.size());
        result.put("days", days);
        result.put("message", "成功获取近" + days + "天内的设备列表(不含global和心跳日志)");

        return ResponseEntity.ok(result);
    }

    /**
     * 获取各设备最新的心跳日志
     * 查询日志级别为"HEART"的最新记录，并按设备分组
     * 将remarks中的"|"替换为换行符，便于前端展示
     *
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志，默认为true
     * @return 设备心跳日志列表
     */
    @GetMapping("/heartbeat-logs")
    public ResponseEntity<Map<String, Object>> getHeartbeatLogs(
            @RequestParam(required = false, defaultValue = "true") Boolean onlyDevice2250) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有设备的最新心跳日志
        List<Map<String, Object>> heartbeatLogs = categoryInspectionService.getLatestHeartbeatLogs(onlyDevice2250);

        // 处理remarks字段，将"|"替换为换行符
        for (Map<String, Object> log : heartbeatLogs) {
            if (log.containsKey("remarks")) {
                String remarks = (String) log.get("remarks");
                if (remarks != null) {
                    // 将"|"替换为换行符
                    remarks = remarks.replace(" | ", "\n");
                    log.put("remarks", remarks);
                }
            }
        }

        result.put("success", true);
        result.put("data", heartbeatLogs);
        result.put("count", heartbeatLogs.size());
        result.put("message", "成功获取设备心跳日志" + (onlyDevice2250 ? "（仅设备2250）" : ""));

        return ResponseEntity.ok(result);
    }

    /**
     * 清理检查记录，只保留最新的10万条记录
     * 使用异步方式执行，不阻塞其他操作
     * 默认不清理HEART级别的日志
     * 
     * @return 清理操作的结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupInspectionLogs() {
        Map<String, Object> result = new HashMap<>();

        // 保留的记录数量
        final int KEEP_RECORDS = 100000;
        // 排除的日志级别
        final String EXCLUDE_LOG_LEVEL = "HEART";

        // 获取总记录数（排除HEART级别）
        LambdaQueryWrapper<CategoryInspection> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.ne(CategoryInspection::getLogLevel, EXCLUDE_LOG_LEVEL);
        long totalCount = categoryInspectionService.count(countWrapper);

        if (totalCount <= KEEP_RECORDS) {
            result.put("success", true);
            result.put("message", "当前非心跳日志记录数(" + totalCount + ")未超过保留阈值(" + KEEP_RECORDS + ")，无需清理");
            result.put("totalCount", totalCount);
            result.put("keepRecords", KEEP_RECORDS);
            result.put("excludeLogLevel", EXCLUDE_LOG_LEVEL);
            result.put("deletedCount", 0);
            return ResponseEntity.ok(result);
        }

        // 异步执行清理操作，排除HEART级别的日志
        int deletedCount = categoryInspectionService.cleanupInspectionLogs(KEEP_RECORDS, EXCLUDE_LOG_LEVEL);

        result.put("success", true);
        result.put("message", "清理操作已启动，将异步删除旧记录，保留最新的" + KEEP_RECORDS + "条非心跳日志记录");
        result.put("totalCount", totalCount);
        result.put("keepRecords", KEEP_RECORDS);
        result.put("excludeLogLevel", EXCLUDE_LOG_LEVEL);
        result.put("estimatedDeleteCount", totalCount - KEEP_RECORDS);

        return ResponseEntity.ok(result);
    }

    /**
     * 自定义清理检查记录
     * 允许指定保留的记录数量和要排除的日志级别
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords     要保留的记录数量，默认为10万
     * @param excludeLogLevel 要排除的日志级别，默认为HEART
     * @return 清理操作的结果
     */
    @PostMapping("/cleanup/custom")
    public ResponseEntity<Map<String, Object>> customCleanupInspectionLogs(
            @RequestParam(required = false, defaultValue = "100000") Integer keepRecords,
            @RequestParam(required = false, defaultValue = "HEART") String excludeLogLevel) {

        Map<String, Object> result = new HashMap<>();

        // 获取总记录数（排除指定日志级别）
        long totalCount;
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            LambdaQueryWrapper<CategoryInspection> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.ne(CategoryInspection::getLogLevel, excludeLogLevel);
            totalCount = categoryInspectionService.count(countWrapper);
        } else {
            totalCount = categoryInspectionService.count();
        }

        if (totalCount <= keepRecords) {
            result.put("success", true);
            result.put("message", "当前记录数(" + totalCount + ")未超过保留阈值(" + keepRecords + ")，无需清理");
            result.put("totalCount", totalCount);
            result.put("keepRecords", keepRecords);
            result.put("excludeLogLevel", excludeLogLevel);
            result.put("deletedCount", 0);
            return ResponseEntity.ok(result);
        }

        // 异步执行清理操作
        int deletedCount = categoryInspectionService.cleanupInspectionLogs(keepRecords, excludeLogLevel);

        result.put("success", true);
        result.put("message", "清理操作已启动，将异步删除旧记录，保留最新的" + keepRecords + "条记录" +
                (excludeLogLevel != null && !excludeLogLevel.isEmpty() ? "（排除" + excludeLogLevel + "级别）" : ""));
        result.put("totalCount", totalCount);
        result.put("keepRecords", keepRecords);
        result.put("excludeLogLevel", excludeLogLevel);
        result.put("estimatedDeleteCount", totalCount - keepRecords);

        return ResponseEntity.ok(result);
    }

    /**
     * 获取设备在指定天数内的应用错误集合
     * 从HEART级别的日志中提取应用问题信息
     * 支持按时间间隔合并相同问题
     *
     * @param deviceId             设备ID
     * @param days                 查询的天数范围，如7表示最近7天
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题
     * @param timeIntervalHours    合并的时间间隔（小时）
     * @param onlyDevice2250       是否只处理设备编号为2250的HEART日志，默认为true
     * @return 应用错误集合
     */
    @GetMapping("/device/errors")
    public ResponseEntity<Map<String, Object>> getDeviceAppErrors(
            @RequestParam String deviceId,
            @RequestParam(defaultValue = "7") Integer days,
            @RequestParam(defaultValue = "true") Boolean mergeShortTimeErrors,
            @RequestParam(defaultValue = "1") Integer timeIntervalHours,
            @RequestParam(required = false, defaultValue = "true") Boolean onlyDevice2250) {

        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        if (timeIntervalHours < 0) {
            result.put("success", false);
            result.put("message", "合并时间间隔不能为负数");
            return ResponseEntity.badRequest().body(result);
        }

        // 获取设备错误信息
        List<Map<String, Object>> errors = categoryInspectionService.getDeviceAppErrors(
                deviceId, days, mergeShortTimeErrors, timeIntervalHours, onlyDevice2250);

        // 处理错误信息，提取图片URL
        for (Map<String, Object> error : errors) {
            String detail = (String) error.get("detail");
            String url = "";

            if (detail != null) {
                // 查找"http://"或"https://"开头的URL
                int httpIndex = detail.indexOf("http://");
                int httpsIndex = detail.indexOf("https://");

                // 找到URL的起始位置
                int urlStartIndex = -1;
                if (httpIndex != -1 && httpsIndex != -1) {
                    // 两种协议都找到，取较早出现的
                    urlStartIndex = Math.min(httpIndex, httpsIndex);
                } else if (httpIndex != -1) {
                    urlStartIndex = httpIndex;
                } else if (httpsIndex != -1) {
                    urlStartIndex = httpsIndex;
                }

                // 如果找到了URL
                if (urlStartIndex != -1) {
                    // 提取URL，可能在"图片URL："之后
                    url = detail.substring(urlStartIndex).trim();

                    // 查找图片URL标记
                    int urlLabelIndex = detail.indexOf("图片URL");
                    if (urlLabelIndex != -1 && urlLabelIndex < urlStartIndex) {
                        // 如果"图片URL"标记在http开头之前，则删除标记及之后的内容
                        detail = detail.substring(0, urlLabelIndex).trim();
                    } else {
                        // 否则只删除URL部分
                        detail = detail.substring(0, urlStartIndex).trim();
                    }

                    error.put("detail", detail);
                }
            }

            // 添加url字段
            error.put("url", url);
        }

        result.put("success", true);
        result.put("data", errors);
        result.put("deviceId", deviceId);
        result.put("days", days);
        result.put("mergeShortTimeErrors", mergeShortTimeErrors);
        result.put("timeIntervalHours", timeIntervalHours);
        result.put("onlyDevice2250", onlyDevice2250);

        return ResponseEntity.ok(result);
    }

    /**
     * 高级查询接口
     * 支持多条件组合查询：设备ID、日志级别、时间范围、备注内容模糊匹配
     * 支持分页和排序控制
     *
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @param deviceId  设备ID（可选）
     * @param logLevel  日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @param remarks   备注内容模糊查询（可选）
     * @param sortAsc   是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @GetMapping("/query")
    public ResponseEntity<Map<String, Object>> queryInspections(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String logLevel,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String remarks,
            @RequestParam(required = false, defaultValue = "false") Boolean sortAsc) {

        Map<String, Object> result = new HashMap<>();
        Page<CategoryInspection> page = new Page<>(pageNum, pageSize);

        // 修复数据库查询的时区偏移：加8小时以匹配数据库的UTC时间
        Date correctedStartTime = fixTimezone(startTime);
        Date correctedEndTime = fixTimezone(endTime);

        // 调用服务层方法，传入修正后的查询参数
        IPage<CategoryInspection> pageResult = categoryInspectionService.advancedQueryInspections(
                page, deviceId, logLevel, correctedStartTime, correctedEndTime, remarks, sortAsc);

        result.put("success", true);
        result.put("data", pageResult);

        // 添加查询参数到结果中，方便前端了解当前查询条件（显示原始用户输入的时间）
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("pageNum", pageNum);
        queryParams.put("pageSize", pageSize);
        queryParams.put("deviceId", deviceId);
        queryParams.put("logLevel", logLevel);
        queryParams.put("startTime", startTime); // 显示用户原始输入时间
        queryParams.put("endTime", endTime);     // 显示用户原始输入时间
        queryParams.put("remarks", remarks);
        queryParams.put("sortAsc", sortAsc);
        result.put("queryParams", queryParams);

        return ResponseEntity.ok(result);
    }

    /**
     * 生成周度测试报告 (已更新统计口径)
     * 使用 WeekReportService 生成报告
     * 包含各个设备的测试统计信息和总体的统计信息
     * - 只统计总轮次大于0的设备
     * - 测试完成轮次：没有测试问题的轮次
     * - 测试完成率：测试完成轮次数 / 总轮次数
     * - 应用问题数：测试完成轮次中应用问题的总数（一轮N个算N个），测试失败轮次中的应用问题不统计
     * - 应用问题率：应用问题数 / (测试完成轮次数 * 6)
     * - 总体数据为各设备对应指标的聚合（具体聚合方式见方法内说明）
     *
     * @param days                 查询的天数范围，默认为7天
     * @param startTime            报告起始时间，默认为当前时间
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题，默认为true
     *                             (此参数影响Service层错误提取，Controller层仅透传)
     * @param timeIntervalHours    合并的时间间隔（小时），默认为1小时 (同上)
     * @param onlyDevice2250       是否只处理设备编号为2250的HEART日志，默认为true (同上)
     * @param mergeSimilarIssues   是否合并描述相同但图片URL不同的问题，默认为true (同上)
     * @return 周度报告数据
     */
    @GetMapping("/weekly-report")
    public ResponseEntity<Map<String, Object>> generateWeeklyReport(
            @RequestParam(required = false, defaultValue = "7") Integer days,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false, defaultValue = "true") Boolean mergeShortTimeErrors,
            @RequestParam(required = false, defaultValue = "1") Integer timeIntervalHours,
            @RequestParam(required = false, defaultValue = "true") Boolean onlyDevice2250,
            @RequestParam(required = false, defaultValue = "true") Boolean mergeSimilarIssues) { // Default true

        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        if (timeIntervalHours < 0) {
            result.put("success", false);
            result.put("message", "合并时间间隔不能为负数");
            return ResponseEntity.badRequest().body(result);
        }

        if (startTime == null) {
            startTime = new Date();
        }

        // 调用新的 WeekReportService 生成报告
        Map<String, Object> reportData;
        try {
            reportData = weekReportService.generateWeeklyReport(
                    startTime, days, mergeShortTimeErrors, timeIntervalHours, onlyDevice2250, mergeSimilarIssues);
        } catch (Exception e) {
            logger.error("生成周报失败(WeekReportService调用异常)", e);
            result.put("success", false);
            result.put("message", "生成周报时发生内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }

        result.put("success", true);
        result.put("data", reportData); // Use data directly from the new service
        result.put("startTime", reportData.get("endDate")); // Use endDate from report data as the effective start time
                                                            // passed to service
        result.put("days", reportData.get("period")); // Use period from report data
        // Pass through the parameters used for generation
        result.put("mergeShortTimeErrors", mergeShortTimeErrors);
        result.put("timeIntervalHours", timeIntervalHours);
        result.put("onlyDevice2250", onlyDevice2250);
        result.put("mergeSimilarIssues", mergeSimilarIssues);

        // 更新计算逻辑说明以反映新的统计口径
        Map<String, String> calculationExplanation = new HashMap<>();
        calculationExplanation.put("totalRounds", "总测试轮次数 (来自HEART日志)");
        calculationExplanation.put("testCompletedRounds", "测试完成轮次数 (没有出现测试问题的轮次, 基于HEART日志判断)");
        calculationExplanation.put("testCompletionRate", "测试完成率 = (测试完成轮次数 / 总轮次数) × 100%");
        calculationExplanation.put("appErrors", "应用问题总数 (来自ERROR日志, imagePath非空)");
        calculationExplanation.put("falsePositiveCount", "误报问题数 (应用问题中以'FP '开头)"); // 新增误报数说明
        calculationExplanation.put("appErrorRate", "应用问题率 = (应用问题总数 / (测试完成轮次数 × 6)) × 100%");
        calculationExplanation.put("falsePositiveRate", "误报率 = (误报问题数 / (测试完成轮次数 × 6)) × 100%"); // 新增误报率说明
        // calculationExplanation.put("avgTimePerRound", "平均每轮耗时 (来自HEART日志)");

        // 总体数据计算说明
        calculationExplanation.put("overallCalculation",
                "总体数据聚合逻辑: 总轮次数=各设备总轮次之和; 总完成轮次=各设备完成轮次之和; 总体完成率=总完成轮次/总轮次数; 总应用问题数=各设备应用问题数之和; 总误报数=各设备误报数之和; 总体应用问题率=总应用问题数/(总完成轮次*6); 总体误报率=总误报数/(总完成轮次*6)");

        // if (mergeSimilarIssues) { // Merging logic is now within the service for Top
        // Errors
        // calculationExplanation.put("mergeRule", "相同问题描述但不同图片URL的问题已在Top Errors中合并");
        // }
        result.put("calculationExplanation", calculationExplanation);

        // 检查是否有有效数据 - Use data from reportData
        Map<String, Object> overallReport = (Map<String, Object>) reportData.getOrDefault("overallReport",
                new HashMap<>());
        int overallTotalRounds = (Integer) overallReport.getOrDefault("totalRounds", 0);

        if (overallTotalRounds == 0) {
            result.put("message", "在指定的时间范围内没有找到有效的测试数据");
        } else {
            // Use the actual start time used in the report generation for the message
            Date reportStartDate = (Date) reportData.get("startDate");
            String datePart = new SimpleDateFormat("yyyy-MM-dd").format(reportStartDate);
            result.put("message",
                    "成功生成从 " + datePart + " 开始，近 " + days + " 天的测试报告" + (onlyDevice2250 ? "（仅设备2250）" : ""));
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 生成多节点时间序列测试报告
     * 根据给定的时间跨度和节点数，返回各设备及总体在各时间节点的关键指标数据
     * 方便前端绘制折线图进行数据趋势分析
     *
     * @param startTime          报告起始时间，默认为当前时间
     * @param timeSpan           每个节点的时间跨度（天），默认为7天
     * @param nodeCount          时间节点数量，默认为4
     * @param deduplication      是否启用时间间隔去重，默认为true (影响Service层错误提取)
     * @param timeIntervalHours  去重的时间间隔（小时），默认为1小时
     * @param onlyDevice2250     是否只处理设备编号为2250的HEART日志，默认为true (同上)
     * @param mergeSimilarIssues 是否合并描述相同但图片URL不同的问题，默认为false (同上)
     * @return 多节点时间序列报告数据
     */
    @GetMapping("/trend-report")
    public ResponseEntity<Map<String, Object>> generateTrendReport(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false, defaultValue = "7") Integer timeSpan,
            @RequestParam(required = false, defaultValue = "4") Integer nodeCount,
            @RequestParam(required = false, defaultValue = "true") Boolean deduplication,
            @RequestParam(required = false, defaultValue = "1") Integer timeIntervalHours,
            @RequestParam(required = false, defaultValue = "true") Boolean onlyDevice2250,
            @RequestParam(required = false, defaultValue = "true") Boolean mergeSimilarIssues) {

        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (timeSpan <= 0) {
            result.put("success", false);
            result.put("message", "时间跨度必须大于0");
            return ResponseEntity.badRequest().body(result);
        }
        if (nodeCount <= 0) {
            result.put("success", false);
            result.put("message", "节点数量必须大于0");
            return ResponseEntity.badRequest().body(result);
        }
        if (timeIntervalHours < 0) {
            result.put("success", false);
            result.put("message", "去重时间间隔不能为负数");
            return ResponseEntity.badRequest().body(result);
        }
        if (startTime == null) {
            startTime = new Date();
        }

        // 生成时间节点 - 修改为只生成nodeCount个节点
        List<Date> timeNodes = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        // 生成每个时间节点
        for (int i = 0; i < nodeCount; i++) {
            timeNodes.add(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, -timeSpan);
        }

        // 准备数据容器
        List<String> timeNodeLabels = new ArrayList<>();
        Map<String, List<Double>> overallTrend = new HashMap<>();
        Map<String, Map<String, List<Double>>> deviceTrends = new HashMap<>();

        // 初始化指标字段（移除图标相关字段，因为不进行总体累加）
        String[] metricFields = { "totalRounds", "testCompletionRate", "appErrors", "appErrorRate", "totalErrorCount",
                "errorRate", "avgTimePerRound" };
        for (String field : metricFields) {
            overallTrend.put(field, new ArrayList<>());
        }

        // 格式化日期的工具
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 遍历时间节点获取数据
        for (Date nodeTime : timeNodes) {
            Map<String, Object> nodeData = new HashMap<>();
            String timeNodeStr = sdf.format(nodeTime);
            timeNodeLabels.add(timeNodeStr);

            try {
                // 调用周报服务获取数据
                Map<String, Object> reportData = weekReportService.generateWeeklyReport(
                        nodeTime, timeSpan, deduplication, timeIntervalHours, onlyDevice2250, mergeSimilarIssues);

                // 提取总体报告数据
                Map<String, Object> overallReport = (Map<String, Object>) reportData.get("overallReport");
                if (overallReport != null) {
                    for (String field : metricFields) {
                        // 处理并添加指标值
                        overallTrend.get(field).add(extractNumericValue(overallReport.get(field)));
                    }
                } else {
                    // 数据缺失，填充null
                    for (String field : metricFields) {
                        overallTrend.get(field).add(null);
                    }
                }

                // 提取各设备报告数据
                List<Map<String, Object>> deviceReports = (List<Map<String, Object>>) reportData.get("deviceReports");
                if (deviceReports != null) {
                    for (Map<String, Object> deviceReport : deviceReports) {
                        String deviceId = (String) deviceReport.get("deviceId");
                        if (deviceId == null)
                            continue;

                        // 确保设备的数据容器已初始化
                        if (!deviceTrends.containsKey(deviceId)) {
                            Map<String, List<Double>> deviceMetrics = new HashMap<>();
                            for (String field : metricFields) {
                                deviceMetrics.put(field, new ArrayList<>());
                                // 为之前缺失的时间节点填充null
                                for (int j = 0; j < overallTrend.get(field).size() - 1; j++) {
                                    deviceMetrics.get(field).add(null);
                                }
                            }
                            deviceTrends.put(deviceId, deviceMetrics);
                        }

                        // 添加当前时间节点的设备指标
                        Map<String, List<Double>> deviceMetrics = deviceTrends.get(deviceId);
                        for (String field : metricFields) {
                            deviceMetrics.get(field).add(extractNumericValue(deviceReport.get(field)));
                        }
                    }
                }

                // 处理此次未出现的设备，为其添加null
                for (Map<String, List<Double>> deviceMetrics : deviceTrends.values()) {
                    for (String field : metricFields) {
                        if (deviceMetrics.get(field).size() < timeNodeLabels.size()) {
                            deviceMetrics.get(field).add(null);
                        }
                    }
                }

                nodeData.put("timeNode", timeNodeStr);
                nodeData.put("success", true);

            } catch (Exception e) {
                logger.error("生成时间节点 {} 的报告失败: {}", timeNodeStr, e.getMessage(), e);

                // 添加错误信息
                nodeData.put("timeNode", timeNodeStr);
                nodeData.put("success", false);
                nodeData.put("error", "生成报告失败: " + e.getMessage());

                // 为总体趋势填充null
                for (String field : metricFields) {
                    overallTrend.get(field).add(null);
                }

                // 为各设备趋势填充null
                for (Map<String, List<Double>> deviceMetrics : deviceTrends.values()) {
                    for (String field : metricFields) {
                        deviceMetrics.get(field).add(null);
                    }
                }
            }
        }

        // 反转所有数据，从早到晚排序
        Collections.reverse(timeNodeLabels);
        for (String field : metricFields) {
            Collections.reverse(overallTrend.get(field));
        }
        for (Map<String, List<Double>> deviceMetrics : deviceTrends.values()) {
            for (String field : metricFields) {
                Collections.reverse(deviceMetrics.get(field));
            }
        }

        // 组装最终结果
        Map<String, Object> trendData = new HashMap<>();
        trendData.put("timeNodes", timeNodeLabels);
        trendData.put("overallTrend", overallTrend);
        trendData.put("deviceTrends", deviceTrends);

        result.put("success", true);
        result.put("data", trendData);
        result.put("message", "成功生成包含" + nodeCount + "个时间节点的趋势报告");
        return ResponseEntity.ok(result);
    }

    /**
     * 从带单位的值中提取数值部分
     * 
     * @param value 原始值，可能带有单位如"50.00%"或"10.5秒"
     * @return 提取出的数值，如50.00或10.5，如果无法提取则返回null
     */
    private Double extractNumericValue(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }

            String strValue = value.toString();

            // 移除百分号
            if (strValue.endsWith("%")) {
                strValue = strValue.substring(0, strValue.length() - 1);
            }

            // 移除"秒"
            if (strValue.endsWith("秒")) {
                strValue = strValue.substring(0, strValue.length() - 1);
            }

            // 尝试转换为数值
            return Double.parseDouble(strValue.trim());
        } catch (Exception e) {
            logger.debug("无法从 {} 提取数值: {}", value, e.getMessage());
            return null;
        }
    }

    /**
     * 根据图片路径查询相关记录
     * 先根据图片路径查找对应的记录，然后获取该记录所属的设备ID，
     * 再查询该设备ID在此记录之前的N条INFO级别日志记录。
     * 
     * @param imagePath 图片路径
     * @param count     要查询的INFO级别日志数量，默认为5条
     * @return 包含图片记录和对应设备的INFO日志的结果集
     */
    @GetMapping("/image-related-records")
    public ResponseEntity<Map<String, Object>> getImageRelatedRecords(
            @RequestParam String imagePath,
            @RequestParam(required = false, defaultValue = "5") Integer count) {

        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (imagePath == null || imagePath.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "图片路径不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        if (count <= 0) {
            result.put("success", false);
            result.put("message", "查询数量必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            // 调用服务层方法获取相关记录
            Map<String, Object> relatedRecords = categoryInspectionService.getImageRelatedRecords(imagePath, count);

            // 检查是否找到对应图片记录
            if (relatedRecords.get("imageRecord") == null) {
                result.put("success", false);
                result.put("message", "未找到包含该图片路径的记录");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            result.put("success", true);
            result.put("data", relatedRecords);
            result.put("message", "成功获取图片相关记录");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取图片相关记录失败，图片路径: {}, 异常: {}", imagePath, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取图片相关记录失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 批量条件删除检查记录
     * 支持按日志级别、图片路径、设备ID、时间范围等条件组合删除
     *
     * @param deleteParams 包含删除条件的参数对象
     * @return 删除结果
     */
    @PostMapping("/batch-delete")
    public ResponseEntity<Map<String, Object>> batchDeleteInspections(@RequestBody Map<String, Object> deleteParams) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从参数中获取各种条件
            String logLevel = (String) deleteParams.get("logLevel");
            String deviceId = (String) deleteParams.get("deviceId");
            Boolean hasImagePath = (Boolean) deleteParams.get("hasImagePath");
            Boolean emptyImagePath = (Boolean) deleteParams.get("emptyImagePath");
            Date startTime = null;
            Date endTime = null;

            // 处理日期参数
            if (deleteParams.containsKey("startTime") && deleteParams.get("startTime") != null) {
                if (deleteParams.get("startTime") instanceof Date) {
                    startTime = (Date) deleteParams.get("startTime");
                } else {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        startTime = sdf.parse(deleteParams.get("startTime").toString());
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "无效的开始时间格式，请使用 yyyy-MM-dd HH:mm:ss");
                        return ResponseEntity.badRequest().body(result);
                    }
                }
            }

            if (deleteParams.containsKey("endTime") && deleteParams.get("endTime") != null) {
                if (deleteParams.get("endTime") instanceof Date) {
                    endTime = (Date) deleteParams.get("endTime");
                } else {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        endTime = sdf.parse(deleteParams.get("endTime").toString());
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "无效的结束时间格式，请使用 yyyy-MM-dd HH:mm:ss");
                        return ResponseEntity.badRequest().body(result);
                    }
                }
            }

            // 参数校验，至少需要一个删除条件
            if (logLevel == null && deviceId == null && hasImagePath == null &&
                    emptyImagePath == null && startTime == null && endTime == null) {
                result.put("success", false);
                result.put("message", "至少需要提供一个删除条件");
                return ResponseEntity.badRequest().body(result);
            }

            // 添加安全限制，避免误删除大量数据
            Boolean skipSafetyCheck = (Boolean) deleteParams.getOrDefault("skipSafetyCheck", false);
            Integer limit = (Integer) deleteParams.getOrDefault("limit", 1000);

            if (limit > 10000 && !skipSafetyCheck) {
                result.put("success", false);
                result.put("message", "为保护数据安全，单次批量删除记录数不能超过10000条。如需删除更多记录，请分批操作或设置skipSafetyCheck=true");
                return ResponseEntity.badRequest().body(result);
            }

            // 调用服务层批量删除方法
            int deletedCount = categoryInspectionService.batchDeleteInspections(
                    logLevel, deviceId, hasImagePath, emptyImagePath, startTime, endTime, limit, skipSafetyCheck);

            result.put("success", true);
            result.put("message", "批量删除成功，共删除" + deletedCount + "条记录");
            result.put("count", deletedCount);

            // 添加删除条件到结果
            Map<String, Object> conditions = new HashMap<>(deleteParams);
            conditions.remove("skipSafetyCheck");
            result.put("conditions", conditions);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("批量删除检查记录失败: {}, 条件: {}", e.getMessage(), deleteParams, e);
            result.put("success", false);
            result.put("message", "批量删除检查记录失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 标记或取消标记指定时间范围内特定设备和备注内容的ERROR日志为误报(FP - False Positive)
     * 仅处理 imagePath 不为空的应用问题日志
     *
     * @param markRequest 包含标记信息的请求体
     * @return 操作结果，包含成功与否及更新的记录数
     */
    @PostMapping("/mark-fp")
    public ResponseEntity<Map<String, Object>> markFalsePositive(@RequestBody MarkFalsePositiveRequest markRequest) {
        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (markRequest == null || markRequest.getDeviceId() == null || markRequest.getDeviceId().trim().isEmpty() ||
                markRequest.getRemarksKeyword() == null || markRequest.getRemarksKeyword().trim().isEmpty() ||
                markRequest.getStartTime() == null || markRequest.getEndTime() == null
                || markRequest.getIsFalsePositive() == null) {
            result.put("success", false);
            result.put("message", "缺少必要的参数: deviceId, startTime, endTime, remarksKeyword, isFalsePositive");
            return ResponseEntity.badRequest().body(result);
        }

        if (markRequest.getEndTime().before(markRequest.getStartTime())) {
            result.put("success", false);
            result.put("message", "结束时间不能早于开始时间");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            int updatedCount = categoryInspectionService.markFalsePositive(
                    markRequest.getDeviceId(),
                    markRequest.getStartTime(),
                    markRequest.getEndTime(),
                    markRequest.getRemarksKeyword(),
                    markRequest.getIsFalsePositive());

            result.put("success", true);
            result.put("message",
                    (markRequest.getIsFalsePositive() ? "标记" : "取消标记") + "误报操作成功，共更新 " + updatedCount + " 条记录");
            result.put("updatedCount", updatedCount);
            result.put("requestParams", markRequest); // 返回请求参数以供确认
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("标记/取消标记误报失败: {}, 请求参数: {}", e.getMessage(), markRequest, e);
            result.put("success", false);
            result.put("message", "标记/取消标记误报失败: " + e.getMessage());
            result.put("requestParams", markRequest);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    // 内部类，用于接收 /mark-fp 接口的请求体
    static class MarkFalsePositiveRequest {
        private String deviceId;
        @JsonDeserialize(using = MultiFormatDateDeserializer.class)
        private Date startTime;
        @JsonDeserialize(using = MultiFormatDateDeserializer.class)
        private Date endTime;
        private String remarksKeyword;
        private Boolean isFalsePositive;

        // Getters and Setters
        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public String getRemarksKeyword() {
            return remarksKeyword;
        }

        public void setRemarksKeyword(String remarksKeyword) {
            this.remarksKeyword = remarksKeyword;
        }

        public Boolean getIsFalsePositive() {
            return isFalsePositive;
        }

        public void setIsFalsePositive(Boolean isFalsePositive) {
            this.isFalsePositive = isFalsePositive;
        }

        @Override
        public String toString() {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return "MarkFalsePositiveRequest{" +
                    "deviceId='" + deviceId + '\'' +
                    ", startTime=" + (startTime != null ? sdf.format(startTime) : "null") +
                    ", endTime=" + (endTime != null ? sdf.format(endTime) : "null") +
                    ", remarksKeyword='" + remarksKeyword + '\'' +
                    ", isFalsePositive=" + isFalsePositive +
                    '}';
        }
    }

    /**
     * 获取所有非global设备的最新一条HEART日志，并解析心跳日志中的关键信息
     * 支持按天数筛选近几天内有检查记录的设备
     * 
     * @param days 查询的天数范围，默认为3天
     */
    @GetMapping("/heartbeat-log/latest")
    public ResponseEntity<Map<String, Object>> getLatestHeartbeatLogForDevices(
            @RequestParam(required = false, defaultValue = "3") Integer days) {
        Map<String, Object> result = new HashMap<>();
        if (days == null || days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }
        // 计算n天前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date daysAgo = calendar.getTime();
        // 获取近n天的设备列表，排除"global"设备和"HEART"级别的日志
        List<Map<String, Object>> devices = categoryInspectionService.getRecentDevices(daysAgo, "HEART");
        // 过滤掉deviceId为"global"的记录
        devices.removeIf(device -> "global".equals(device.get("deviceId")));
        // 为每个设备查找最新一条HEART日志
        List<Map<String, Object>> deviceLogs = new ArrayList<>();
        for (Map<String, Object> device : devices) {
            String deviceId = (String) device.get("deviceId");
            if (deviceId == null || deviceId.equals("global"))
                continue;
            // 查找该设备最新一条HEART日志
            List<Map<String, Object>> heartLogs = categoryInspectionService.getLatestHeartbeatLogs(false);
            Map<String, Object> latestHeartLog = null;
            for (Map<String, Object> log : heartLogs) {
                if (deviceId.equals(log.get("deviceId"))) {
                    latestHeartLog = log;
                    break;
                }
            }
            if (latestHeartLog != null) {
                String remarks = (String) latestHeartLog.get("remarks");
                Map<String, Object> parsed = parseHeartbeatRemarks(remarks);
                Map<String, Object> deviceInfo = new HashMap<>(latestHeartLog);
                deviceInfo.put("parsed", parsed);
                deviceLogs.add(deviceInfo);
            }
        }
        result.put("success", true);
        result.put("data", deviceLogs);
        result.put("count", deviceLogs.size());
        result.put("days", days);
        result.put("message", "成功获取近" + days + "天内各设备最新HEART日志及解析字段");
        return ResponseEntity.ok(result);
    }

    // 解析remarks字段，提取心跳日志关键信息
    private Map<String, Object> parseHeartbeatRemarks(String remarks) {
        Map<String, Object> parsed = new HashMap<>();
        if (remarks == null)
            return parsed;
        // 程序运行时间
        parsed.put("programRunTime", extractByRegex(remarks, "程序已运行：([\\u4e00-\\u9fa5\\d小时分钟秒]+)"));
        // 总轮次 - 修改为提取当前轮次而不是历史轮次
        parsed.put("totalRounds", extractByRegex(remarks, "已完成第\\s*(\\d+)\\s*轮测试"));
        // 测试完成率 - 从"完成轮次：数字 (百分比)"中提取括号中的百分比
        parsed.put("testCompletionRate", extractByRegex(remarks, "完成轮次[:：]\\s*\\d+\\s*\\((\\d+\\.?\\d*%)\\)"));
        // 测试问题率
        parsed.put("testIssueRate", extractByRegex(remarks, "测试问题率[:：]\\s*([\\u4e00-\\u9fa5\\d\\.]+%)"));
        // 应用问题率
        parsed.put("appIssueRate", extractByRegex(remarks, "应用问题率[:：]\\s*([\\u4e00-\\u9fa5\\d\\.]+%)"));
        // 平均耗时
        parsed.put("avgTimePerRound", extractByRegex(remarks, "平均耗时[:：]\\s*([\\u4e00-\\u9fa5\\d\\.]+秒)"));

        // 简单直接的错误计数逻辑：统计"详情"出现的次数
        int totalErrorCount = 0;
        String keyword = "详情";
        int index = 0;
        while (index < remarks.length()) {
            index = remarks.indexOf(keyword, index);
            if (index == -1)
                break;
            totalErrorCount++;
            index += keyword.length(); // 移动到下一个潜在匹配处
        }

        parsed.put("totalErrorCount", String.valueOf(totalErrorCount));
        return parsed;
    }

    // 正则提取工具 (保留 extractByRegex)
    private String extractByRegex(String text, String regex) {
        if (text == null)
            return null;
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 根据设备ID和轮次号聚合查询检查记录
     * 查询指定设备在指定轮次的所有记录，并按日志级别进行分类统计
     * 便于前端展示该轮次的测试完整情况
     *
     * @param deviceId 设备ID（必传）
     * @param roundNum 轮次号（必传）
     * @return 聚合统计结果，包含该轮次的详细信息和统计数据
     */
    @GetMapping("/aggregate-by-round")
    public ResponseEntity<Map<String, Object>> aggregateByDeviceAndRound(
            @RequestParam String deviceId,
            @RequestParam Long roundNum) {

        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (deviceId == null || deviceId.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "设备ID不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        if (roundNum == null) {
            result.put("success", false);
            result.put("message", "轮次号不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            // 构建查询条件：根据deviceId和roundNum查询所有相关记录
            LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                       .eq(CategoryInspection::getRoundNum, roundNum)
                       .orderBy(true, true, CategoryInspection::getInspectionTime);

            List<CategoryInspection> records = categoryInspectionService.list(queryWrapper);

            if (records.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到设备ID为" + deviceId + "，轮次号为" + roundNum + "的检查记录");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // 按日志级别分类统计
            Map<String, List<CategoryInspection>> recordsByLevel = new HashMap<>();
            Map<String, Integer> countByLevel = new HashMap<>();
            
            // 统计各个级别的记录
            for (CategoryInspection record : records) {
                String logLevel = record.getLogLevel() != null ? record.getLogLevel() : "UNKNOWN";
                recordsByLevel.computeIfAbsent(logLevel, k -> new ArrayList<>()).add(record);
                countByLevel.put(logLevel, countByLevel.getOrDefault(logLevel, 0) + 1);
            }

            // 构建聚合结果
            Map<String, Object> aggregateData = new HashMap<>();
            
            // 基本信息
            aggregateData.put("deviceId", deviceId);
            aggregateData.put("roundNum", roundNum);
            aggregateData.put("totalRecords", records.size());
            
            // 时间范围信息
            Date startTime = records.get(0).getInspectionTime();
            Date endTime = records.get(records.size() - 1).getInspectionTime();
            aggregateData.put("startTime", startTime);
            aggregateData.put("endTime", endTime);
            
            // 计算总耗时（如果有开始和结束时间）
            if (startTime != null && endTime != null) {
                long durationMs = endTime.getTime() - startTime.getTime();
                double durationSeconds = durationMs / 1000.0;
                aggregateData.put("totalDurationSeconds", durationSeconds);
                aggregateData.put("totalDurationFormatted", String.format("%.2f秒", durationSeconds));
            }

            // 按日志级别的统计信息
            aggregateData.put("statisticsByLevel", countByLevel);
            
            // 详细记录（按级别分组）
            Map<String, Object> detailsByLevel = new HashMap<>();
            for (Map.Entry<String, List<CategoryInspection>> entry : recordsByLevel.entrySet()) {
                String level = entry.getKey();
                List<CategoryInspection> levelRecords = entry.getValue();
                
                Map<String, Object> levelInfo = new HashMap<>();
                levelInfo.put("count", levelRecords.size());
                levelInfo.put("records", levelRecords);
                
                // 对于ERROR级别，额外统计有图片的应用问题
                if ("ERROR".equals(level)) {
                    List<CategoryInspection> appErrors = levelRecords.stream()
                        .filter(r -> r.getImagePath() != null && !r.getImagePath().trim().isEmpty())
                        .collect(Collectors.toList());
                    levelInfo.put("appErrorCount", appErrors.size());
                    levelInfo.put("appErrors", appErrors);
                }
                
                detailsByLevel.put(level, levelInfo);
            }
            aggregateData.put("detailsByLevel", detailsByLevel);

            // 生成测试结果摘要
            String summary = generateRoundSummary(deviceId, roundNum, countByLevel, records.size());
            aggregateData.put("summary", summary);

            result.put("success", true);
            result.put("data", aggregateData);
            result.put("message", "成功聚合设备" + deviceId + "第" + roundNum + "轮次的检查记录");

            // 添加接口和字段说明
            Map<String, Object> apiDocumentation = new HashMap<>();
            apiDocumentation.put("description", "根据设备ID和轮次号聚合查询检查记录，返回该轮次的完整测试情况");
            apiDocumentation.put("endpoint", "GET /compass/api/category/inspection/aggregate-by-round");
            
            Map<String, String> requiredParams = new HashMap<>();
            requiredParams.put("deviceId", "设备ID，字符串类型，必传");
            requiredParams.put("roundNum", "轮次号，数字类型，必传");
            apiDocumentation.put("requiredParams", requiredParams);

            Map<String, Object> fieldExplanations = new HashMap<>();
            fieldExplanations.put("deviceId", "设备标识符");
            fieldExplanations.put("roundNum", "测试轮次编号");
            fieldExplanations.put("totalRecords", "该轮次的总记录数");
            fieldExplanations.put("startTime", "该轮次开始时间");
            fieldExplanations.put("endTime", "该轮次结束时间");
            fieldExplanations.put("totalDurationSeconds", "总耗时（秒）");
            fieldExplanations.put("totalDurationFormatted", "格式化的总耗时");
            fieldExplanations.put("statisticsByLevel", "按日志级别的统计信息 (INFO/ERROR/HEART等)");
            fieldExplanations.put("detailsByLevel", "按日志级别分组的详细记录");
            fieldExplanations.put("detailsByLevel.ERROR.appErrorCount", "应用问题数量（ERROR级别且有图片的记录）");
            fieldExplanations.put("detailsByLevel.ERROR.appErrors", "应用问题详细列表");
            fieldExplanations.put("summary", "该轮次测试结果的文字摘要");

            Map<String, Object> recordFieldExplanations = new HashMap<>();
            recordFieldExplanations.put("inspectionId", "检查记录的唯一标识");
            recordFieldExplanations.put("createdAt", "记录创建时间戳");
            recordFieldExplanations.put("imagePath", "截图链接URL");
            recordFieldExplanations.put("remarks", "问题描述或备注信息");
            recordFieldExplanations.put("logLevel", "日志级别 (INFO/ERROR/HEART)");
            recordFieldExplanations.put("inspectionTime", "检查执行时间");
            recordFieldExplanations.put("roundNum", "所属轮次号");

            apiDocumentation.put("responseFields", fieldExplanations);
            apiDocumentation.put("recordFields", recordFieldExplanations);
            result.put("apiDocumentation", apiDocumentation);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("聚合查询失败，设备ID: {}, 轮次号: {}, 异常: {}", deviceId, roundNum, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "聚合查询失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 生成轮次测试结果摘要
     * 
     * @param deviceId 设备ID
     * @param roundNum 轮次号
     * @param countByLevel 各级别统计
     * @param totalRecords 总记录数
     * @return 测试结果摘要文字
     */
    private String generateRoundSummary(String deviceId, Long roundNum, Map<String, Integer> countByLevel, int totalRecords) {
        StringBuilder summary = new StringBuilder();
        summary.append("设备").append(deviceId).append("第").append(roundNum).append("轮测试");
        
        int errorCount = countByLevel.getOrDefault("ERROR", 0);
        int infoCount = countByLevel.getOrDefault("INFO", 0);
        int heartCount = countByLevel.getOrDefault("HEART", 0);
        
        if (errorCount > 0) {
            summary.append("发现").append(errorCount).append("个问题");
        } else {
            summary.append("测试通过，无问题发现");
        }
        
        if (infoCount > 0) {
            summary.append("，包含").append(infoCount).append("条信息记录");
        }
        
        if (heartCount > 0) {
            summary.append("，").append(heartCount).append("条心跳记录");
        }
        
        summary.append("。总计").append(totalRecords).append("条记录。");
        
        return summary.toString();
    }

    /**
     * 获取指定设备最近N轮测试的详情
     * 按轮次号倒序返回，每轮包含统计信息、时间信息和详细记录
     * 便于前端展示设备的测试历史
     *
     * @param deviceId 设备ID（必传）
     * @param roundCount 要获取的轮次数量，默认为10轮
     * @return 最近N轮测试的详细信息
     */
    @GetMapping("/recent-rounds")
    public ResponseEntity<Map<String, Object>> getRecentRounds(
            @RequestParam String deviceId,
            @RequestParam(required = false, defaultValue = "10") Integer roundCount) {

        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (deviceId == null || deviceId.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "设备ID不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        if (roundCount <= 0 || roundCount > 50) {
            result.put("success", false);
            result.put("message", "轮次数量必须在1-50之间");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            // 首先查询该设备的最近N个不同轮次号
            LambdaQueryWrapper<CategoryInspection> roundQueryWrapper = new LambdaQueryWrapper<>();
            roundQueryWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                           .isNotNull(CategoryInspection::getRoundNum)
                           .select(CategoryInspection::getRoundNum)
                           .orderByDesc(CategoryInspection::getRoundNum)
                           .groupBy(CategoryInspection::getRoundNum);

            List<CategoryInspection> roundNumResults = categoryInspectionService.list(roundQueryWrapper);
            
            if (roundNumResults.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到设备ID为" + deviceId + "的测试记录");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            // 提取轮次号，限制数量
            List<Long> recentRoundNums = roundNumResults.stream()
                .map(CategoryInspection::getRoundNum)
                .distinct()
                .limit(roundCount)
                .collect(Collectors.toList());

            // 为每个轮次获取详细信息
            List<Map<String, Object>> roundDetails = new ArrayList<>();
            
            for (Long roundNum : recentRoundNums) {
                Map<String, Object> roundInfo = getRoundDetailInfo(deviceId, roundNum);
                if (roundInfo != null) {
                    roundDetails.add(roundInfo);
                }
            }

            // 构建返回结果
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("deviceId", deviceId);
            responseData.put("totalRoundsFound", recentRoundNums.size());
            responseData.put("requestedCount", roundCount);
            responseData.put("rounds", roundDetails);

            // 计算总体统计
            Map<String, Object> overallStats = calculateOverallStats(roundDetails);
            responseData.put("overallStats", overallStats);

            result.put("success", true);
            result.put("data", responseData);
            result.put("message", "成功获取设备" + deviceId + "最近" + recentRoundNums.size() + "轮测试详情");

            // 添加接口和字段说明
            Map<String, Object> apiDocumentation = new HashMap<>();
            apiDocumentation.put("description", "获取指定设备最近N轮测试的详情，按轮次号倒序返回，包含每轮的统计信息、时间信息和详细记录");
            apiDocumentation.put("endpoint", "GET /compass/api/category/inspection/recent-rounds");
            
            Map<String, String> params = new HashMap<>();
            params.put("deviceId", "设备ID，字符串类型，必传");
            params.put("roundCount", "要获取的轮次数量，数字类型，可选，默认10轮，最大50轮");
            apiDocumentation.put("params", params);

            Map<String, Object> fieldExplanations = new HashMap<>();
            fieldExplanations.put("deviceId", "设备标识符");
            fieldExplanations.put("totalRoundsFound", "实际找到的轮次数量");
            fieldExplanations.put("requestedCount", "请求的轮次数量");
            fieldExplanations.put("rounds", "轮次详情列表，按轮次号倒序排列");

            Map<String, Object> roundFieldExplanations = new HashMap<>();
            roundFieldExplanations.put("roundNum", "轮次编号");
            roundFieldExplanations.put("totalRecords", "该轮次的总记录数");
            roundFieldExplanations.put("startTime", "轮次开始时间");
            roundFieldExplanations.put("endTime", "轮次结束时间");
            roundFieldExplanations.put("durationSeconds", "轮次耗时（秒）");
            roundFieldExplanations.put("durationFormatted", "格式化的轮次耗时");
            roundFieldExplanations.put("statisticsByLevel", "按日志级别统计 (INFO/ERROR/HEART等)");
            roundFieldExplanations.put("appErrorCount", "应用问题数量（ERROR级别且有图片）");
            roundFieldExplanations.put("appErrors", "应用问题详细列表");
            roundFieldExplanations.put("testStatus", "测试状态 (PASSED/FAILED)");
            roundFieldExplanations.put("hasIssues", "是否有问题（布尔值）");
            roundFieldExplanations.put("summary", "轮次测试结果摘要");
            roundFieldExplanations.put("records", "该轮次的所有记录（简化版）");

            Map<String, Object> overallStatsExplanations = new HashMap<>();
            overallStatsExplanations.put("totalRounds", "总轮次数");
            overallStatsExplanations.put("passedRounds", "通过的轮次数");
            overallStatsExplanations.put("failedRounds", "失败的轮次数");
            overallStatsExplanations.put("passRate", "通过率百分比");
            overallStatsExplanations.put("totalAppErrors", "总应用问题数");
            overallStatsExplanations.put("avgAppErrorsPerRound", "平均每轮应用问题数");
            overallStatsExplanations.put("avgDurationSeconds", "平均每轮耗时（秒）");
            overallStatsExplanations.put("avgDurationFormatted", "格式化的平均每轮耗时");
            overallStatsExplanations.put("totalDurationFormatted", "格式化的总耗时");

            Map<String, Object> recordFieldExplanations = new HashMap<>();
            recordFieldExplanations.put("inspectionId", "检查记录的唯一标识");
            recordFieldExplanations.put("inspectionTime", "检查执行时间");
            recordFieldExplanations.put("createdAt", "记录创建时间戳");
            recordFieldExplanations.put("logLevel", "日志级别 (INFO/ERROR/HEART)");
            recordFieldExplanations.put("imagePath", "截图链接URL");
            recordFieldExplanations.put("remarks", "问题描述或备注信息");
            recordFieldExplanations.put("isAppError", "是否为应用问题（布尔值）");

            fieldExplanations.put("rounds[]", roundFieldExplanations);
            fieldExplanations.put("overallStats", overallStatsExplanations);
            
            apiDocumentation.put("responseFields", fieldExplanations);
            apiDocumentation.put("recordFields", recordFieldExplanations);
            
            Map<String, String> usageExamples = new HashMap<>();
            usageExamples.put("获取最近10轮", "GET /compass/api/category/inspection/recent-rounds?deviceId=iPhone_14p");
            usageExamples.put("获取最近5轮", "GET /compass/api/category/inspection/recent-rounds?deviceId=iPhone_14p&roundCount=5");
            apiDocumentation.put("usageExamples", usageExamples);
            
            result.put("apiDocumentation", apiDocumentation);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取设备最近轮次失败，设备ID: {}, 异常: {}", deviceId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取设备最近轮次失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 获取单个轮次的详细信息
     * 
     * @param deviceId 设备ID
     * @param roundNum 轮次号
     * @return 轮次详细信息
     */
    private Map<String, Object> getRoundDetailInfo(String deviceId, Long roundNum) {
        try {
            // 查询该轮次的所有记录
            LambdaQueryWrapper<CategoryInspection> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CategoryInspection::getDeviceId, deviceId)
                       .eq(CategoryInspection::getRoundNum, roundNum)
                       .orderBy(true, true, CategoryInspection::getInspectionTime);

            List<CategoryInspection> records = categoryInspectionService.list(queryWrapper);
            
            if (records.isEmpty()) {
                return null;
            }

            Map<String, Object> roundInfo = new HashMap<>();
            
            // 基本信息
            roundInfo.put("roundNum", roundNum);
            roundInfo.put("totalRecords", records.size());
            
            // 时间信息
            Date startTime = records.get(0).getInspectionTime();
            Date endTime = records.get(records.size() - 1).getInspectionTime();
            roundInfo.put("startTime", startTime);
            roundInfo.put("endTime", endTime);
            
            // 计算耗时
            if (startTime != null && endTime != null) {
                long durationMs = endTime.getTime() - startTime.getTime();
                double durationSeconds = durationMs / 1000.0;
                roundInfo.put("durationSeconds", durationSeconds);
                roundInfo.put("durationFormatted", String.format("%.2f秒", durationSeconds));
            }

            // 按日志级别统计
            Map<String, Integer> countByLevel = new HashMap<>();
            Map<String, List<CategoryInspection>> recordsByLevel = new HashMap<>();
            
            for (CategoryInspection record : records) {
                String logLevel = record.getLogLevel() != null ? record.getLogLevel() : "UNKNOWN";
                countByLevel.put(logLevel, countByLevel.getOrDefault(logLevel, 0) + 1);
                recordsByLevel.computeIfAbsent(logLevel, k -> new ArrayList<>()).add(record);
            }
            
            roundInfo.put("statisticsByLevel", countByLevel);

            // 应用问题统计（ERROR级别且有图片的记录）
            List<CategoryInspection> appErrors = records.stream()
                .filter(r -> "ERROR".equals(r.getLogLevel()) && 
                           r.getImagePath() != null && !r.getImagePath().trim().isEmpty())
                .collect(Collectors.toList());
            
            roundInfo.put("appErrorCount", appErrors.size());
            roundInfo.put("appErrors", appErrors);

            // 测试结果状态
            int errorCount = countByLevel.getOrDefault("ERROR", 0);
            roundInfo.put("testStatus", errorCount > 0 ? "FAILED" : "PASSED");
            roundInfo.put("hasIssues", errorCount > 0);

            // 详细记录（只返回关键字段，减少数据量）
            List<Map<String, Object>> simplifiedRecords = records.stream()
                .map(this::simplifyRecord)
                .collect(Collectors.toList());
            roundInfo.put("records", simplifiedRecords);

            // 生成轮次摘要
            String summary = generateRoundSummary(deviceId, roundNum, countByLevel, records.size());
            roundInfo.put("summary", summary);

            return roundInfo;

        } catch (Exception e) {
            logger.error("获取轮次详情失败，设备ID: {}, 轮次: {}, 异常: {}", deviceId, roundNum, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 简化记录对象，只保留关键字段
     * 
     * @param record 原始记录
     * @return 简化后的记录信息
     */
    private Map<String, Object> simplifyRecord(CategoryInspection record) {
        Map<String, Object> simplified = new HashMap<>();
        simplified.put("inspectionId", record.getInspectionId());
        simplified.put("inspectionTime", record.getInspectionTime());
        simplified.put("createdAt", record.getCreatedAt());
        simplified.put("logLevel", record.getLogLevel());
        simplified.put("imagePath", record.getImagePath());
        simplified.put("remarks", record.getRemarks());
        
        // 标记是否为应用问题
        boolean isAppError = "ERROR".equals(record.getLogLevel()) && 
                           record.getImagePath() != null && !record.getImagePath().trim().isEmpty();
        simplified.put("isAppError", isAppError);
        
        return simplified;
    }

    /**
     * 计算多轮测试的总体统计信息
     * 
     * @param roundDetails 轮次详情列表
     * @return 总体统计信息
     */
    private Map<String, Object> calculateOverallStats(List<Map<String, Object>> roundDetails) {
        Map<String, Object> stats = new HashMap<>();
        
        if (roundDetails.isEmpty()) {
            return stats;
        }

        int totalRounds = roundDetails.size();
        int passedRounds = 0;
        int totalAppErrors = 0;
        double totalDuration = 0;
        int roundsWithDuration = 0;

        for (Map<String, Object> round : roundDetails) {
            // 统计通过的轮次
            if ("PASSED".equals(round.get("testStatus"))) {
                passedRounds++;
            }
            
            // 统计应用问题数
            Integer appErrorCount = (Integer) round.get("appErrorCount");
            if (appErrorCount != null) {
                totalAppErrors += appErrorCount;
            }
            
            // 统计总耗时
            Double duration = (Double) round.get("durationSeconds");
            if (duration != null) {
                totalDuration += duration;
                roundsWithDuration++;
            }
        }

        stats.put("totalRounds", totalRounds);
        stats.put("passedRounds", passedRounds);
        stats.put("failedRounds", totalRounds - passedRounds);
        stats.put("passRate", String.format("%.2f%%", (passedRounds * 100.0) / totalRounds));
        stats.put("totalAppErrors", totalAppErrors);
        stats.put("avgAppErrorsPerRound", String.format("%.2f", totalAppErrors / (double) totalRounds));
        
        if (roundsWithDuration > 0) {
            double avgDuration = totalDuration / roundsWithDuration;
            stats.put("avgDurationSeconds", avgDuration);
            stats.put("avgDurationFormatted", String.format("%.2f秒", avgDuration));
            stats.put("totalDurationFormatted", String.format("%.2f秒", totalDuration));
        }

        return stats;
    }
}