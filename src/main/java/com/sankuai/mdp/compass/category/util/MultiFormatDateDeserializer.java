package com.sankuai.mdp.compass.category.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class MultiFormatDateDeserializer extends JsonDeserializer<Date> {

    private static final Logger logger = LoggerFactory.getLogger(MultiFormatDateDeserializer.class);

    // 定义支持的日期格式列表，将 'yyyy-MM-dd HH:mm:ss' 放在前面优先尝试
    private static final List<String> SUPPORTED_FORMATS = Arrays.asList(
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSSZ", // ISO 8601 with timezone
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", // ISO 8601 UTC
            "yyyy-MM-dd'T'HH:mm:ssZ",    // ISO 8601 without milliseconds
            "yyyy-MM-dd'T'HH:mm:ss'Z'",   // ISO 8601 UTC without milliseconds
            "EEE MMM dd HH:mm:ss zzz yyyy" // Default Java Date.toString() format
    );

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String dateString = p.getText().trim();

        // 尝试解析数字（时间戳）
        try {
            long timestamp = Long.parseLong(dateString);
            // 假设是毫秒级时间戳
            if (String.valueOf(timestamp).length() >= 10) { // 简单的长度检查
                 // 避免过大的时间戳被误识别，例如大于当前时间很多年的
                if (timestamp < System.currentTimeMillis() + 315360000000L) { // ~+10 years buffer
                    return new Date(timestamp);
                }
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续尝试字符串格式
        }

        // 依次尝试定义的日期格式
        for (String format : SUPPORTED_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                // 对于包含时区信息的格式，需要正确设置SDF
                if (format.contains("Z") || format.contains("zzz")) {
                   // 默认时区通常可以工作，但更健壮的做法是处理时区
                } else {
                    // 对于不带时区的格式，最好假定它是服务器的本地时区，或者UTC
                    // sdf.setTimeZone(TimeZone.getTimeZone("UTC")); // 如果需要强制UTC
                }
                return sdf.parse(dateString);
            } catch (ParseException e) {
                // 解析失败，尝试下一个格式
            }
        }

        // 如果所有已知格式都失败了，记录警告并尝试让 Jackson 的默认机制处理
        logger.warn("无法使用预定义格式解析日期字符串: '{}'。将尝试默认解析。", dateString);
        try {
            // 尝试让 Jackson 的标准 Date 解析器处理，它可能支持更多格式或配置
             return ctxt.parseDate(dateString);
        } catch (Exception e) {
            logger.error("使用所有已知格式及默认解析器均无法解析日期: '{}'", dateString, e);
            throw new IOException("无法将字符串 '" + dateString + "' 解析为支持的日期格式", e);
        }
    }
} 