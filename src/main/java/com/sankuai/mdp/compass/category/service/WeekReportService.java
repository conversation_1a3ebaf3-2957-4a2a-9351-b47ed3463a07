package com.sankuai.mdp.compass.category.service;

import java.util.Date;
import java.util.Map;

/**
 * 周报生成服务接口
 * 定义生成周度测试报告的业务逻辑
 */
public interface WeekReportService {

    /**
     * 生成周度测试报告 (新统计口径)
     *
     * @param startTime 报告的统计结束时间 (以此时间点往前推 days 天)
     * @param days 查询的天数范围
     * @param mergeShortTimeErrors 是否合并短时间内出现的相同问题 (影响错误提取)
     * @param timeIntervalHours 合并的时间间隔（小时） (影响错误提取)
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志 (影响错误提取)
     * @param mergeSimilarIssues 是否合并描述相同但图片URL不同的问题 (影响Top Error展示)
     * @return 报告数据，包含设备统计和总体统计，格式与 CategoryInspectionController 中定义的保持一致
     */
    Map<String, Object> generateWeeklyReport(Date startTime, int days, boolean mergeShortTimeErrors, int timeIntervalHours, boolean onlyDevice2250, boolean mergeSimilarIssues);

} 