package com.sankuai.mdp.compass.category.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper; // 引入 MybatisPlus 查询构造器
import com.sankuai.mdp.compass.category.entity.CategoryInspection;
import com.sankuai.mdp.compass.category.service.CategoryInspectionService;
import com.sankuai.mdp.compass.category.service.WeekReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils; // Import StringUtils

import java.text.DecimalFormat;
import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;
import java.util.regex.Matcher; // <-- Add import for Matcher
import java.util.regex.Pattern; // <-- Add import for Pattern

/**
 * WeekReportService 实现类
 */
@Service
public class WeekReportServiceImpl implements WeekReportService {

    private static final Logger logger = LoggerFactory.getLogger(WeekReportServiceImpl.class);
    private static final DecimalFormat df = new DecimalFormat("0.00"); // 用于格式化
    private static final String FP_PREFIX = "FP "; // 误报标记前缀

    // 内部类，用于聚合Top Error信息
    private static class AggregatedErrorDetail {
        int count = 0;
        String firstRemark; // 存储遇到的第一个原始备注（含FP前缀）
        Set<String> imageUrls = new HashSet<>();
    }

    @Autowired
    private CategoryInspectionService categoryInspectionService; // 依赖 CategoryInspectionService 获取数据

    // 存储预先获取的关键INFO日志
    private Map<String, Map<String, Long>> deviceRoundNumCache;

    /**
     * 时区修正工具方法（WeekReportService版本）
     * 修复数据库查询的时区偏移问题（加8小时）
     * 
     * @param originalTime 原始时间
     * @return 修正后的时间（加8小时以匹配数据库的UTC时间）
     */
    private Date fixTimezoneForQuery(Date originalTime) {
        if (originalTime == null) {
            return null;
        }
        
        // 修复数据库查询的时区偏移：数据库存储为UTC时间，但查询时需要加8小时
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(originalTime.getTime())); // 创建副本
        calendar.add(Calendar.HOUR_OF_DAY, 8);
        return calendar.getTime();
    }

    /**
     * 初始化设备轮次缓存 - 重构后由外部调用，在查询主要日志时一并查询
     */
    private void initDeviceRoundNumCache(List<String> deviceIds, Date reportStartTime, Date reportEndTime) {
        deviceRoundNumCache = new HashMap<>();

        // 起始点2分钟
        Date startWindowEnd = new Date(reportStartTime.getTime() + 2 * 60 * 1000L);
        // 结束点2分钟
        Date endWindowStart = new Date(reportEndTime.getTime() - 2 * 60 * 1000L);

        try {
            // 查询所有设备在时间窗口内的INFO日志
            LambdaQueryWrapper<CategoryInspection> query = new LambdaQueryWrapper<>();
            query.in(CategoryInspection::getDeviceId, deviceIds)
                    .eq(CategoryInspection::getLogLevel, "INFO")
                    .and(w -> w.and(sw -> sw.ge(CategoryInspection::getCreatedAt, reportStartTime)
                            .le(CategoryInspection::getCreatedAt, startWindowEnd))
                            .or(ew -> ew.ge(CategoryInspection::getCreatedAt, endWindowStart)
                                    .le(CategoryInspection::getCreatedAt, reportEndTime)))
                    .select(CategoryInspection::getDeviceId, CategoryInspection::getRoundNum,
                            CategoryInspection::getCreatedAt);

            List<CategoryInspection> infoLogs = categoryInspectionService.list(query);
            logger.info("获取到 {} 条用于计算轮次的INFO日志", infoLogs.size());

            // 按设备ID分组
            Map<String, List<CategoryInspection>> deviceInfoLogs = infoLogs.stream()
                    .collect(Collectors.groupingBy(CategoryInspection::getDeviceId));

            // 为每个设备计算起始和结束轮次
            for (Map.Entry<String, List<CategoryInspection>> entry : deviceInfoLogs.entrySet()) {
                String deviceId = entry.getKey();
                List<CategoryInspection> logs = entry.getValue();

                // 找出起始轮次（最早时间窗口内的第一个有效轮次）
                Long startRound = logs.stream()
                        .filter(log -> log.getCreatedAt().compareTo(startWindowEnd) <= 0)
                        .sorted(Comparator.comparing(CategoryInspection::getCreatedAt))
                        .map(CategoryInspection::getRoundNum)
                        .filter(r -> r != null && r > 0)
                        .findFirst().orElse(null);

                // 找出结束轮次（最晚时间窗口内的最后一个有效轮次）
                Long endRound = logs.stream()
                        .filter(log -> log.getCreatedAt().compareTo(endWindowStart) >= 0)
                        .sorted(Comparator.comparing(CategoryInspection::getCreatedAt).reversed())
                        .map(CategoryInspection::getRoundNum)
                        .filter(r -> r != null && r > 0)
                        .findFirst().orElse(null);

                Map<String, Long> roundInfo = new HashMap<>();
                if (startRound != null)
                    roundInfo.put("start", startRound);
                if (endRound != null)
                    roundInfo.put("end", endRound);

                deviceRoundNumCache.put(deviceId, roundInfo);
            }

            logger.info("成功初始化 {} 个设备的轮次缓存", deviceRoundNumCache.size());
        } catch (Exception e) {
            logger.error("初始化设备轮次缓存失败: {}", e.getMessage(), e);
            deviceRoundNumCache = Collections.emptyMap();
        }
    }

    /**
     * 尝试通过INFO日志的round_num字段计算轮次（已优化：使用缓存）
     * 
     * @param deviceId        设备ID
     * @param reportStartTime 统计起始时间（不再使用）
     * @param reportEndTime   统计结束时间（不再使用）
     * @return 如果能用round_num计算则返回有效轮次，否则返回-1
     */
    private int calculateRoundsByInfoRoundNum(String deviceId, Date reportStartTime, Date reportEndTime) {
        // 使用缓存
        if (deviceRoundNumCache == null || !deviceRoundNumCache.containsKey(deviceId)) {
            return -1;
        }

        Map<String, Long> roundInfo = deviceRoundNumCache.get(deviceId);
        Long startRound = roundInfo.get("start");
        Long endRound = roundInfo.get("end");

        if (startRound != null && endRound != null) {
            int rounds = (int) (endRound - startRound);
            return rounds >= 0 ? rounds : -1;
        }
        return -1; // 回退
    }

    @Override
    public Map<String, Object> generateWeeklyReport(Date startTime, int days, boolean mergeShortTimeErrors,
            int timeIntervalHours, boolean onlyDevice2250, boolean mergeSimilarIssues) {
        Map<String, Object> report = new HashMap<>();
        List<Map<String, Object>> deviceReports = new ArrayList<>();

        // 计算时间范围
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        Date reportEndTime = calendar.getTime(); // 报告结束时间
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date reportStartTime = calendar.getTime(); // 报告开始时间

        logger.info("开始生成周度报告(WeekReportService)，时间范围: {} 到 {} （不进行时区偏移）", reportStartTime, reportEndTime);

        // 修复数据库查询的时区偏移：加8小时以匹配数据库的UTC时间
        Date correctedStartTime = fixTimezoneForQuery(reportStartTime);
        Date correctedEndTime = fixTimezoneForQuery(reportEndTime);
        logger.info("数据库查询时间范围（+8小时偏移）: {} 到 {}", correctedStartTime, correctedEndTime);

        // 1. 获取活动设备列表
        List<String> activeDeviceIds;
        try {
            LambdaQueryWrapper<CategoryInspection> deviceQuery = new LambdaQueryWrapper<>();
            deviceQuery.eq(CategoryInspection::getLogLevel, "HEART")
                    .ge(CategoryInspection::getCreatedAt, correctedStartTime)
                    .le(CategoryInspection::getCreatedAt, correctedEndTime)
                    .select(CategoryInspection::getDeviceId)
                    .groupBy(CategoryInspection::getDeviceId);
            List<Object> deviceIdObjects = categoryInspectionService.listObjs(deviceQuery); // 使用注入的 service
            activeDeviceIds = deviceIdObjects.stream()
                    .map(Object::toString)
                    .filter(id -> !"global".equals(id)) // 过滤掉 global
                    .distinct()
                    .collect(Collectors.toList());
            logger.info("找到 {} 个活动设备: {}", activeDeviceIds.size(), activeDeviceIds);
        } catch (Exception e) {
            logger.error("获取活动设备列表失败", e);
            activeDeviceIds = Collections.emptyList();
        }

        // 1.1 初始化设备轮次缓存（批量查询INFO日志中的round_num）
        initDeviceRoundNumCache(activeDeviceIds, correctedStartTime, correctedEndTime);

        // 2. 一次性获取所有设备的HEART日志
        Map<String, List<CategoryInspection>> deviceHeartLogsMap = new HashMap<>();
        try {
            LambdaQueryWrapper<CategoryInspection> heartLogQuery = new LambdaQueryWrapper<>();
            heartLogQuery.in(CategoryInspection::getDeviceId, activeDeviceIds)
                    .eq(CategoryInspection::getLogLevel, "HEART")
                    .ge(CategoryInspection::getCreatedAt, correctedStartTime)
                    .le(CategoryInspection::getCreatedAt, correctedEndTime)
                    .select(CategoryInspection::getDeviceId, CategoryInspection::getRemarks,
                            CategoryInspection::getCreatedAt)
                    .orderByAsc(CategoryInspection::getCreatedAt); // 按时间升序
            List<CategoryInspection> allHeartLogs = categoryInspectionService.list(heartLogQuery);

            // 按设备ID分组
            deviceHeartLogsMap = allHeartLogs.stream()
                    .collect(Collectors.groupingBy(CategoryInspection::getDeviceId));

            logger.info("一次性获取到 {} 个设备的 {} 条HEART日志", deviceHeartLogsMap.keySet().size(), allHeartLogs.size());
        } catch (Exception e) {
            logger.error("批量获取设备HEART日志失败", e);
        }

        // 2.1 一次性获取所有设备的ERROR日志
        Map<String, List<CategoryInspection>> deviceErrorLogsMap = new HashMap<>();
        try {
            LambdaQueryWrapper<CategoryInspection> errorLogQuery = new LambdaQueryWrapper<>();
            errorLogQuery.in(CategoryInspection::getDeviceId, activeDeviceIds)
                    .eq(CategoryInspection::getLogLevel, "ERROR")
                    .ge(CategoryInspection::getCreatedAt, correctedStartTime)
                    .le(CategoryInspection::getCreatedAt, correctedEndTime)
                    .select(CategoryInspection::getDeviceId, CategoryInspection::getRemarks,
                            CategoryInspection::getImagePath, CategoryInspection::getCreatedAt);
            List<CategoryInspection> allErrorLogs = categoryInspectionService.list(errorLogQuery);

            // 按设备ID分组
            deviceErrorLogsMap = allErrorLogs.stream()
                    .collect(Collectors.groupingBy(CategoryInspection::getDeviceId));

            logger.info("一次性获取到 {} 个设备的 {} 条ERROR日志", deviceErrorLogsMap.keySet().size(), allErrorLogs.size());
        } catch (Exception e) {
            logger.error("批量获取设备ERROR日志失败", e);
        }

        // 初始化总体数据
        Map<String, Object> overallReport = new HashMap<>();
        int overallTotalRounds = 0;
        int overallTestCompletedRounds = 0;
        int overallAppErrorsCount = 0;
        int overallFalsePositiveCount = 0; // 新增：总体误报数
        double overallTotalTimeSpentSeconds = 0;
        // 用于 Top Error 统计 (使用新的聚合结构)
        Map<String, AggregatedErrorDetail> allAppErrorsMap = new HashMap<>();
        Map<String, AggregatedErrorDetail> allTestErrorsMap = new HashMap<>();
        int overallAppErrorsRawCount = 0;
        int overallTestProblemRounds = 0;
        int overallTotalErrorCount = 0;

        // 3. 遍历设备
        for (String deviceId : activeDeviceIds) {
            logger.info("处理设备: {}", deviceId);
            Map<String, Object> deviceReport = new HashMap<>();
            deviceReport.put("deviceId", deviceId);

            // 获取当前设备的HEART日志
            List<CategoryInspection> heartLogs = deviceHeartLogsMap.getOrDefault(deviceId, Collections.emptyList());

            // 4. 计算总轮次（优先用INFO日志的round_num）
            int deviceRounds = -1;
            int infoRounds = calculateRoundsByInfoRoundNum(deviceId, correctedStartTime, correctedEndTime);
            if (infoRounds > 0) {  // 改为 > 0，当计算结果为0时也回退到HEART日志
                deviceRounds = infoRounds;
                logger.info("设备 {} 通过INFO日志round_num计算总轮次为 {}", deviceId, deviceRounds);
            } else {
                if (infoRounds == 0) {
                    logger.warn("设备 {} INFO日志round_num计算结果为0（可能在统计期间轮次未变化），回退到HEART日志分析", deviceId);
                }
                // 回退为原有HEART日志分析逻辑
                if (heartLogs.isEmpty()) {
                    logger.warn("设备 {} 在时间范围内没有 HEART 日志，跳过", deviceId);
                    continue;
                }
                int initialHistoryRounds = extractHistoryRounds(heartLogs.get(0).getRemarks(), onlyDevice2250);
                int finalHistoryRounds = extractHistoryRounds(heartLogs.get(heartLogs.size() - 1).getRemarks(),
                        onlyDevice2250);
                String finalRemarks = heartLogs.get(heartLogs.size() - 1).getRemarks();
                deviceRounds = calculateDeviceRounds(deviceId, initialHistoryRounds, finalHistoryRounds,
                        finalRemarks, onlyDevice2250);
                logger.info("设备 {} 通过HEART日志分析总轮次为 {}", deviceId, deviceRounds);
            }

            if (deviceRounds <= 0) {
                logger.info("设备 {} 计算总轮次为 {}，不参与统计", deviceId, deviceRounds);
                continue;
            }
            deviceReport.put("totalRounds", deviceRounds);

            // 提取平均耗时
            String latestRemarks = null;
            if (!heartLogs.isEmpty()) {
                latestRemarks = heartLogs.get(heartLogs.size() - 1).getRemarks();
            }
            double avgTimePerRoundSeconds = extractAvgTime(latestRemarks, onlyDevice2250);
            deviceReport.put("avgTimePerRound", df.format(avgTimePerRoundSeconds) + "秒");

            // 提取新的图标相关数据
            int totalIcons = extractTotalIcons(latestRemarks, onlyDevice2250);
            int completedIcons = extractCompletedIcons(latestRemarks, onlyDevice2250);
            double iconCompletionRate = extractIconCompletionRate(latestRemarks, onlyDevice2250);
            double avgIconTime = extractAvgIconTime(latestRemarks, onlyDevice2250);
            
            deviceReport.put("totalIcons", totalIcons);
            deviceReport.put("completedIcons", completedIcons);
            deviceReport.put("iconCompletionRate", df.format(iconCompletionRate) + "%");
            deviceReport.put("avgIconTime", df.format(avgIconTime) + "秒");

            // 7. 处理 ERROR 日志获取应用问题和测试问题详情
            List<CategoryInspection> appErrorLogs = new ArrayList<>();
            List<CategoryInspection> testErrorLogs = new ArrayList<>();

            // 从预先获取的错误日志映射中获取当前设备的错误日志
            List<CategoryInspection> allErrorLogs = deviceErrorLogsMap.getOrDefault(deviceId, Collections.emptyList());

            // 分类处理
            for (CategoryInspection errorLog : allErrorLogs) {
                // 应用问题：imagePath 不为空
                if (StringUtils.hasText(errorLog.getImagePath())) {
                    appErrorLogs.add(errorLog);
                }
                // 测试问题：imagePath 为空或空字符串
                else {
                    testErrorLogs.add(errorLog);
                }
            }

            logger.info("设备 {} 处理到 {} 条应用错误日志 (ERROR & imagePath非空) 和 {} 条测试错误日志 (ERROR & imagePath为空)",
                    deviceId, appErrorLogs.size(), testErrorLogs.size());

            // 8. 计算应用问题数、误报数、召回数、误报率
            int deviceAppErrorsCount = appErrorLogs.size();
            int deviceFalsePositiveCount = 0;
            Map<String, AggregatedErrorDetail> currentDeviceAppErrorsMap = new HashMap<>(); // 用于设备 Top Errors
            Map<String, AggregatedErrorDetail> currentDeviceTestErrorsMap = new HashMap<>(); // 用于设备 Top Errors

            // 处理应用问题 (ERROR & imagePath 非空)
            for (CategoryInspection appErrorLog : appErrorLogs) {
                String originalRemark = appErrorLog.getRemarks();
                if (originalRemark == null)
                    continue;

                boolean isFP = originalRemark.startsWith(FP_PREFIX);
                if (isFP) {
                    deviceFalsePositiveCount++;
                }

                // 使用去除 FP 前缀的 remark 作为分组依据
                String problemKey = extractProblemName(originalRemark); // 假设 extractProblemName 会处理 FP 前缀 (需要实现)
                String imageUrl = appErrorLog.getImagePath(); // Directly use imagePath

                // 统计设备级 Top Errors
                AggregatedErrorDetail detail = currentDeviceAppErrorsMap.computeIfAbsent(problemKey,
                        k -> new AggregatedErrorDetail());
                detail.count++;
                if (detail.firstRemark == null) {
                    detail.firstRemark = originalRemark; // 存储第一个遇到的原始备注
                }
                if (imageUrl != null) {
                    detail.imageUrls.add(imageUrl);
                }

                // 累加到全局 Top Errors
                AggregatedErrorDetail globalDetail = allAppErrorsMap.computeIfAbsent(problemKey,
                        k -> new AggregatedErrorDetail());
                globalDetail.count++;
                if (globalDetail.firstRemark == null) {
                    globalDetail.firstRemark = originalRemark;
                }
                if (imageUrl != null) {
                    globalDetail.imageUrls.add(imageUrl);
                }
            }

            // 处理测试问题 (ERROR & imagePath 为空)
            for (CategoryInspection testErrorLog : testErrorLogs) {
                String originalRemark = testErrorLog.getRemarks();
                if (originalRemark == null)
                    continue;

                // 测试问题理论上不应有 FP 前缀，但仍按应用问题逻辑提取 problemKey
                String problemKey = extractProblemName(originalRemark);

                // 统计设备级 Top Errors
                AggregatedErrorDetail detail = currentDeviceTestErrorsMap.computeIfAbsent(problemKey,
                        k -> new AggregatedErrorDetail());
                detail.count++;
                if (detail.firstRemark == null) {
                    detail.firstRemark = originalRemark;
                }
                // 测试问题没有图片URL

                // 累加到全局 Top Errors
                AggregatedErrorDetail globalDetail = allTestErrorsMap.computeIfAbsent(problemKey,
                        k -> new AggregatedErrorDetail());
                globalDetail.count++;
                if (globalDetail.firstRemark == null) {
                    globalDetail.firstRemark = originalRemark;
                }
            }

            // 计算召回数（即有效应用问题数）
            int deviceRecallCount = Math.max(0, deviceAppErrorsCount - deviceFalsePositiveCount);
            deviceReport.put("appErrors", deviceRecallCount); // 召回数
            deviceReport.put("falsePositiveCount", deviceFalsePositiveCount); // 误报数

            // 新增总错误数 = 召回数 + 误报数
            int deviceTotalErrorCount = deviceRecallCount + deviceFalsePositiveCount;
            deviceReport.put("totalErrorCount", deviceTotalErrorCount);

            // 统计测试问题轮次数（即ERROR日志中imagePath为空的数量）
            int deviceTestProblemRounds = testErrorLogs.size();
            // 计算测试完成轮次 = 总轮次 - 测试问题轮次数
            int deviceTestCompletedRounds = Math.max(0, deviceRounds - deviceTestProblemRounds);
            deviceReport.put("testCompletedRounds", deviceTestCompletedRounds);

            double deviceTestCompletionRate = (deviceRounds > 0)
                    ? ((double) deviceTestCompletedRounds / deviceRounds * 100)
                    : 0.0;
            deviceReport.put("testCompletionRate", df.format(deviceTestCompletionRate) + "%");

            // 计算召回率（appErrorRate）
            double deviceAppErrorRate = (deviceAppErrorsCount > 0)
                    ? ((double) deviceRecallCount / deviceAppErrorsCount * 100)
                    : 0.0;
            deviceReport.put("appErrorRate", df.format(deviceAppErrorRate) + "%");

            // 计算误报率 = 100% - 召回率
            double deviceFalsePositiveRate = 100.0 - deviceAppErrorRate;
            deviceReport.put("falsePositiveRate", df.format(deviceFalsePositiveRate) + "%");

            // 新增出错率 = (召回数+误报数)/测试完成轮次
            double deviceErrorRate = (deviceTestCompletedRounds > 0)
                    ? ((double) (deviceRecallCount + deviceFalsePositiveCount) / deviceTestCompletedRounds * 100)
                    : 0.0;
            deviceReport.put("errorRate", df.format(deviceErrorRate) + "%");

            // 9. 生成设备 Top Errors (只包含应用问题, 来自ERROR日志)
            List<Map.Entry<String, AggregatedErrorDetail>> sortedDeviceAppErrors = currentDeviceAppErrorsMap.entrySet()
                    .stream()
                    .sorted((e1, e2) -> Integer.compare(e2.getValue().count, e1.getValue().count)) // 按count降序
                    .limit(10) // 最多取10个
                    .collect(Collectors.toList());

            List<Map<String, Object>> topDeviceErrors = new ArrayList<>();
            for (Map.Entry<String, AggregatedErrorDetail> entry : sortedDeviceAppErrors) {
                Map<String, Object> errorInfo = new HashMap<>();
                AggregatedErrorDetail detail = entry.getValue();
                errorInfo.put("detail", detail.firstRemark); // 显示原始备注 (含FP)
                errorInfo.put("count", detail.count);
                errorInfo.put("type", "应用问题");
                errorInfo.put("imageUrls", new ArrayList<>(detail.imageUrls));
                topDeviceErrors.add(errorInfo);
            }
            // 可以选择是否包含测试问题的Top Errors到设备报告
            // ... (如果需要，添加类似 currentDeviceTestErrorsMap 的处理逻辑) ...
            deviceReport.put("topErrors", topDeviceErrors);

            // 10. 累加总体数据
            overallTotalRounds += deviceRounds;
            overallTestCompletedRounds += deviceTestCompletedRounds;
            overallAppErrorsCount += deviceRecallCount; // 累加召回数
            overallFalsePositiveCount += deviceFalsePositiveCount; // 累加误报数
            overallTotalTimeSpentSeconds += (avgTimePerRoundSeconds * deviceRounds);
            // 注意：图标相关数据不进行累加，只保留各设备从最新HEART日志中读取的信息
            // 新增：累计总应用问题数（含误报）
            overallAppErrorsRawCount += deviceAppErrorsCount;
            // 新增：累计测试问题轮次数
            overallTestProblemRounds += deviceTestProblemRounds;
            // 新增：累计总错误数
            overallTotalErrorCount += deviceTotalErrorCount;

            deviceReports.add(deviceReport);
            logger.info("设备 {} 处理完成. 轮次: {}, 完成轮次: {}, 完成率: {}, 应用问题: {}, 误报数: {}, 应用问题率: {}, 误报率: {}, 平均耗时: {}, 总图标: {}, 完成图标: {}, 图标完成率: {}, 平均图标耗时: {}",
                    deviceId, deviceRounds, deviceTestCompletedRounds, deviceReport.get("testCompletionRate"),
                    deviceRecallCount, deviceFalsePositiveCount, deviceReport.get("appErrorRate"),
                    deviceReport.get("falsePositiveRate"), deviceReport.get("avgTimePerRound"), 
                    totalIcons, completedIcons, deviceReport.get("iconCompletionRate"), deviceReport.get("avgIconTime"));

        } // 结束设备循环

        // 11. 计算总体指标
        double overallTestCompletionRate = (overallTotalRounds > 0)
                ? ((double) overallTestCompletedRounds / overallTotalRounds * 100)
                : 0.0;
        // 召回率: 所有设备召回数之和 / 所有设备总应用问题数之和
        double overallAppErrorRate = (overallAppErrorsRawCount > 0)
                ? ((double) overallAppErrorsCount / overallAppErrorsRawCount * 100)
                : 0.0;
        // 误报率 = 100% - 召回率
        double overallFalsePositiveRate = 100.0 - overallAppErrorRate;
        double overallAvgTimePerRoundSeconds = (overallTotalRounds > 0)
                ? (overallTotalTimeSpentSeconds / overallTotalRounds)
                : 0.0;
        // 新增总体出错率 = (召回数+误报数)/测试完成轮次
        double overallErrorRate = (overallTestCompletedRounds > 0)
                ? ((double) (overallAppErrorsCount + overallFalsePositiveCount) / overallTestCompletedRounds * 100)
                : 0.0;
        // 新增总体总错误数
        int totalErrorCount = overallTotalErrorCount;

        // 12. 填充总体报告
        overallReport.put("totalDevices", deviceReports.size());
        overallReport.put("totalRounds", overallTotalRounds);
        overallReport.put("testCompletedRounds", overallTestCompletedRounds);
        overallReport.put("testCompletionRate", df.format(overallTestCompletionRate) + "%");
        overallReport.put("appErrors", overallAppErrorsCount); // 召回数
        overallReport.put("falsePositiveCount", overallFalsePositiveCount); // 误报数
        overallReport.put("appErrorRate", df.format(overallAppErrorRate) + "%"); // 召回率
        overallReport.put("falsePositiveRate", df.format(overallFalsePositiveRate) + "%"); // 误报率
        overallReport.put("avgTimePerRound", df.format(overallAvgTimePerRoundSeconds) + "秒");
        overallReport.put("errorRate", df.format(overallErrorRate) + "%"); // 出错率
        overallReport.put("totalErrorCount", totalErrorCount); // 总错误数
        // 注意：不再包含图标相关总体字段，各设备图标数据单独展示

        // 13. 生成总体 Top Errors (Top 10 应用问题 + Top 10 测试问题, 来自ERROR日志)
        List<Map.Entry<String, AggregatedErrorDetail>> sortedOverallAppErrors = allAppErrorsMap.entrySet()
                .stream()
                .sorted((e1, e2) -> Integer.compare(e2.getValue().count, e1.getValue().count))
                .limit(10)
                .collect(Collectors.toList());

        List<Map.Entry<String, AggregatedErrorDetail>> sortedOverallTestErrors = allTestErrorsMap.entrySet()
                .stream()
                .sorted((e1, e2) -> Integer.compare(e2.getValue().count, e1.getValue().count))
                .limit(10)
                .collect(Collectors.toList());

        List<Map<String, Object>> topOverallErrors = new ArrayList<>();
        for (Map.Entry<String, AggregatedErrorDetail> entry : sortedOverallAppErrors) {
            Map<String, Object> errorInfo = new HashMap<>();
            AggregatedErrorDetail detail = entry.getValue();
            errorInfo.put("detail", detail.firstRemark); // 显示原始备注
            errorInfo.put("count", detail.count);
            errorInfo.put("type", "应用问题");
            errorInfo.put("imageUrls", new ArrayList<>(detail.imageUrls));
            topOverallErrors.add(errorInfo);
        }
        for (Map.Entry<String, AggregatedErrorDetail> entry : sortedOverallTestErrors) {
            Map<String, Object> errorInfo = new HashMap<>();
            AggregatedErrorDetail detail = entry.getValue();
            errorInfo.put("detail", detail.firstRemark); // 显示原始备注
            errorInfo.put("count", detail.count);
            errorInfo.put("type", "测试问题");
            errorInfo.put("imageUrls", Collections.emptyList()); // 测试问题无图片
            topOverallErrors.add(errorInfo);
        }

        // 按 count 再次排序并取 Top 10
        topOverallErrors.sort((a, b) -> ((Integer) b.get("count")).compareTo((Integer) a.get("count")));
        if (topOverallErrors.size() > 10) {
            topOverallErrors = topOverallErrors.subList(0, 10);
        }
        overallReport.put("topErrors", topOverallErrors);

        report.put("deviceReports", deviceReports);
        report.put("overallReport", overallReport);
        report.put("period", days);
        report.put("startDate", reportStartTime);
        report.put("endDate", reportEndTime);

        logger.info(
                "周度报告(WeekReportService)生成完成. 总体轮次: {}, 总体完成轮次: {}, 总体完成率: {}, 总体有效应用问题数(appErrors): {}, 总体误报数: {}, 总体应用问题率: {}, 总体误报率: {}",
                overallTotalRounds, overallTestCompletedRounds, overallReport.get("testCompletionRate"),
                overallAppErrorsCount, overallFalsePositiveCount, overallReport.get("appErrorRate"),
                overallReport.get("falsePositiveRate"));

        // 14. 生成 calculationExplanation 说明
        Map<String, String> calculationExplanation = new LinkedHashMap<>();
        calculationExplanation.put("appErrors", "召回数（真实应用问题数，ERROR日志中imagePath非空且非FP开头）");
        calculationExplanation.put("falsePositiveCount", "误报数（ERROR日志中imagePath非空且FP开头）");
        calculationExplanation.put("totalErrorCount", "总错误数 = 召回数 + 误报数");
        calculationExplanation.put("totalRounds", "总测试轮次数（优先INFO日志，否则HEART日志）");
        calculationExplanation.put("testCompletedRounds", "测试完成轮次数 = 总轮次 - 该设备所有ERROR日志中imagePath为空的数量");
        calculationExplanation.put("testCompletionRate", "测试完成率 = (测试完成轮次数 / 总轮次数) × 100%");
        calculationExplanation.put("appErrorRate", "召回率 = (appErrors / (appErrors + falsePositiveCount)) × 100%");
        calculationExplanation.put("falsePositiveRate", "误报率 = 100% - 召回率");
        calculationExplanation.put("errorRate", "出错率 = (appErrors + falsePositiveCount) / testCompletedRounds × 100%");
        calculationExplanation.put("avgTimePerRound", "平均每轮耗时（秒）");
        calculationExplanation.put("totalIcons", "总图标数（来自HEART日志）");
        calculationExplanation.put("completedIcons", "完成图标总数（来自HEART日志）");
        calculationExplanation.put("iconCompletionRate", "图标完成率 = (完成图标总数 / 总图标数) × 100%");
        calculationExplanation.put("avgIconTime", "平均图标耗时（秒，来自HEART日志）");
        calculationExplanation.put("topErrors", "Top 10 应用问题（按出现次数排序）");
        calculationExplanation.put("overallCalculation",
                "总体数据聚合逻辑: 总轮次数=各设备总轮次之和; 总完成轮次=各设备完成轮次之和; 总体完成率=总完成轮次/总轮次数; 总召回数=各设备召回数之和; 总误报数=各设备误报数之和; 总体召回率=总召回数/(总召回数+总误报数); 总体误报率=100%-总体召回率; 总体出错率=(总召回数+总误报数)/总完成轮次; 图标数据不进行总体聚合，各设备图标信息从最新HEART日志中单独读取");
        report.put("calculationExplanation", calculationExplanation);

        return report;
    }

    // --- 辅助方法 ---

    /**
     * 计算设备的有效总轮次
     */
    private int calculateDeviceRounds(String deviceId, int initialHistoryRounds, int finalHistoryRounds,
            String latestRemarks, boolean onlyDevice2250) {
        int deviceRounds = 0;
        if (initialHistoryRounds != -1 && finalHistoryRounds != -1) {
            deviceRounds = finalHistoryRounds - initialHistoryRounds;
            if (deviceRounds < 0) {
                // Handle wrap-around or reset scenario
                int currentRound = extractCurrentRound(latestRemarks, onlyDevice2250);
                if (currentRound > 0 && finalHistoryRounds > 0 && finalHistoryRounds < initialHistoryRounds) {
                    // Likely a reset followed by new rounds
                    deviceRounds = finalHistoryRounds; // Consider only rounds after reset
                    logger.warn("设备 {} 历史轮次出现回滚 ({} -> {}), 采用最终历史轮次 {} 作为新基准下的轮次数", deviceId, initialHistoryRounds,
                            finalHistoryRounds, deviceRounds);
                } else if (finalHistoryRounds > 0) {
                    // If final is positive but less than initial (without clear current round),
                    // maybe just use final?
                    deviceRounds = finalHistoryRounds;
                    logger.warn("设备 {} 历史轮次计算为负 ({} -> {}), 且无法确定当前轮次，暂用最终历史轮次 {} 作为轮次数", deviceId,
                            initialHistoryRounds, finalHistoryRounds, deviceRounds);
                } else {
                    deviceRounds = 0; // Cannot determine rounds
                    logger.warn("设备 {} 历史轮次计算为负 ({} -> {}), 无法确定轮次数，计为 0", deviceId, initialHistoryRounds,
                            finalHistoryRounds);
                }
            }
        } else if (finalHistoryRounds != -1 && finalHistoryRounds >= 0) {
            // Only final history round is available
            deviceRounds = finalHistoryRounds; // Assume starting from 0 or reset
            logger.warn("设备 {} 仅获取到最终历史轮次 {}，将其作为总轮次", deviceId, deviceRounds);
        } else {
            logger.warn("设备 {} 未能提取到有效的历史轮次信息 (初始: {}, 最终: {})", deviceId, initialHistoryRounds, finalHistoryRounds);
            int currentRound = extractCurrentRound(latestRemarks, onlyDevice2250);
            if (currentRound > 0) {
                // Fallback to current round if history is unreliable
                deviceRounds = currentRound;
                logger.info("使用设备 {} 的最新当前轮次 {} 作为总轮次", deviceId, deviceRounds);
            } else {
                logger.warn("设备 {} 总轮次无法确定，计为 0", deviceId);
                deviceRounds = 0;
            }
        }
        // Ensure non-negative
        return Math.max(0, deviceRounds);
    }

    /**
     * 计算测试完成的轮次数
     * (此方法逻辑保留不变，依赖从 HEART 日志解析出的 testFailedRoundSet)
     */
    private int calculateCompletedRounds(String deviceId, int deviceRounds, int initialHistoryRounds,
            int finalHistoryRounds, Set<Integer> testFailedRoundSet, Set<Integer> roundsWithErrorData) {
        int completedRounds = 0;
        // 尝试根据历史轮次确定轮次范围
        Set<Integer> potentialRounds = new HashSet<>();
        // 只有当历史轮次信息完整且与计算出的deviceRounds一致时，才认为这个范围是可靠的
        if (initialHistoryRounds >= 0 && finalHistoryRounds > initialHistoryRounds
                && (finalHistoryRounds - initialHistoryRounds == deviceRounds)) {
            for (int r = initialHistoryRounds + 1; r <= finalHistoryRounds; r++) {
                potentialRounds.add(r);
            }
            logger.debug("设备 {} 根据历史轮次确定潜在轮次范围: {} 到 {}", deviceId, initialHistoryRounds + 1, finalHistoryRounds);
        } else {
            // 如果历史轮次信息不可靠或与 deviceRounds 不匹配，使用错误数据中的轮次号作为参考
            potentialRounds.addAll(roundsWithErrorData); // roundsWithErrorData now comes from HEART parsing
            logger.warn("设备 {} 历史轮次信息不可靠或与总轮数({})不符，使用HEART解析出的问题轮次集合({})作为参考",
                    deviceId, deviceRounds, potentialRounds.size());
            // 检查错误数据中的最大轮次是否接近 finalHistoryRounds (如果可用)
            int maxErrorRound = potentialRounds.stream().max(Integer::compare).orElse(0);
            if (finalHistoryRounds > 0 && maxErrorRound > 0 && maxErrorRound < finalHistoryRounds * 0.9) { // 增加
                                                                                                           // maxErrorRound
                                                                                                           // > 0 判断
                logger.warn("设备 {} HEART问题中最大轮次 {} 远小于最终历史轮次 {}，完成轮次计算可能不准", deviceId, maxErrorRound,
                        finalHistoryRounds);
            }
            // 如果错误数据中的轮次集合大小远小于deviceRounds，也认为不准
            if (potentialRounds.size() < deviceRounds * 0.5) { // 例如，小于一半
                logger.warn("设备 {} HEART问题轮次集合大小({})远小于计算总轮次({})，完成轮次计算可能不准",
                        deviceId, potentialRounds.size(), deviceRounds);
                potentialRounds.clear(); // 清空，强制使用近似计算
            }

        }

        if (!potentialRounds.isEmpty()) {
            int calculatedCompleted = 0;
            for (int round : potentialRounds) {
                if (!testFailedRoundSet.contains(round)) { // Use testFailedRoundSet from HEART
                    calculatedCompleted++;
                }
            }
            // 校验：只有当潜在轮次集合大小等于计算的总轮次时，才认为精确计算是可靠的
            if (potentialRounds.size() == deviceRounds) {
                completedRounds = calculatedCompleted;
                logger.info("设备 {} 精确计算完成轮次: {}", deviceId, completedRounds);
            } else {
                // 否则，认为潜在轮次集合不完整，使用近似值
                completedRounds = Math.max(0, deviceRounds - testFailedRoundSet.size());
                logger.warn("设备 {} 潜在轮次集合大小({})与计算总轮次({})不符，使用近似计算完成轮次: {}",
                        deviceId, potentialRounds.size(), deviceRounds, completedRounds);
            }

        } else {
            // 如果无法确定潜在轮次，只能用近似值
            completedRounds = Math.max(0, deviceRounds - testFailedRoundSet.size());
            logger.warn("设备 {} 无法确定潜在轮次集合，使用最终近似计算完成轮次: {}", deviceId, completedRounds);
        }

        // Ensure non-negative and not exceeding total rounds
        completedRounds = Math.max(0, completedRounds);
        completedRounds = Math.min(completedRounds, deviceRounds);

        return completedRounds;
    }

    // 提取历史轮次
    private int extractHistoryRounds(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return -1;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return -1;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            // 增加对 "历史总轮次" 的匹配
            if (line.contains("历史轮次：") || line.contains("历史轮次:") || line.contains("历史总轮次：")
                    || line.contains("历史总轮次:")) {
                try {
                    String prefix = "";
                    if (line.contains("历史总轮次："))
                        prefix = "历史总轮次：";
                    else if (line.contains("历史总轮次:"))
                        prefix = "历史总轮次:";
                    else if (line.contains("历史轮次："))
                        prefix = "历史轮次：";
                    else if (line.contains("历史轮次:"))
                        prefix = "历史轮次:";

                    String historyRoundStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    if (historyRoundStr.contains("(") || historyRoundStr.contains("（")) {
                        historyRoundStr = historyRoundStr.replaceAll("[（(].*[)）]", "").trim();
                    }
                    // Find the first sequence of digits
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+");
                    java.util.regex.Matcher matcher = pattern.matcher(historyRoundStr);
                    if (matcher.find()) {
                        return Integer.parseInt(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析历史轮次时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return -1;
    }

    // 提取平均耗时
    private double extractAvgTime(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return 0.0;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return 0.0;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            if (line.contains("平均耗时：") || line.contains("平均耗时:")) {
                try {
                    String prefix = line.contains("平均耗时：") ? "平均耗时：" : "平均耗时:";
                    String avgTimeStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    avgTimeStr = avgTimeStr.replace("秒", "").trim();
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+(\\.\\d+)?");
                    java.util.regex.Matcher matcher = pattern.matcher(avgTimeStr);
                    if (matcher.find()) {
                        return Double.parseDouble(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析平均耗时时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return 0.0;
    }

    // 提取总图标数
    private int extractTotalIcons(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return 0;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return 0;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            if (line.contains("总图标数：") || line.contains("总图标数:")) {
                try {
                    String prefix = line.contains("总图标数：") ? "总图标数：" : "总图标数:";
                    String totalIconsStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+");
                    java.util.regex.Matcher matcher = pattern.matcher(totalIconsStr);
                    if (matcher.find()) {
                        return Integer.parseInt(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析总图标数时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return 0;
    }

    // 提取完成图标总数
    private int extractCompletedIcons(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return 0;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return 0;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            if (line.contains("完成图标总数：") || line.contains("完成图标总数:")) {
                try {
                    String prefix = line.contains("完成图标总数：") ? "完成图标总数：" : "完成图标总数:";
                    String completedIconsStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+");
                    java.util.regex.Matcher matcher = pattern.matcher(completedIconsStr);
                    if (matcher.find()) {
                        return Integer.parseInt(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析完成图标总数时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return 0;
    }

    // 提取图标完成率
    private double extractIconCompletionRate(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return 0.0;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return 0.0;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            if (line.contains("图标完成率：") || line.contains("图标完成率:")) {
                try {
                    String prefix = line.contains("图标完成率：") ? "图标完成率：" : "图标完成率:";
                    String iconCompletionRateStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    iconCompletionRateStr = iconCompletionRateStr.replace("%", "").trim();
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+(\\.\\d+)?");
                    java.util.regex.Matcher matcher = pattern.matcher(iconCompletionRateStr);
                    if (matcher.find()) {
                        return Double.parseDouble(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析图标完成率时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return 0.0;
    }

    // 提取平均图标耗时
    private double extractAvgIconTime(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return 0.0;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return 0.0;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            if (line.contains("平均图标耗时：") || line.contains("平均图标耗时:")) {
                try {
                    String prefix = line.contains("平均图标耗时：") ? "平均图标耗时：" : "平均图标耗时:";
                    String avgIconTimeStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    avgIconTimeStr = avgIconTimeStr.replace("秒", "").trim();
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+(\\.\\d+)?");
                    java.util.regex.Matcher matcher = pattern.matcher(avgIconTimeStr);
                    if (matcher.find()) {
                        return Double.parseDouble(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析平均图标耗时时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return 0.0;
    }

    // 提取当前轮次
    private int extractCurrentRound(String remarks, boolean onlyDevice2250) {
        if (remarks == null || remarks.isEmpty())
            return -1;
        if (onlyDevice2250 && !remarks.contains("【MBP-PVFCQ0LQ62-2250】"))
            return -1;

        String normalizedRemarks = remarks.replace(" | ", "\n");
        String[] lines = normalizedRemarks.split("\n");
        for (String line : lines) {
            if (line.contains("当前轮次：") || line.contains("当前轮次:")) {
                try {
                    String prefix = line.contains("当前轮次：") ? "当前轮次：" : "当前轮次:";
                    String currentRoundStr = line.substring(line.indexOf(prefix) + prefix.length()).trim();
                    // Find the first sequence of digits
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("^\\d+");
                    java.util.regex.Matcher matcher = pattern.matcher(currentRoundStr);
                    if (matcher.find()) {
                        return Integer.parseInt(matcher.group(0));
                    }
                } catch (Exception e) {
                    logger.error("解析当前轮次时出错: {} - {}", line, e.getMessage());
                }
            }
        }
        return -1;
    }

    // --- 新增或修改的辅助方法 ---

    /**
     * 从错误详情中提取问题名称 (纯文本描述)
     * (Adapted from CategoryInspectionServiceImpl - handles FP prefix)
     */
    private String extractProblemName(String detail) {
        if (detail == null || detail.isEmpty()) {
            return "";
        }

        String problemText = detail;

        // Remove FP prefix if present for grouping purposes
        if (problemText.startsWith(FP_PREFIX)) {
            problemText = problemText.substring(FP_PREFIX.length());
        }

        // 查找 "图片URL：" 或 "图片URL:" 并移除之后的部分
        int urlLabelIndex = problemText.indexOf("图片URL");
        if (urlLabelIndex != -1) {
            // 确保是标记而不是文本包含
            if (problemText.length() > urlLabelIndex + 4
                    && (problemText.charAt(urlLabelIndex + 4) == ':' || problemText.charAt(urlLabelIndex + 4) == '：')) {
                problemText = problemText.substring(0, urlLabelIndex).trim();
            }
        } else {
            // 如果没有标记，查找 "http://" 或 "https://" 并移除之后的部分
            int httpIndex = problemText.indexOf("http://");
            int httpsIndex = problemText.indexOf("https://");
            int urlStartIndex = -1;
            if (httpIndex != -1 || httpsIndex != -1) {
                urlStartIndex = (httpIndex != -1 && httpsIndex != -1) ? Math.min(httpIndex, httpsIndex)
                        : (httpIndex != -1 ? httpIndex : httpsIndex);
                if (urlStartIndex > 0) { // Only remove if URL is not at the beginning
                    problemText = problemText.substring(0, urlStartIndex).trim();
                } else if (urlStartIndex == 0) {
                    // 如果URL在开头，可能没有前面的描述，返回空字符串或原始字符串？返回空更安全
                    problemText = "";
                }
            }
        }

        // 取第一行
        if (problemText.contains("\n")) {
            problemText = problemText.split("\n")[0].trim();
        }

        // 去除结尾标点
        problemText = problemText.replaceAll("[，。；,;.\\s]+$", "");

        return problemText.trim(); // Trim again for safety
    }

    // extractImageUrl - 可以移除，因为直接使用 CategoryInspection 的 imagePath 字段
    /*
     * private String extractImageUrl(String detail) {
     * // ... (Implementation as before, but now unused) ...
     * }
     */

}