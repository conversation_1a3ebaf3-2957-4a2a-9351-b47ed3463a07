package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by xieyongrui on 2019/11/22.
 */
@Slf4j
public class ConanUtil {
    public static final String CONAN_API = "http://conan.sankuai.com/api/";
    public static final String CONAN_JOB_INFO_API = "http://conan.sankuai.com/ci/data/job/";
    public static final String CONAN_TASK_API = "http://conan.sankuai.com/ci/data/job/query?ids=-1";
    public static final String CONAN_DOWNLOAD_API = "https://conan.sankuai.com/ci/auto-function/appium/file/";
    public static final String CONAN_CASE_API = "http://conan.sankuai.com/ci/data/auto-function/case/list/";
    public static final String CONAN_CRASH_API = "http://conan.sankuai.com/ci/data/jira/list/job/";
    public static final String CONAN_CRASH_DETAIL_API = "http://conan.sankuai.com/ci/crash/getInfo?jobIdList=";
    public static final String CRASH_DETAIL_API = "https://crash.sankuai.com/token/api/v2/crashTrack/crash/detail?access_token=5a1b8b8ff4eed21d025ca72f&size=1&type=crash&eq=id%2CGUID-";
    public static final String MAX_MIN_VERSIONS = "https://crash.sankuai.com/token/api/v2/crashTrack/crash-records/MaxMinVersions?access_token=5a93ac817be9024a11e37c90";
    public static final String CONAN_PASS_RATE = "https://conan.sankuai.com/ci/auto-function/pass-rate";
    public static final String CONAN_CRASH_ONES_BASE_URL = "";
    JsonParser jsonParser = new JsonParser();

    /**
     * 获取task信息
     *
     * @param jobIds
     * @return
     */
    public JsonArray getTaskInfo(String jobIds) {
        JsonArray jsonArray = new JsonArray();
        try {
            String result = HttpUtil.vGet(CONAN_TASK_API+","+jobIds);
            if (null !=  result && !result.isEmpty()) {
                JsonArray jobArray = jsonParser.parse(result).getAsJsonArray();
                for (int jobIndex = 0; jobIndex < jobArray.size(); jobIndex++) {
                    JsonObject taskJsonObject = jobArray.get(jobIndex).getAsJsonObject();
                    JsonArray taskJsonArray = taskJsonObject.get("taskList").getAsJsonArray();
                    String appVersion = taskJsonObject.get("appVersion").getAsString();
                    String platform = taskJsonObject.get("platform").getAsString();
                    for (int taskIndex = 0; taskIndex < taskJsonArray.size(); taskIndex++) {
                        JsonObject itemJson = taskJsonArray.get(taskIndex).getAsJsonObject();
//                        if ("运行完成".equals(itemJson.getString("statusStr"))) {
                            JsonObject jsonObject = new JsonObject();
                            jsonObject.addProperty("id",itemJson.get("id").getAsInt());
                            jsonObject.addProperty("deviceModel",itemJson.get("deviceModel").getAsString());
                            jsonObject.addProperty("deviceVersion",itemJson.get("deviceVersion").getAsString());
                            jsonObject.addProperty("totalTime",itemJson.get("totalTime").getAsDouble());
                            jsonObject.addProperty("appVersion",appVersion);
                            jsonObject.addProperty("platform",platform);
                            jsonArray.add(jsonObject);
//                        }
                    }
                }
            }
        } catch (Exception e) {
//            log.error("getTaskInfo err:"+e.getMessage());
        }
        return jsonArray;
    }

    /**
     * 获取task信息
     *
     * @param jobId
     * @return
     */
    public JsonArray getAllTaskInfo(String jobId) {
        try {
            String result = HttpUtil.vGet(CONAN_TASK_API+","+jobId);
            if (null !=  result && !result.isEmpty()) {
                JsonArray jobArray = jsonParser.parse(result).getAsJsonArray();
                return jobArray;
            }
        } catch (Exception e) {
//            log.error("getAllTaskInfo>>"+e.getMessage());
        }
        return null;
    }

    /**
     * 获取单个job信息
     * @param jobId
     * @return
     */
    public JsonObject getJobInfo(String jobId) {
        try {
            String result = HttpUtil.vGet(CONAN_JOB_INFO_API+jobId);
            if (null != result && !result.isEmpty()) {
                JsonObject jobInfo = jsonParser.parse(result).getAsJsonObject();
                return jobInfo;
            }
        } catch (Exception e) {
//            log.error("getJobInfo>>"+e.getMessage());
        }
        return null;
    }

    /**
     * 取消job
     * @param jobId
     * @return
     */
    public void cancelJob(String jobId) {
        try {
            String url = CONAN_API+jobId+"/cancel";
            String param = "misId=sunkangtong&conanKey=2d7d4fb7-ec69-4b61-a310-a4f694fde9e8";
            String result = HttpUtil.sendPost(url,param);
            if (null != result) {
//                log.info("取消任务",result);
            }
        } catch (Exception e) {
//            log.info("取消任务失败");
//            log.error(e.getMessage());
        }
    }

    public JsonArray getAllCaseInfo(String taskId) {
        try {
            String result = HttpUtil.vGet(CONAN_CASE_API+taskId);
            if (null != result && !result.isEmpty()) {
                return jsonParser.parse(result).getAsJsonArray();
            }
        }  catch (Exception e) {
        }
        return null;
    }

    public JsonArray getAllCrashInfo(String jobId) {
        try {
            String result = HttpUtil.vGet(CONAN_CRASH_API+jobId);
            if (null != result && !result.isEmpty()) {
                JsonArray crashList = jsonParser.parse(result).getAsJsonArray();
                return crashList;
            }
        } catch (Exception e) {
        }
        return null;
    }


    /**
     * 获取文件下载URL
     *
     * @param taskId
     * @return
     */
    public String getDownloadURL(String taskId) {
        String url = null;
        try {
            JsonObject jsonObject = jsonParser.parse(HttpUtil.sendPost(CONAN_DOWNLOAD_API+taskId,"")).getAsJsonObject();
            if (null != jsonObject.get("output")) {
                url = jsonObject.get("output").getAsString();
            }
        } catch (IOException e) {
//            log.error("getDownloadURL err:" + e.getMessage());
        }
        return url;
    }

    public JsonArray getJobCrashInfo(String jobId) {
        JsonArray crashJsonArray = new JsonArray();
        JsonObject crashInfoJson;
        try {
            String result = HttpUtil.vGet(CONAN_CRASH_DETAIL_API+jobId);
            if (null !=  result && !result.isEmpty()) {
                crashInfoJson = jsonParser.parse(result).getAsJsonObject();
                if (!crashInfoJson.get("isError").getAsBoolean()){
                    String crashList = crashInfoJson.get("data").getAsString();
                    crashJsonArray = jsonParser.parse(crashList).getAsJsonArray().get(0).getAsJsonObject().get("crashIssueInfoList").getAsJsonArray();
                    System.out.println(crashList);
                } else{
                    log.info("从云测获取Crash链接失败");
                }
            }
        } catch (Exception e) {
            log.error("getJobCrashInfo err:" + e.getMessage());
        }
        return crashJsonArray;
    }

    public Map<String, Object> getCrashDetail(String crashUUID,String project,String startTime,String endTime) {
        JsonObject crashDetailObject;
        Map<String, Object> crashDetail = new HashMap();
        try {
            String param = crashUUID+"&project="+project+"&start="+startTime+"%2000:00:00&end="+endTime+"%2023:59:59";
            log.info("collectUICrash---getCrashDetail---param: " + param);
            String result = HttpUtil.vGet(CRASH_DETAIL_API + param);
            if ("" !=  result) {
                log.info("collectUICrash---getCrashDetail: CRASH_DETAIL_API返回不为空");
                crashDetailObject = jsonParser.parse(result).getAsJsonArray().get(0).getAsJsonObject();
                crashDetail.put("type",crashDetailObject.get("type").getAsString());
                crashDetail.put("default_bg_bu_chinese",crashDetailObject.get("default_bg_bu_chinese").getAsString());
                crashDetail.put("default_component",crashDetailObject.get("default_component").getAsString());
                crashDetail.put("crash_hash",crashDetailObject.get("hash").getAsString());
            }
        } catch (Exception e) {
            log.info("collectUICrash---getCrashDetail:CRASH_DETAIL_API返回出错");
            log.error(e.getMessage());
        }
        return crashDetail;
    }

    public String getMaxMinCrashVersion(String crashHash,String type,String project){
        String maxMinCrashVersion = "";
        JsonObject body = new JsonObject();
        body.addProperty("type",type);
        body.addProperty("project",project);
        JsonArray filters = new JsonArray();
        JsonObject filter = new JsonObject();
        JsonArray crashHashArray = new JsonArray();
        crashHashArray.add(crashHash);
        filter.addProperty("fieldName","hash");
        filter.add("fieldInValues",crashHashArray);
        filters.add(filter);
        body.add("filters",filters);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        try {
            JsonElement result = HttpUtil.post(MAX_MIN_VERSIONS,headers,null,body);
            if(!result.isJsonNull()) {
                JsonObject data = result.getAsJsonObject().getAsJsonObject("data");
                if(null != data){
                    String minVersion = data.get("minVersion").getAsString();
                    String maxVersion = data.get("maxVersion").getAsString();
                    if(null != minVersion || null != maxVersion){
                        maxMinCrashVersion = minVersion + "-" + maxVersion;
                    }
                }
            }
        } catch (Exception e) {
            log.error("getMaxMinCrashVersion err:" + e.getMessage());
        }
        return maxMinCrashVersion;
    }

    public JsonObject getPassrate(String jobId) {
        try {
            String url = CONAN_PASS_RATE + "/" + jobId;
            String param = null;
            String result = HttpUtil.sendPost(url,param);

            if (null != result) {
                JsonObject jobInfo = jsonParser.parse(result).getAsJsonObject();
                return jobInfo;
            }
        } catch (Exception e) {
            log.info("查询失败");
            log.error(e.getMessage());
        }
        return null;
    }

}
