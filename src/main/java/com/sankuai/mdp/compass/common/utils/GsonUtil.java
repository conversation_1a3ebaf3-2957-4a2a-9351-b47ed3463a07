package com.sankuai.mdp.compass.common.utils;

import com.google.gson.*;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sankuai.mdp.compass.common.enums.Resp;
public class GsonUtil {
    private static final Logger logger = LoggerFactory.getLogger(GsonUtil.class);
    private static final String DATEFORMAT_default = "yyyy-MM-dd HH:mm:ss";

    private static final Gson gson = new GsonBuilder()
            /**
             * 重写map的反序列化
             */
            .disableHtmlEscaping()
            .setDateFormat(DATEFORMAT_default)
            .registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapTypeAdapter()).create();


    // 转换具体类型的时候，不会因为类型不匹配，而抛出异常
    private static final Gson gsonNoException = new GsonBuilder()
            /**
             * 重写map的反序列化
             */
            .disableHtmlEscaping()
            .setDateFormat(DATEFORMAT_default)
            .registerTypeAdapterFactory(new GsonTypeAdapterFactory()).create();

    private static final JsonParser jsonParser = new JsonParser();

    /**
     * 实现格式化的时间字符串转时间对象
     */


    /**
     * object转为Json字符串
     *   PublishOrder p = new PublishOrder();
     *   p.setApp("ios");
     *   p.setFilterChannelList(null);
     *   System.out.println(GsonUtil.toJson(p));
     *
     *      {"ios":1}
     * @param object
     * @return
     */
    public static String toJson(Object object) {
        if (object == null) return "";
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.setDateFormat(DATEFORMAT_default);

        Gson gson = gsonBuilder.disableHtmlEscaping().create();
        return gson.toJson(object);
    }

    public static String getString(JsonElement element) {
        if (element == null) return "";
        try {
            return element.getAsString();
        }catch (Exception e){
            return GsonUtil.toJson(element);
        }
    }

    /**
     * object转为Json字符串: 对象属性为null时，保留该属性
     *   PublishOrder p = new PublishOrder();
     *   p.setApp("ios");
     *   p.setFilterChannelList(null);
     *   System.out.println(GsonUtil.toJson(p));
     *
     *      {"filterChannelList":null,"ios":1}
     * @param object
     * @return
     */
    public static String toJsonHasNull(Object object) {
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.setDateFormat(DATEFORMAT_default);

        Gson gson = gsonBuilder.serializeNulls().create();
        return gson.toJson(object);
    }




    /**
     * Json字符串转为某个类的Object
     *
     * @param json
     * @param classOfT
     * @return
     */
    public static <T> T fromJson(String json, Class<T> classOfT) {
        if (StringUtil.isBlank(json)) json = "{}";
        // 转换具体类型的时候，不会因为类型不匹配，而抛出异常
        return gsonNoException.fromJson(json, classOfT);
    }

    public static <T> T fromJson(JsonObject jsonObject, Class<T> classOfT) {
        String json = GsonUtil.toJson(jsonObject);
        if (StringUtil.isBlank(json)) json = "{}";
        return gsonNoException.fromJson(json, classOfT);
    }


    public static <T> T fromJson(String json, Type typeOfT) {
        return gsonNoException.fromJson(json, typeOfT);
    }

    /**
     * 字符串Json转JsonObject
     */
    public static JsonObject getJsonObject(String json) {
        if (StringUtil.isBlank(json)) return new JsonObject();
        JsonElement jsonElement = jsonParser.parse(json);
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        return jsonObject;
    }

    /**
     * 字符串Json转JsonObject
     */
    public static JsonObject getJsonObjectNoException(String json) {
        try {
            return getJsonObject(json);
        }catch (Exception e){
            return new JsonObject();
        }
    }

    public static JsonObject getJsonObject(Object object) {
        if (object == null) return new JsonObject();
        String json = toJson(object);

        JsonElement jsonElement = jsonParser.parse(json);
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        return jsonObject;
    }

    /**
     * 取JsonObject中子jsonObject某一项
     *
     * @param jsonObject
     * @param name
     * @return
     */
    public static JsonObject getJsonObjectByName(JsonObject jsonObject, String name) {
        JsonObject subObj = jsonObject.get(name).getAsJsonObject();
        return subObj;
    }

    /**
     * 取JsonObject中子某一项Int值
     *
     * @param jsonObject
     * @param name
     * @return
     */
    public static Integer getIntByName(JsonObject jsonObject, String name) {
        Integer subObj = jsonObject.get(name).getAsInt();
        return subObj;
    }

    /**
     * 取Long
     *
     * @param jsonObject
     * @param name
     * @return
     */
    public static Long getLongByName(JsonObject jsonObject, String name) {
        Long subObj = jsonObject.get(name).getAsLong();
        return subObj;
    }

    /**
     * 取JsonObject中子某一项String值
     *
     * @param jsonObject
     * @param name
     * @return
     */
    public static String getStringByName(JsonObject jsonObject, String name) {
        JsonElement jsonElement = jsonObject.get(name);
        return getAsString(jsonElement);
    }

    /**
     * Json转String
     *
     * @param jsonElement
     * @return
     */
    public static String getAsString(JsonElement jsonElement) {
        if (jsonElement instanceof JsonPrimitive) {
            return jsonElement.getAsString();
        } else if (jsonElement instanceof JsonArray) {
            return jsonElement.toString();
        } else {
            return jsonElement.toString();
        }
    }

    /**
     * Resp取data转jsonObject
     *
     * @param response
     * @return
     */
    public static JsonObject getJsonBody(Resp response) {
        String body = response.getData().toString();
        JsonElement jsonElement = jsonParser.parse(body);
        JsonObject jsonObject = jsonElement.getAsJsonObject();

        return jsonObject;
    }

    /**
     * 字符串数组转JsonArray
     *
     * @param arrJson
     * @return
     */
    public static JsonArray getJsonArray(String arrJson) {
        if (StringUtil.isBlank(arrJson)) return new JsonArray();

//        s = s.replace("\\", "");
        JsonArray jsonArray = jsonParser.parse(arrJson).getAsJsonArray();
        return jsonArray;
    }

    public static JsonArray getJsonArray(Object object) {
        String arrJson = toJson(object);

        JsonArray jsonArray = jsonParser.parse(arrJson).getAsJsonArray();
        return jsonArray;
    }

    /**
     * 取jsonObject中的成员Array
     *
     * @param jsonObject
     * @param member
     * @return
     */
    public static JsonArray getJsonMemberArray(JsonObject jsonObject, String member) {
        try {
            JsonArray jsonArray = jsonObject.getAsJsonArray(member);
            return jsonArray;
        } catch (Exception e) {
            return new JsonArray();
        }
    }


    /**
     * json串 转 list
     */
    public static <T> List<T> json2List(String str_json, Class clazz) {
        Type type = new ParameterizedTypeImpl(clazz);
        List<T> list = gson.fromJson(str_json, type);
        return list;
    }

    public static <T> List<T> json2ListString(String str_json) {
        Type type = new TypeToken<List<String>>() {
        }.getType();
        List<T> list = gson.fromJson(str_json, type);
        return list;
    }


    private static class ParameterizedTypeImpl implements ParameterizedType {
        Class clazz;

        public ParameterizedTypeImpl(Class clz) {
            clazz = clz;
        }

        @Override
        public Type[] getActualTypeArguments() {
            return new Type[]{clazz};
        }

        @Override
        public Type getRawType() {
            return List.class;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }
    }


    /**
     * json串 转 list
     */
    public static Map<String, Object> json2map(String str_json) {
        Map<String, Object> res = null;
        try {
            res = gson.fromJson(str_json, new TypeToken<Map<String, Object>>() {
            }.getType());
        } catch (JsonSyntaxException e) {
            logger.error(e.getMessage(), e);
        }
        return res;
    }

    public static Map<String, Object> json2map(Object object) {
        Map<String, Object> res = null;
        try {
            String str_json = toJsonHasNull(object);
            res = gson.fromJson(str_json, new TypeToken<Map<String, Object>>() {
            }.getType());
        } catch (JsonSyntaxException e) {
            logger.error(e.getMessage(), e);
        }
        if (res == null) res = new HashMap<>();
        return res;
    }

    public static Map<String, String> json2mapStr(String str_json) {
        try {
            return json2mapStr(GsonUtil.getJsonObjectNoException(str_json));
        }catch (Exception e) {
            return new HashMap<>();
        }
    }

    public static Map<String, String> json2mapStr(JsonObject jsonObject) {
        Map<String, String> res = new HashMap<>();
        if (jsonObject != null){
            for (String key:jsonObject.keySet()){
                if (jsonObject.get(key).isJsonPrimitive()) {
                    res.put(key, GsonUtil.toJson(jsonObject.get(key)));
                }
            }
        }
        return res;
    }


    public static class MapTypeAdapter extends TypeAdapter<Object> {

        @Override
        public Object read(JsonReader in) throws IOException {
            JsonToken token = in.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    List<Object> list = new ArrayList<>();
                    in.beginArray();
                    while (in.hasNext()) {
                        list.add(read(in));
                    }
                    in.endArray();
                    return list;

                case BEGIN_OBJECT:
                    Map<String, Object> map = new LinkedTreeMap<>();
                    in.beginObject();
                    while (in.hasNext()) {
                        map.put(in.nextName(), read(in));
                    }
                    in.endObject();
                    return map;

                case STRING:
                    return in.nextString();

                case NUMBER:
                    /**
                     * 改写数字的处理逻辑，将数字值分为整型与浮点型。
                     */
                    double dbNum = in.nextDouble();

                    // 数字超过long的最大值，返回浮点类型
                    if (dbNum > Long.MAX_VALUE) {
                        return dbNum;
                    }


                    // 判断数字是否为整数值
                    long lngNum = (long) dbNum;
                    double dbNum2 = (double) lngNum;
                    String dbNunString = Double.toString(dbNum);
                    String dbNunString2 = Double.toString(dbNum2);
                    if (!dbNunString.equals(dbNunString2)) {
                        return dbNum;
                    } else {
                        return lngNum;
                    }

                case BOOLEAN:
                    return in.nextBoolean();

                case NULL:
                    in.nextNull();
                    return null;

                default:
                    throw new IllegalStateException();
            }
        }

        @Override
        public void write(JsonWriter out, Object value) throws IOException {
            // 序列化无需实现
        }
    }

    public static <T> List<T> getJsonList(String name) {
        List<T> list = gson.fromJson(name, new TypeToken<List<T>>() {
        }.getType());
        return list;
    }

    /**
     * 合并两个JsonObject
     *
     * @param firstObj
     * @param secondObj
     */
    public static JsonObject merge(JsonObject firstObj, JsonObject secondObj) {
        if (firstObj == null){
            firstObj = new JsonObject();
        }
        if (secondObj == null){
            secondObj = new JsonObject();
        }
        for (String keyInSecondObj : secondObj.keySet()) {
            if (!firstObj.has(keyInSecondObj)) {
                firstObj.add(keyInSecondObj, secondObj.get(keyInSecondObj));
            }
        }
        return firstObj;
    }

    /**
     * 合并两个JsonObject, 第二个参数具有更高的优先级
     *
     * @param firstObj
     * @param secondObj
     */
    public static JsonObject mergeSecond(JsonObject firstObj, JsonObject secondObj) {
        if (firstObj == null){
            firstObj = new JsonObject();
        }
        if (secondObj == null){
            secondObj = new JsonObject();
        }
        for (String keyInSecondObj : secondObj.keySet()) {
            firstObj.add(keyInSecondObj, secondObj.get(keyInSecondObj));
        }
        return firstObj;
    }

    public static JsonObject mergeSecond(Object obj1, Object obj2) {
        JsonObject firstObj = GsonUtil.getJsonObject(obj1);
        JsonObject secondObj = GsonUtil.getJsonObject(obj2);
        return mergeSecond(firstObj, secondObj);
    }

    /**
     * 合并两个JsonObject, 第二个参数具有更高的优先级,字符串衔接一起
     *
     * @param firstObj
     * @param secondObj
     */
    public static JsonObject mergeSecondString(JsonObject firstObj, JsonObject secondObj) {
        if (firstObj == null){
            firstObj = new JsonObject();
        }
        if (secondObj == null){
            secondObj = new JsonObject();
        }
        for (String keyInSecondObj : secondObj.keySet()) {
            if (firstObj.has(keyInSecondObj) && firstObj.get(keyInSecondObj).isJsonPrimitive() && secondObj.get(keyInSecondObj).isJsonPrimitive()){
                firstObj.addProperty(keyInSecondObj, firstObj.get(keyInSecondObj).getAsString()+"\n"+secondObj.get(keyInSecondObj).getAsString());
            }else {
                firstObj.add(keyInSecondObj, secondObj.get(keyInSecondObj));
            }
        }
        return firstObj;
    }

    /**
     * 剪裁一个jsonArray，取start，和end之间的数据
     *
     * @param jsonArray
     * @param start
     * @param end
     * @return
     */
    public static JsonArray subJsonArray(JsonArray jsonArray, Integer start, Integer end) {
        JsonArray subJsonArray = new JsonArray();
        if (jsonArray == null || jsonArray.size() < start){
            return subJsonArray;
        }
        if (jsonArray.size() < end) {
            end = jsonArray.size();
        }
        for (int i = start; i < end; i++){
            subJsonArray.add(jsonArray.get(i));
        }
        return subJsonArray;
    }

    /**
     * 将一个json字符串中的key，对应的值都设置成value
     * @param json
     * @param key
     * @param value
     * @return
     */
    public static String setKV(String json, String key, String value){
        if (StringUtil.isBlank(json) || StringUtil.isBlank(key)) return json;
        try {
            JsonObject jsonObject = setKV(GsonUtil.getJsonObject(json), key, value);
            return GsonUtil.toJson(jsonObject);
        }catch (Exception e){
            logger.error("setKV JsonObject: {}", e);
        }

        try {
            JsonArray jsonArray = setKV(GsonUtil.getJsonArray(json), key, value);
            return GsonUtil.toJson(jsonArray);
        }catch (Exception e){
            logger.error("setKV JsonArray: {}", e);
        }
        return json;
    }

    private static JsonObject setKV(JsonObject jsonObject, String key, String value){
        for (String k:jsonObject.keySet()){
            if (k.equals(key) && jsonObject.get(k).isJsonPrimitive()){
                jsonObject.addProperty(key, value);
            }
            if (jsonObject.get(k).isJsonObject()){
                setKV(jsonObject.get(k).getAsJsonObject(), key, value);
            }
            if (jsonObject.get(k).isJsonArray()){
                setKV(jsonObject.get(k).getAsJsonArray(), key, value);
            }
        }
        return jsonObject;
    }

    private static JsonArray setKV(JsonArray jsonArray, String key, String value){
        for (JsonElement jsonElement:jsonArray){
            if (jsonElement.isJsonObject()){
                setKV(jsonElement.getAsJsonObject(), key, value);
            }
        }
        return jsonArray;
    }

}
