package com.sankuai.mdp.compass.common.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.SetUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by skt on 2021/10/8.
 */
public class StringUtil extends com.sankuai.it.iam.common_base.utils.StringUtil {
    public boolean isZero(String s){
        String res = s.replaceAll("\\.","").replaceAll("0","");
        if ("".equals(res)){
            return true;
        }
        return false;
    }
    public static String unicodeToUtf8(String theString) {
        char aChar;
        int len = theString.length();
        StringBuffer outBuffer = new StringBuffer(len);
        for (int x = 0; x < len;) {
            aChar = theString.charAt(x++);
            if (aChar == '\\') {
                aChar = theString.charAt(x++);
                if (aChar == 'u') {
                    // Read the xxxx
                    int value = 0;
                    for (int i = 0; i < 4; i++) {
                        aChar = theString.charAt(x++);
                        switch (aChar) {
                            case '0':
                            case '1':
                            case '2':
                            case '3':
                            case '4':
                            case '5':
                            case '6':
                            case '7':
                            case '8':
                            case '9':
                                value = (value << 4) + aChar - '0';
                                break;
                            case 'a':
                            case 'b':
                            case 'c':
                            case 'd':
                            case 'e':
                            case 'f':
                                value = (value << 4) + 10 + aChar - 'a';
                                break;
                            case 'A':
                            case 'B':
                            case 'C':
                            case 'D':
                            case 'E':
                            case 'F':
                                value = (value << 4) + 10 + aChar - 'A';
                                break;
                            default:
                                throw new IllegalArgumentException(
                                        "Malformed   \\uxxxx   encoding.");
                        }
                    }
                    outBuffer.append((char) value);
                } else {
                    if (aChar == 't')
                        aChar = '\t';
                    else if (aChar == 'r')
                        aChar = '\r';
                    else if (aChar == 'n')
                        aChar = '\n';
                    else if (aChar == 'f')
                        aChar = '\f';
                    outBuffer.append(aChar);
                }
            } else
                outBuffer.append(aChar);
        }
        return outBuffer.toString();
    }
    public boolean numEqual(String st1, String st2){
        if (isDouble(st1) && isDouble(st2)){
            try{
                float num1 = Float.parseFloat(st1);
                float num2 = Float.parseFloat(st2);
                float c = Math.abs(num1-num2);
                if (c <= 0.0001)return true;
            }catch (Exception e){
                return false;
            }
        }
        return false;
    }
    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }
    public boolean isDouble(String str) {
        if (null == str || "".equals(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        return pattern.matcher(str).matches();
    }

    public String replaceLast(String str, String regex, String replacement) {
        return str.replaceFirst( "(?s)" + regex + "(?!.*?" + regex + ")", replacement );
    }


    public static boolean isJSON2(String str) {
        boolean result = false;
        try {
            Object obj= JSON.parse(str);
            result = true;
        } catch (Exception e) {
            result=false;
        }
        return result;
    }





    //定义一个similarRates方法获取两个字符串间不同字符的个数并求出两个字符串的相似率
    public static int similarRates(String str1 , String str2){
        //确定二维距离表distance的维度
        int str1Len = str1.length();
        int str2Len = str2.length();
        //如果一个字符串的内容为空就返回另一个字符串的长度
        if (str1Len == 0) return str2Len;
        if (str2Len == 0) return str1Len;
        //定义一张二维距离表distance
        int[][] distance = new int[str1Len + 1][str2Len + 1];
        //给二维数组的第一行第一列赋值
        int maxLen = str1Len > str2Len ? str1Len : str2Len;
        for (int num = 0; num < maxLen + 1; num++){
            if (num<str1Len + 1) distance[num][0] = num;
            if (num<str2Len + 1) distance[0][num] = num;
        }
        /**
         * 补全二维数组除第一行第一列的其他值
         * 行列索引进行对比，相同的话直接取左上方值，不同的话采用最小距离算法
         */
        for (int row = 1; row < str1Len+1; row++){
            char c1 = str1.charAt(row - 1);
            for (int col = 1; col < str2Len+1; col++){
                char c2 = str2.charAt(col - 1);
                if (c1 == c2) {
                    distance[row][col] = distance[row - 1][col - 1];
                } else {
                    // 最小距离算法就是，取该元素左上方值、左边值、上边值，找到三个之中的最小值再加1即最终距离
                    distance[row][col] = mostMin(distance[row-1][col], distance[row][col-1], distance[row-1][col-1]) + 1;
                }
            }
        }
        //二维数组中的最后一个元素即是两个字符串间不同字符的个数
        int notSimilarNum = distance[str1Len][str2Len];
        //求出相似率
        double similarRates = (1- (double)notSimilarNum / maxLen)*100;
        return (int)similarRates;
    }
    //取三个数中的最小值
    public static int mostMin(int up, int left, int upLeft){
        int min = up < left ? up : left;
        min = min < upLeft ? min : upLeft;
        return min;
    }


}
