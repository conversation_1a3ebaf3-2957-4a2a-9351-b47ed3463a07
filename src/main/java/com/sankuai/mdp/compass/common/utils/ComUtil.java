package com.sankuai.mdp.compass.common.utils;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.stream.IntStream;

/**
 * Created by xieyongrui on 2019/11/10.
 */

@Slf4j
public class ComUtil {
    private static final Logger logger = LoggerFactory.getLogger(ComUtil.class);


    /**
     * 缓存查询摸板，先查缓存，如果缓存查询失败再从数据库查询
     *
     * @param cacheSelector    查询缓存的方法
     * @param databaseSelector 数据库查询方法
     * @return T
     */
//    @SuppressWarnings("unchecked")
//    public static <T> T selectCacheByTemplate(CacheSelector<?> cacheSelector, Supplier<?> databaseSelector) {
////        try {
////            // 先查 Redis缓存
////            log.info("query data from redis ······");
////            return (T) cacheSelector.select();
////        } catch (Exception e) {
////            // 数据库查询
////            log.info("redis error：", e);
////            log.debug("query data from database ······");
////            return (T) databaseSelector.get();
////        }
//        return (T) databaseSelector.get();
//    }

    /**
     * 获取当前操作用户
     *
     * @return 用户信息
     */
//    public static User getCurrentUser() {
//        String token = (String) SecurityUtils.getSubject().getPrincipal();
//        String username = JWTUtil.getUsername(token);
//        UserService userService = SpringContextUtil.getBean(UserService.class);
//        CacheService cacheService = SpringContextUtil.getBean(CacheService.class);
//
//        return selectCacheByTemplate(() -> cacheService.getUser(username), () -> userService.findByName(username));
//    }

    /**
     * token 加密
     *
     * @param token token
     * @return 加密后的 token
     */
//    public static String encryptToken(String token) {
//        try {
//            EncryptUtil encryptUtil = new EncryptUtil(FebsConstant.TOKEN_CACHE_PREFIX);
//            return encryptUtil.encrypt(token);
//        } catch (Exception e) {
//            log.info("token加密失败：", e);
//            return null;
//        }
//    }

    /**
     * token 解密
     *
     * @param encryptToken 加密后的 token
     * @return 解密后的 token
     */
//    public static String decryptToken(String encryptToken) {
//        try {
//            EncryptUtil encryptUtil = new EncryptUtil(FebsConstant.TOKEN_CACHE_PREFIX);
//            return encryptUtil.decrypt(encryptToken);
//        } catch (Exception e) {
//            log.info("token解密失败：", e);
//            return null;
//        }
//    }

    /**
     * 驼峰转下划线
     *
     * @param value 待转换值
     * @return 结果
     */
    public static String camelToUnderscore(String value) {
        if (StringUtils.isBlank(value))
            return value;
        String[] arr = StringUtils.splitByCharacterTypeCamelCase(value);
        if (arr.length == 0)
            return value;
        StringBuilder result = new StringBuilder();
        IntStream.range(0, arr.length).forEach(i -> {
            if (i != arr.length - 1)
                result.append(arr[i]).append(StringPool.UNDERSCORE);
            else
                result.append(arr[i]);
        });
        return StringUtils.lowerCase(result.toString());
    }

    public static String doPython(String path, String params) {
        StringBuilder sb = new StringBuilder();
        BufferedReader in = null;
        BufferedReader isError = null;
//        BufferedReader in2 = null;
        try {
            Process pr = Runtime.getRuntime().exec("python3 " + path + " " + params);
//            InputStream error = process.getErrorStream();
//            pr.getErrorStream()
//            in = new BufferedReader(new InputStreamReader(
//                    pr.getErrorStream(), StandardCharsets.UTF_8));
            in = new BufferedReader(new InputStreamReader(
                    pr.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                sb.append(line);
            }
            isError = new BufferedReader(new InputStreamReader(pr.getErrorStream(), StandardCharsets.UTF_8));
            StringBuilder sbError = new StringBuilder();
            String lineError= null;
            while ((lineError= isError.readLine()) != null) {
                sbError.append(lineError);
                sbError.append("\n");
            }
            pr.waitFor();
            logger.info(sb.toString(),sb);
            logger.info(sbError.toString(),sbError);
        } catch (Exception e) {
            logger.info(e.getMessage(),e);
        } finally {
            try {
                if(in != null)
                    in.close();
                if(isError != null)
                    isError.close();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }

        return sb.toString();
    }

    public static String doPython2(String path, String params,String mockID){
        StringBuilder sb = new StringBuilder();
        try {
            String[] args1 = new String[] { "python3", path, params, mockID };
            Process proc = Runtime.getRuntime().exec(args1);// 执行py文件

            BufferedReader in = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            String line = null;
            while ((line = in.readLine()) != null) {
//                System.out.println(line);
                sb.append(line);
            }

            in.close();
            proc.waitFor();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

}
