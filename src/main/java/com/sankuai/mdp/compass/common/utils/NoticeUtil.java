package com.sankuai.mdp.compass.common.utils;

import com.sankuai.mdp.compass.entity.CompatilityJob;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * Created by skt on 2021/11/18.
 */
@Service
public class NoticeUtil {
    public static final String adminUser = "liujiao11";
    DxUtil dxUtil = new DxUtil();

    /**
     * 处理通知信息并发送给相关人员(云测报告过程通知)
     *
     * @param compatilityJob 兼容性任务对象
     * @param template       模板名称
     * @param typeStr        测试类型
     * @param testScenes     测试场景
     * @param finishType     完成类型
     * @param leftCount      剩余测试设备数量
     * @param createdAt      开始时间
     * @param reportUrl      报告链接
     * @param misId          接收通知的人员ID
     */
    public void processNotice(CompatilityJob compatilityJob, String template,
                              String typeStr, String testScenes, String finishType,
                              int leftCount, String createdAt, String reportUrl, String misId) {
        try {
            dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n 【任务ID】："
                    + compatilityJob.getId() + "\n" +
                    "【模版名称】：" + template + "\n" +
                    "【测试类型】：" + typeStr + "\n" +
                    "【测试场景】：" + testScenes + "\n" +
                    "【测试进度】：⏳ " + finishType + "新增结果了，剩余" +
                    leftCount + "个设备正在测试中 \n" +
                    "【开始时间】：" + createdAt + "\n" +
                    "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                    "如有问题请联系" + adminUser, misId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理通知信息并发送给相关人员(云测报告结果通知)
     *
     * @param compatilityJob 兼容性任务对象
     * @param template       模板名称
     * @param typeStr        测试类型
     * @param testScenes     测试场景
     * @param finishType     完成类型
     * @param leftCount      剩余测试设备数量
     * @param createdAt      开始时间
     * @param reportUrl      报告链接
     * @param misId          接收通知的人员ID
     */
    public void finishNotice(CompatilityJob compatilityJob, String template,
                             String typeStr, String testScenes, String createdAt, String finishTime,
                             String reportUrl, String wordUrl, String misId) {
        try {
            dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n 【任务ID】：" + compatilityJob.getId() + "\n" +
                    "【模版名称】：" + template + "\n" +
                    "【测试类型】：" + typeStr + "\n" +
                    "【测试场景】：" + testScenes + "\n" +
                    "【测试进度】：✅ 已完成，请及时对结果进行确认\n" +
                    "【开始时间】：" + createdAt + "\n" +
                    "【完成时间】：" + finishTime + "\n" +
                    "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                    "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                    "如有问题请联系" + adminUser, misId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理通知信息并发送给相关人员(云测报告异常结果通知)
     *
     * @param compatilityJob 兼容性任务对象
     * @param template       模板名称
     * @param typeStr        测试类型
     * @param testScenes     测试场景
     * @param finishType     完成类型
     * @param leftCount      剩余测试设备数量
     * @param createdAt      开始时间
     * @param reportUrl      报告链接
     * @param misId          接收通知的人员ID
     */
    public void alertNotice(CompatilityJob compatilityJob, String template,
                            String typeStr, String testScenes, String finishType,
                            int leftCount, String createdAt, String reportUrl, String misId, boolean isSuccess) {
        try {
            dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n 【任务ID】："
                    + compatilityJob.getId() + "\n" +
                    "【模版名称】：" + template + "\n" +
                    "【测试类型】：" + typeStr + "\n" +
                    "【测试场景】：" + testScenes + "\n" +
                    "【测试进度】：⏳ " + finishType + "出现异常结果 \n" +
                    "【此次结果】：" + (isSuccess ? "✅正常" : "❌异常") + "\n" +
                    "【开始时间】：" + createdAt + "\n" +
                    "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                    "如有问题请联系" + adminUser, misId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
