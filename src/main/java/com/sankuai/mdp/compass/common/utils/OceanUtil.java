package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.bi.ocean.config.api.model.EventModulesWithBLOBs;
import com.sankuai.bi.ocean.config.api.query.EntityQueryParam;
import com.sankuai.bi.ocean.config.api.query.EventQueryBody;
import com.sankuai.bi.ocean.config.api.query.EventQueryConfig;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;


/**
 * Created by xieyongrui on 2020/2/5.
 */
public class OceanUtil {
    private static String URL = "https://open-ocean.sankuai.com/api/client/v3/common/event";
    private static String URI = "/api/client/v3/common/event";
    private static final String AUTH_METHOD = "MWS";
    private static final String APPKEY = "com.sankuai.sigma.compass";
    private static final String APPSECRET = "59a21241ace51466f50e132815cad83a";
    private static final String ALGORITHM_HMAC_SHA1 = "HmacSHA1";

    public JsonObject getEntityIdentifierInfo(String identifer) {

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(URL);
        String date = authDate();
        String authorization = getAuthorization(URI, "POST", date, APPKEY);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
        headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headers.set("Date", date);
        headers.set("Authorization", authorization);
        EventQueryBody queryBody = new EventQueryBody();
        EntityQueryParam<EventModulesWithBLOBs> param = new EntityQueryParam<>();
        param.setEntityIdentifier(identifer);
        queryBody.setParam(param);
        EventQueryConfig config = new EventQueryConfig();
        config.setOtherEntityAttributesRequired(true);
        config.setBusinessFieldsRequired(true);
        queryBody.setConfig(config);
        org.springframework.http.HttpEntity entity = new org.springframework.http.HttpEntity<>(queryBody, headers);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                builder.build().encode().toUri(), HttpMethod.POST, entity, String.class);
        return (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) ?
                null : new JsonParser().parse(responseEntity.getBody()).getAsJsonObject();
    }

    public static String getSignature(String clientSecret, String requestMethod, String requestPath, String requestdate) throws InvalidKeyException, NoSuchAlgorithmException {
        String string_to_sign = requestMethod + " " + requestPath + "\n" + requestdate;
        return generateSignature(string_to_sign.getBytes(), clientSecret.getBytes());
    }

    private static String generateSignature(byte[] data, byte[] key) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec signingKey = new SecretKeySpec(key, ALGORITHM_HMAC_SHA1);
        Mac mac = Mac.getInstance(ALGORITHM_HMAC_SHA1);
        mac.init(signingKey);
        byte[] rawHmac = mac.doFinal(data);
        return new String(Base64.encodeBase64(rawHmac));
    }

    public static String authDate() {
        DateFormat df = new SimpleDateFormat("EEE, d MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.format(new Date());
    }

    private static String getAuthorization(String path, String methodName,String date,String clientId) {
        String signature = null;
        try {
            signature = getSignature(APPSECRET, methodName, path, date);
        } catch (InvalidKeyException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return String.format("MWS %s:%s",clientId,signature);
    }

}
