package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xieyongrui on 2020/6/12.
 */
public class HpxUtil {
    private static final Logger logger = LoggerFactory.getLogger(HpxUtil.class);
    public static final String GET_COMPONENT = "https://hpx.sankuai.com/api/open/getComponentMapping?appName=";
    public static final String GETByPackageNameAndBuildNum = "https://hpx.sankuai.com/api/open/getSpecifiedTaskListByTypeList";
    public static  final  String getBranchNewestPackageInfo = "https://hpx.sankuai.com/api/open/getBranchNewestPackageInfo";

    private static String ANDROID_APPNAME = "com.sankuai.meituan";
    private static String IOS_APPNAME = "imeituan";
    private String APPNAME = "";

    public List<String> getCompList(String platform) {
        try {
            List<String> result = new ArrayList<>();
            if (platform.equals("Android")) {
                APPNAME = ANDROID_APPNAME;
            } else {
                APPNAME = IOS_APPNAME;
            }
            String response = HttpUtil.vGet(GET_COMPONENT+APPNAME, TokenUtil.getToken("hpx_token"));
            JsonObject jsonObject = new JsonParser().parse(response).getAsJsonObject();
            JsonArray jsonArray = jsonObject.getAsJsonArray("data");

            for (int i = 0; i < jsonArray.size(); i++) {
                String name = jsonArray.get(i).getAsJsonObject().get("componentName").getAsString();
                result.add(name);
            }
            return result;
        } catch (Exception e) {
            logger.info("查询失败",e);
            return null;
        }

    }

    public String getBuildInfo(String platform, Integer buildNum) throws Exception {
        if (platform.equals("Android")) {
            APPNAME = ANDROID_APPNAME;
        } else {
            APPNAME = IOS_APPNAME;
        }
        JSONObject data = new JSONObject();
        data.put("appName",APPNAME);
        data.put("buildNumber",buildNum);
        JSONObject o = HttpUtil.sendPostJson(GETByPackageNameAndBuildNum,data);
        System.out.println(o);

        return o.toString();
    }
    public  String getBranchNewestPackageInfo(String appName,String branch,String buildTypeName){
        String requestUrl = getBranchNewestPackageInfo+String.format("?appName=%s&branch=%s&buildTypeName=%s",appName,branch,buildTypeName);
        String response = HttpUtil.vGet(requestUrl, TokenUtil.getToken("hpx_token"));
        return response;
    }


}
