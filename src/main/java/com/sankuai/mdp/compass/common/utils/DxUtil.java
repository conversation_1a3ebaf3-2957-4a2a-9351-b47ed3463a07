package com.sankuai.mdp.compass.common.utils;

import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.xm.pub.push.Pusher;
import com.sankuai.xm.pub.push.PusherBuilder;
import org.springframework.stereotype.Component;

/**
 * Created by guanxin on 2020/4/24.
 * 单播消息测试 推送给个人
 * 适用公众号类型：系统号，应用号
 *
 */
@Component
public class DxUtil {
    private static Pusher pusher;
    private static Pusher CovPusher;
    private static Pusher pusherForIndividual;
    private static Pusher pushertogroup;
    private static Pusher kingKongPusherForIndividual;
    private static Pusher kingKongPushertogroup;
    static {
        /**
         *
         * withAppkey: 开发者信息中的appKey
         * withApptoken: 开发者信息中的appSecret
         * withTargetUrl: 服务地址，如果访问线上服务，请使用线上公众号
         *                线下地址：http://api.xm.test.sankuai.com/api/pub/push
         *                线上地址：https://xmapi.vip.sankuai.com/api/pub/push
         * withFromUid: 填写PubID，即公众号ID，因为这里是long型，需要加L
         *
         * **/
        pusher = PusherBuilder.defaultBuilder().withAppkey("0201062116430266").withApptoken("8089db9c46dadb7612ede8a0729625a5")
                .withTargetUrl("https://xmapi.vip.sankuai.com/api/pub/push").withFromUid(137438954427L).build();
        CovPusher = PusherBuilder.defaultBuilder().withAppkey("02k4129233508110").withApptoken("5ac604699899fbcf40f74917823dc18b")
                .withTargetUrl("https://xmapi.vip.sankuai.com/api/pub/push").withFromUid(137535362314L).build();

    }
    static {
        /**
         *
         * withAppkey: 开发者信息中的appKey
         * withApptoken: 开发者信息中的appSecret
         * withTargetUrl: 服务地址，如果访问线上服务，请使用线上公众号
         *                线下地址：http://api.xm.test.sankuai.com/api/pub/push
         *                线上地址：https://xmapi.vip.sankuai.com/api/pub/push
         * withFromUid: 填写PubID，即公众号ID，因为这里是long型，需要加L
         *
         * **/
        pusherForIndividual = PusherBuilder.defaultBuilder().withAppkey("0h657100505092X2").withApptoken("e27cb78dcca875a5b7a425e9962a5df0")
                .withTargetUrl("https://xmapi.vip.sankuai.com/api/pub/push").withFromUid(137453378441L).build();

    }

    static {
        /**
         *
         * withAppkey: 开发者信息中的appKey
         * withApptoken: 开发者信息中的appSecret
         * withTargetUrl: 服务地址，如果访问线上服务，请使用线上公众号
         *                线下地址：http://api.xm.test.sankuai.com/api/pub/pushToRoom
         *                线上地址：https://xmapi.vip.sankuai.com/api/pub/pushToRoom
         * withFromUid: 填写PubID，即公众号ID，因为这里是long型，需要加L
         * withToAppid: 1为大象
         * **/
        pushertogroup = PusherBuilder.defaultBuilder().withAppkey("0h657100505092X2").withApptoken("e27cb78dcca875a5b7a425e9962a5df0")
                .withTargetUrl("https://xmapi.vip.sankuai.com/api/pub/pushToRoom").withFromUid(137453378441L).build();
    }

    static {
        /**
         * KingKong 公众号推送配置
         * withAppkey: 400211354274zz22
         * withApptoken: 3ebee6f428ca4f53f3db1c00d1ba7b45
         * withFromUid: 138548532393L
         * 推送地址同原有
         */
        kingKongPusherForIndividual = PusherBuilder.defaultBuilder().withAppkey("400211354274zz22").withApptoken("3ebee6f428ca4f53f3db1c00d1ba7b45")
                .withTargetUrl("https://xmapi.vip.sankuai.com/api/pub/push").withFromUid(138548532393L).build();
        kingKongPushertogroup = PusherBuilder.defaultBuilder().withAppkey("400211354274zz22").withApptoken("3ebee6f428ca4f53f3db1c00d1ba7b45")
                .withTargetUrl("https://xmapi.vip.sankuai.com/api/pub/pushToRoom").withFromUid(138548532393L).build();
    }

    /**
     * 推送消息给个人
     * 此处使用Uid数组作为接收人
     * 用户Uid查询：针对于少量固定的用户推送可直接在该页面https://dxw.sankuai.com/pubinfo/trans查询用户完整账号对应的id;
     针对于大量用户可申请权限访问用户信息查询服务，申请地址为：Uinfo开放接口 https://wiki.sankuai.com/pages/viewpage.action?pageId=192957449
     * **/


    /**
     * 推送消息给个人
     * 此处使用mis号作为接收人
     * **/
    public String sendToPersionByCompass(String message, String... receivers){
        try{
            return pusher.push(message, receivers).toString();
        }catch (Exception e){
            return "";
        }
    }

    public String sendMessageByCoverageHelper(String message, String... receivers){
        try{
            return CovPusher.push(message, receivers).toString();
        }catch (Exception e){
            return "";
        }
    }

     /**
     * 推送消息给个人
     * 此处使用mis号作为接收人
     * **/
    public String sendToIndividualByCompassAuto(String message, String... receivers) throws Exception {
        return pusherForIndividual.push(message, receivers).toString();
    }
    /**
     * 推送消息给组
     * 此处使用room号作为接收人
     * **/
    public String sendToGroupByCompass(String message, long receivers) throws Exception {
        return pushertogroup.pushToRoom(message, receivers).toString();
    }

    /**
     * KingKong 公众号推送消息给个人
     */
    public String sendToIndividualByKingKong(String message, String... receivers) throws Exception {
        return kingKongPusherForIndividual.push(message, receivers).toString();
    }
    /**
     * KingKong 公众号推送消息给群组
     */
    public String sendToGroupByKingKong(String message, long receivers) throws Exception {
        return kingKongPushertogroup.pushToRoom(message, receivers).toString();
    }
}
