package com.sankuai.mdp.compass.common.enums;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.Data;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Data
@Repository
public class Resp<T> implements Serializable {
    private String code;
    private String msg;
    private T data;

    public Resp() {
    }

    public Resp(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
    public Resp(Integer code, String msg, T data) {
        this.code = String.valueOf(code);
        this.msg = msg;
        this.data = data;
    }

    /**
     * 返回一个成功code码为200的json
     *
     * @param data json里的主要内容
     * @return
     */
    public static Resp success(Object data) {
        return new Resp(200, "成功",  data);
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setCode(Integer code){this.code = String.valueOf(code);}

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    /**
     * 返回一个returnData为空对象的成功消息的json
     *
     * @return
     */
    public static Resp success() {
        return new Resp(ResultEnum.Success.getCode(), ResultEnum.Success.getMsg(), "");
    }

    public static Resp success(JsonArray jsonArray) {
        return new Resp(ResultEnum.Success.getCode(), ResultEnum.Success.getMsg(),  jsonArray);
    }

    public static Resp success(JsonObject jsonObject) {
        return new Resp(ResultEnum.Success.getCode(), ResultEnum.Success.getMsg(), JSONObject.parse(jsonObject.toString()));
    }

    /**
     * 返回错误信息JSON
     *
     * @param errorEnum 错误码的errorEnum
     * @return
     */
    public static Resp error(ErrorEnum errorEnum) {
        return new Resp(errorEnum.getErrorCode(), errorEnum.getErrorMsg(), null);
    }

    public static Resp error() {
        return new Resp(0, "失败", null);
    }

    public static Resp error(ErrorEnum errorEnum,String msg) {
        return new Resp(errorEnum.getErrorCode(),msg ,null);
    }


    public static Resp error(ErrorEnum errorEnum, Object data) {
        return new Resp(errorEnum.getErrorCode(), errorEnum.getErrorMsg(), data);
    }

    public static Resp error(ErrorEnum errorEnum, String msg, Object data) {
        return new Resp(errorEnum.getErrorCode(), msg, data);
    }

    public static Resp error(int code, String msg, Object data) {
        return new Resp(String.valueOf(code), msg, data);
    }

}
