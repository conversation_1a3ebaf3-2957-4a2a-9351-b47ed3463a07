package com.sankuai.mdp.compass.common.domain;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/11/10.
 */
public class ComConstant {

        /**
         * user相关
         */
        // user缓存前缀
        public static final String USER_CACHE_PREFIX = "febs.cache.user.";
        // user角色缓存前缀
        public static final String USER_ROLE_CACHE_PREFIX = "febs.cache.user.role.";
        // user权限缓存前缀
        public static final String USER_PERMISSION_CACHE_PREFIX = "febs.cache.user.permission.";
        // user个性化配置前缀
        public static final String USER_CONFIG_CACHE_PREFIX = "febs.cache.user.config.";
        // token缓存前缀
        public static final String TOKEN_CACHE_PREFIX = "febs.cache.token.";

        // 存储在线用户的 zset前缀
        public static final String ACTIVE_USERS_ZSET_PREFIX = "febs.user.active";

        // 排序规则： descend 降序
        public static final String ORDER_DESC = "descend";
        // 排序规则： ascend 升序
        public static final String ORDER_ASC = "ascend";

        // 按钮
        public static final String TYPE_BUTTON = "1";
        // 菜单
        public static final String TYPE_MENU = "0";

        public static final String USER_NAME = "ptmobiletest";

        //临时文件目录
        public static final String OUT_PUT = "./output";
        //接口自动化文件目录
        public static final String INTERFACE_OUT_PUT = "./output";
        //本地截图目录
        public static final String UI_DIFF_TEST_DIR = "/test/";
//        public static final String UI_DIFF_TEST_DIR = "/Users/<USER>/Downloads/test3/";

        /**
         * Venus
         */
        public static final String BUCKET = "ptautotest";

        public static final String CLIENT_ID = "ptautotest";

        public static final String CLIENT_SECRET = "1292ee16e73a9d36aa52cbd45b3d0bcc";

        public static final String P = "50";

}
