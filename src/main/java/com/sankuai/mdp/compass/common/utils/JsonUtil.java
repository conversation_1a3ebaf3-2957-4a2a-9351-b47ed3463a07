package com.sankuai.mdp.compass.common.utils;

import com.google.gson.*;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.enums.Significance;
import com.sankuai.mdp.compass.robust.config.RobustCaseConfig;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import com.sankuai.mdp.compass.robust.util.RobustUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by skt on 2022/4/12.
 */
@Slf4j
public class JsonUtil {

    static String homeMine = "{\"code\":0,\"data\":{\"actionBar\":{\"biz\":{\"account\":{\"footstep\":{\"gameName\":\"走路赚钱\",\"iconUrl\":\"https://p0.meituan.net/linglong/dd83de89907e30b86483be08f3d99593134193.gif\",\"targetUrl\":\"imeituan://www.meituan.com/web?notitlebar=1&url=https://ux.meituan.com/footstep/index.html\",\"content\":\"走路赚钱\",\"isShow\":false},\"avatarUrl\":\"https://img.meituan.net/avatar/7f1eddcb4d1d7fcf855bb13529c9c37e81540.jpg\",\"nickname\":\"测试页面\",\"iconDestUrl\":\"imeituan://www.meituan.com/userinfo\",\"id\":*************,\"growthDestUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=group&mrn_entry=member-center&mrn_component=member-center\",\"growthValue\":35.12,\"verifyInfo\":{\"verifyStatus\":0,\"more\":{\"activity_id\":\"-999\",\"biz_id\":\"65\",\"display_name\":\"去实名\",\"config_id\":\"5615\",\"activity_from\":\"-999\"},\"link\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fnpay.meituan.com%2Ffup%2Fverification%2Findex.html\",\"content\":\"去实名\"}}},\"titleColor\":\"#222222\",\"type\":\"simple_slide_gradient\"},\"extra\":{\"moduleVersion\":2},\"groups\":[{\"id\":\"minepage_account_group\",\"items\":[],\"showCount\":-1,\"style\":{\"background\":{},\"borderWidth\":\"0\",\"margin\":[\"0\",\"0\",\"10\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"},{\"items\":[],\"showCount\":-1,\"style\":{\"background\":{},\"borderWidth\":\"1.23\",\"margin\":[\"30\",\"0\",\"10\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"}],\"id\":\"minepage\",\"itemCount\":17,\"overlap\":true,\"refreshTop\":{\"enable\":true,\"type\":\"default\"},\"style\":{\"background\":{\"color\":\"#F4f4f4\"},\"ab_info\": \"{\\\"ab_group_homepage_v12_youxuan\\\":\\\"-999\\\",\\\"ab_group_homepage_v12\\\":\\\"-999\\\"}\",\"zIndex\":0}}}";
    //    List<List> arrayList = new ArrayList<>();
    static int count = 0;

    public JsonUtil() {

    }

    private static final Logger logger = LoggerFactory.getLogger(HpxUtil.class);

    MockUtil mockUtil = new MockUtil();
    StringUtil st = new StringUtil();
    private static final String rootPath = "$";



    /**
     * 用来递归jsonObject拿到所有子节点的kv
     *
     * @param object
     */
    public static List readObject(Object object, String jsonPath, List<List> arrayList) {

        if (object instanceof JsonObject) {
            JsonObject jsonObject = (JsonObject) object;
            String parentPath = jsonPath;
            for (Map.Entry entry : jsonObject.entrySet()) {
                Object o = entry.getValue();
                String key = entry.getKey().toString();
                if (key.contains("."))continue;
                jsonPath = parentPath + "." + key;
               // documentContext.put(parentPath,key,o);
                if (o instanceof JsonNull) {
                    continue;
                }
                if (o instanceof JsonObject) {
                    List list = new ArrayList();
                    list.add(jsonPath);
                    list.add("");
                    list.add(Significance.JsObject);
                    arrayList.add(list);
                    readObject(o, jsonPath, arrayList);
                } else if (o instanceof JsonArray) {
                    List list = new ArrayList();
                    list.add(jsonPath);
                    list.add("");
                    list.add(Significance.Array);
                    arrayList.add(list);
                    readArray((JsonArray) o, jsonPath, arrayList);
                } else {
                    JsonPrimitive value = (JsonPrimitive) entry.getValue();
                    Significance mean = ValueMeaning(value);
                    List list = new ArrayList();
                    list.add(jsonPath);
                    list.add(value);
                    list.add(mean);
                    arrayList.add(list);
                  if(mean == Significance.Dict){
                    DictMeaning(value,jsonPath,arrayList);
                  }
                }
            }
        }
        return arrayList;
    }

    public static Significance ValueMeaning(JsonPrimitive value) {
        String test = "";
        if (value.isBoolean()) {
            return Significance.Bool;
        } else if (value.isNumber()) {
            return NumberMeaning(value);
        } else {
            return StringMeaning(value);
        }
    }


    protected static void readArray(JsonArray array, String jsonPath, List<List> arrayList) {
        String parentPath = jsonPath;
        for (int i = 0; i < array.size(); i++) {
            Object value = array.get(i);
            jsonPath = parentPath + "[" + i + "]";
            if (value instanceof JsonNull) {
                continue;
            }
            if (value instanceof JsonArray) {
                List list = new ArrayList();
                list.add(jsonPath);
                list.add("");
                list.add(Significance.Array);
                arrayList.add(list);
                readArray((JsonArray) value, jsonPath, arrayList);
            } else if (value instanceof JsonObject) {
                readObject((JsonObject) value, jsonPath, arrayList);
            } else {
                JsonPrimitive temo = (JsonPrimitive) value;
                Significance mean = ValueMeaning(temo);
                if (mean.equals(Significance.JsonString)) {
                    value = ((JsonPrimitive) value).getAsString();
                }
                List list = new ArrayList();
                list.add(jsonPath);
                list.add(value);
                list.add(mean);
                arrayList.add(list);
            }
        }
    }

    private static Significance NumberMeaning(JsonPrimitive value) {
        try {
            String temp = value.getAsString();
            boolean double_flag = Pattern.compile("^-?([1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*|0?\\.0+|0)$").matcher(temp).find();
            if (double_flag) return Significance.FLOAT;
            if (temp.length() < 10) {
                return Significance.INT;
            } else {
                if (temp.length() == 13) return Significance.Millisecond;
                if (temp.length() == 10) return Significance.Second;
            }
        } catch (Exception e) {
            log.info("NumberMeaning" + e.toString());
        }
        return Significance.INT;
    }
    private static Significance StringMeaning(JsonPrimitive value) {
        String valueString = value.getAsString();
        if (StringUtil.isBlank(valueString)) return Significance.Content;


        //字符串类型的value都有哪几种类型呢：内容、颜色、地址、参数
        String isColor = "^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$";
        String isScheme = "imeituan://";
        String isLink = "http";
        if (Pattern.matches(isColor, valueString)) {
            return Significance.Color;
        }
        Pattern SchemePattern = Pattern.compile(isScheme);
        Matcher SchemeMatcher = SchemePattern.matcher(valueString);
        Pattern LinkPattern = Pattern.compile(isLink);
        Matcher LinkMatcher = LinkPattern.matcher(valueString);
        if (valueString.contains("{") && valueString.contains("}") && valueString.contains(":")) {
          return Significance.Dict;
        }
        if (LinkMatcher.find()) {
            return Significance.Link;
        }
        boolean int_flag = Pattern.compile("^-?[1-9]\\d*$").matcher(valueString).find();
        boolean double_flag = Pattern.compile("^-?([1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*|0?\\.0+|0)$").matcher(valueString).find();
        if (int_flag || "0".equals(valueString)) return Significance.INTofString;
        if (double_flag) return Significance.FLOATofString;
        if (SchemeMatcher.find()) {
            return Significance.Scheme;
        }
        return Significance.Content;

    }

  private static void DictMeaning(JsonPrimitive value,String jsonpath,List<List> arrayList) {
      String valueString = value.getAsString();
      JsonObject valueObject = new JsonObject();
      valueObject = new JsonParser().parse(valueString).getAsJsonObject();
      readObject(valueObject,jsonpath,arrayList);
  }
    protected static void deleteKey(String originResponse, List<List> pathLists, String url, ArrayList<RobustResult> robustResults, boolean needMock) {
        JsonParser jsonParser = new JsonParser();
        DocumentContext tempDocumentContext = null;
        String keyPath = "";
        int total = 0;
        for (List pathList : pathLists) {
            total += 1;
            keyPath = pathList.get(0).toString();
            Configuration configuration = Configuration.builder()
                    .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
            DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(originResponse);
            tempDocumentContext = tempItemDataJson.delete(keyPath);
            originResponse = tempDocumentContext.jsonString();
//            originResponse = jsonParser.parse(tempDocumentContext.jsonString()).getAsString();
        }
        JsonObject caseData = null;
        caseData = jsonParser.parse(tempDocumentContext.jsonString()).getAsJsonObject();
        Integer mockId = 0;
        if (needMock) {
            JsonObject mockResult = new MockUtil().create(url, caseData);
            if (null != mockResult) {
                mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
            }
        }
        JSONArray modifyDiffList = new JSONArray();
        Map diffMap = new HashMap<>();
        diffMap.put("modify_desc", "删除数组中包含" + keyPath + "的key" + " 共构造了" + total + "个字段");
        diffMap.put("key", keyPath + " ");
        diffMap.put("value", "删除key");

        modifyDiffList.put(diffMap);


        JSONObject modifyDetailMap = new JSONObject();
        modifyDetailMap.put("modify_url", url);
        if (needMock) {
            modifyDetailMap.put("mockId", mockId);
        } else {
            modifyDetailMap.put("caseData", caseData);
        }
        modifyDetailMap.put("modify_diff", modifyDiffList);

        JSONArray modifyDetailList = new JSONArray();
        modifyDetailList.put(modifyDetailMap);

        RobustResult robustResult = new RobustResult();
        robustResult.setModifyDetail(modifyDetailList.toString());
        robustResults.add(robustResult);

    }
    protected static void setKey(String originResponse, List<List> arrayLists, Object targetValue, String url, ArrayList<RobustResult> robustResults, boolean needMock) {
        Integer mockId = 0;
        JsonParser jsonParser = new JsonParser();
        DocumentContext tempDocumentContext = null;
        String keyPath = "";
        String keyPathNew = "";
        int count = arrayLists.size();
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();

        DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(originResponse);
        for (List arrayList : arrayLists) {
            keyPath = arrayList.get(0).toString();
            keyPathNew += "【" + keyPath + "】 ";
            if (null == tempDocumentContext) {
                tempDocumentContext = tempItemDataJson.set(keyPath, targetValue);
            } else {
                tempDocumentContext = tempDocumentContext.set(keyPath, targetValue);
            }
        }

        JsonObject caseData = null;
        caseData = jsonParser.parse(tempDocumentContext.jsonString()).getAsJsonObject();

        if (needMock && EnvUtil.isOnline()) {
            for (int i = 0; i < 2; i++) {
                //如果mock失败了，重试一次
                JsonObject mockResult = new MockUtil().create(url, caseData);
                if (null != mockResult) {
                    mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                    break;
                }
            }
        }
        JSONArray modifyDiffList = new JSONArray();

        Map diffMap = new HashMap<>();
        diffMap.put("key", keyPathNew);
        diffMap.put("value", targetValue);
        if ("".equals(targetValue)) {
            targetValue = "空字符串";
        }
        diffMap.put("modify_desc", "共构造了" + count + "个字段，" + keyPathNew + " 值为 " + targetValue);

        modifyDiffList.put(diffMap);

        JSONObject modifyDetailMap = new JSONObject();
        modifyDetailMap.put("modify_url", url);
        if (needMock) {
            modifyDetailMap.put("mockId", mockId);
        } else {
            modifyDetailMap.put("caseData", caseData);
        }
        modifyDetailMap.put("modify_diff", modifyDiffList);
        JSONArray modifyDetailList = new JSONArray();
        modifyDetailList.put(modifyDetailMap);
        RobustResult robustResult = new RobustResult();
        robustResult.setModifyDetail(modifyDetailList.toString());
        robustResults.add(robustResult);

    }
    protected static void setKey4JsonString(String originResponse, List<List> arrayLists, Object targetValue, String url, ArrayList<RobustResult> robustResults, boolean needMock, String JsonModifyKey) {
        Integer mockId = 0;
        JsonParser jsonParser = new JsonParser();
        DocumentContext tempDocumentContext = null;
        String keyPath = "";
        String keyPathNew = "";
        int count = arrayLists.size();
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();

        DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(originResponse);
        for (List arrayList : arrayLists) {
            keyPath = arrayList.get(0).toString();
            keyPathNew += "【" + keyPath + "】 ";
            if (null == tempDocumentContext) {
                tempDocumentContext = tempItemDataJson.set(keyPath, targetValue);
            } else {
                tempDocumentContext = tempDocumentContext.set(keyPath, targetValue);
            }
        }

        JsonObject caseData = null;
        caseData = jsonParser.parse(tempDocumentContext.jsonString()).getAsJsonObject();

        if (needMock && EnvUtil.isOnline()) {
            for (int i = 0; i < 2; i++) {
                //如果mock失败了，重试一次
                JsonObject mockResult = new MockUtil().create(url, caseData);
                if (null != mockResult) {
                    mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                    break;
                }
            }
        }
        JSONArray modifyDiffList = new JSONArray();

        Map diffMap = new HashMap<>();
        diffMap.put("key", "jsonString " + JsonModifyKey);
        diffMap.put("value", "");
        if ("".equals(targetValue)) {
            targetValue = "空字符串";
        }

        modifyDiffList.put(diffMap);

        JSONObject modifyDetailMap = new JSONObject();
        modifyDetailMap.put("modify_url", url);
        if (needMock) {
            modifyDetailMap.put("mockId", mockId);
        } else {
            modifyDetailMap.put("caseData", caseData);
        }
        modifyDetailMap.put("modify_diff", modifyDiffList);
        JSONArray modifyDetailList = new JSONArray();
        modifyDetailList.put(modifyDetailMap);
        RobustResult robustResult = new RobustResult();
        robustResult.setModifyDetail(modifyDetailList.toString());
        robustResults.add(robustResult);

    }



    /**
     * @param originResponse 原始数据
     * @param arrayList      readObject生产的构造组
     * @param url            api链接
     * @param robustResults  结果
     * @param needMock       是否需要构造真实mock
     * @oaram level 递归层级，超过2暂时不够造了，避免jsonString构造太深导致其他预期外的问题
     */
    public static JsonObject createCase(String originResponse, List<List> arrayList, String url, ArrayList<RobustResult> robustResults, boolean needMock, int level, boolean needSaveResult, JSONArray modifyDetailList) {
        if (level > 2) return null;
        JsonObject changeResponse = null;
        Significance sign = (Significance) arrayList.get(0).get(2);
        Boolean childNode = (Boolean) arrayList.get(0).get(4);
        ArrayList<String> mockCaseList = new ArrayList<>();
        RobustCaseConfig robustCaseConfig = new RobustCaseConfig();
        if (robustCaseConfig.caseConfig.containsKey(sign)) {
            mockCaseList = robustCaseConfig.caseConfig.get(sign);
        }
        int listSize = arrayList.size();
        int mockCaseSize = mockCaseList.size();
        //// FIXME: 2023/3/21  优选接口有些key带着"."
        if (childNode  && listSize >= mockCaseSize) {
            try{
                changeResponse = new RobustUtil().setKeyNew(originResponse, arrayList, mockCaseList, url, robustResults, needMock, needSaveResult, modifyDetailList);
            }catch (Exception e){
                log.error(e.toString());
            }
        } else {
            for (String mockCase : mockCaseList) {
                modifyDetailList = new JSONArray();
                try{
                    changeResponse = new RobustUtil().setKey(originResponse, arrayList, mockCase, url, robustResults, needMock, modifyDetailList);
                }catch (Exception e){
                    log.error(e.toString());
                }

            }
        }
        return changeResponse;
    }


            /* 处理jsonString的逻辑，暂时不要删掉
            if (sign.equals(Significance.JsonString)) {
            Object obj = arrayList.get(0).get(1);
            String order = "";
            if (obj instanceof String) {
                order = (String) obj;
            } else if (obj instanceof JsonPrimitive) {
                order = ((JsonPrimitive) obj).getAsString();
            }

            JsonObject orderCard = new JsonParser().parse(order).getAsJsonObject();
            List<List> allField = new ArrayList<>();
            readObject(orderCard, "$", allField);
            System.out.println(allField);

            ArrayList<RobustResult> tempResult = new ArrayList<>();
            for (List list : allField) {
                ArrayList pathList = new ArrayList();
                pathList.add(list);
                createCase(order, pathList, url, tempResult, false, level + 1);
            }
            //tempResult里的caseData取出来，直接塞到待构造的数据中
            for (RobustResult robustResult : tempResult) {
                Configuration configuration = Configuration.builder()
                        .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
                String modifyDetail = robustResult.getModifyDetail();
                DocumentContext tempTemplateDataJson = JsonPath.using(configuration).parse(modifyDetail);
                JsonArray tempTemplateData = new JsonParser().parse(tempTemplateDataJson.jsonString()).getAsJsonArray();
                String caseData = tempTemplateData.get(0).getAsJsonObject().get("caseData").getAsString();
                String modifyDiff = "jsonString内部构造";
                try {
                    modifyDiff = tempTemplateData.get(0).getAsJsonObject().get("modify_diff").getAsJsonArray().get(0).getAsJsonObject().get("modify_desc").getAsString();
                } catch (Exception e) {
                    logger.info("jsonString内部构造遍历错误");
                }
                setKey4JsonString(originResponse, arrayList, caseData, url, robustResults, needMock, modifyDiff);
            }
        } */


    public void createCodeStatusCase(String url, ArrayList<RobustResult> robustResults, boolean needMock) {
        ArrayList<Integer> codeStatusList = new ArrayList();
        codeStatusList.add(502);
        codeStatusList.add(204);

        for (Integer codeStatus : codeStatusList) {
            Integer mockId = 0;
            JSONArray modifyDiffList = new JSONArray();
            Map diffMap = new HashMap<>();
            if (needMock) {
                JsonObject mockResult = mockUtil.create(url, codeStatus);
                if (null != mockResult) {
                    mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                }
            }
            diffMap.put("modify_desc", "code码修改为:" + codeStatus);
            diffMap.put("key", "code码");
            diffMap.put("value", codeStatus);
            modifyDiffList.put(diffMap);
            JSONObject modifyDetailMap = new JSONObject();
            modifyDetailMap.put("modify_url", url);
            modifyDetailMap.put("mockId", mockId);
            modifyDetailMap.put("modify_diff", modifyDiffList);
            JSONArray modifyDetailList = new JSONArray();
            modifyDetailList.put(modifyDetailMap);
            RobustResult robustResult = new RobustResult();
            robustResult.setModifyDetail(modifyDetailList.toString());
            robustResults.add(robustResult);

        }
    }

    public static float keyDiff(List<String> key1List, List<String> key2List) {
        float result = 0;
        if (key1List.size() > 0 && key2List.size() > 0) {
            List<String> key1ListTemp = new ArrayList<>();
            CollectionUtils.addAll(key1ListTemp, new Object[key1List.size()]);
            Collections.copy(key1ListTemp, key1List);
            List<String> key2ListTemp = new ArrayList<>();
            CollectionUtils.addAll(key2ListTemp, new Object[key2List.size()]);
            Collections.copy(key2ListTemp, key2List);
            for (int i = 0; i < key1List.size(); i++) {
                if (key2List.contains(key1List.get(i))) {
                    key1ListTemp.remove(key1List.get(i));
                    key2ListTemp.remove(key1List.get(i));
                }
            }
            result = 1 - (float) (key1ListTemp.size() + key2ListTemp.size()) / (key1List.size() + key2List.size());
        } else if (0 == key1List.size() + key2List.size()) {
            result = 1;
        }
        return result;
    }

    public static List getKeyList(List<List> arrayList) {
        List keyList = new ArrayList();
        for (int i = 0; i < arrayList.size(); i++) {
            keyList.add(arrayList.get(i).get(0).toString().replaceAll("\\d+", ""));
        }
        keyList = removeDuplicationByHashSet(keyList);
        return keyList;
    }

    /**
     * 使用HashSet实现List去重(无序)
     *
     * @param list
     */
    public static List removeDuplicationByHashSet(List<String> list) {
        HashSet set = new HashSet(list);
        //把List集合所有元素清空
        list.clear();
        //把HashSet对象添加至List集合
        list.addAll(set);
        return list;
    }

    /**
     * 标记重复字段，在生成用例时一起构造数据
     *
     * @param arrayList index=0 代表节点
     *                  index=1 代表返回数据
     *                  index=2 代表类型，significance形式
     *                  index=3 代表和那个index重复
     *                  index=4 代表是否为子数组节点
     */
    public static List markDuplicationNode(List<List> arrayList) {
        List allKey = getKeyList(arrayList);
        //childNode里存了所有被判定是子节点的path（不含index）
        List childNode = findChildNode(allKey);
        List<List> allKeyList = new ArrayList<>();
        //把allKey的每个元素都转成list，方便做标记
        for (int keyCount = 0; keyCount < allKey.size(); keyCount++) {
            List keyList = new ArrayList();
            keyList.add(allKey.get(keyCount));
            allKeyList.add(keyList);
        }
        for (int i = 0; i < arrayList.size(); i++) {
            List originList = arrayList.get(i);
            String arrayKey = arrayList.get(i).get(0).toString().replaceAll("\\d+", "");
            for (int j = 0; j < allKey.size(); j++) {
                List keyList = allKeyList.get(j);
                String noIndexKey = (String) keyList.get(0);
                if (arrayKey.equals(noIndexKey)) {
                    boolean isChildNode = childNode.contains(noIndexKey);
                    if (keyList.size() > 1) {
                        //说明已经有key匹配成功过了，所以这个key是重复的
                        originList.add(keyList.get(1));//标记一下是和哪个key重复了
                    } else {
                        //还没有key匹配成功过
                        keyList.add(i); //标记一下是和arrayList里哪个元素匹配了
                        originList.add(i);
                    }
                    originList.add(isChildNode);
                    arrayList.set(i, originList);
                }
            }
        }
        return arrayList;
    }

    private static ArrayList<String> findChildNode(List<String> allKey) {
        ArrayList<String> childNode = new ArrayList<>();
        for (int i = 0; i < allKey.size(); i++) {
            boolean flag = true;
            for (int j = 0; j < allKey.size(); j++) {
                String nodei = allKey.get(i);
                String nodej = allKey.get(j);
                if (j == i || !nodei.contains("[]") || !nodej.contains("[]")) {
                    continue;
                }
                if (nodej.contains(nodei)) {
                    String tempNode = nodej.replace(nodei, "");
                    if (tempNode.contains("[]")) {
//                        System.out.println("丢掉的node：：" + nodei + "  " + nodej);
                        flag = false;
                        break;
                    }
                }
            }
            if (flag) {
                childNode.add(allKey.get(i));
            }
        }
        return childNode;
    }


}
