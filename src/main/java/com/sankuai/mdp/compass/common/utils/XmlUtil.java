package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonObject;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.Option;
import org.json.*;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;

import static com.jayway.jsonpath.JsonPath.using;


/**
 * Created by xieyongrui on 2020/10/22.
 */
public class XmlUtil {

//    public static void main(String[] args) {
//        String xml = FileUtil.linesToStr("/Users/<USER>/Code/compass/src/test.xml");
//        String xPath = "/Container/Container[0]/Container[1]/Container[1]/Container[0]/For.~i.30.37[0]/Container[0]";
//        System.out.print(getLoopVar(xml));
//    }

    public static String xPathForXml(String xml, String xPath) {
        try {
            JSONObject xmlJsonObj = XML.toJSONObject(xml);
            String[] pathList = xPath.split("/");
            for (int i = 0; i < pathList.length; i++) {
                String subPath = pathList[i];
                if (0 == i) {
                    continue;
                }
                if (subPath.startsWith("For")) {
                    break;
                }
                if (subPath.contains("[") || subPath.contains("]")) {
                    String path = subPath.split("\\[")[0];
                    String indexStr = subPath.split("\\[")[1].split("]")[0];
                    Integer index = Integer.valueOf(indexStr);
                    Object object = xmlJsonObj.get(path);
                    if (object instanceof JSONObject) {
                        xmlJsonObj = xmlJsonObj.getJSONObject(path);
                    } else if (object instanceof JSONArray) {
                        xmlJsonObj = xmlJsonObj.getJSONArray(path).optJSONObject(index);
                    }
                } else {
                    xmlJsonObj = xmlJsonObj.getJSONObject(subPath);
                }
            }
            if (xmlJsonObj != null) {
                String data = xmlJsonObj.toString();
                if (data.contains("{i}")) {
                    data = data.split("\\{i}")[0];
                    if (data.contains("{{")) {
                        data = data.split("\\{\\{")[1].replace("[","");
                        return data;
                    }
                }
            }
            return null;

        } catch (Exception e) {
            return null;
        }
    }

    public static String getLoopVar(String xml) {
        try {
            JSONObject xmlJsonObj = XML.toJSONObject(xml);
            xml = xmlJsonObj.toString();
            System.out.println(xml);
            if (xml != null && xml.contains("[{i}]")) {
            String data = xml.split("\\[\\{i}]")[0];
                String[] arr = data.split(":");
                data = arr[arr.length-1];
                if (data.contains("{")) {
                    String[] arr1 = data.split("\\{");
                    data = arr1[arr1.length-1].replace("[","");
                    return data;
                }
            }
            return null;

        } catch (Exception e) {
            return null;
        }
    }
}
