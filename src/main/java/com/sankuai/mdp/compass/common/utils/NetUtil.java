package com.sankuai.mdp.compass.common.utils;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import com.sankuai.mdp.compass.common.enums.Resp;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/6/13.
 */
public class NetUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(NetUtil.class);

    private NetUtil() {
    }

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(240, TimeUnit.SECONDS)
            .readTimeout(240, TimeUnit.SECONDS)
            .writeTimeout(240, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    /**
     * Get请求，只需要url
     * @param url
     * @return
     */
    public static Resp httpGet(String url){
        return httpGet(url,null,null);
    }


    public static Resp httpGet(String url, Map<String, String> headers) {
        return httpGet(url, null,headers);
    }

    /**
     * 最全get请求
     * @param url
     * @param headers
     * @return
     */
    public static Resp httpGet(String url, Map<String, String> params, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder();
        StringBuffer paramStr = new StringBuffer();

        try {
            if(headers != null){
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }

            if (params != null) {//构建请求参数
                paramStr.append("?");
                for (String key:params.keySet()) {
                    if(params.get(key) != null) {
                        paramStr.append(key + "=" + URLEncoder.encode(params.get(key), "utf-8") + "&");
                    }
                }
                paramStr.deleteCharAt(paramStr.length() - 1);//删掉最后一个&

                url = url.concat(paramStr.toString());
            }

        } catch (Exception e) {
            LOGGER.error("Exception:" + e);
        }

        Request request = builder
                .url(url)
                .build();

        Resp response = executeRemoteCall(request);
        return response;
    }

    public static Resp httpPost(String url, RequestBody body,Map<String,String> headers,String auth,Map<String, String> cookies) {
        //Cookie
        Request.Builder builder = new Request.Builder();
        if(headers != null){
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        String cookieValue = "";
        if(cookies != null){
            for (String cookie : cookies.keySet()) {
                cookieValue += cookie + "=" + cookies.get(cookie) + ";";
            }
            builder.addHeader("Cookie", cookieValue);

        }

        //auth
        if (auth != null){
            builder.addHeader("Authorization", auth);

        }

        if(body == null){
            body =  RequestBody.create(null, "");
        }

        Request request = builder
                .url(url)
                .post(body)
                .build();

        Resp response = executeRemoteCall(request);
        return response;
    }

        /**
         * post请求 支持form表单提交
         * @param url
         * @param body
         * @param headers
         * @param auth
         * @param cookies
         * @return
         */
    public static Resp httpPostWithFormAndClient(String url, Map<String, String> body,Map<String,String> headers,String auth,Map<String, String> cookies) {
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        LOGGER.info("httpPostWithFormAndClient body:" + GsonUtil.toJson(body));

        //body是key-value格式的
        if(body != null){
            for (Map.Entry<String, String> entry : body.entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                formBodyBuilder.add(entry.getKey(), entry.getValue());
            }
        }
        return httpPost(url,formBodyBuilder.build(),headers,auth,cookies);
    }
    /**
     * POST请求 XML格式body
     * @param url
     * @param body
     * @param auth
     * @return
     */
    public static Resp httpPostWithXmlBody(String url, String body, String auth) {
        MediaType mediaType = MediaType.parse("application/xml;charset=UTF-8");
        return httpPost(url,RequestBody.create(mediaType, body),null,auth,null);
    }

    /**
     * POST请求，JSON请求体
     * @param url
     * @param body
     * @param headers
     * @return
     */
    public static Resp httpPostWithJSONBody(String url, String body, Map<String,String> headers) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        return httpPost(url,RequestBody.create(mediaType, body),headers,null,null);
    }

    public static Resp httpPostWithJSONBody(String url, String body, Map<String,String> headers, Map<String,String> cookies) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        return httpPost(url,RequestBody.create(mediaType, body),headers,null,cookies);
    }

    public static Resp httpPostWithJSONBody(String url, String body) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        return httpPost(url,RequestBody.create(mediaType, body),null,null,null);
    }

    /**
     * POST表单请求，传url和auth
     * @param url
     * @param auth
     * @return
     */
    public static Resp httpPostWithoutBody(String url, String auth) {
        return httpPost(url,null,null,auth,null);
    }

    /**
     * post请求，以表单类型提交
     * @param url
     * @param body    可以为null
     * @param headers 可以为null
     * @param auth    可以为null
     * @param cookies 可以为null
     * @return
     */
    public static Resp httpPostWithForm(String url, Map<String, String> body,Map<String,String> headers,String auth,Map<String, String> cookies) {
        return httpPostWithFormAndClient(url,body,headers,auth,cookies);
    }
    /**
     * 不需要Header
     * @param url
     * @param body
     * @param auth
     * @param cookies
     * @return
     */
    public static Resp httpPostFormWithoutHeader(String url, Map<String, String> body,String auth,Map<String, String> cookies) {
        Resp response = httpPostWithForm(url,body,null,auth,cookies);
        return response;
    }

    public static Resp httpPostFormWithHeader(String url, Map<String, String> body,Map<String,String> headers) {
        Resp response = httpPostWithForm(url,body,headers,null,null);
        return response;
    }

    /**
     * 不需要Cookie
     * @param url
     * @param body
     * @param headers
     * @param auth
     * @return
     */
    public static Resp httpPostFormWithoutCookie(String url, Map<String, String> body,Map<String,String> headers,String auth) {
        Resp response = httpPostWithForm(url,body,headers,auth,null);
        return response;
    }

    public static Resp originHttpPost(String url, String body, String auth) {
        MediaType MEDIA_TYPE_TEXT = MediaType.parse("application/json;charset=UTF-8");
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", auth)
                .post(RequestBody.create(MEDIA_TYPE_TEXT, body))
                .build();
        Resp response = executeRemoteCall(request);
        return response;
    }





    /**
     * Put请求，body放json数据
     * @param url 请求的url
     * @param content json格式的body
     * @param headers
     * @return
     * @throws IOException
     */
    public static Resp httpPutWithClient(String url, String content, Map<String,String> headers){
        Request.Builder builder = new Request.Builder();
        if(headers != null){
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Request request = builder
                .url(url)
                .put(RequestBody.create(MediaType.parse("application/json"), content))
                .build();
        Resp response = executeRemoteCall(request);
        return response;
    }

    public static Resp httpPut(String url, String content, Map<String,String> headers) {
        return httpPutWithClient(url,content,headers);
    }

    public static Resp httpPut(String url, String content, String auth, Map<String,String> headers){

        if(auth == null){
            auth = "";
        }else{
            headers.put("Authorization", auth);
        }

        return httpPutWithClient(url,content,headers);
    }

    public static boolean httpPut(String url, byte[] content, String auth) {
        if(auth == null){
            auth = "";
        }
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", auth)
                .put(RequestBody.create(MediaType.parse("application/json"), content))
                .build();
        Response response = null;
        try {
            response = CLIENT.newCall(request).execute();
            if (response.isSuccessful()) {
                return true;
            } else {
                LOGGER.error(String.format("Error occurs during PUT request url: %s , response code: %d", url, response.code()));
                return false;
            }
        } catch (Exception e) {
            LOGGER.error(String.format("Exception occurs during PUT request url: %s , cause reason: %s, exception message: %s", url, e.getCause(), e));
            return false;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }


    public static Resp executeRemoteCall(Request request) {
        Response response = null;

        try {
            response = CLIENT.newCall(request).execute();
            if(response.code() == 200 || response.code() == 201){
                String body = response.body().string();
                return Resp.success(body);
            }else{
                int code = response.code();
                String message = response.message();
                String body = (response.body() == null) ? "" : response.body().string();
                String errMsg = String.format("Exception occurs during request url: %s , httpCode: %s, httpMsg: %s, httpResponse: %s", request.url(), code, message, body);
                LOGGER.error(errMsg);
                throw new RuntimeException(errMsg);
            }
        } catch (Exception e) {
            String errMsg  =String.format("Exception occurs during request url: %s , cause reason: %s, exception message: %s", request.url(), e.getCause(), e);
            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * post 附带Content-Type:application/x-www-form-urlencoded
     *      没有body内容
     */
    public static Resp httpPostWithWWWForm(String url, String body, String auth, Map<String, String> params) {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded; charset=utf-8");
        Request.Builder builder = new Request.Builder()
                .url(concatUrl(url, params))
                .addHeader("Authorization", auth);
        Request request = builder
                .post(RequestBody.create(mediaType, body))
                .build();
        Resp resp = executeRemoteCall(request);
        return resp;
    }
    public static String concatUrl(String url, Map<String, String> params) {
        StringBuffer paramStr = new StringBuffer();

        try {
            if (params != null) {//构建请求参数
                if (!url.contains("?"))
                    paramStr.append("?");
                else
                    paramStr.append("&");
                for (String key:params.keySet()) {
                    if(params.get(key) != null) {
                        paramStr.append(key + "=" + URLEncoder.encode(params.get(key), "utf-8") + "&");
                    }
                }
                paramStr.deleteCharAt(paramStr.length() - 1);//删掉最后一个&

                url = url.concat(paramStr.toString());
            }

        } catch (Exception e) {
            LOGGER.error("Exception:" + e);
        }
        return url;
    }
}

