package com.sankuai.mdp.compass.common.utils;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.*;
import java.net.URL;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by xieyongrui on 2020/2/21.
 */
@Slf4j
public class VenusUtil {
    /**
     * Venus
     */
    public final String BUCKET = "ptautotest";

    public final String CLIENT_ID = "ptautotest";

    public final String CLIENT_SECRET = "1292ee16e73a9d36aa52cbd45b3d0bcc";

    public final String P = "50";

    /**
     * 上传本地图片到服务器
     * @param path
     * @return 返回图片链接
     */
    public String uploadPicture(String path) {
        try {
            String bucket = BUCKET;
            String client_id = CLIENT_ID;
            String client_secret = CLIENT_SECRET;
            ImageUploadClient client = new ImageUploadClientImpl(bucket, client_id, client_secret);

            File file = new File(path);
            ImageRequest request = new ImageRequest(1, file, false);
            ImageResult res = client.postImage(request);
            return res.getOriginalLink();
        } catch (Exception e) {
//            e.printStackTrace();
            log.error("图片上传失败>>"+e.getMessage());
            return null;
        }
    }
}
