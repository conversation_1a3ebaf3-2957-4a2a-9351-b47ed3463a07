package com.sankuai.mdp.compass.common.utils;


import com.google.gson.*;
import com.jayway.jsonpath.*;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Profession;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.jboss.logging.Logger;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by xieyongrui on 2020/1/20.
 */
@Slf4j
public class MbcUtil {
    public static final String HOST_PROD = "http://appupdate.sankuai.com/";
    public static final String HOST_TEST = "http://appupdate.wpt.test.sankuai.com/";
//    public static final String HOST_TEST = "http://appupdate.sankuai.com/";


    static MbcUtil mbcUtil = new MbcUtil();
    static ValueUtil valueUtil = new ValueUtil();
    static FileUtil fileUtil = new FileUtil();
    static ComConstant comConstant = new ComConstant();


    public static final String TEMPLARE_URL_API = "mbc/api/templateinstance/list?pageSize=2000&currentPageNum=1";
    public static final String TEMPLARE_URL_API_NEW = "mbc/api/templateinstance/list?pageSize=10&currentPageNum=1&name=";
    public static final String TEMPLATE_DETAILS_API = "mbc/api/templateinstance/details?instVersionID=";
    public static final String MGE_API = "mbc/api/datacollection/init?instVersionID=";
    public static final String MAGIC_STATUS_CHANGE = "magic/api/layer/status/change";

    public static final String PLATFORM = "dynamiclayout/business/list?uname=sunkangtong";//拿到所有的平台信息和对应id
    public static final String BUSINESS = "dynamiclayout/module/list?businessId=";//拿到与平台对应的业务信息，需要先调用平台的接口去拿id（目前美团平台的id=2）


    public static String host;

    public MbcUtil() {
        if (EnvUtil.isOnline()) {
            host = HOST_PROD;
        } else {
            host = HOST_PROD;
        }
    }

    public static String getId(String templateName, String scenes) {
        JsonObject jsonObject = getMoudleByTemplate(templateName, scenes);
        String instVersionID = "";
        try {
            JsonArray jsonArray = jsonObject.get("data").getAsJsonObject().getAsJsonArray("list");
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject templateData = jsonArray.get(i).getAsJsonObject();
                if (templateData.get("ename").getAsString().equals(templateName) & !templateData.get("statusDesc").getAsString().equals("已上线")) {
                    instVersionID = templateData.get("instVersionID").getAsString();
                    break;
                }
                if (instVersionID.equals("") & templateData.get("ename").getAsString().equals(templateName)) {
                    instVersionID = templateData.get("instVersionID").getAsString();
                    break;
                }
            }
        } catch (Exception e) {
            log.error("getId err:" + e.getMessage());
        } finally {

        }
        return instVersionID;
    }

    public String getBusiness(String templateName) {
        JsonObject jsonObject = getMoudleByTemplate(templateName, "");
        String business = "";
        try {
            JsonArray jsonArray = jsonObject.get("data").getAsJsonObject().getAsJsonArray("list");
            JsonObject templateData = jsonArray.get(0).getAsJsonObject();
            business = templateData.get("module").getAsJsonObject().get("ename").getAsString();
        } catch (Exception e) {
            log.error("getBusinessError:" + e.getMessage());
        } finally {

        }
        return business;
    }

    public static JsonObject getModule() {
        String result;
        String url = host + TEMPLARE_URL_API;
        ;
        result = HttpUtil.vGet(url).toString();
        return new JsonParser().parse(result).getAsJsonObject();
    }

    public JsonObject getTemplate() {
        String result;
        String url = host + TEMPLARE_URL_API;
        result = HttpUtil.vGet(url).toString();
        return new JsonParser().parse(result).getAsJsonObject();
    }

    public static JsonObject getMoudleByTemplate(String template, String scenes) {
        String statusQuery = "";
        if (StringUtil.isBlank(scenes)) {
            scenes = "smoke";
        }
        if (scenes.equals("regression")) {
            statusQuery = "&status=5";
        }
        String result;
        String url = host + TEMPLARE_URL_API_NEW + template + statusQuery;
        result = HttpUtil.vGet(url).toString();
        return new JsonParser().parse(result).getAsJsonObject();
    }


    public String getTemplateZipApi(String templateName, String scenes) {
        String instVersionID = getId(templateName, scenes);
        String zipUrl = null;
        String url = host + TEMPLATE_DETAILS_API + instVersionID;
        try {
            String zipString = HttpUtil.vGet(url).toString();
            JsonObject zipJson = new JsonParser().parse(zipString).getAsJsonObject();
            zipUrl = zipJson.get("data").getAsJsonObject().get("layoutUrl").getAsString();
        } catch (Exception e) {
            log.error("getTemplateZipApi err:" + e.getMessage());
        }
        return zipUrl;
    }

    // 判断MBC是否有埋点配置，false表示无配置
    public Boolean isMbcConfigured(String templateName, String scenes) {
        String instVersionID = getId(templateName, scenes);
        String url = host + MGE_API + instVersionID;
        Boolean result = false;

        JsonObject jsonObject = new JsonParser().parse(HttpUtil.vGet(url).toString()).getAsJsonObject();
        JsonObject data = jsonObject.get("data").getAsJsonObject();
        if (data.has("mgeConfigs")) {
            if (data.get("mgeConfigs").isJsonNull() || data.get("mgeConfigs").getAsJsonArray().size() == 0) {
                result = false;
            } else {
                result = true;
            }
        }
        return result;
    }

    public JsonObject getMgeConfiguration(String templateName, String scenes) {
        String instVersionID = getId(templateName, scenes);
        String url = host + MGE_API + instVersionID;
        JsonObject result = new JsonObject();
        JsonArray mgeConfigs = new JsonArray();
        try {
            JsonObject jsonObject = new JsonParser().parse(HttpUtil.vGet(url).toString()).getAsJsonObject();
            JsonObject data = jsonObject.get("data").getAsJsonObject();
            if (data.has("mgeConfigs")) {
                if (data.get("mgeConfigs").isJsonNull()) {
                    log.info("埋点未配置");
                    result.add("mgeCfgList", mgeConfigs);
                    return result;

                }

                mgeConfigs = data.get("mgeConfigs").getAsJsonArray();
                Gson gson = new Gson();

                JsonArray valLabSingle = new JsonArray();
                for (int i = 0; i < mgeConfigs.size(); i++) {
                    JsonObject lab = mgeConfigs.get(i).getAsJsonObject().get("valLab").getAsJsonObject();
                    List<Map<String, String>> arrayList = new ArrayList<>();

                    for (Map.Entry entry : lab.entrySet()) {
                        Map map = new HashMap();
                        String key = entry.getKey().toString();
                        String value = entry.getValue().toString();
                        map.put("key", key);
                        map.put("val", value.replaceAll("\\\\\"", ""));
                        arrayList.add(map);
                        String s = gson.toJson(arrayList);
                        valLabSingle = new JsonParser().parse(s).getAsJsonArray();
                    }
                    mgeConfigs.get(i).getAsJsonObject().add("valLab", valLabSingle);
                }
            }
        } catch (Exception e) {
            log.error("getMgeConfiguration err:" + e.getMessage());
        } finally {
            result.add("mgeCfgList", mgeConfigs);
        }
        return result;
    }

    /**
     * 根据模版名称从mbc系统获取卡片的mock数据
     *
     * @param templateName
     * @return
     */
    public static String getCardData(String templateName, String business, String scenes) {
        JsonObject resp = new JsonObject();
        JsonParser jsonParser = new JsonParser();
        String instVersionID = getId(templateName, scenes);
        String url = host + TEMPLATE_DETAILS_API + instVersionID;
        String details = HttpUtil.vGet(url).toString();
        JsonObject detailsJson = new JsonParser().parse(details).getAsJsonObject();
        String zipUrl = detailsJson.getAsJsonObject("data").get("layoutUrl").getAsString();
        String cardData = detailsJson.getAsJsonObject("data").getAsJsonObject("apiData").get("jsonData").getAsString();
        JsonObject jsonObject = new JsonParser().parse(cardData).getAsJsonObject();

        //重置模版名称（RD可能直接copy的其他卡片的数据，模版名称不对，会影响抓埋点，这里把模版名称都改成正确的）
        try {
            jsonObject.remove("templateName");
            jsonObject.remove("templateUrl");
            jsonObject.addProperty("templateUrl", zipUrl);
            jsonObject.addProperty("templateName", templateName);

            Configuration configuration = Configuration.builder()
                    .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
            DocumentContext tempTemplateDataJson = JsonPath.using(configuration).parse(jsonObject.toString());
            JsonElement jsonElement = jsonParser.parse(tempTemplateDataJson.jsonString());


            //找到所有key为templateName的path
            Configuration conf = Configuration.builder()
                    .options(Option.AS_PATH_LIST).build();
            List<String> list = new ArrayList<>();
            try {
                list.addAll(JsonPath.using(conf).parse(cardData).read("$..templateName"));
                list.addAll(JsonPath.using(conf).parse(cardData).read("$..template_name"));
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            List<String> result = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                String path = list.get(i);
                if (path == null || path == "") {
                    continue;
                }
                String[] subPath = path.split("\'");
                String resultPath = "";
                for (int index = 0; index < subPath.length; index++) {
                    String keyStr = subPath[index].replaceAll("\\[", "").replaceAll("\\]", "");
                    Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
                    Matcher isNum = pattern.matcher(keyStr);
                    if ("".equals(keyStr) || "$".equals(keyStr)) {
                        continue;
                    }
                    if (isNum.matches()) {
                        resultPath += "[" + keyStr + "]";
                    } else {
                        resultPath += "." + keyStr;
                    }
                }
                result.add(resultPath);
            }

            //没有模版名称字段，加一个
            if (result.isEmpty()) {
                tempTemplateDataJson = JsonPath.using(configuration).parse(jsonElement.toString());
                jsonElement = jsonParser.parse(tempTemplateDataJson.put("$", "templateName", templateName).jsonString());
            }

            for (int i = 0; i < result.size(); i++) {
                tempTemplateDataJson = JsonPath.using(configuration).parse(jsonElement.toString());
                jsonElement = jsonParser.parse(tempTemplateDataJson.set("$" + result.get(i), templateName).jsonString());
            }

            if (business != null && (business.equals("search") || business.equals("staggered_feed") || business.equals("groupbuy"))) {
                return jsonElement.toString();
            }
            JsonObject config = new JsonObject();
            config.addProperty("exposeDelay", 500);
            config.addProperty("exposePart", 0.7);
            resp.add("biz", jsonParser.parse(jsonElement.toString()));
            resp.addProperty("type", "dynamic");
            resp.addProperty("templateUrl", zipUrl);
            resp.addProperty("templateName", templateName);
            resp.addProperty("position", 0);
            resp.add("config", config);
            return resp.toString();
        } catch (Exception e) {
            log.info("重置模版信息失败", e);
            return resp.toString();
        }

    }

    /**
     * 根据输入参数替换mockdata数据
     *
     * @param templateName
     * @return
     */
    public static String replaceCardData(String templateName, String business, String scenes, String templateUrl, String mockData) {
        JsonObject resp = new JsonObject();
        JsonParser jsonParser = new JsonParser();
        String instVersionID = getId(templateName, scenes);
        String url = host + TEMPLATE_DETAILS_API + instVersionID;
        String details = HttpUtil.vGet(url).toString();
        JsonObject detailsJson = new JsonParser().parse(details).getAsJsonObject();
        String zipUrl = "".equals(templateUrl) ? detailsJson.getAsJsonObject("data").get("layoutUrl").getAsString() : templateUrl;
        String cardData = mockData;
        JsonObject jsonObject = new JsonParser().parse(cardData).getAsJsonObject();
        //重置模版名称（RD可能直接copy的其他卡片的数据，模版名称不对，会影响抓埋点，这里把模版名称都改成正确的）
        try {
            jsonObject.remove("templateName");
            jsonObject.remove("templateUrl");
            jsonObject.addProperty("templateUrl", zipUrl);
            jsonObject.addProperty("templateName", templateName);

            Configuration configuration = Configuration.builder()
                    .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
            DocumentContext tempTemplateDataJson = JsonPath.using(configuration).parse(jsonObject.toString());
            JsonElement jsonElement = jsonParser.parse(tempTemplateDataJson.jsonString());


            //找到所有key为templateName的path
            Configuration conf = Configuration.builder()
                    .options(Option.AS_PATH_LIST).build();
            List<String> list = new ArrayList<>();
            try {
                list.addAll(JsonPath.using(conf).parse(cardData).read("$..templateName"));
                list.addAll(JsonPath.using(conf).parse(cardData).read("$..template_name"));
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            List<String> result = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                String path = list.get(i);
                if (path == null || path == "") {
                    continue;
                }
                String[] subPath = path.split("\'");
                String resultPath = "";
                for (int index = 0; index < subPath.length; index++) {
                    String keyStr = subPath[index].replaceAll("\\[", "").replaceAll("\\]", "");
                    Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
                    Matcher isNum = pattern.matcher(keyStr);
                    if ("".equals(keyStr) || "$".equals(keyStr)) {
                        continue;
                    }
                    if (isNum.matches()) {
                        resultPath += "[" + keyStr + "]";
                    } else {
                        resultPath += "." + keyStr;
                    }
                }
                result.add(resultPath);
            }

            //没有模版名称字段，加一个
            if (result.isEmpty()) {
                tempTemplateDataJson = JsonPath.using(configuration).parse(jsonElement.toString());
                jsonElement = jsonParser.parse(tempTemplateDataJson.put("$", "templateName", templateName).jsonString());
            }

            for (int i = 0; i < result.size(); i++) {
                tempTemplateDataJson = JsonPath.using(configuration).parse(jsonElement.toString());
                jsonElement = jsonParser.parse(tempTemplateDataJson.set("$" + result.get(i), templateName).jsonString());
            }

            if (business != null && (business.equals("search") || business.equals("staggered_feed") || business.equals("groupbuy"))) {
                return jsonElement.toString();
            }

            JsonObject config = new JsonObject();
            config.addProperty("exposeDelay", 500);
            config.addProperty("exposePart", 0.7);
            resp.add("biz", jsonParser.parse(jsonElement.toString()));
            resp.addProperty("type", "dynamic");
            resp.addProperty("templateUrl", zipUrl);
            resp.addProperty("templateName", templateName);
            resp.addProperty("position", 0);
            resp.add("config", config);
            return resp.toString();
        } catch (Exception e) {
            log.info("重置模版信息失败", e);
            return resp.toString();
        }

    }

    /**
     * 构造mock数据
     *
     * @param object 参数组
     * @return
     */
    public static JsonObject handleMockData(String mockData, Map<String, Map<String, Object>> object) {
        JsonParser jsonParser = new JsonParser();

        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        DocumentContext tempTemplateDataJson = JsonPath.using(configuration).parse(mockData);
        JsonElement jsonElement = jsonParser.parse(tempTemplateDataJson.jsonString());


        Iterator<String> iterator = object.keySet().iterator();
        while (iterator.hasNext()) {
            String path = iterator.next();
            Map<String, Object> map = object.get(path);

            //在{path}的位置插入{key}:{value}
            Iterator<String> params = map.keySet().iterator();
            while (params.hasNext()) {
                String key = params.next();
                Object value = map.get(key);
                try {
                    tempTemplateDataJson = JsonPath.using(configuration).parse(jsonElement.toString());
                    jsonElement = jsonParser.parse(tempTemplateDataJson.put("$." + path, key, value).jsonString());
                } catch (Exception e) {
                    log.info("重置模版信息失败", e);
                }
            }
        }
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        return jsonObject;
    }


    /**
     * 拿到mbc平台的所有 业务名称+模板名称
     *
     * @return 两个数组的集合，可以按顺序进行拆分
     */
    public static ArrayList<List> getAllTemplate() {
        String url = host + TEMPLARE_URL_API;
        String result = HttpUtil.vGet(url).toString();
        ReadContext ctx = JsonPath.parse(result);
        List<String> templateName = ctx.read("$.data.list[*].ename");
        List<String> business = ctx.read("$.data.list[*].module.ename");
        ArrayList<List> arrayList = new ArrayList<>();
        arrayList.add(templateName);
        arrayList.add(business);
        return arrayList;
    }

    /**
     * @param templateName   模板名称
     * @param professionEnum 需要获取的相关负责人
     * @return 岗位负责人的数组
     */
    public static JsonArray getPermission(String templateName, Enum<Profession> professionEnum) {
        JsonArray professionList = new JsonArray();
        try {
            String instVersionID = getId(templateName, "");
            String url = "";
            url = host + TEMPLATE_DETAILS_API + instVersionID;
            String zipString = HttpUtil.vGet(url).toString();
            JsonObject zipJson = new JsonParser().parse(zipString).getAsJsonObject();
            JsonArray list = zipJson.get("data").getAsJsonObject().get("permission").getAsJsonObject().get(professionEnum.name()).getAsJsonArray();
            if (!list.isJsonNull()) {
                for (int i = 0; i < list.size(); i++) {
                    professionList.add(list.get(i));
                }
            }
        } catch (Exception e) {
            log.info("获取权限失败");
        } finally {
            return professionList;
        }
    }


    public static String handleGuideLayerTestResult(int id, int status) {
        String url = "http://*************:8003/";
        if (EnvUtil.isOnline()) {
            url = HOST_PROD + MAGIC_STATUS_CHANGE;
        } else {
            url = HOST_TEST + MAGIC_STATUS_CHANGE;
        }
        JSONObject jsonObject = new JSONObject();
        int mbcStatus = 2;
        int nextStatus = -1;
        if (status == 0) {
            nextStatus = 1;
        } else if (status == 1) {
            nextStatus = 3;
        }
        jsonObject.put("id", id);
        jsonObject.put("status", mbcStatus);
        jsonObject.put("nextStatus", nextStatus);
        String resp = null;
        try {
            resp = HttpUtil.sendPostJson(url, jsonObject).toString();
        } catch (Exception e) {
            log.info("handleGuideLayerTestResult err:" + e.getMessage());
        } finally {
            return resp;
        }
    }

    public boolean isValid(String s) {
        String total = "";
        Stack<Character> mystack = new Stack<>();
        try {//        String total = "";

            for (int i = 0; i < s.length(); i++) {

                char c = s.charAt(i);
                total += c;
                if (c == '（' || c == '）') {
                    return false;
                }
                if (c == '(') {
                    mystack.push(c);
                } else if (c == ')') {
                    if (mystack.isEmpty()) {
                        return false;
                    }
                    char topChar = mystack.pop();
                    if (c == ')' && topChar != '(') {
                        return false;
                    }
                }
            }
            return mystack.isEmpty();
        } catch (Exception e) {

        }
        return mystack.isEmpty();
    }

    public String getXmlContentByTemplateName(String templateName, String scenes) {
        String xmlContent = null;
        String zipUrl = null;
        try {
            zipUrl = mbcUtil.getTemplateZipApi(templateName, scenes);
            if (null != zipUrl) {
                FileUtil.downloadAndReadFile(zipUrl, comConstant.OUT_PUT, templateName);
                ArrayList<String> fileList = new ArrayList<>();
                String filePath = comConstant.OUT_PUT + "/" + templateName + "/";
                fileUtil.getFileList(filePath, "", fileList);
                for (int i = 0; i < fileList.size(); i++) {
                    String fileName = fileList.get(i);
                    xmlContent = FileUtil.linesToStr(filePath + fileName);
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        } finally {
            FileUtil.delFolder(comConstant.OUT_PUT);
        }
        return xmlContent;
    }
}

