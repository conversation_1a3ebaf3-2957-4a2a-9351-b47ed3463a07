package com.sankuai.mdp.compass.common.enums;

public enum ErrorEnum {
    /*
     * 错误信息
     * */
    S_302("302", "没有登录,跳转登录页"),
    S_400("400", "请求处理异常"),
    S_100("400", "请求处理异常"),
    S_401("401", "登录状态失效,请重新登陆"),
    S_403("403", "权限不足"),
    S_404("404", "请求路径不存在"),
    S_500("500", "系统内部异常"),
    E_Limit_Connect("900", "请求次数过多"),
    ST_500("500", "获取数据异常");
    private String errorCode;

    private String errorMsg;

    ErrorEnum() {
    }

    ErrorEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
