package com.sankuai.mdp.compass.common.utils;

import com.dianping.lion.client.util.Json;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.jayway.jsonpath.*;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.jayway.jsonpath.JsonPath.read;
import static com.jayway.jsonpath.JsonPath.using;


/**
 * Created by skt on 2021/11/24.
 */
public class PageUtil {
    static String shoppingCart = "{\"code\":0,\"data\":{\"actionBar\":{\"background\":{\"color\":\"#F2F2F2\"},\"leftActions\":[{\"config\":{\"exposeDelay\":500,\"exposePart\":0.7,\"mge4_click\":{\"bid\":\"b_group_etynb2pd_mc\",\"cid\":\"c_group_h8tgwbjm\"}},\"iUrl\":\"mbc://page.close\",\"iconUrl\":\"https://p0.meituan.net/travelcube/4900b887dcd5e04652680c44b0189342650.png\",\"text\":\"返回\"}],\"title\":\"购物车\",\"titleColor\":\"#E6000000\",\"type\":\"shopping_cart_actionbar\"},\"extra\":{\"code\":0,\"globalEdit\":true,\"bottom\":false,\"global_id\":\"79fe1e7b3327e0a751378b1da93805c6\",\"refresh\":false,\"valLab\":{\"extension\":\"{\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"ranker_model\\\":\\\"yidi\\\"}\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"position\":\"shopping_cart\",\"type\":\"doubleFeed\"},\"session_id\":\"8b02a4609660d9055b7486fc434b28f9\",\"rolltop\":0,\"errorMsg\":\"成功\",\"waimaiTemplateInfo\":{\"templateName\":\"shoppingcart_bottom_separate_pay_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1697_1630578400084.zip\"},\"moduleVersion\":10,\"actionInfo\":{\"click_detail_btn\":{\"templateName\":\"shoppingcart_amount_detail\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1077_1619493595756.zip\"}},\"operationType\":\"\"},\"groups\":[{\"dataType\":\"replace\",\"id\":\"shoppingcart_empty\",\"items\":[],\"showCount\":-1,\"style\":{\"columnCount\":1,\"margin\":[\"10\",\"0\",\"0\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"},{\"dataType\":\"replace\",\"id\":\"shoppingcart_recommendHeader\",\"items\":[{\"biz\":{\"tabList\":[{\"tabId\":\"homepage\",\"tabName\":\"猜你喜欢\",\"bu\":\"\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"position\":\"shopping_cart\",\"type\":\"twoColumn\"}]},\"config\":{\"exposeDelay\":500,\"exposePart\":0.001},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_title_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1249_1622781738538.zip\",\"type\":\"dynamic\"}],\"showCount\":-1,\"style\":{\"columnCount\":1,\"margin\":[\"500pt\",\"0\",\"0\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"},{\"dataType\":\"replace\",\"id\":\"shoppingcart_recommendList\",\"items\":[{\"biz\":{\"nonBannerIndex\":0,\"mainMessage2\":\"8.5\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"0\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"团购\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"728254311\",\"item_type\":\"deal\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"022884522008135050531326117192798155269_c0_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"DAOCAN_MEISHI_DEAL\",\"position\":\"shopping_cart\",\"bu_type\":\"meishi\"},\"title\":\"沪上阿姨鲜果茶 功夫红豆双皮奶\",\"type\":\"DAOCAN_MEISHI_DEAL\",\"_iUrl\":\"imeituan://www.meituan.com/food/deal?did=728254311&stid=022884522008135050531326117192798155269_c0_e79fe1e7b3327e0a751378b1da93805c6&poiid=1532388229\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"味道超级好\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/deal/b45bc9d113dabff7ae5af76e6648179062128.jpg\",\"subMessageStyle\":1,\"key\":\"728254311:DAOCAN_MEISHI_DEAL\",\"_from\":\"DAOCAN_MEISHI_DEAL\",\"mainMessage\":\"¥\",\"subTitle2\":\"半年售5000+\",\"_type\":\"deal\",\"index\":0,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"728254311\",\"imageTag2\":\"平凉路/东外滩\",\"imageTag\":\"到店吃\",\"subMessage\":\"¥18\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":1,\"mainMessage2\":\"9.2\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100091103786656\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c2_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"海蓝之星蓝色典藏52%vol 500ml/瓶\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100091103786656%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"7812人已加购\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/groceryimages/ab465b486fa96077b8b07ebf0ef0e32a149392.jpg\",\"subMessageStyle\":1,\"key\":\"100091103786656:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":1,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100091103786656\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"deadlineTime\":1636559999000,\"nonBannerIndex\":2,\"mainMessage2\":\"0.1\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"0\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"isSecKill\\\":\\\"1\\\",\\\"xzName\\\":\\\"团购\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"*********\",\"item_type\":\"deal\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"057743403483197389313923936291490487151_c3_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_countdown_card\",\"bu\":\"DAOCAN_MEISHI_DEAL\",\"position\":\"shopping_cart\",\"bu_type\":\"meishi\"},\"title\":\"<font><image src=\\\"https://p0.meituan.net/travelcube/96f12d528ec86cb0e8f09f5cbb9061424503.png@150w_45h_80q\\\" width=\\\"47.5pt\\\" margin-right=\\\"4pt\\\"  margin-top=\\\"2.5pt\\\" height=\\\"15pt\\\"/><b>太平洋咖啡 手调饮品升杯券（升2级）</b></font>\",\"type\":\"DAOCAN_MEISHI_DEAL\",\"_iUrl\":\"imeituan://www.meituan.com/food/deal?did=*********&stid=057743403483197389313923936291490487151_c3_e79fe1e7b3327e0a751378b1da93805c6&poiid=*********\",\"promotionId\":\"*********\",\"imageStyle\":0,\"fixedHeight\":false,\"atmosphereTitleTag\":\"daocan_deal_seckill_flag\",\"subTitle\":\"环境特别好\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/deal/5ad7231209c2d2f97ea31ae727689b3339485.jpg\",\"subMessageStyle\":1,\"key\":\"*********:DAOCAN_MEISHI_DEAL\",\"_from\":\"DAOCAN_MEISHI_DEAL\",\"mainMessage\":\"¥\",\"subTitle2\":\"703人已抢\",\"_type\":\"deal\",\"index\":2,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_countdown_card\",\"style\":\"itemDynamic\",\"_id\":\"*********\",\"imageTag2\":\"陆家嘴\",\"imageTag\":\"到店吃\",\"subMessage\":\"¥6\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_countdown_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"deadlineTime\":1636559999000,\"nonBannerIndex\":3,\"mainMessage2\":\"38.99\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"0\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"isSecKill\\\":\\\"1\\\",\\\"xzName\\\":\\\"团购\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"*********\",\"item_type\":\"deal\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"057743403483197389313923936291490487151_c4_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_countdown_card\",\"bu\":\"DAOCAN_MEISHI_DEAL\",\"position\":\"shopping_cart\",\"bu_type\":\"meishi\"},\"title\":\"<font><image src=\\\"https://p0.meituan.net/travelcube/96f12d528ec86cb0e8f09f5cbb9061424503.png@150w_45h_80q\\\" width=\\\"47.5pt\\\" margin-right=\\\"4pt\\\"  margin-top=\\\"2.5pt\\\" height=\\\"15pt\\\"/><b>汉堡王 【双人餐】皇堡+双层脆鸡堡+王道嫩香鸡块+薯霸王（小）+可口可乐（中）*2\\n_10614_汉堡王</b></font>\",\"type\":\"DAOCAN_MEISHI_DEAL\",\"_iUrl\":\"imeituan://www.meituan.com/food/deal?did=*********&stid=057743403483197389313923936291490487151_c4_e79fe1e7b3327e0a751378b1da93805c6&poiid=1846374711\",\"promotionId\":\"*********\",\"imageStyle\":0,\"fixedHeight\":false,\"atmosphereTitleTag\":\"daocan_deal_seckill_flag\",\"subTitle\":\"非常好吃,不错\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/deal/564ef3a5c7346baa79fc0f1a3f1c75f223839.jpg\",\"subMessageStyle\":1,\"key\":\"*********:DAOCAN_MEISHI_DEAL\",\"_from\":\"DAOCAN_MEISHI_DEAL\",\"mainMessage\":\"¥\",\"subTitle2\":\"5000+人已抢\",\"_type\":\"deal\",\"index\":3,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_countdown_card\",\"style\":\"itemDynamic\",\"_id\":\"*********\",\"imageTag2\":\"金山卫\",\"imageTag\":\"到店吃\",\"subMessage\":\"¥78\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_countdown_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":4,\"mainMessage2\":\"8\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100086108057841\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c5_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"味滋润养胃多乳酸菌风味饮品 340ML*12瓶\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100086108057841%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"4362人已买过\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/groceryimages/e4ec1db5cd3e5fcf9b8cb243274bee90159877.jpg\",\"subMessageStyle\":1,\"key\":\"100086108057841:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":4,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100086108057841\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":5,\"mainMessage2\":\"8.2\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100151590691888\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c6_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"网红重庆酸辣粉发整箱 6桶装\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100151590691888%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"8678人已买过\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/groceryimages/94de1239e7407d3866e006cef1c75aea488738.jpg\",\"subMessageStyle\":1,\"key\":\"100151590691888:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":5,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100151590691888\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"deadlineTime\":1636559999000,\"nonBannerIndex\":6,\"mainMessage2\":\"37.79\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"0\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"isSecKill\\\":\\\"1\\\",\\\"xzName\\\":\\\"团购\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"*********\",\"item_type\":\"deal\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"057743403483197389313923936291490487151_c7_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_countdown_card\",\"bu\":\"DAOCAN_MEISHI_DEAL\",\"position\":\"shopping_cart\",\"bu_type\":\"meishi\"},\"title\":\"<font><image src=\\\"https://p0.meituan.net/travelcube/96f12d528ec86cb0e8f09f5cbb9061424503.png@150w_45h_80q\\\" width=\\\"47.5pt\\\" margin-right=\\\"4pt\\\"  margin-top=\\\"2.5pt\\\" height=\\\"15pt\\\"/><b>赛百味SUBWAY 单人金枪鱼三明治曲奇热饮套餐</b></font>\",\"type\":\"DAOCAN_MEISHI_DEAL\",\"_iUrl\":\"imeituan://www.meituan.com/food/deal?did=*********&stid=057743403483197389313923936291490487151_c7_e79fe1e7b3327e0a751378b1da93805c6&poiid=88238643\",\"promotionId\":\"*********\",\"imageStyle\":0,\"fixedHeight\":false,\"atmosphereTitleTag\":\"daocan_deal_seckill_flag\",\"subTitle\":\"金枪鱼三明治曲奇热饮套餐\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/deal/563b2034872cc89ba28dcbfbb5d0f7b130910.jpg\",\"subMessageStyle\":1,\"key\":\"*********:DAOCAN_MEISHI_DEAL\",\"_from\":\"DAOCAN_MEISHI_DEAL\",\"mainMessage\":\"¥\",\"subTitle2\":\"93人已抢\",\"_type\":\"deal\",\"index\":6,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_countdown_card\",\"style\":\"itemDynamic\",\"_id\":\"*********\",\"imageTag2\":\"世纪公园/科技馆\",\"imageTag\":\"到店吃\",\"subMessage\":\"¥54\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_countdown_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":7,\"mainMessage2\":\"6.5\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100067144057258\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c8_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"新鲜草鸡蛋 10枚/盒\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100067144057258%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"102893人想买\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/groceryimages/e42766a6e025a26631b306c4c94b2a78209350.jpg\",\"subMessageStyle\":1,\"key\":\"100067144057258:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":7,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100067144057258\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":8,\"mainMessage2\":\"4.5\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100111875263029\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c9_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"冠淮荞麦挂面 1kg/桶\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100111875263029%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"嘉定镇街道粮油副食热销产品\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/groceryimages/1006b39f6478d5e00695259cbae41346275657.jpg\",\"subMessageStyle\":1,\"key\":\"100111875263029:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":8,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100111875263029\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"deadlineTime\":1636559999000,\"nonBannerIndex\":9,\"mainMessage2\":\"96\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"0\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"isSecKill\\\":\\\"1\\\",\\\"xzName\\\":\\\"团购\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"36174918\",\"item_type\":\"deal\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"057743403483197389313923936291490487151_c10_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_countdown_card\",\"bu\":\"DAOCAN_MEISHI_DEAL\",\"position\":\"shopping_cart\",\"bu_type\":\"meishi\"},\"title\":\"<font><image src=\\\"https://p0.meituan.net/travelcube/96f12d528ec86cb0e8f09f5cbb9061424503.png@150w_45h_80q\\\" width=\\\"47.5pt\\\" margin-right=\\\"4pt\\\"  margin-top=\\\"2.5pt\\\" height=\\\"15pt\\\"/><b>甜蜜芝语 精致王冠：1磅精致王冠蛋糕、爱情火烈鸟、奔驰大G、公主蛋糕、布朗熊、小马王国等精选唯美网红生日蛋糕，送女神，送男神，送宝宝，好看又好吃，祝你实现愿望 55 选 1</b></font>\",\"type\":\"DAOCAN_MEISHI_DEAL\",\"_iUrl\":\"imeituan://www.meituan.com/food/deal?did=36174918&stid=057743403483197389313923936291490487151_c10_e79fe1e7b3327e0a751378b1da93805c6&poiid=96938295\",\"promotionId\":\"*********\",\"imageStyle\":0,\"fixedHeight\":false,\"atmosphereTitleTag\":\"daocan_deal_seckill_flag\",\"subTitle\":\"服务周到\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/dealwatera/02b42ca981c45ef22655d9a3cacdc58a909770.jpg\",\"subMessageStyle\":1,\"key\":\"36174918:DAOCAN_MEISHI_DEAL\",\"_from\":\"DAOCAN_MEISHI_DEAL\",\"mainMessage\":\"¥\",\"subTitle2\":\"5000+人已抢\",\"_type\":\"deal\",\"index\":9,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_countdown_card\",\"style\":\"itemDynamic\",\"_id\":\"36174918\",\"imageTag2\":\"滴水湖临港地区\",\"imageTag\":\"到店吃\",\"subMessage\":\"¥258\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_countdown_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":10,\"mainMessage2\":\"3.8\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100167393554336\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c11_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"福建平和 红心蜜柚 3斤±0.5斤\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100167393554336%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"60561人已加购\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/groceryimages/c8fd85c797387c948dc77f85bb0e04da453884.jpg\",\"subMessageStyle\":1,\"key\":\"100167393554336:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":10,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100167393554336\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":11,\"mainMessage2\":\"10.8\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100132581880543\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c12_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"精选优质鲜冻琵琶腿（纯干货，不注水） 1kg/袋\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100132581880543%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"14821人已加购\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/groceryimages/b91e6bcc9be361cb206e612ead920595103091.jpg\",\"subMessageStyle\":1,\"key\":\"100132581880543:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":11,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100132581880543\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"deadlineTime\":1636559999000,\"nonBannerIndex\":12,\"mainMessage2\":\"98\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"0\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"isSecKill\\\":\\\"1\\\",\\\"xzName\\\":\\\"团购\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"*********\",\"item_type\":\"deal\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"057743403483197389313923936291490487151_c13_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_countdown_card\",\"bu\":\"DAOCAN_MEISHI_DEAL\",\"position\":\"shopping_cart\",\"bu_type\":\"meishi\"},\"title\":\"<font><image src=\\\"https://p0.meituan.net/travelcube/96f12d528ec86cb0e8f09f5cbb9061424503.png@150w_45h_80q\\\" width=\\\"47.5pt\\\" margin-right=\\\"4pt\\\"  margin-top=\\\"2.5pt\\\" height=\\\"15pt\\\"/><b>BON CAKE 1 个惊喜甜蜜盲盒 1 个</b></font>\",\"type\":\"DAOCAN_MEISHI_DEAL\",\"_iUrl\":\"imeituan://www.meituan.com/food/deal?did=*********&stid=057743403483197389313923936291490487151_c13_e79fe1e7b3327e0a751378b1da93805c6&poiid=1144016846\",\"promotionId\":\"*********\",\"imageStyle\":0,\"fixedHeight\":false,\"atmosphereTitleTag\":\"daocan_deal_seckill_flag\",\"subTitle\":\"口味做的不错\",\"_style\":\"dynamicV2\",\"imageUrl\":\"http://p0.meituan.net/dealwatera/6b46156a5b9ba343875c6ebeafb78101440191.jpg\",\"subMessageStyle\":1,\"key\":\"*********:DAOCAN_MEISHI_DEAL\",\"_from\":\"DAOCAN_MEISHI_DEAL\",\"mainMessage\":\"¥\",\"subTitle2\":\"216人已抢\",\"_type\":\"deal\",\"index\":12,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_countdown_card\",\"style\":\"itemDynamic\",\"_id\":\"*********\",\"imageTag2\":\"龙柏地区\",\"imageTag\":\"到店吃\",\"subMessage\":\"¥268\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_countdown_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1801_1632644082832.zip\",\"type\":\"shoppingcart_recommend\"},{\"biz\":{\"nonBannerIndex\":13,\"mainMessage2\":\"0.95\",\"valLab\":{\"tabId\":\"homepage\",\"extension\":\"{\\\"tabId\\\":\\\"homepage\\\",\\\"yxVrtlPoiid\\\":\\\"143001947520698\\\",\\\"scene_poi_id\\\":\\\"\\\",\\\"scene_deal_id\\\":\\\"\\\",\\\"tabName\\\":\\\"猜你喜欢\\\",\\\"scene_order_id\\\":\\\"\\\",\\\"xzIpayFlag\\\":\\\"1\\\",\\\"poi_type\\\":\\\"0\\\",\\\"page_tab\\\":\\\"\\\",\\\"client_source\\\":\\\"\\\",\\\"xzName\\\":\\\"优选\\\"}\",\"tabName\":\"猜你喜欢\",\"item_id\":\"100145880686089\",\"item_type\":\"youxuan_sku\",\"globalId\":\"79fe1e7b3327e0a751378b1da93805c6\",\"stid\":\"045815140445216847059147564033418725995_c14_e79fe1e7b3327e0a751378b1da93805c6\",\"type\":\"doubleFeed\",\"mode\":\"\",\"template_name\":\"shoppingcart_normal_card\",\"bu\":\"YOUXUAN_YOUXUAN_SKU\",\"position\":\"shopping_cart\",\"bu_type\":\"youxuan\"},\"title\":\"高原黄心土豆 500±50g\",\"type\":\"YOUXUAN_YOUXUAN_SKU\",\"_iUrl\":\"imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc&targetPath=%2Fpages%2Findex%2Findex%3FpoiId%3D11016797%26searchSkuIds%3D100145880686089%26longitude%3D%26latitude%3D%26poiIdStr%3DL3rVw7f9_5LEws_oExAtVQE\",\"imageStyle\":0,\"fixedHeight\":false,\"subTitle\":\"115801人已加购\",\"_style\":\"dynamicV2\",\"imageUrl\":\"https://img.meituan.net/750.750/groceryinfrastructureimages/216efad3afee432c2f3f431ce4b5440c420451391.png\",\"subMessageStyle\":1,\"key\":\"100145880686089:YOUXUAN_YOUXUAN_SKU\",\"_from\":\"YOUXUAN_YOUXUAN_SKU\",\"mainMessage\":\"¥\",\"_type\":\"youxuan_sku\",\"index\":13,\"imageHeight\":0,\"tags\":[],\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"adCard\":false,\"templateName\":\"shoppingcart_normal_card\",\"style\":\"itemDynamic\",\"_id\":\"100145880686089\",\"imageTag2\":\"明日16:00后\",\"imageTag\":\"次日自提\"},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"foldState\":0,\"lazyLoadImage\":0,\"position\":0,\"templateName\":\"shoppingcart_normal_card\",\"templateUrl\":\"http://layout.meituan.net/v1/mss_bfdec1ae5f4c426985f480eb46e435b6/layout/tmpl_instance_1798_1632645061904.zip\",\"type\":\"shoppingcart_recommend\"}],\"showCount\":-1,\"style\":{\"columnCount\":2,\"columnWeight\":[1,1],\"hGap\":\"9pt\",\"margin\":[\"0\",\"11pt\",\"0\",\"11pt\"],\"vGap\":\"9pt\",\"zIndex\":0},\"type\":\"type_staggered\"}],\"id\":\"shoppingCart\",\"itemCount\":17,\"metricsInfo\":{\"totalTime\":1},\"overlap\":false,\"refreshBottom\":{\"enable\":true,\"preloadNum\":8,\"type\":\"default\"},\"refreshTop\":{\"enable\":true,\"preloadScreen\":0,\"type\":\"default\"},\"statusBar\":{\"darkMode\":true,\"type\":\"default\"},\"style\":{\"background\":{\"color\":\"#F2F2F2\"},\"zIndex\":0}}}";

    static String homeMine = "{\"code\":0,\"data\":{\"actionBar\":{\"biz\":{\"account\":{\"footstep\":{\"gameName\":\"走路赚钱\",\"iconUrl\":\"https://p0.meituan.net/linglong/dd83de89907e30b86483be08f3d99593134193.gif\",\"targetUrl\":\"imeituan://www.meituan.com/web?notitlebar=1&url=https://ux.meituan.com/footstep/index.html\",\"content\":\"走路赚钱\",\"isShow\":false},\"avatarUrl\":\"https://img.meituan.net/avatar/7f1eddcb4d1d7fcf855bb13529c9c37e81540.jpg\",\"nickname\":\"测试页面\",\"iconDestUrl\":\"imeituan://www.meituan.com/userinfo\",\"id\":**********,\"growthDestUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=group&mrn_entry=member-center&mrn_component=member-center\",\"growthValue\":35,\"verifyInfo\":{\"verifyStatus\":0,\"more\":{\"activity_id\":\"-999\",\"biz_id\":\"65\",\"display_name\":\"去实名\",\"config_id\":\"5615\",\"activity_from\":\"-999\"},\"link\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fnpay.meituan.com%2Ffup%2Fverification%2Findex.html\",\"content\":\"去实名\"}}},\"titleColor\":\"#222222\",\"type\":\"simple_slide_gradient\"},\"extra\":{\"moduleVersion\":2},\"groups\":[{\"id\":\"minepage_account_group\",\"items\":[],\"showCount\":-1,\"style\":{\"background\":{},\"borderWidth\":\"0\",\"margin\":[\"0\",\"0\",\"10\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"},{\"items\":[],\"showCount\":-1,\"style\":{\"background\":{},\"borderWidth\":\"0\",\"margin\":[\"30\",\"0\",\"10\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"}],\"id\":\"minepage\",\"itemCount\":17,\"overlap\":true,\"refreshTop\":{\"enable\":true,\"type\":\"default\"},\"style\":{\"background\":{\"color\":\"#F4f4f4\"},\"ab_info\": \"{\\\"ab_group_homepage_v12_youxuan\\\":\\\"-999\\\",\\\"ab_group_homepage_v12\\\":\\\"-999\\\"}\",\"zIndex\":0}}}";

    static String search = "{\"gathers\":[{\"@c\":\".BusinessSpsGather\",\"id\":\"all\",\"items\":[],\"trace\":{\"dqu_block_id\":\"0\"},\"showType\":\"singleRow\",\"index\":0,\"name\":\"全部\",\"addressType\":\"daojia\",\"floatingMinItemIndex\":-1}],\"version\":\"v5\",\"queryId\":\"6440523023353586381\",\"realSize\":5,\"limit\":10,\"totalCount\":5,\"pagePosition\":\"more\",\"trace\":{\"request_id\":\"75569225-3A6E-4555-BFCD-6AB6D8422962\",\"global_id\":\"2056078074304815993\",\"search_id\":\"3123783494837323813\",\"query_Id\":\"6440523023353586381\",\"st_id\":\"136321426579363828242775930690670858617\",\"search_key\":\"火锅\",\"offset\":\"0\",\"version\":\"sps\",\"model_type\":\"poi\",\"tab_index\":\"0\",\"tab_name\":\"全部\",\"tab_id\":\"all\",\"original_ads_position_temp\":\"\",\"float_ads_position_temp\":\"3+4N\"},\"pageFeedbackMap\":{\"lastItemAgg\":\"true\",\"itemDeCardPosition\":\"5\"},\"subVersion\":\"sps\",\"tab\":{\"@c\":\".TabItem\",\"elements\":[{\"@c\":\".TabElement\",\"type\":\"all\",\"id\":\"all\",\"trace\":{\"element_index\":\"0\",\"element_click_type\":\"tiaozhuan\"},\"title\":{\"text\":\"全部\",\"fontColor\":\"#4D4D4D\",\"fontSize\":16},\"selectedTitle\":{\"text\":\"全部\",\"fontColor\":\"#222222\",\"fontSize\":16},\"feedbackMap\":{\"spsTabId\":\"all\"},\"selected\":true,\"topDisplayInfo\":[{\"iconType\":\"mapInfo\",\"newStyle\":true,\"display\":true}],\"addressType\":\"daojia\",\"showType\":\"singleRow\",\"cartButton\":{\"displayCartButton\":true,\"jumperUrl\":\"imeituan://www.meituan.com/shoppingcart\",\"count\":328,\"displayCount\":true,\"trace\":{\"item_click_type\":\"yixiang\"}},\"triggerComparePrice\":true,\"assistantButton\":{\"jumperUrl\":\"imeituan://www.meituan.com/msc?appId=032b788601b74315&targetPath=%2Fpages%2Fhome%2Findex%3FmoduleName%3Dhome-query-guide%2Chome-query-inspiration%26entrance%3D1%26query%3D%E7%81%AB%E9%94%85%26globalId%3D2056078074304815993\",\"explainBubble\":{\"url\":\"https://p0.meituan.net/travelcube/dd7ced81af7091026e4a98a5f03abf0455624.png\",\"width\":183.0,\"height\":33.0},\"iconUrl\":\"https://p0.meituan.net/travelcube/1feb9f19d3bf3bddba0e5830e706fd30220388.gif\",\"trace\":{\"item_click_type\":\"yixiang\"}}}],\"trace\":{\"item_click_type\":\"tiaozhuan\"},\"selectedIndex\":0},\"modelType\":\"poi\",\"topButton\":{\"iconType\":\"topButtonInfo\",\"trace\":{\"item_click_type\":\"jiaohu\"},\"display\":true},\"expSign\":{\"navibarStay\":\"true\"},\"extensionRequestInfo\":{\"needSecondExtensionRequest\":true,\"pokerABTestMap\":{\"spsFilterSplit\":\"new\"}},\"gradientSwitch\":true,\"productFrame\":\"food_commodity_v1\"}";
    static String feed = "{\"globalId\":\"57f71739d27bb25366e93c97dcd7f694\",\"title\":\"猜你喜欢\",\"stid\":\"259681590900192807271165890995088727208_c2_e57f71739d27bb25366e93c97dcd7f694\",\"data\":[],\"topData\":[],\"bottomData\":[],\"sessionId\":\"871eb06a3a398a9c1cf73d88eba0ed31\",\"rolltop\":5,\"bottom\":true,\"styleType\":\"feed\",\"feedStyle\":\"twoColumn\",\"mge\":{\"tab_index\":0,\"ext\":{}},\"preNum\":6,\"beginTime\":\"1692177509051\",\"endTime\":\"1692177509652\",\"extendInfo\":{\"showCardStyle\":\"style1\"},\"userInfo\":{\"isStudent\":false,\"isStudentVerified\":false},\"refresh\":true,\"speedFactor\":0.4,\"serverInfo\":{},\"feedbackVesion\":\"v2\",\"guessApiExpKey\":\"Api_homepage_Hp19H2NewBase_20221228_contentMRNExp4\",\"nonWifiAutoPlayVideo\":false,\"contentTextFold\":\"2\",\"slideOptSwitch\":false,\"uniqueType\":0,\"youxuanZhuanquPattern\":\"\",\"slideSign\":{\"display\":false},\"grayCardCount\":4,\"globalLayoutInfo\":{\"type\":\"style2023\",\"gridTopGap\":\"8\",\"gridHorizontalGap\":\"12\",\"gridMidGap\":\"8\",\"backgroundColor\":\"#EBEBEB\"}}";
    static String groupBuy = "{\"gathers\":[{\"@c\":\".BusinessSpsGather\",\"id\":\"all\",\"items\":[],\"trace\":{\"dqu_block_id\":\"0\"},\"showType\":\"singleRow\",\"index\":0,\"name\":\"全部\",\"addressType\":\"daojia\",\"floatingMinItemIndex\":-1}],\"version\":\"v5\",\"queryId\":\"2736869606*********\",\"realSize\":5,\"limit\":10,\"totalCount\":5,\"pagePosition\":\"more\",\"trace\":{\"request_id\":\"cdfa45a1-a5b7-416a-96df-4679c981156e\",\"global_id\":\"8885743850663783544\",\"search_id\":\"194778590812046203\",\"query_Id\":\"2736869606*********\",\"search_key\":\"\",\"offset\":\"0\",\"tab_id\":\"all\",\"original_ads_position_temp\":\"\",\"float_ads_position_temp\":\"\"},\"pageFeedbackMap\":{\"itemDeCardPosition\":\"7\"},\"tab\":{\"@c\":\".TabItem\",\"elements\":[{\"@c\":\".TabElement\",\"type\":\"recommend\",\"id\":\"recommend\",\"trace\":{\"element_index\":\"0\",\"element_click_type\":\"tiaozhuan\"},\"title\":{\"text\":\"推荐\"},\"feedbackMap\":{\"spsTabId\":\"recommend\"},\"selected\":true,\"image\":{\"url\":\"https://p1.meituan.net/travelcube/1536a38bf3842bd8c760efd931ccde689546.png\"}}],\"trace\":{\"item_click_type\":\"tiaozhuan\"}},\"expSign\":{},\"extensionRequestInfo\":{\"needSecondExtensionRequest\":true},\"backgroundColor\":\"#F5F5F5\",\"gradientSwitch\":true,\"topCards\":[],\"productFrame\":\"tuanList\",\"hasMore\":false,\"nextOffset\":10}";

    public static final ImmutableMap<String,String> pageApiData = ImmutableMap.<String, String>builder()
            .put("shoppingcart",shoppingCart)
            .put("mine",homeMine)
            .put("search",search)
            //0816增加猜喜逻辑
            .put("staggered_feed",feed)
            .put("groupbuy",groupBuy)
            .build();


    public static void jsonLoop(Object object){
        if (object instanceof JsonObject){
            JsonObject jsonObject = (JsonObject) object;
            for (Map.Entry entry: jsonObject.entrySet()){
                Object o = entry.getValue();

                if (o instanceof JsonPrimitive){
                    JsonPrimitive temo = (JsonPrimitive) entry.getValue();
                    if (temo.isBoolean()){
//                        System.out.println("isBoolean:"+temo);
                    }else if (temo.isNumber()){
//                        System.out.println("isNumber:"+temo);
                    }else if (temo.isString()){
                        System.out.println(entry.getKey()+" 含义:"+ValueMeaning(temo.getAsString())+"  "+temo);
                    }

                }else if (o instanceof JsonArray){
                    //// TODO: 2022/4/13 单独处理
                }else {
                    jsonLoop(o);
                }
            }
        }
    }


    public static String ValueMeaning(String test){
        //字符串类型的value都有哪几种类型呢：内容、颜色、地址、参数
        String isColor = "^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$";
        String isScheme = "imeituan://";
        String isLink = "http";

        if (Pattern.matches(isColor,test)){
            return "color";
        }
        Pattern SchemePattern =  Pattern.compile(isScheme);
        Matcher SchemeMatcher =  SchemePattern.matcher(test);
        if (SchemeMatcher.find()){
            return "scheme";
        }
        Pattern LinkPattern =  Pattern.compile(isLink);
        Matcher LinkMatcher =  LinkPattern.matcher(test);
        if (LinkMatcher.find()){
            return "link";
        }
        if(test.contains("{")&&test.contains("}")&&test.contains(":")){
            return "dict";
        }

        return "content";

    }

    public static List<String> pathList;

    public static void readObject(Object object,String jsonPath){
        if (object instanceof JsonObject){
            JsonObject jsonObject = (JsonObject) object;
            String parentPath = jsonPath;
            for (Map.Entry entry: jsonObject.entrySet()){
                Object o = entry.getValue();
                String key = entry.getKey().toString();
                jsonPath = parentPath + "." + key;

                if (o instanceof JsonObject){

                    readObject(o,jsonPath);
                }else if (o instanceof JsonArray){
                    readArray((JsonArray)o,jsonPath);
                }else {
                    JsonPrimitive value = (JsonPrimitive) entry.getValue();
                    if (value.isBoolean()){
//                        System.out.println("isBoolean:"+temo);
                        System.out.println(jsonPath+" "+entry.getValue());
                    }else if (value.isNumber()){
//                        System.out.println("isNumber:"+temo);
                        System.out.println(jsonPath+" "+entry.getValue());
                    }else if (value.isString()){
                        System.out.println(jsonPath+" "+entry.getValue()+" 含义:"+ValueMeaning(value.getAsString()));
                    }
                }
            }
        }
    }


    public static void readArray(JsonArray array,String jsonPath){
        String parentPath = jsonPath;
        for (int i = 0; i < array.size(); i++) {
            Object value = array.get(i);
            jsonPath = parentPath+"["+i+"]";
            if (value instanceof JsonArray){
                readArray((JsonArray)value,jsonPath);
            }else if(value instanceof JsonObject){
                readObject((JsonObject)value,jsonPath);
            }else{
                JsonPrimitive temo = (JsonPrimitive) value;
                if (temo.isBoolean()){
//                        System.out.println("isBoolean:"+temo);
                    System.out.println(jsonPath+" "+temo);
                }else if (temo.isNumber()){
//                        System.out.println("isNumber:"+temo);
                    System.out.println(jsonPath+" "+temo);
                }else if (temo.isString()){
                    System.out.println(jsonPath+" "+temo+" 含义:"+ValueMeaning(temo.getAsString()));
                }
            }
        }
    }

    public static void main(String[] args) {
        JsonObject jsonObject = new JsonParser().parse(homeMine).getAsJsonObject();
        readObject(jsonObject,"$");
//        System.out.println(pathList);





//
//        jsonLoop(jsonObject);

//        String test = "#00ff00";
//        String res = ValueMeaning(test);
//        System.out.println(res);




//        Configuration configuration = Configuration.builder()
//                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();

//        Configuration conf = Configuration.defaultConfiguration();
//        DocumentContext documentContext = using(conf).parse(homeMine);
//        List<Object> text = documentContext.read("$");
//        System.out.println(text);
//        for (String path : text){
//            System.out.println(path);
//        }

//        Configuration conf = Configuration.defaultConfiguration();
//        List<String> jsonPaths = JsonPath.using(conf).parse(homeMine).read("$");
//
//        for (String path : jsonPaths) {
//            System.out.println(path);
//        }
//        List<Object> img = documentContext.read("$..src");
    }
}
