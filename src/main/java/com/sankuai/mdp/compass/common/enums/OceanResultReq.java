package com.sankuai.mdp.compass.common.enums;

import lombok.Data;


@Data
public class OceanResultReq {
    /*通道id*/
    private Integer channleId;

    /*应用英文标识*/
    private String appIdentifier;

    /*平台名称*/
    private String platformName;

    /*事件标识*/
    private String bid;

    /*页面标识*/
    private String cid;

    /*测试人(mis号)*/
    private String tester;

    /*开始测试时间*/
    private String testTimeStart;

    /*测试结束时间*/
    private String testTimeEnd;

    /*验证类型*/
    private Integer validationType;

    /*验证结果*/
    private Integer validateResult;

    /*埋点负责人(mis号)*/
    private String ownerIdentifier;

    /*埋点类型*/
    private String eventType;

    /*埋点更新开始时间*/
    private String lastModifyTimeStart;

    /*埋点更新结束时间*/
    private String lastModifyTimeEnd;







}
