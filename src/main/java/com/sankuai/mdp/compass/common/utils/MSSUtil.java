package com.sankuai.mdp.compass.common.utils;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.*;
import java.net.URL;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by xieyong<PERSON><PERSON> on 2020/2/21.
 */
public class MSSUtil {
    private static String accessKey = "894a8fbe96b64894ab526523ea318aee";
    private static String endpoint = "s3plus.vip.sankuai.com";
    private static String bucketName = "autotest";
    private static AWSCredentials credentials = new BasicAWSCredentials(accessKey, TokenUtil.getToken("mss_secret"));
    private static AmazonS3Client mssClient = new AmazonS3Client(credentials);

    public static void main(String argv[]) {
        CreateAmazonS3Conn(endpoint);
        createBucketIfNotExist(bucketName);
//        getObject(bucketName,"mge_report_1582515396304_2020-02-24.xlsx");
        String url = presignUrl(bucketName,"mge_report_1582515396304_2020-02-24.xlsx");
    }

    public static String uploadExcel(String fileName) {
        FileUtil fileUtil = new FileUtil();
        long currentTime = new Date().getTime();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String date = df.format(new Date());
        String ObjectName = "mge_report_"+currentTime+"_"+date+".xlsx";
        try {
            CreateAmazonS3Conn(endpoint);
            createBucketIfNotExist(bucketName);
            putObjectFile(bucketName, ObjectName, fileName);
            fileUtil.delete(fileName);
            return presignUrl(bucketName,ObjectName);
        } catch (Exception e) {
            System.out.print("上传文件失败");
        }
        return null;
    }

    public static String uploadZip(String filePath) {
        FileUtil fileUtil = new FileUtil();
        long currentTime = new Date().getTime();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String date = df.format(new Date());
        String ObjectName = "auto_screenshot_" + currentTime + "_" + date + ".zip";
        try {
            CreateAmazonS3Conn(endpoint);
            createBucketIfNotExist(bucketName);
            putObjectFile(bucketName, ObjectName, filePath);
            fileUtil.delete(filePath);
            return presignUrl(bucketName, ObjectName);
        } catch (Exception e) {
            System.out.print("上传文件失败");
        }
        return null;
    }

    public static AmazonS3 CreateAmazonS3Conn(String hostname){

        //配置云存储服务地址
        mssClient.setEndpoint(hostname);

        //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        mssClient.setS3ClientOptions(s3ClientOptions);
        return mssClient;
    }

    public static void createBucketIfNotExist(String bucketName){
        try{
            //判断待创建的bucket是否存在，如果存在不用重复创建，重复创建同名bucket服务器端会返回错误
            if (mssClient.doesBucketExist(bucketName) == false) {
                mssClient.createBucket(bucketName);
            }
        }catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3创建桶失败：Caught an ServiceException.");
        }catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3创建桶失败：Caught an ClientException.");
        }
    }

    public static void putObjectFile(String bucketName, String objectName, String uploadFileName){
        try{
            FileInputStream inputStream = new FileInputStream(uploadFileName);
            mssClient.putObject(bucketName,objectName,inputStream,null);

        }catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3上传失败：Caught an AmazonServiceException.");

        }catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3上传失败：Caught an ClientException.");

        }catch(IOException e) {
            System.out.println("s3上传失败：IOException.");

        }

    }

    public static void getObject(String bucketName, String objectName){
        try{
            S3Object s3object = mssClient.getObject(new GetObjectRequest(
                    bucketName, objectName));
            InputStream content = s3object.getObjectContent();
            BufferedReader reader = new BufferedReader(new InputStreamReader(content));
            if (content != null) {
                while (true) {
                    String line = reader.readLine();
                    if (line == null) break;
                    System.out.println("\n" + line);
                }
                //获取object后需要close(),释放连接
                s3object.close();
            }
        }catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3下载失败：Caught an ServiceException.");
            System.out.print(ase);
        }catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3下载失败：Caught an ClientException.");
        }catch (Exception e) {
            System.out.println("s3下载失败：Exception");
        }
    }

    public static String presignUrl(String bucketName, String objectName) {
        String resultUrl = "";
        try{
            //设定url的有效时间
            java.util.Date expiration = new java.util.Date();
//            long milliSeconds = expiration.getTime();
//            milliSeconds += 1000 * 60 * 60; // Add 1 hour.
//            System.out.print(milliSeconds);
//            System.out.print(milliSeconds*1000);
            long milliSeconds = getAfterMonth(3);
            expiration.setTime(milliSeconds);
            //指定授权的bucket和object
            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName, objectName);
            //指定授权的请求类型
            generatePresignedUrlRequest.setMethod(HttpMethod.GET);
            generatePresignedUrlRequest.setExpiration(expiration);
            //生成授权的url
            URL url = mssClient.generatePresignedUrl(generatePresignedUrlRequest);
            System.out.println("Pre-Signed URL = " + url.toString());
            return url.toString();

        }catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3生成url失败：Caught an ServiceException.");

        }catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3生成url失败：Caught an ClientException.");
        }
        return resultUrl;
    }

    public static long getAfterMonth(int number) {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String inputDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        Date date = null;
        try{
            date = sdf.parse(inputDate);
        }catch(Exception e){

        }
        c.setTime(date);
        c.add(Calendar.MONTH,number);
        String strDate = sdf.format(c.getTime());

        long milliSeconds = sdf.parse(strDate, new ParsePosition(0)).getTime();
//        System.out.print(milliSeconds);
        return milliSeconds;
    }
}
