package com.sankuai.mdp.compass.common.utils;

import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Credentials;

@Slf4j
public class TokenUtil {
    public static String getToken(String name) {
        String appKey = "com.sankuai.sigma.compass";
        String value = null;
        try {
            value = Kms.getByName(appKey, name);
//            log.info("从kms获取到token:"+value);

        } catch (KmsResultNullException e) {
            log.info("从kms获取token出错，token的name为"+name+"  错误信息："+e.toString());
        } finally {
            return value;
        }
    }
    
    public static String getNewJenkinsToken(){
        String credential = Credentials.basic("fengenci","1129a888ab32339c15230c4be7c94b3eba");
        return credential;
    }
    public static String getNewJenkinsUserCentreToken(){
        String credential = Credentials.basic("liuyang359","11632a9281b4005ff427412f04c19abd19");
        return credential;
    }
}
