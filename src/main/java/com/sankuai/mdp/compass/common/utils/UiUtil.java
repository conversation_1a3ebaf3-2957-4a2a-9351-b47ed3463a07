package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.horus.service.OCRServices;
import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.inf.patriot.org.json.JSONObject;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import org.python.antlr.ast.Str;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Logger;

/**
 * Created by skt on 2020/10/26.
 */
public class UiUtil {
    private static String OVERLAP = "/opt/meituan/script/diff/overlap.py";
    ComUtil comUtil = new ComUtil();
    static ComConstant comConstant = new ComConstant();



    public ArrayList<Object> overlapJudgment(String mockId,String id,int jobId){
        int isValid = 0;
        String overlap_pic = "";
        String exra = "";

        FileUtil r = new FileUtil();
        String coordPath = comConstant.OUT_PUT +jobId+ "/" + id + "/coordinate/";
        File file = new File(coordPath + mockId + ".txt");
        isValid = 0;
        overlap_pic = "";
        exra += "";

        if (file.exists()) {
            String coordAll = r.read(coordPath + mockId + ".txt");
            String coordPicPath = coordPath + mockId;
            String isOverlap = comUtil.doPython2(OVERLAP, coordAll, coordPicPath);

            if (isOverlap.equals("False")) {
                isValid = 1;
                overlap_pic = uploadPicture(coordPath + mockId + ".png");
//                exra = "【有重叠】";
            }

            ArrayList<String> arrayList1 = doOverlap(coordAll,"text","text");
            for (String  s1 : arrayList1) {
                exra+=s1;
            }
            //TODO 【图片&图片校验】、【图片&文字校验】先注释掉，用的时候再打开--2023-03-29更新：打开试试
            ArrayList<String> arrayList2= doOverlap(coordAll,"text","image");
            for (String  s2 : arrayList2) {
                exra+=s2;
            }
            ArrayList<String> arrayList3 = doOverlap(coordAll,"image","image");
            for (String  s3 : arrayList3) {
                exra+=s3;
            }

        }
        ArrayList<Object> coordList = new ArrayList<Object>();
//        coordList.add(isValid);
        coordList.add(overlap_pic);
        coordList.add(exra);
        return coordList;
    }


    /**
     *
     * @param coordAll 传入坐标txt
     * @param firstType 需要校验的类型1
     * @param secondType 需要校验的类型2
     * @return
     */
    public ArrayList doOverlap(String coordAll, String firstType, String secondType){
        JsonArray jsonArray = new JsonParser().parse(coordAll).getAsJsonArray();
        ArrayList arrayList = new ArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            JsonObject coord1 = jsonArray.get(i).getAsJsonObject();
            String type1 = coord1.get("type").getAsString();
            if(!type1.equals(firstType) && !type1.equals(secondType))continue;

            for (int j = i+1; j < jsonArray.size(); j++) {
                JsonObject coord2 = jsonArray.get(j).getAsJsonObject();
                String type2 = coord2.get("type").getAsString();
                //这一坨的逻辑是说：如果想判断的两个类型不一样，避免重复校验一样类型的数据
                if((!type2.equals(firstType) && !type2.equals(secondType)) || ((!firstType.equals(secondType))&&(type1.equals(type2))))continue;
                Boolean result = new UiUtil().checkOverlapAlgorithm(coord1,coord2);

                if(!result){
                    String text1 = coord1.get("data").getAsString();
                    String text2 = coord2.get("data").getAsString();
                    if (!text1.equals(text2)){
                        String coordDetail = "【"+ text1+"】与"+"【"+ text2+"】重叠"+"\n";
                        arrayList.add(coordDetail);
                    }

                }
            }
        }
        return arrayList;
    }



    /*
    重叠算法实现
    * */
    private Boolean checkOverlapAlgorithm(JsonObject coord1,JsonObject coord2){
        try {
            int Xmin1 = coord1.get("x").getAsInt();
            int Ymin1 = coord1.get("y").getAsInt();
            int W1 = coord1.get("w").getAsInt();
            int Xmax1 = W1+Xmin1;
            int H1 = coord1.get("h").getAsInt();
            int Ymax1 = H1+Ymin1;

            int Xmin2 = coord2.get("x").getAsInt();
            int Ymin2 = coord2.get("y").getAsInt();
            int W2 = coord2.get("w").getAsInt();
            int Xmax2 = W2+Xmin2;
            int H2 = coord2.get("h").getAsInt();
            int Ymax2 = H2+Ymin2;

            int dx = Math.min(Xmax1,Xmax2) - Math.max(Xmin1,Xmin2);
            int dy = Math.min(Ymax1,Ymax2) - Math.max(Ymin1,Ymin2);

            //dx 和 dy 的gap可以调整，因为布局代码可能本身会有一丢丢重叠，肉眼辨认不出来就好
            if(dx> 5 && dy > 5){
                int intersection = dx*dy;
                int union = H1*W1+H2*W2-dx*dy;
                float IOU = ((float) intersection) / (float)union;
                if(IOU>0.8){
                    if ((((dx*dy) == (W1*H1)) && ("text".equals(coord1.get("type").getAsString()))) && ("image".equals(coord2.get("type").getAsString()))  || ((dx*dy)==(W2*H2)) && ("text".equals(coord2.get("type").getAsString())) && ("image".equals(coord1.get("type").getAsString()))) {
                        //此处是一种特殊情况：包含关系，不算重叠--2023-03-29新增限制：只有图片包含文字的情况才不算重叠
                        return true;
                    }
                    return false;
                }else{
                    return true;
                }
            }
        }catch (Exception e){
            System.out.println(e);
        }
        return true;


    }

    public String uploadPicture(String path) {
        try {
            String bucket = comConstant.BUCKET;
            String client_id = comConstant.CLIENT_ID;
            String client_secret = comConstant.CLIENT_SECRET;
            ImageUploadClient client = new ImageUploadClientImpl(bucket, client_id, client_secret);

            File file = new File(path);
            ImageRequest request = new ImageRequest(1, file, false);
            ImageResult res = client.postImage(request);
            return res.getOriginalLink();
        } catch (Exception e) {
//            e.printStackTrace();
            System.out.print("图片上传失败");
            return null;
        }
    }


    public void doMerge(String picPath,String templateName, String name){
        FileUtil fileUtil = new FileUtil();
        ArrayList<String> imgList = new ArrayList<>();
        ArrayList<String> filenameDict = new ArrayList<>();
        ArrayList<String> mergeList = new ArrayList<>();
        ArrayList<String> postList = new ArrayList<>();
        ArrayList<String> postUrlList = new ArrayList<>();
        OcrUtil ocrUtil = new OcrUtil();
        String filePath = picPath + templateName + "/" + name;
        fileUtil.getFileList(filePath, "", imgList);

        for (String s : imgList) {
            filenameDict.add(s.split("\\.png")[0]);
        }
        String mergeFile = filePath + "merge";
        fileUtil.getFileList(mergeFile, "", mergeList);
        for (String dict : filenameDict) {
            //外循环遍历，会通过原始文件夹，去merge文件夹找名字相似的，重新拼接，放回原始文件夹
            for (String mergeName : mergeList) {
                if (mergeName.contains(dict)){
                    postList.add(mergeName);
                }
            }
            Collections.sort(postList);//必须要排序，融合算法模型需要按顺序进行融合
            for (String post : postList) {
                String url = uploadPicture(mergeFile+"/"+post);
                postUrlList.add(url);
            }
            if (postUrlList.size()>=2){
                String mergePath = filePath+"/"+dict+".png";
                String projectPath = System.getProperty("user.dir");
                mergePath = mergePath.replaceFirst(".",projectPath);
                ocrUtil.mergePic(postUrlList,mergePath);
                postUrlList.clear();
            }
        }
    }

    public ArrayList<List> handleLocation(String result){
        JsonObject ocrObject = new JsonParser().parse(result).getAsJsonObject();
        JsonArray locationArray = ocrObject.get("location").getAsJsonArray();
        ArrayList<List> locationList = new ArrayList();
        for (JsonElement jsonElement : locationArray) {
            ArrayList tempList = new ArrayList();
            String re = jsonElement.toString().replace("[","").replace("]","").replace("\"", "");
            for (String s : re.split(" ")) {
                if (!s.isEmpty()){
                    tempList.add(s);
                }
            }
            locationList.add(tempList);
        }
        return locationList;
    }


}