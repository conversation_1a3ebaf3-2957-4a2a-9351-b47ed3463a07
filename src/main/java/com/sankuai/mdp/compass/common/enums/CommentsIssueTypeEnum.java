package com.sankuai.mdp.compass.common.enums;

/**
 * Created by skt on 2022/12/2.
 */
public enum CommentsIssueTypeEnum {
    REQUIREMENT("REQUIREMENT","需求"),
    DEVTASK("DEVTASK","任务"),
    DEFECT("DEFECT","缺陷"),
    TESTCASE("TESTCASE","测试用例"),
    TICE("TICE","提测");

    private String Code;

    private String Text;

    CommentsIssueTypeEnum(){
    }

    CommentsIssueTypeEnum(String code, String text){
        this.Code=code;
        this.Text=text;
    }

    public static CommentsIssueTypeEnum getByCode(String code) {
        switch (code){
            case "REQUIREMENT":
                return REQUIREMENT;
            case "DEFECT":
                return DEFECT;
            case "TESTCASE":
                return TESTCASE;
            case "TICE":
                return TICE;
            case "DEVTASK":
            default:
                return DEVTASK;
        }
    }

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        Code = code;
    }

    public String getText() {
        return Text;
    }

    public void setText(String text) {
        Text = text;
    }
}
