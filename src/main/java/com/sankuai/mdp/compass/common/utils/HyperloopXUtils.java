package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.coverageTest.entity.BuildRequestBody;
import com.sankuai.mdp.compass.coverageTest.entity.HookInfo;
import com.sankuai.mdp.compass.coverageTest.mapper.HookInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: sunkangtong
 * @Date: 2022-11-3
 * @Description:
 */

@Component
public class HyperloopXUtils {

    @Value("${hyperloopxBaseUrl}")
    private String hpxBaseUrl;

    @Value("${compassUrl}")
    private String compassUrl;

    @Autowired
    HookInfoMapper hookInfoMapper;

    private String token = TokenUtil.getToken("hpx_token");


    public void buildCoveragePackage(HookInfo hookInfo) {
        new JsonParser().parse(hookInfo.getIntegrationList()).getAsJsonArray();
        BuildRequestBody buildRequestBody = new BuildRequestBody();
        buildRequestBody.setBuildTypeName("通用覆盖率打包");
        buildRequestBody.setBranch(hookInfo.getBranch());
        buildRequestBody.setCompared_branch(hookInfo.getBranch());
        buildRequestBody.setAppName(hookInfo.getAppName());
        buildRequestBody.setIs_component(true);
        buildRequestBody.setProbe_type("increment");
        buildRequestBody.setBuild_type("release");
        String versionName = hookInfo.getBuildNum() + "_" + hookInfo.getMisId() + "_origin";
        buildRequestBody.setVersion_name(versionName);
        JsonArray revicesList = new JsonParser().parse(hookInfo.getMessageReceivers()).getAsJsonArray();
        buildRequestBody.setMessageReceivers(revicesList);
        JsonArray integrationList = new JsonParser().parse(hookInfo.getIntegrationList()).getAsJsonArray();
        buildRequestBody.setIntegrationList(integrationList);

        String buildNum = hookInfo.getBuildNum();
        String misId = hookInfo.getMisId();
        JsonArray messageReceiverList = new JsonParser().parse(hookInfo.getMessageReceivers()).getAsJsonArray();
        String aar = "";
        for (JsonElement jsonElement : integrationList) {
            String arrSingleName = jsonElement.getAsJsonObject().get("name").getAsString();
            if (StringUtil.isBlank(aar)) {
                aar += arrSingleName;
            } else {
                aar += "," + arrSingleName;
            }
        }
        buildRequestBody.setAar(aar);
        buildRequestBody.setCallbackUrl(compassUrl.concat("/compass/api/autoJob/taskCallBack"));
        Resp resp = NetUtil.httpPostWithJSONBody(hpxBaseUrl.concat("/api/open/build"), GsonUtil.toJson(buildRequestBody), getHeaders());
        JsonObject respData = new JsonParser().parse(resp.getData().toString()).getAsJsonObject();
        int status = respData.get("status").getAsInt();
        String coverageTaskDetailUrl = "";
        int coverBuildNum = 0;
        DxUtil dxUtil = new DxUtil();
        if (status == 1) {
            coverageTaskDetailUrl = respData.get("data").getAsJsonObject().get("taskDetailUrl").getAsString();
            coverBuildNum = respData.get("data").getAsJsonObject().get("buildNumber").getAsInt();
            //1.2 大象消息通知下相关用户，命中了覆盖率内测，等待一下覆盖率的包
            dxUtil.sendMessageByCoverageHelper("构建号为【" + buildNum + "】的提测包" + "命中了覆盖率管控内测，请等待下覆盖率打包，系统已自动为您触发同源构建", misId);
            for (JsonElement jsonElement : messageReceiverList) {
                dxUtil.sendMessageByCoverageHelper("构建号为【" + buildNum + "】的提测包" + "命中了覆盖率管控内测，请等待下覆盖率打包，系统已自动为您触发同源构建:" + coverageTaskDetailUrl, jsonElement.getAsString());
            }
        }
        hookInfo.setCoverageTaskDetailUrl(coverageTaskDetailUrl);
        hookInfo.setCoverBuildNum(coverBuildNum);
        int retryTime = hookInfo.getRetryBuildTime();
        hookInfo.setRetryBuildTime(retryTime + 1);
        hookInfo.setUpdateTime(new Date());
        hookInfoMapper.updateById(hookInfo);
    }

    private Map<String, String> getHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        return headers;
    }
}
