package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.enums.Resp;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by xieyongrui on 2019/11/16.
 */
@Slf4j
public class HttpUtil {


    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);


    private static final String USER_AGENT = "user-agent";
    private static final String USER_AGENT_VALUE = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36";
    private static final String CONNECTION = "connection";
    private static final String CONNECTION_VALUE = "keep-alive";
    private static final String ACCEPT = "accept";
    private static final String UTF8 = "utf-8";
    private static final String ACCEPT_CHARSET = "Accept-Charset";
    private static final String CONTENTTYPE = "contentType";
    private static final String SSL = "ssl";
    private static final String COOKIES = "Cookie";
    private static final String COOKIES_VALUE = "_lxsdk_cuid=16afdcf95b7c8-0438d854a41767-37677e04-1fa400-16afdcf95b7c8;_lxsdk=16afdcf95b7c8-0438d854a41767-37677e04-1fa400-16afdcf95b7c8; sid4mobileadmin=zhangkai64%3BeAHjYBTonTGTUeHVtL-vNugacZmaJ1qapxoZW5paKSQbWCQbp6WkWJokGpukWRonGqQkaSWlGZmamJqkJhqkOukp3Ps7ZcMWXSOCSi1AlkQppCSnmaamGqcmWWilmqQamlmapwChiXGisbmxgZmlBQC3vivm**eAEFwQkBACAIBLBK_BxxUKR_BDcRb_ZO9xaLkRPnFfHc7AoEkS5We-ZtriUbQCGvsi9WoB8qexEy%3B10ca45d42af2bdd381d1c133a0f66902; misLogin=zhangkai64; ssoid=eAHjYBTonTGTUeHVtL-vNugacZmaJ1qapxoZW5paKSQbWCQbp6WkWJokGpukWRonGqQkaSWlGZmamJqkJhqkOukp3Ps7ZcMWXSOCSi1AlkQppCSnmaamGqcmWWilmqQamlmapwChiXGisbmxgZmlBQC3vivm**eAEFwQkBACAIBLBK_BxxUKR_BDcRb_ZO9xaLkRPnFfHc7AoEkS5We-ZtriUbQCGvsi9WoB8qexEy";
    private static final String USER_NAME = "ptmobiletest";

    protected HttpUtil() {

    }

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(240, TimeUnit.SECONDS)
            .readTimeout(240, TimeUnit.SECONDS)
            .writeTimeout(240, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    public static String sendGet(String url, String param) throws IOException {
        return sendGet(url, param, null);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param, Map<String, String> cookies) throws IOException {
        String urlNameString = url;
        if (StringUtils.isNotBlank(param))
            urlNameString += "?" + param;
        URL realUrl = new URL(urlNameString);
        URLConnection connection = realUrl.openConnection();
        StringBuilder result = new StringBuilder();
        connection.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
        connection.setRequestProperty(CONNECTION, CONNECTION_VALUE);
        connection.setRequestProperty(ACCEPT, "*/*");

        // 添加cookies
        if (cookies != null && cookies.size() > 0) {
            connection.setRequestProperty(COOKIES, getCookies(cookies));
        }
        connection.connect();
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        } catch (Exception e) {
            log.error("发送GET请求出现异常！", e);
        }
        return result.toString();
    }

    /**
     * 使用虚拟账号向指定 URL 发送GET方法的请求
     *
     * @param url
     * @return
     */
    public static String vGet(String url) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpget = new HttpGet(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(10000).setConnectTimeout(20000)
                .setConnectionRequestTimeout(10000).build();
        httpget.setConfig(requestConfig);
        String context = StringUtils.EMPTY;

        // 设置回调接口接收的消息头
        httpget.addHeader("Content-Type", "application/json");
        //虚拟账号:ptmobiletest
        httpget.addHeader("Authorization", "Basic cHRtb2JpbGV0ZXN0OlFhQ2xpZW50MTExIQ==");
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpget);
            HttpEntity entity = response.getEntity();
            context = EntityUtils.toString(entity, HTTP.UTF_8);
        } catch (Exception e) {
            e.getStackTrace();
        } finally {
            try {
                response.close();
                httpget.abort();
                httpClient.close();
            } catch (Exception e) {
                e.getStackTrace();
            }
        }
        return context;
    }


    public static String sendPost(String url, String param) throws IOException {
        return sendPost(url, param, null);

    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param, Map<String, String> cookies) throws IOException {
        StringBuilder result = new StringBuilder();

        String urlNameString = url + "?" + param;
        URL realUrl = new URL(urlNameString);
        URLConnection conn = realUrl.openConnection();
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setRequestProperty(CONTENTTYPE, UTF8);
        conn.setRequestProperty(ACCEPT_CHARSET, UTF8);
        conn.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
        conn.setRequestProperty(CONNECTION, CONNECTION_VALUE);
        conn.setRequestProperty(ACCEPT, "*/*");

        // 添加cookies
        if (cookies != null && cookies.size() > 0) {
            conn.setRequestProperty(COOKIES, getCookies(cookies));
        }

        try (PrintWriter out = new PrintWriter(conn.getOutputStream()); BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            out.flush();
            out.print(param);
        } catch (Exception e) {
            log.error("发送 POST 请求出现异常！", e);
        }
        return result.toString();
    }

    public static String sendSSLPost(String url, String param) {
        return sendSSLPost(url, param, null);
    }

    public static String sendSSLPost(String url, String param, Map<String, String> cookies) {
        StringBuilder result = new StringBuilder();
        String urlNameString = url + "?" + param;
        try {
            SSLContext sc = SSLContext.getInstance(SSL);
            sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
            URL console = new URL(urlNameString);
            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setRequestProperty(ACCEPT, "*/*");
            conn.setRequestProperty(CONNECTION, CONNECTION_VALUE);
            conn.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
            conn.setRequestProperty(ACCEPT_CHARSET, UTF8);
            conn.setRequestProperty(CONTENTTYPE, UTF8);

            // 添加cookies
            if (cookies != null && cookies.size() > 0) {
                conn.setRequestProperty(COOKIES, getCookies(cookies));
            }

            conn.setDoOutput(true);
            conn.setDoInput(true);
            byte[] data = (param.toString()).getBytes();
            // 设置文件长度
            conn.setRequestProperty("Content-Length", String.valueOf(data.length));

            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.connect();
            InputStream is = conn.getInputStream();
            BufferedReader indata = new BufferedReader(new InputStreamReader(is));
            String ret = "";
            while (ret != null) {
                ret = indata.readLine();
                if (ret != null && !ret.trim().equals("")) {
                    result.append(ret);
                }
            }
            conn.disconnect();
            indata.close();
        } catch (Exception e) {
            log.error("发送SSL POST 请求出现异常！", e);
        }
        return result.toString();
    }

    private static class TrustAnyTrustManager implements X509TrustManager {
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
            //trust anything
        }

        public void checkServerTrusted(X509Certificate[] chain, String authType) {
            //trust anything
        }

        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }

    private static String getCookies(Map<String, String> cookies) {
        String strCookies = "";
        for (Map.Entry<String, String> entry : cookies.entrySet()) {
            strCookies = strCookies + entry.getKey() + "=" + entry.getValue() + ";";
        }
        return strCookies;
    }

    public static String jsonPost(String strURL, JsonObject body) {
        String result = null;
//        OutputStreamWriter out = null;
        InputStream is = null;
        try {
            URL url = new URL(strURL);// 创建连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            connection.setRequestProperty(CONNECTION, CONNECTION_VALUE);
            connection.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
            connection.setRequestProperty(ACCEPT_CHARSET, UTF8);
            connection.setRequestProperty("Accept", "application/json"); // 设置接收数据的格式
            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式

            connection.setRequestProperty("Authorization", "Basic cHRtb2JpbGV0ZXN0OlNhbmt1YWl0ZXN0OTk5");
            //转换为字节数组
            byte[] bodyData = (body.toString()).getBytes();
            // 设置文件长度
            connection.setRequestProperty("Content-Length", String.valueOf(bodyData.length));

            connection.connect();
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream(), "UTF-8"));
            writer.write(body.toString());
            writer.close();

            // 读取响应
            is = connection.getInputStream();
            int length = (int) connection.getContentLength();// 获取长度
            if (length != -1) {
                byte[] data = new byte[length];
                byte[] temp = new byte[512];
                int readLen = 0;
                int destPos = 0;
                while ((readLen = is.read(temp)) > 0) {
                    System.arraycopy(temp, 0, data, destPos, readLen);
                    destPos += readLen;
                }
                result = new String(data, "UTF-8"); // utf-8编码
                System.out.println("主机返回:" + result);
            }

        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("IO");
            return null;

        } finally {
            return result;
        }
    }

    // HTTP POST请求
    static JSONObject sendPostJson(String url, JSONObject jsonObject) throws Exception {
        URL obj = new URL(url);
        HttpsURLConnection con = (HttpsURLConnection) obj.openConnection();
        DefaultHttpClient client = new DefaultHttpClient();
        HttpPost post = new HttpPost(url);
        JSONObject response = null;
        try {
            StringEntity s = new StringEntity(jsonObject.toString());
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            post.setEntity(s);
            HttpResponse res = client.execute(post);
            if (res.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                HttpEntity entity = res.getEntity();
                String result = EntityUtils.toString(res.getEntity());// 返回json格式：
                response = JSONObject.fromObject(result);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return response;

    }


    public static String jsonPostForMock(String rule, String data) throws IOException {
        OkHttpClient client = new OkHttpClient();

        MediaType mediaType = MediaType.parse("multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
        String payload = "------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"rule\"\r\n\r\n" + rule + "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"group\"\r\n\r\n1336\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"forceAdd\"\r\n\r\ntrue\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"desc\"\r\n\r\n测试\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"userDomain\"\r\n\r\nptmobiletest\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"response\"\r\n\r\n" + data + "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--";

        RequestBody body = RequestBody.create(mediaType, payload);
        Request request = new Request.Builder()
                .url("https://appmock.sankuai.com/appmockapi/mock/addMockConfig.api")
                .post(body)
                .addHeader("content-type", "multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW")
                .addHeader("Content-Type", "application/json")
                .addHeader("cache-control", "no-cache")
                .addHeader("Postman-Token", "7013f4e0-118c-43e3-9fa7-48abd27b24c5")
                .build();

        Response response = client.newCall(request).execute();
        return response.body().string();
    }

    public static String jsonPostForMockCodeStatus(String rule, int status) throws IOException {
        OkHttpClient client = new OkHttpClient();

        MediaType mediaType = MediaType.parse("multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
        String payload = "------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"rule\"\r\n\r\n" + rule + "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"group\"\r\n\r\n1336\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"forceAdd\"\r\n\r\ntrue\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"desc\"\r\n\r\n测试\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"userDomain\"\r\n\r\nptmobiletest\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW\r\nContent-Disposition: form-data; name=\"statusCode\"\r\n\r\n" + status + "\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--";

        RequestBody body = RequestBody.create(mediaType, payload);
        Request request = new Request.Builder()
                .url("https://appmock.sankuai.com/appmockapi/mock/addMockConfig.api")
                .post(body)
                .addHeader("content-type", "multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW")
                .addHeader("Content-Type", "application/json")
                .addHeader("cache-control", "no-cache")
                .addHeader("Postman-Token", "7013f4e0-118c-43e3-9fa7-48abd27b24c5")
                .build();

        Response response = client.newCall(request).execute();
        return response.body().string();
    }

    public static String vpostbyToken(String URL, String params, String token) throws IOException {
        StringBuilder result = new StringBuilder();
        String urlNameString = URL + "?" + params;
        URL realUrl = new URL(urlNameString);
        URLConnection conn = realUrl.openConnection();
        conn.setDoInput(true);
        conn.setDoOutput(true);
        conn.setRequestProperty(CONTENTTYPE, UTF8);
        conn.setRequestProperty(ACCEPT_CHARSET, UTF8);
        conn.setRequestProperty(USER_AGENT, USER_AGENT_VALUE);
        conn.setRequestProperty(CONNECTION, CONNECTION_VALUE);
        conn.setRequestProperty(ACCEPT, "*/*");
        conn.setRequestProperty("Authorization", token);
        try (PrintWriter out = new PrintWriter(conn.getOutputStream()); BufferedReader in = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            if (in != null) {
                String line;
                while ((line = in.readLine()) != null) {
                    result.append(line);
                }
                out.flush();
                out.print(params);
            }
        } catch (Exception e) {
            log.error("发送 POST 请求出现异常！", e);
        }
        return result.toString();
    }

    public static String jsonPost(String strURL, JsonObject body, JsonObject header) {
        String result = null;
//        OutputStreamWriter out = null;
        InputStream is = null;
        try {
            URL url = new URL(strURL);// 创建连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);
            connection.setRequestProperty("USERNAME", header.get("USERNAME").getAsString());
            connection.setRequestProperty("Accept", "application/json"); // 设置接收数据的格式
            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式

            connection.setRequestProperty("Authorization", header.get("Authorization").getAsString());
            //转换为字节数组
            byte[] bodyData = (body.toString()).getBytes();
            // 设置文件长度
            connection.setRequestProperty("Content-Length", String.valueOf(bodyData.length));

            connection.connect();
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream(), "UTF-8"));
            writer.write(body.toString());
            writer.close();

            // 读取响应
            is = connection.getInputStream();
            int length = (int) connection.getContentLength();// 获取长度
            if (length != -1) {
                byte[] data = new byte[length];
                byte[] temp = new byte[512];
                int readLen = 0;
                int destPos = 0;
                while ((readLen = is.read(temp)) > 0) {
                    System.arraycopy(temp, 0, data, destPos, readLen);
                    destPos += readLen;
                }
                result = new String(data, "UTF-8"); // utf-8编码
                logger.info("主机返回:" + result);
            }

        } catch (IOException e) {
            e.printStackTrace();

        } finally {
            return result;
        }
    }

    public static String toGMTString() {
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("E, dd MMM yyyy HH:mm:ss z", Locale.UK);
        df.setTimeZone(new java.util.SimpleTimeZone(0, "GMT"));
        return df.format(date);
    }


    public static String vGet(String url, String token) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpget = new HttpGet(url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(10000).setConnectTimeout(20000)
                .setConnectionRequestTimeout(10000).build();
        httpget.setConfig(requestConfig);
        String context = StringUtils.EMPTY;

        // 设置回调接口接收的消息头
        httpget.addHeader("Content-Type", "application/json");
        httpget.addHeader("Authorization", token);
        httpget.addHeader("Date", toGMTString());
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpget);
            HttpEntity entity = response.getEntity();
            context = EntityUtils.toString(entity, HTTP.UTF_8);
        } catch (Exception e) {
            e.getStackTrace();
        } finally {
            try {
                response.close();
                httpget.abort();
                httpClient.close();
            } catch (Exception e) {
                e.getStackTrace();
            }
        }
        return context;
    }

    public static void downloadPicture(String urlList, String path) throws IOException {
        URL url = null;
        DataInputStream dataInputStream = new DataInputStream(url.openStream());
        FileOutputStream fileOutputStream = new FileOutputStream(new File(path));
        try {
            url = new URL(urlList);
            dataInputStream = new DataInputStream(url.openStream());
            ByteArrayOutputStream output = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = dataInputStream.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }
            fileOutputStream.write(output.toByteArray());
            dataInputStream.close();
            fileOutputStream.close();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            dataInputStream.close();
            fileOutputStream.close();
        }
    }

    /**
     * Post请求 (HttpClient4.5.x)
     *
     * @param url      url
     * @param params   query string，use NameValuePair for better compatibility. eg. a=1&a=2
     * @param headers  http headers
     * @param jsonBody FastJson Object
     * @return FastJson Object
     */
    public static JsonElement post(String url, Map<String, String> headers,
                                   List<NameValuePair> params,
                                   JsonObject jsonBody) {

        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        JsonElement results = JsonNull.INSTANCE;

        try {
            if (params != null && params.size() > 0) {
                url += "?" + EntityUtils.toString(new UrlEncodedFormEntity(params, Consts.UTF_8));
            }
            URIBuilder uriBuilder = new URIBuilder(url);

            HttpPost httpPost = new HttpPost(uriBuilder.build());
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            if (jsonBody != null) {
                StringEntity entity = new StringEntity(jsonBody.toString(),
                        ContentType.APPLICATION_JSON);
                httpPost.setEntity(entity);
            }

            logger.info("Request URL: " + httpPost.getURI().toString());
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == HttpStatus.SC_OK) {
                logger.info("Successful. HTTP Status Code: " + statusCode);
                HttpEntity entity = response.getEntity();
                results = new JsonParser().parse(EntityUtils.toString(entity));
                EntityUtils.consume(entity);
            } else {
                logger.warn("Failed. HTTP Status Code: " + statusCode);
            }

        } catch (URISyntaxException e) {
            logger.error("URISyntaxException(URL: " + url + ")", e);
        } catch (IOException e) {
            logger.error("IOException(URL: " + url + ")", e);
        } finally {
            closeResponse(response);
        }
        return results;
    }

    private static void closeResponse(CloseableHttpResponse response) {
        if (response != null) {
            try {
                response.close();
            } catch (IOException e) {
                logger.error("Failed to close the closeable response!!!", e);
            }
        }
    }

    public static Resp httpPost(String url, RequestBody body, Map<String, String> headers, String auth, Map<String, String> cookies) {
        //Cookie
        Request.Builder builder = new Request.Builder();
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        String cookieValue = "";
        if (cookies != null) {
            for (String cookie : cookies.keySet()) {
                cookieValue += cookie + "=" + cookies.get(cookie) + ";";
            }
            builder.addHeader("Cookie", cookieValue);

        }

        //auth
        if (auth != null) {
            builder.addHeader("Authorization", auth);

        }

        if (body == null) {
            body = RequestBody.create(null, "");
        }

        Request request = builder
                .url(url)
                .post(body)
                .build();

        Resp response = executeRemoteCall(request);
        return response;
    }

    private static Resp executeRemoteCall(Request request) {
        Response response = null;

        try {
            response = CLIENT.newCall(request).execute();
            if (response.code() == 200 || response.code() == 201) {
                String body = response.body().string();
                return Resp.success(body);
            } else {
                int code = response.code();
                String message = response.message();
                String body = (response.body() == null) ? "" : response.body().string();
                String errMsg = String.format("Exception occurs during request url: %s , httpCode: %s, httpMsg: %s, httpResponse: %s", request.url(), code, message, body);
//                LOGGER.error(errMsg);
                throw new RuntimeException(errMsg);
            }
        } catch (Exception e) {
            String errMsg = String.format("Exception occurs during request url: %s , cause reason: %s, exception message: %s", request.url(), e.getCause(), e);
//            LOGGER.error(errMsg);
            throw new RuntimeException(errMsg);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    /**
     * 最全get请求
     *
     * @param url
     * @param headers
     * @return
     */
    public static Resp httpGet(String url, Map<String, String> params, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder();
        StringBuffer paramStr = new StringBuffer();

        try {
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    builder.addHeader(entry.getKey(), entry.getValue());
                }
            }

            if (params != null) {//构建请求参数
                paramStr.append("?");
                for (String key : params.keySet()) {
                    if (params.get(key) != null) {
                        paramStr.append(key + "=" + URLEncoder.encode(params.get(key), "utf-8") + "&");
                    }
                }
                paramStr.deleteCharAt(paramStr.length() - 1);//删掉最后一个&

                url = url.concat(paramStr.toString());
            }

        } catch (Exception e) {
        }

        Request request = builder
                .url(url)
                .build();

        Resp response = executeRemoteCall(request);
        return response;
    }


}
