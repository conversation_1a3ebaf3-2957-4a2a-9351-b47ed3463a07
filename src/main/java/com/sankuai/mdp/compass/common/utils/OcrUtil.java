
package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import org.apache.commons.collections4.SetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * Created by skt on 2020/9/28.
 */
public class OcrUtil {

    static String url = " http://localhost:9092/vision/text";
    static String merge_url = "http://localhost:9092/vision/merge";
    private static final Logger logger = LoggerFactory.getLogger(OcrUtil.class);

    private static final Pattern p_html = Pattern.compile("<[a-zA-z]{1,9}((?!>).)*>", Pattern.CASE_INSENSITIVE);

    private static final Pattern t_html = Pattern.compile("</[a-zA-z]{1,9}>", Pattern.CASE_INSENSITIVE);

    /**
     *  富文本的适配，转换为文本格式
     * @param html
     * @return
     */
    public static String getTextByHtml(String html) {
        Matcher m_script = p_html.matcher(html);
        html = m_script.replaceAll("");
        Matcher l_script = t_html.matcher(html);
        return l_script.replaceAll("");
    }

    public static String unicodeDecode(String string) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(string);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            string = string.replace(matcher.group(1), ch + "");
        }
        return string;
    }

    /**
     * 部署在compass服务器的ocr模型
     * @param PicUrl
     * @return 拿到的文字数组
     */
    public JsonArray getText(String PicUrl){
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("image",PicUrl);
        String result = "";
        try {
            result = unicodeDecode(new HttpUtil().jsonPost(url,jsonObject));
        }catch (Exception e){
            return null;
        }

        JsonObject allText = new JsonParser().parse(result).getAsJsonObject();
        JsonArray jsonArrayAll = allText.get("data").getAsJsonObject().getAsJsonArray("roi_text");
        JsonArray jsonArray = new JsonArray();
        for(int i = 0; i < jsonArrayAll.size(); i++){
            String text = jsonArrayAll.get(i).getAsJsonObject().get("text").getAsString();
            jsonArray.add(text);
        }
        return jsonArray;
    }

    public String get0crMessage(String PicUrl){
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("image",PicUrl);
        String result = "";
        try {
            result = unicodeDecode(new HttpUtil().jsonPost(url,jsonObject));
        }catch (Exception e){
            return null;
        }
        return result;
    }

    public void mergePic(ArrayList<String> list,String path){
        JsonObject jsonObject = new JsonObject();
        JsonArray jsonArray = new JsonArray();
        for (String url : list) {
            jsonArray.add(url);
        }
        jsonObject.add("image_list",jsonArray);
        jsonObject.addProperty("name",path);
        new HttpUtil().jsonPost(merge_url,jsonObject);
    }
    /**
     *
     * @param mockText testSDK想要校验的
     * @param textArray ocr拿到的
     * @return
     */
    public boolean ExistTest(String mockText,JsonArray textArray,String mockId){
        //JsonArray textArray = getText(url);
        String mockTextTemp = mockText;
        mockText = getTextByHtml(mockTextTemp);

        if(mockText.contains("¥")||mockText.contains("￥")){
            //// TODO: 2021/5/18 双端不一致，暂时屏蔽掉￥这种字符
            return true;
        }
        for(int i = 0; i < textArray.size(); i++){
            String text = textArray.get(i).getAsString();
            if(jaccard(mockText,text)>0.01){
//                logger.info(mockId+"文字缺失"+"mockText:"+mockText+" text:"+text+"匹配IOU\n");
                return true;
            }
        }
        return false;
    }



    /**
     * 用来计算文字之间的相似度
     * @param a
     * @param b
     * @return
     */
    public static float jaccard(String a, String b) {
        if (a == null && b == null) {
            return 1f;
        }
        // 都为空相似度为 1
        if (a == null || b == null) {
            return 0f;
        }
        Set<Integer> aChar = a.chars().boxed().collect(Collectors.toSet());
        Set<Integer> bChar = b.chars().boxed().collect(Collectors.toSet());
        // 交集数量
        int intersection = SetUtils.intersection(aChar, bChar).size();
        if (intersection == 0) return 0;
        // 并集数量
        int union = SetUtils.union(aChar, bChar).size();
        return ((float) intersection) / (float)union;
    }
//    public static void main(String[] args) {
//        OcrUtil ocrUtil = new OcrUtil();
////        JsonArray jsonArray = ocrUtil.getText("234312.png");
//////
//
////        System.out.println(ocrUtil.ExistTest("北京",jsonArray));
////        String url = "http://p0.meituan.net/ptautotest/7ba9cb2684b34c2cd768937a7974f898473627.png";
////        HttpUtil.downloadPicture(url,"/Users/<USER>/Documents/new/test.png");
////        String url = "http://p0.meituan.net/ptautotest/2a16c3ce0de21661ef35db8fdbe6d62c499620.png";
//
//        String url = "http://p0.meituan.net/ptautotest/ca37a3f428c09c7d4fb0f4dd158d23cc409096.png";
//        JsonArray jsonArray =  ocrUtil.getText(url);
//        System.out.println(jsonArray);
//        System.out.println("1111");
//
//        for(int i = 0; i < jsonArray.size(); i++){
//            String text = jsonArray.get(i).getAsJsonObject().get("text").toString();
//            JsonArray pos = jsonArray.get(i).getAsJsonObject().get("pos").getAsJsonArray();
//            int x = pos.get(0).getAsInt();
//            int y = pos.get(1).getAsInt();
//            System.out.println("中心横坐标："+x+" 中心纵坐标："+y+" 展示文字为："+text);
//        }
//    }

}
