package com.sankuai.mdp.compass.common.utils;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

public class GsonTypeAdapterFactory implements TypeAdapterFactory {

    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
        final TypeAdapter<T> adapter = gson.getDelegateAdapter(this, type);
        return new TypeAdapter<T>() {
            @Override
            public void write(JsonWriter out, T value) throws IOException {
                adapter.write(out, value);
            }

            @Override
            public T read(JsonReader reader) throws IOException {
                try {
                    return adapter.read(reader);
                } catch (Throwable e) {
//                    e.printStackTrace();
                    if (reader.hasNext()) {
                        JsonToken peek = reader.peek();
                        if (peek == JsonToken.NAME) {
                            reader.nextName();
                        } else {
                            reader.skipValue();
                        }
                    }
                    return null;
                }
            }
        };
    }

}
