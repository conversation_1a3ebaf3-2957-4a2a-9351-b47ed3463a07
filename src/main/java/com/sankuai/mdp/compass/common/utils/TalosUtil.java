package com.sankuai.mdp.compass.common.utils;

/**
 * Created by xieyongrui on 2020/2/18.
 */

import com.meituan.talos.commons.domain.Column;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.Talos;
import com.sankuai.data.talos.exception.TalosException;
import com.sankuai.data.talos.model.Engine;
import com.sankuai.data.talos.model.QueryInfo;
import com.sankuai.data.talos.model.QueryResult;
import com.sankuai.mdp.compass.conan.entity.MgeJob;
import com.sankuai.mdp.compass.conan.mapper.MgeJobMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class TalosUtil {
    private static final Logger logger = LoggerFactory.getLogger(TalosUtil.class);
    private static String username = "talos_ptqa_dashboard";
    //QA环境域名，如果使用QA环境，参考https://km.sankuai.com/page/70389098，把依赖的hive表数据从线上同步到QA环境
    //String endpoint= "talostwo.data.test.sankuai.com:8080";
    //线上环境域名
    String endpoint = "talostwo.data.sankuai.com:8080";
    private MgeJob mgeJob;
    @Autowired
    MgeJobMapper mgeJobMapper;

    @SneakyThrows
    public static Boolean submit(String sql, String fileName) {

        FileUtil fileUtil = new FileUtil();

        //endpoint不设置，默认提交到线上环境 (talostwo.data.sankuai.com:8080)
        AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, TokenUtil.getToken("talos_password"));

        /*
         * 提交到QA环境
         * AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, password, endpoint);
         */
        OutputStream out = null;
        try {
            //必须开启session
            asyncTalosClient.openSession();

            Engine engine = Engine.Hive;    //如果使用Presto引擎，Engine.Presto
            String dsn = "hdim";

            //提交SQL查询，返回查询ID
            //String qid = asyncTalosClient.submit(engine, dsn, sql);


            //不使用缓存，每次提交查询，都重新执行一遍
            String qid = asyncTalosClient.submit(engine, dsn, sql, false);


            //等待查询结束
//            int res = asyncTalosClient.waitForFinished(qid); //如果需要打印执行日志 waitForFinished(qid, true)
            int res = asyncTalosClient.waitForFinished(qid);

            if (res == 1) {
                //查询成功，获取查询结果。更多相关方法请参考 https://km.sankuai.com/page/111057708#id-3.6%E8%8E%B7%E5%8F%96%E6%9F%A5%E8%AF%A2%E7%BB%93%E6%9E%9C
                QueryResult queryResult = asyncTalosClient.getQueryResult(qid);
                InputStream dataStream = asyncTalosClient.download(qid);
                File file = new File("tmp.txt");

                out = new FileOutputStream(file);

                byte[] buffer = new byte[4096];
                int readLen = 0;
                while ((readLen = dataStream.read(buffer)) > 0) {
                    out.write(buffer, 0, readLen);
                }

                List<List<Object>> rows = fileUtil.readFileByLines("tmp.txt", "\\x01");

                List<Column> columns = queryResult.getColumns();
                List<Object> cols = new ArrayList<>();
                System.out.print(rows);
                for (int i = 0; i < columns.size(); i++) {
                    cols.add(columns.get(i).getName());
                }
                ExcelUtil excelUtil = new ExcelUtil();
                try {
                    excelUtil.createAExcel(cols, rows, fileName);
                } catch (Exception e) {
                    logger.error("TalosUtil", "Excel创建失败");
                }
                asyncTalosClient.closeSession();
                fileUtil.deleteFile(file);
            } else if (res == -1) {
                //查询失败，获取查询失败原因
                QueryInfo queryInfo = asyncTalosClient.getQueryInfo(qid);
                logger.error("TalosUtil", "Query failed, error: " + queryInfo.getMessage());
                logger.error("TalosUtil", "Query EngineLog:\n " + queryInfo.getEngineLog());
            } else {
                //查询任务尚未结束
                logger.error("TalosUtil", "Query is running.");
            }

            //关闭session
            asyncTalosClient.closeSession();
        } catch (TalosException e) {
            log.error("TalosException>>"+e.getMessage());
        } catch (FileNotFoundException e) {
            log.error("FileNotFoundException>>"+e.getMessage());
        } catch (IOException e) {
            log.error("IOException>>"+e.getMessage());
        }finally {
            out.close();
        }
        return false;
    }

    public String getQid(String sql) {
        AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, TokenUtil.getToken("talos_password"));
        try {
            //必须开启session
            asyncTalosClient.openSession();

            Engine engine = Engine.Hive;    //如果使用Presto引擎，Engine.Presto
            String dsn = "hdim";
            //不使用缓存，每次提交查询，都重新执行一遍
            String qid = asyncTalosClient.submit(engine, dsn, sql, false);
            asyncTalosClient.closeSession();
            return qid;
        } catch (TalosException e) {
            log.error("getQidErr>>"+e.getMessage());
            return null;
        }
    }

    @SneakyThrows
    public int waitfinished(String qid, String fileName) {
        FileUtil fileUtil = new FileUtil();
        AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, TokenUtil.getToken("talos_password"));

        /*
         * 提交到QA环境
         * AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, password, endpoint);
         */

        OutputStream out = null;
        try {
            //必须开启session
            asyncTalosClient.openSession();
            Engine engine = Engine.Hive;    //如果使用Presto引擎，Engine.Presto
            String dsn = "hdim";
            int res = asyncTalosClient.waitForFinished(qid);

            if (res == 1) {
                //查询成功，获取查询结果。更多相关方法请参考 https://km.sankuai.com/page/111057708#id-3.6%E8%8E%B7%E5%8F%96%E6%9F%A5%E8%AF%A2%E7%BB%93%E6%9E%9C
                QueryResult queryResult = asyncTalosClient.getQueryResult(qid);
                InputStream dataStream = asyncTalosClient.download(qid);
                File file = new File("tmp.txt");

                out = new FileOutputStream(file);

                byte[] buffer = new byte[4096];
                int readLen = 0;
                while ((readLen = dataStream.read(buffer)) > 0) {
                    out.write(buffer, 0, readLen);
                }
                List<List<Object>> rows = fileUtil.readFileByLines("tmp.txt", "\\x01");

                List<Column> columns = queryResult.getColumns();
                List<Object> cols = new ArrayList<>();
                logger.info(rows.toString());
                for (int i = 0; i < columns.size(); i++) {
                    cols.add(columns.get(i).getName());
                }
                ExcelUtil excelUtil = new ExcelUtil();
                try {
                    excelUtil.createAExcel(cols, rows, fileName);
                } catch (Exception e) {
                    logger.error("TalosUtil", "Excel创建失败");
                    e.printStackTrace();
                    res = 3;
                }
                asyncTalosClient.closeSession();
                fileUtil.deleteFile(file);
            } else if (res == -1) {
                //查询失败，获取查询失败原因
                QueryInfo queryInfo = asyncTalosClient.getQueryInfo(qid);
                String message = queryInfo.getMessage();
                String log = queryInfo.getEngineLog();
                logger.error("TalosUtil", "Query failed, error: " + message);
                logger.error("TalosUtil", "Query EngineLog:\n " + log);
            } else {
                //查询任务尚未结束
                logger.error("TalosUtil", "Query is running.");
            }
            //关闭session
            asyncTalosClient.closeSession();
            return res;
        } catch (TalosException e) {
            e.printStackTrace();
            return 5;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return 7;
        } catch (IOException e) {
            e.printStackTrace();
            return 9;
        } catch (Exception e) {
            e.printStackTrace();
            return -3;
        }finally {
            out.close();
        }
    }



    public static void download(String qid, String fileName) {
        try {
            FileUtil fileUtil = new FileUtil();

            AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, TokenUtil.getToken("talos_password"));
            asyncTalosClient.openSession();

            QueryResult queryResult = asyncTalosClient.getQueryResult(qid);
            InputStream dataStream = asyncTalosClient.download(qid);
            File file = new File("tmp.txt");

            OutputStream out = new FileOutputStream(file);

            byte[] buffer = new byte[4096];
            int readLen = 0;
            while ((readLen = dataStream.read(buffer)) > 0) {
                out.write(buffer, 0, readLen);
            }
            out.close();
            List<List<Object>> rows = fileUtil.readFileByLines("tmp.txt", "\\x01");

            List<Column> columns = queryResult.getColumns();
            List<Object> cols = new ArrayList<>();
//            System.out.print(rows);
            for (int i = 0; i < columns.size(); i++) {
                cols.add(columns.get(i).getName());
            }
            ExcelUtil excelUtil = new ExcelUtil();
            try {
                excelUtil.createAExcel(cols, rows, fileName);
            } catch (Exception e) {
                logger.error("TalosUtil", "Excel创建失败");
            }
            asyncTalosClient.closeSession();
            fileUtil.deleteFile(file);
        } catch (Exception e) {
            logger.error("err", e);
        }
    }
    @SneakyThrows
    public static int downloadForMge(String qid, String fileName) {
        int res=1;
        OutputStream out = null;
        try {
            FileUtil fileUtil = new FileUtil();

            AsyncTalosClient asyncTalosClient = new AsyncTalosClient(username, TokenUtil.getToken("talos_password"));
            asyncTalosClient.openSession(new Talos.RequestOption(60,60,60, TimeUnit.SECONDS));

            QueryResult queryResult = asyncTalosClient.getQueryResult(qid);
            InputStream dataStream = asyncTalosClient.download(qid);
            File file = new File("tmp.txt");

            out = new FileOutputStream(file);

            byte[] buffer = new byte[4096];
            int readLen = 0;
            while ((readLen = dataStream.read(buffer)) > 0) {
                out.write(buffer, 0, readLen);
            }

            List<List<Object>> rows = fileUtil.readFileByLines("tmp.txt", "\\x01");

            List<Column> columns = queryResult.getColumns();
            List<Object> cols = new ArrayList<>();
            System.out.print(rows);
            for (int i = 0; i < columns.size(); i++) {
                cols.add(columns.get(i).getName());
            }
            ExcelUtil excelUtil = new ExcelUtil();
            try {
                excelUtil.createAExcel(cols, rows, fileName);
            } catch (Exception e) {
                logger.error("TalosUtil", "Excel创建失败");
                res=3;
            }
            asyncTalosClient.closeSession();
            fileUtil.deleteFile(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            res=7;
        } catch (IOException e) {
            e.printStackTrace();
            res=9;
        } catch (TalosException e) {
            e.printStackTrace();
            res=5;
        }finally {
            out.close();
        }
        return res;

    }
}
