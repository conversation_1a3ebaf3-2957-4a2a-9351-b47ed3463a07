package com.sankuai.mdp.compass.common.enums;

public enum ResultEnum {
    /*
     * 错误信息
     * */
    Success("0000","ok");

    private String code;

    private String msg;

    ResultEnum() {
    }

    ResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
