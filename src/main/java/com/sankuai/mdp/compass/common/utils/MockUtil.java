package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.Option;

import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.entity.ChangeComplianceTemplate;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.scheduling.annotation.Async;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import static com.jayway.jsonpath.JsonPath.using;

/**
 * Created by xieyongrui on 2019/11/18.
 */
@Slf4j
public class MockUtil {

    private static final String DOMAIN = "https://appmock.sankuai.com";

    private static final String GET_MOCK_DATA_URL = "/appmockapi/mock/getMockConfigByMockId.api";

    private static final String UPDATE_OR_ADD_MOCK_DATA_URL = "/appmockapi/mock/addMockConfig.api";

    private static final String updateMockStatus = "/appmockapi/mock/updateMockStatus.api";


    public static String get(Integer mockId) {
        Configuration configuration = Configuration.builder().options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        String response = "";
        try {
            String url = DOMAIN + GET_MOCK_DATA_URL + "?mockId=" + mockId;
            String result = HttpUtil.vGet(url);
            DocumentContext documentContext = using(configuration).parse(result);
            if (null != result) {
                response = documentContext.read("$.data.response").toString();
            } else {

            }
        } catch (Exception e) {
            System.out.print(e);
        } finally {
            return response;
        }
    }

    public static JsonObject create(String api, JsonObject data) {
        JsonObject result = null;
        // 本地调试将环境取反
        if (EnvUtil.isOnline()) {
            try {
                String response = HttpUtil.jsonPostForMock(api, data.toString());
                result = new JsonParser().parse(response).getAsJsonObject();
                log.info("create mock return:>>>>>" + result);
//            FileUtil.delete(MOCK_DATA_PATH);
            } catch (Exception e) {
                log.error("create mock error:>>>>>" + e.getMessage());
            }
        }
        return result;
    }


    public static JsonObject create(String api, int statusCode) {
        JsonObject result = null;

//        System.out.println(data);
        // 本地调试将环境取反
        if (EnvUtil.isOnline()) {
            try {
                String response = HttpUtil.jsonPostForMockCodeStatus(api, statusCode);
                result = new JsonParser().parse(response).getAsJsonObject();
                log.info("create mock return:>>>>>" + result);
//            FileUtil.delete(MOCK_DATA_PATH);
            } catch (Exception e) {
                log.error("create mock error:>>>>>" + e.getMessage());
            }
        }
        return result;
    }

    public static Resp updateMockStatus(int mockId, String misId, int status) {
        Resp resp = new Resp<>();
        String url = DOMAIN + updateMockStatus + "?mockId=" + mockId + "&userDomain=" + misId + "&status=" + status;
        String result = HttpUtil.vGet(url);
        JsonObject o = new JsonParser().parse(result).getAsJsonObject();
        resp.setMsg(o.toString());
        return resp;
    }


    public static void main(String[] argv) {
//        String api ="/aggroup/homepage/display";
//        JsonObject result =  create(api,500);
//        System.out.println(result);

        System.out.println(get(158265));

    }
}
