package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import static com.sankuai.mdp.compass.common.domain.ComConstant.USER_NAME;

/**
 * Created by xieyongrui on 2019/11/16.
 */
public class EvaUtil {
    public static final String LIST_API = "http://appupdate.sankuai.com/dynamiclayout/list?";
    public static final String MGE_API = "http://appupdate.sankuai.com/dynamiclayout/mgeconfiguration/init?";
    public static final String TEMPLATE_ZIP_API = "http://entryop.wpt.st.sankuai.com/api/layout/getTemplates?";


    public JsonObject getModule() {

        String result;
        String url = LIST_API + "uname="+USER_NAME;

        result = HttpUtil.vGet(url).toString();
        return new JsonParser().parse(result).getAsJsonObject();
    }

    public JsonObject getTemplate() {

        String result;
        String url = LIST_API + "uname="+USER_NAME;

        result = HttpUtil.vGet(url).toString();
        return new JsonParser().parse(result).getAsJsonObject();
    }

    public Integer getId(String name) {
        Integer id = -1;
        String url = LIST_API + "uname="+USER_NAME;
        try {
            JsonObject jsonObject = new JsonParser().parse(HttpUtil.vGet(url)).getAsJsonObject();
            JsonArray jsonArray = jsonObject.getAsJsonArray("data");
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject itemJson = jsonArray.get(i).getAsJsonObject();
                if (itemJson.get("nameEn").getAsString().equals(name)) {
                    id = itemJson.get("styleSetId").getAsInt();
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return id;
        }
    }

    public JsonObject getMgeConfiguration(Integer id) {
        String url = MGE_API + "styleSetId="+id+"&uname="+USER_NAME;
        JsonObject result = new JsonObject();
        try {
            JsonObject jsonObject = new JsonParser().parse(HttpUtil.vGet(url).toString()).getAsJsonObject();
            JsonObject data = jsonObject.get("data").getAsJsonObject();
            if (data.has("mgeCfgList")) {
                result.add("mgeCfgList", data.get("mgeCfgList").getAsJsonArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return result;
        }
    }

    public JsonObject getTemplateZipApi(String business) {
        String url = TEMPLATE_ZIP_API + "group="+business+"&uname="+USER_NAME;
        String result = HttpUtil.vGet(url);
        JsonObject json = new JsonParser().parse(result).getAsJsonObject();
        JsonObject list = new JsonObject();
        try{
            JsonArray templateList = json.getAsJsonObject("data").getAsJsonArray("templates");
            for (int i = 0; i < templateList.size(); i++) {
                JsonObject item = templateList.get(i).getAsJsonObject();
                String templateName = item.get("areaName").getAsString();
                String version = item.get("versionLimit").getAsString();
                String zipUrl = item.get("url").getAsString();

                if (!list.has(templateName)) {
                    list.add(templateName,new JsonObject());
                }
                list.getAsJsonObject(templateName).addProperty("version",version);
                list.getAsJsonObject(templateName).addProperty("url",zipUrl);


            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return list;
        }
    }

}
