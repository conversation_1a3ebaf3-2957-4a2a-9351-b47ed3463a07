package com.sankuai.mdp.compass.common.utils;

import lombok.SneakyThrows;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import com.sankuai.security.sdk.SecSdk;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by xieyongrui on 2019/11/22.
 */
@Slf4j
public class FileUtil {
    public static void main(String[] args) {
        String output = "./output";
        File file = new File(output);
        file.delete(); //删除空文件夹
//        Boolean res = downloadAndReadFile("./output/aa",  "./output", "bb");
    }

    /**
     * 远程zip文件下载
     *
     * @param filePath zip文件下载链接
     * @param dir      文件存储路径
     */
    @SneakyThrows
    public static void downloadAndReadFileNew(String filePath, String dir, String saveName) {
        if (!SecSdk.isSecurityFilePath(filePath) || !SecSdk.isSecurityFilePath(dir)) {
            log.error("文件路径非法！");
            //return;
        }
        File savePath = new File(dir);
        if (!savePath.exists()) {
            savePath.mkdir();
        }
        OutputStream outputStream = null;
        FileInputStream fs = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        try {
            String[] dirName = filePath.split("/");
            String name = dirName[dirName.length - 1];

            File file = new File(savePath + "/" + name);

            if(!file.exists()) {
                file.createNewFile();
            }

            outputStream = Files.newOutputStream(file.toPath());

            URL url = new URL(filePath);

            HttpURLConnection uc = (HttpURLConnection) url.openConnection();

            //设置是否要从 URL 连接读取数据,默认为true
            uc.setDoInput(true);
            uc.connect();
            InputStream inputStream = uc.getInputStream();
            log.info("file size is:" + uc.getContentLength());

            byte[] buffer = new byte[4 * 1024];
            int byteRead = -1;
            while((byteRead = (inputStream.read(buffer))) != -1) {
                outputStream.write(buffer, 0, byteRead);
            }
            outputStream.flush();
            inputStream.close();

            //读取文件
            StringBuilder strb = new StringBuilder();
            fs = new FileInputStream(new File(savePath + "/" + name));
            isr = new InputStreamReader(fs, StandardCharsets.UTF_8);
            br = new BufferedReader(isr);
            String data = "";
            while((data = br.readLine())  != null){
                strb.append(data).append("\n");
            }

            unZipFiles(dir + "/" + name, dir + "/" + saveName + "/");

        } catch (Exception e) {
            log.error("下载或读取文件失败，URL：" + filePath + "，保存路径：" + dir + "，文件名：" + saveName, e);
        }finally {
            if (outputStream != null) {
                outputStream.close();
            }
            if (br != null) {
                br.close();
            }
            if (fs != null) {
                fs.close();
            }
            if (isr != null) {
                isr.close();
            }
        }
    }
    @SneakyThrows
    public static Boolean downloadAndReadFile(String filePath, String dir, String saveName) {
        if (!SecSdk.isSecurityFilePath(filePath) || !SecSdk.isSecurityFilePath(dir)) {
            System.out.println("文件路径非法！");
//            return false;
        }
        File savePath = new File(dir);
        if (!savePath.exists()) {
            savePath.mkdir();
        }
        OutputStream outputStream = null;
        FileInputStream fs = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        try {
            String[] dirName = filePath.split("/");
            String name = dirName[dirName.length - 1];

            File file = new File(savePath + "/" + name);

            if(file != null && !file.exists()) {
                file.createNewFile();
            }

            outputStream = new FileOutputStream(file);

            URL url = new URL(filePath);

            HttpURLConnection uc = (HttpURLConnection) url.openConnection();

            //设置是否要从 URL 连接读取数据,默认为true
            uc.setDoInput(true);
            uc.connect();
            InputStream inputStream = uc.getInputStream();
            System.out.println("file size is:" + uc.getContentLength());

            byte[] buffer = new byte[4 * 1024];
            int byteRead = -1;
            while((byteRead = (inputStream.read(buffer))) != -1) {
                outputStream.write(buffer, 0, byteRead);
            }
            outputStream.flush();
            inputStream.close();

            //读取文件
            StringBuffer strb = new StringBuffer();
            fs = new FileInputStream(new File(savePath + "/" + name));
            isr = new InputStreamReader(fs,"UTF-8");
            br = new BufferedReader(isr);
            String data = "";
            while((data = br.readLine())  != null){
                strb.append(data + "\n");
            }

            return unZipFiles(dir + "/" + name, dir + "/" + saveName + "/");

        } catch (Exception e) {
            System.out.println("读取失败！");
            e.printStackTrace();
            return false;
        }finally {
            outputStream.close();
            br.close();
            fs.close();
            isr.close();
        }
    }


    /**
     * 解压到指定目录
     *
     * @param zipPath
     * @param descDir
     */
    public static Boolean unZipFiles(String zipPath, String descDir) throws IOException{
        if (!SecSdk.isSecurityFilePath(zipPath) || !SecSdk.isSecurityFilePath(descDir)) {
            System.out.println("文件路径非法！");
//            return false;
        }

        return unZipFiles(new File(zipPath), descDir);

    }

    /**
     * 解压文件到指定目录且删除zip文件
     *
     * @param zipFile
     * @param descDir
     */
    @SuppressWarnings("rawtypes")
    public static Boolean unZipFiles(File zipFile, String descDir) throws IOException{
        if (!SecSdk.isSecurityFilePath(descDir)) {
            System.out.println("文件路径非法！");
//            return false;
        }
        File pathFile = new File(descDir);
        if(!pathFile.exists()){
            pathFile.mkdirs();
        }
        ZipFile zip = new ZipFile(zipFile);
        OutputStream out = null;
        try{

            for(Enumeration entries = zip.entries(); entries.hasMoreElements();) {
                ZipEntry entry = (ZipEntry)entries.nextElement();
                String zipEntryName = entry.getName();
                InputStream in = zip.getInputStream(entry);
                String outPath = (descDir+zipEntryName).replaceAll("\\*", "/");;
                File file = new File(outPath.substring(0, outPath.lastIndexOf('/')));
                if(!file.exists()){
                    file.mkdirs();
                }

                //判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
                if(new File(outPath).isDirectory()){
                    continue;
                }

                //输出文件路径信息
                System.out.println(outPath);

                out = new FileOutputStream(outPath);
                byte[] buf1 = new byte[1024];
                int len;
                while((len = in.read(buf1)) > 0){
                    out.write(buf1,0,len);
                }
                in.close();
                out.close();
            }
            deleteFile(zipFile);
            System.out.println("******************解压完毕********************");
        }catch (Exception e){
            return false;
        }finally {
            zip.close();
            out.close();
        }
        return true;

    }

    /**
     * 下载文件url图片
     */


    /**
     * 删除文件
     *
     * @param file
     */
    public static void deleteFile(File file) {
        if (file.exists()) {
            if (file.isFile()) {
                file.delete();
            } else {
                for (File f : file.listFiles()) {
                    deleteFile(f);
                }
            }
        }
    }

    /**
     * 删除文件夹
     *
     * @param fileName
     */
    public static void delete(String fileName) {
        File file = new File(fileName);
        deleteFile(file);
    }

    /**
     * 读取文件
     *
     * @param path
     * @return
     */
    @SneakyThrows
    public static String read(String path) {
        String result = null;
        InputStreamReader reader = null;
        BufferedReader br = null;

        File filename = new File(path);
        try {
            reader = new InputStreamReader(new FileInputStream(filename));
            br = new BufferedReader(reader);
            result = br.readLine();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.print(e);
        } finally {
            reader.close();
            return result;
        }
    }

    /**
     * 读取文件所有行
     *
     * @param path
     * @return 所有行拼一个字符串返回
     */
    public static String readAll(String path) {
        String result = "";
        File file = new File(path);
        BufferedReader reader = null;
        String line = "";
        if (!file.exists()) {
            System.out.println(path + "文件不存在");
            return result;
        }
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                line = tempString;
                result += line;
            }
            reader.close();
        } catch (Throwable e) {
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Throwable e1) {
                } finally {
                }
            }
            return result;
        }
    }

    /**
     * 获取指定文件夹下所有文件名称
     *
     * @param path
     * @param fileNameList
     */
    public static void getFileList(String path, String dirName, ArrayList<String> fileNameList){
        File file = new File(path);
        if (file.exists()){
            File[] fileList = file.listFiles();
            for(int i = 0; i < fileList.length; i++){
                if(fileList[i].isFile()){
                    fileNameList.add(dirName+fileList[i].getName());

                }else if(fileList[i].isDirectory()){
                    String dirPath = fileList[i].getPath();
                    String[] dirNameList = dirPath.split("/");
                    getFileList(dirPath,dirName+dirNameList[dirNameList.length-1]+"/",fileNameList);
                }
            }
        }
    }

    /**
     * 删除文件夹
     *
     * @param folderPath
     */
    public static void delFolder(String folderPath) {
        try {
            delAllFile(folderPath); //删除完里面所有内容
            String filePath = folderPath;
            filePath = filePath.toString();
            File myFilePath = new File(filePath);
            myFilePath.delete(); //删除空文件夹
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除path路径下的所有文件
     *
     * @param path
     * @return
     */
    public static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        }
        if (!file.isDirectory()) {
            return flag;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {
            if (path.endsWith(File.separator)) {
                temp = new File(path + tempList[i]);
            } else {
                temp = new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                temp.delete();
            }
            if (temp.isDirectory()) {
                delAllFile(path + "/" + tempList[i]);//先删除文件夹里面的文件
                delFolder(path + "/" + tempList[i]);//再删除空文件夹
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 按行读取文件
     *
     * @param fileName
     * @return
     */
    public static List<String> readFileByLines(String fileName) {
        List<String> list = new ArrayList();
        File file = new File(fileName);
        BufferedReader reader = null;
        String line = "";
        if (!file.exists()) {
            return list;
        }
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                line = tempString;
                list.add(line);
            }
            reader.close();
        } catch (Throwable e) {
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Throwable e1) {
                } finally {}
            }
            return list;
        }
    }

    /**
     * 按行读取文件
     *
     * @param fileName
     * @return
     */
    public static String linesToStr(String fileName) {
        String result = "";
        File file = new File(fileName);
        BufferedReader reader = null;
        String line = "";
        if (!file.exists()) {
            return result;
        }
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                line = tempString;
                result += line;
            }
            reader.close();
        } catch (Throwable e) {
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Throwable e1) {
                } finally {}
            }
            return result;
        }
    }

    /**
     * 按行读取文件,一般用作txt转xls
     *
     * @param txtFileName
     * @return
     */
    public static List<List<Object>> readFileByLines(String txtFileName, String regex) {
        List<List<Object>> list = new ArrayList();
        File file = new File(txtFileName);
        BufferedReader reader = null;
        String line = "";
        if (!file.exists()) {
            return list;
        }
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                line = tempString;
                String[] arr = line.split(regex);
                List<Object> tempList = new ArrayList<>();
                for (int i = 0; i < arr.length; i++) {
                    tempList.add(arr[i]);
                }
                list.add(tempList);
            }
            reader.close();
        } catch (Throwable e) {
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Throwable e1) {
                } finally {}
            }
            return list;
        }
    }

    @SneakyThrows
    public static void writeFile(String fileName, String data){
        boolean isExist = true;
        File file = new File(fileName);
        if (!file.exists()) {
            file.getParentFile().mkdirs();
            isExist = false;
        }

        RandomAccessFile raf = null;
        FileReader fr = null;
        BufferedReader br = null;

        if (isExist){
            try {
                raf = new RandomAccessFile(fileName, "rw");
                fr = new FileReader(fileName);
                br = new BufferedReader(fr);
                long pointer = 0;
                raf.seek(pointer);
                //raf.writeBytes(strResult);
                raf.write(data.getBytes());
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                raf.close();
                fr.close();
                br.close();
            }

        }else {
            try {
                file.createNewFile();
                raf = new RandomAccessFile(fileName, "rw");
                fr = new FileReader(fileName);
                br = new BufferedReader(fr);
                long pointer = 0;
                String str = null;
                raf.seek(pointer);
                raf.write(data.getBytes());
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                raf.close();
                fr.close();
                br.close();
            }
        }

    }


    /**
     * 压缩文件夹
     *
     * @param srcDirPath  需要压缩的文件夹路径
     * @param zipFilePath 压缩后的zip文件路径
     * @throws Exception
     */
    public static void zip(String srcDirPath, String zipFilePath) throws Exception {
        File srcDir = new File(srcDirPath);
        if (!srcDir.exists()) {
            throw new Exception("文件夹不存在");
        }

        File zipFile = new File(zipFilePath);
        if (zipFile.exists()) {
            zipFile.delete();
        }

        FileOutputStream fos = new FileOutputStream(zipFile);
        ZipOutputStream zos = new ZipOutputStream(fos);

        zip(srcDir, "", zos);

        zos.close();
        fos.close();
    }

    /**
     * 压缩文件夹
     *
     * @param srcDir 需要压缩的文件夹
     * @param prefix 压缩文件夹内文件的前缀
     * @param zos    ZipOutputStream对象
     * @throws Exception
     */
    private static void zip(File srcDir, String prefix, ZipOutputStream zos) throws Exception {
        File[] files = srcDir.listFiles();
        byte[] buf = new byte[1024];
        int len;
        for (File file : files) {
            if (file.isDirectory()) {
                zip(file, prefix + file.getName() + "/", zos);
            } else {
                FileInputStream fis = new FileInputStream(file);
                zos.putNextEntry(new ZipEntry(prefix + file.getName()));
                while ((len = fis.read(buf)) > 0) {
                    zos.write(buf, 0, len);
                }
                fis.close();
            }
        }
    }
}

