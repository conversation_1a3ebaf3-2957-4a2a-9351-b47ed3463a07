package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.bi.ocean.config.api.model.EventModulesWithBLOBs;
import com.sankuai.bi.ocean.config.api.query.EntityQueryParam;
import com.sankuai.bi.ocean.config.api.query.EventQueryBody;
import com.sankuai.bi.ocean.config.api.query.EventQueryConfig;
import com.sankuai.inf.patriot.org.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.sankuai.mdp.compass.common.enums.Resp;


/**
 * Created by sunkangtong on 2022/5/30
 */
@Service
public class SigmaUtil {
    private static String MeituanAndroid_URL = "http://sigma.sankuai.com/api/open/listAppIteration?appId=1&pageSize=200&pageNum=1";
    private static String YouXuanAndroid_URL = "http://sigma.sankuai.com/api/open/listAppIteration?appId=13&pageSize=200&pageNum=1";

    private static String MeituanIOS_URL = "http://sigma.sankuai.com/api/open/listAppIteration?appId=2&pageSize=200&pageNum=1";
    private static String YouXuanIOS_URL = "http://sigma.sankuai.com/api/open/listAppIteration?appId=12&pageSize=200&pageNum=1";

    private static String URI = "http://sigma.sankuai.com/api/open/listAppIteration?appId=1&pageSize=20&pageNum=1";
    private static final String APPKEY = "com.sankuai.sigma.compass";
    private static final String APPSECRET = "e3b68c6eb61d157fe6a2f6f789e7c150";

    @Value("${sigmaDomain}")
    public String sigmaDomain;

    private static String toGMTString() {
        Date date = new Date();
        SimpleDateFormat df = new SimpleDateFormat("E, dd MMM yyyy HH:mm:ss z", Locale.UK);
        df.setTimeZone(new java.util.SimpleTimeZone(0, "GMT"));
        return df.format(date);
    }


    private static String getAuthorization(String url, String method, String date, String clientId, String secret) {
        String stringToSign = null;
        try {
            stringToSign = method + " " + new URL(url).getPath() + "\n" + date;
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        String signature = getSignature(stringToSign, secret);
        return "MWS " + clientId + ":" + signature;
    }

    private static String getSignature(String data, String secret) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8.name()));
            //必须使用 commons-codec 1.5及以上版本，否则base64加密后会出现换行问题
            String result = Base64.encodeBase64String(rawHmac).replaceAll("\n", "");
            return result;
        } catch (Exception var6) {
//            LOG.warn("", var6);
            throw new IllegalStateException("Failed to generate HMAC : " + var6.getMessage());
        }
    }
    public static JsonObject getBranchStatus(String platform, String appName) {
        String date = toGMTString();
        String authorization = "";
        UriComponentsBuilder builder;
        String androidUrl = "";
        String iOSUrl = "";
        if (appName.equals("meituan")){
            androidUrl = MeituanAndroid_URL;
            iOSUrl = MeituanIOS_URL;
        }else if (appName.equals("youxuan")){
            androidUrl = YouXuanAndroid_URL;
            iOSUrl = YouXuanIOS_URL;
        }else {
            androidUrl = MeituanAndroid_URL;
            iOSUrl = MeituanIOS_URL;
        }
        if (platform.contains("Android")) {
            builder = UriComponentsBuilder.fromHttpUrl(androidUrl);
            authorization = getAuthorization(androidUrl, "GET", toGMTString(), APPKEY, APPSECRET);
        } else {
            builder = UriComponentsBuilder.fromHttpUrl(iOSUrl);
            authorization = getAuthorization(iOSUrl, "GET", toGMTString(), APPKEY, APPSECRET);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
        headers.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headers.set("Date", date);
        headers.set("Authorization", authorization);
        EventQueryBody queryBody = new EventQueryBody();
        EntityQueryParam<EventModulesWithBLOBs> param = new EntityQueryParam<>();
        queryBody.setParam(param);
        EventQueryConfig config = new EventQueryConfig();
        config.setOtherEntityAttributesRequired(true);
        config.setBusinessFieldsRequired(true);
        queryBody.setConfig(config);
        org.springframework.http.HttpEntity entity = new org.springframework.http.HttpEntity<>(queryBody, headers);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
                builder.build().encode().toUri(), HttpMethod.GET, entity, String.class);
        return (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) ?
                null : new JsonParser().parse(responseEntity.getBody()).getAsJsonObject();
    }

    public static String getBranch(int inCurrentBranch, String platform, int processTypeToGet,String appName) {
        String branch = "";
        JsonObject allBranch = getBranchStatus(platform,appName);
        String tempBranch = "";
        try {
            JsonArray recordArray = allBranch.get("data").getAsJsonObject().get("records").getAsJsonArray();
            //首先精准拦截下对应阶段的分支，如果在这里没有找到的话，走到下一个for循环进行模糊匹配
            for (JsonElement jsonElement : recordArray) {
                if (processTypeToGet == 0) break;
                branch = jsonElement.getAsJsonObject().get("branch").getAsString();
                int processType = jsonElement.getAsJsonObject().get("processType").getAsInt();
                if (processTypeToGet == processType && branch.contains("release")) {
                    return branch;
                }
            }
            //在这里进行模糊匹配
            int compareBranch = 99999999;
            for (JsonElement jsonElement : recordArray) {
                branch = jsonElement.getAsJsonObject().get("branch").getAsString();
                int processType = jsonElement.getAsJsonObject().get("processType").getAsInt();
                int iterationType = jsonElement.getAsJsonObject().get("iterationType").getAsInt();
                if (iterationType > 0)continue;//非0则不是正常迭代
                if (processType != 0 && processTypeToGet == processType) {
                    return branch;
                }
                if (branch.contains("release") && inCurrentBranch == 1) {
                    //currentBranch=1 代表取测试版本
                    if (4 <= processType && processType <= 10) {
                        int branchInt = Integer.parseInt(Pattern.compile("[^0-9]").matcher(branch).replaceAll("").trim());
                        if (branchInt < compareBranch) tempBranch = branch;
                    }
                } else if (branch.contains("release") && inCurrentBranch == 0) {
                    if (11 <= processType && processType <= 13) {
                        return branch;
                    }
                    if (14 <= processType && processType <= 15 && tempBranch == "") {
                        tempBranch = branch;
                    }
                }
            }
        } catch (Exception e) {
            return e.toString();
        }
        return tempBranch;
    }

    public String getTargetBranches(int needProcessType, String platform) {
        String branch = "";
        String tempBranch = "";
        JsonObject allBranch = getBranchStatus(platform,"meituan");
        try {
            JsonArray recordArray = allBranch.get("data").getAsJsonObject().get("records").getAsJsonArray();
            for (JsonElement jsonElement : recordArray) {
                branch = jsonElement.getAsJsonObject().get("branch").getAsString();
                int processType = jsonElement.getAsJsonObject().get("processType").getAsInt();
                if (branch.contains("release") && processType == needProcessType) {
                    return branch;
                }
                if (needProcessType <= 10 && tempBranch == "" && branch.contains("release") && 5 <= processType && processType <= 10) {
                    tempBranch = branch;
                }
            }
        } catch (Exception e) {
            return e.toString();
        }
        return tempBranch;

    }

    //向sigma发送通知，见https://km.sankuai.com/collabpage/1550137822
    public JsonObject sendNotificationToSigma(String issueKey, String testItem, String version, String appName, String crashName, String pushedText){
        if(pushedText.getBytes(StandardCharsets.UTF_8).length >= 127){
            JsonObject failedRes=new JsonObject();
            failedRes.addProperty("code","-1");
            failedRes.addProperty("msg","too long");
            return failedRes;
        }
        final String sendRoute = "/api/crash/open/updateRobust";
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("issueKey", issueKey);
        requestBody.addProperty("testItem", testItem);
        requestBody.addProperty("version", version);
        requestBody.addProperty("appName", appName);
        requestBody.addProperty("identification", crashName);
        requestBody.addProperty("pushedText",pushedText);
        Resp resp = NetUtil.httpPostWithJSONBody(sigmaDomain+sendRoute, requestBody.toString(),headers);
        return GsonUtil.getJsonBody(resp);
    }
}
