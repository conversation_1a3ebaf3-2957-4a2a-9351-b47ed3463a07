package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.enums.Resp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/4/16.
 */
public class OnesUtil {
    private static final Logger logger = LoggerFactory.getLogger(OnesUtil.class);

    private String CREATE_ONES_ISSUE = "https://ones.sankuai.com/api/1.0/ones/projects/7991/issue";
    private String onesHost = "https://ones.sankuai.com/";
    private String issue = "http://ones.vip.sankuai.com/api/1.0/ones/issue/";
    private String ONES_USER = "ptmobiletest";

    public String createIssue(JsonObject params) {
        try {
            JsonObject headers = new JsonObject();
            headers.addProperty("Authorization", TokenUtil.getToken("ones_token"));
            headers.addProperty("USERNAME", ONES_USER);
            headers.addProperty("Content-Type", "application/json;charset=utf-8");

            String summary = params.get("summary").getAsString();//项目名称
            String assignee = params.get("assignee").getAsString();//通知人
            String description = params.get("description").getAsString();//描述
            String parentId = params.get("parentId").getAsString();
            long timestamp = new Date().getTime();
            long expectClose = timestamp + 86400 * 1000;

            if (null == parentId || "" == parentId) {
                parentId = "";
            }
            JsonArray label = new JsonArray();
            label.add(7533);

            JsonObject data = new JsonObject();
            data.addProperty("name", summary);
            data.addProperty("desc", description);
            data.addProperty("type", "DEVTASK");
            data.addProperty("subtypeId", 14259);
            data.addProperty("projectId", 7991);
            data.addProperty("moduleId", 6220);
            data.add("labels", label);
            data.addProperty("assigned", assignee);
            data.addProperty("priority", 1);
            data.addProperty("expectStart", timestamp);
            data.addProperty("expectClose", expectClose);
            data.addProperty("parentId", parentId);
            return HttpUtil.jsonPost(CREATE_ONES_ISSUE, data, headers);
        } catch (Exception e) {
            logger.error("create one","ones任务创建失败");
            return null;
        }
    }

    public String createBug(String detail, JsonArray cc, String title, String assignee, Integer projectId,Integer subtypeId, JsonArray labelNum, String onesIssue ,String parentId){
        try{
            JsonObject headers = new JsonObject();
            headers.addProperty("Authorization", TokenUtil.getToken("ones_token"));
            headers.addProperty("USERNAME", ONES_USER);
            headers.addProperty("Content-Type", "application/json;charset=utf-8");
            long timestamp = new Date().getTime();
            long expectClose = timestamp + 86400 * 1000;
            JsonObject data = new JsonObject();
            data.addProperty("type","DEFECT");//ones类型，defect是指  缺陷
            data.addProperty("name", title);//问题标题
            data.addProperty("projectId",projectId);//项目id，从浏览器地址里查看
            data.addProperty("assigned",assignee);//指派给
            data.addProperty("desc",detail);//问题描述
            data.addProperty("priority",1);
            data.addProperty("expectStart", timestamp);//预计开始时间
            data.addProperty("expectClose", expectClose);//预计结束时间
            data.add("labels", labelNum);//标签 labels 含义：标签。7533 是指 自动化
            data.addProperty("subtypeId", subtypeId);//subtypeId是指 子类型，13786是指 客户端bug
            data.add("cc",cc);//抄送
            data.addProperty("parentId", parentId);
            try {
                SslUtil.ignoreSsl();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return HttpUtil.jsonPost(onesIssue, data, headers);
        } catch (Exception e) {
            logger.error("create one","ones任务创建失败");
            return null;

        }
    }

    public String robustCrashReason(int issueId){
        HashMap params = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", TokenUtil.getToken("ones_token"));
//        headers.put("Authorization","Basic cHRtb2JpbGV0ZXN0OnB0bW9iaWxldGVzdF9vbmVz");
        headers.put("USERNAME","ptmobiletest");
        headers.put("Content-Type","application/json");
        Resp a =  HttpUtil.httpGet(issue+issueId,params,headers);

        return "";

    }

    private Map<String, String> getCommentHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", TokenUtil.getToken("ones_token"));
        return headers;
    }

    public JsonObject addComments( Long issueId,  String text){
        // wiki ---  https://km.sankuai.com/page/129996034
        int projectId = 5704;

        String url = String.format("%sapi/1.0/ones/project/%d/comments",onesHost,projectId);
        logger.info("addComments url: {}", url);

        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("parentId", 0);
        requestBody.addProperty("type", "ADDED");
        requestBody.addProperty("repliedId", 0);
        requestBody.addProperty("text", text);
        requestBody.addProperty("issueId", issueId);
        requestBody.addProperty("issueType", "DEFECT");

        Resp resp = NetUtil.httpPostWithJSONBody(url, requestBody.toString(),getCommentHeaders());
        JsonObject jsonObject = GsonUtil.getJsonBody(resp);
        logger.info("getSubmitTest1: {}", jsonObject);
        if (jsonObject.get("code").getAsInt() == 200) {
            JsonObject dataJson = jsonObject.get("data").getAsJsonObject();
            logger.info("getSubmitTest2: {}", dataJson);
            return dataJson;
        } else {
            throw new RuntimeException(jsonObject.get("message").getAsString());
        }
    }




    public static void main(String[] args) {
//        new OnesUtil().robustCrashReason(67913755);
        new OnesUtil().addComments(72543352L,"<a href=\"http://qaassist.sankuai.com/compass/dashboard?#/robustAutoTest/jobReport?id=410\"target=\"_blank\">健壮性工具：发现此工单项目地址</a>");

    }

}

