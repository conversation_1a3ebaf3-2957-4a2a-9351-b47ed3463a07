package com.sankuai.mdp.compass.common.utils;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import com.sankuai.mdp.compass.common.enums.TestType;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import lombok.SneakyThrows;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by skt on 2021/7/26.
 */
public class ValueUtil {

    static int nm = 1000*60;
    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }
    public  static boolean isTrue(String str){
        if (null == str || str.equals("0") || str.equals("false")){
            return false;
        }
        return true;
    }
    public static int getDatePoor(Date endDate, Date startDate) {

        long diff = endDate.getTime() - startDate.getTime();

        int min = (int) (diff / nm);

        return Math.abs(min) ;
    }

    public static String whichPlatform(String url){
        if (StringUtil.isBlank(url))return "illegal";
        String[] ss = url.split("\\.");
        try{
            if (ss[ss.length-1].toLowerCase().contains("apk")){
                return "Android";
            }else if (ss[ss.length-1].toLowerCase().contains("ipa")){
                return "iOS";
            }else{
                System.out.println(ss[ss.length-1].toLowerCase());
                return "false";
            }
        }catch (Exception e){
            return  "illegal";
        }
    }

    public static HashMap getWeekReport(LambdaQueryWrapper<CompatilityJob> compatilityJobWrapper, CompatilityMapper compatilityMapper,TestType type){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String nowDate = sdf.format(date);

        //1.总平均时间
        double avgTime = 0;
        //2.TP90平均时间
        double avgTP90Time = 0;
        //3.最快平均时间
        double fistTime = 0;
        //4.TP90最快平均时间
        double fistTP90Time = 0;

        if (type.equals(TestType.UI)){
            compatilityJobWrapper.last("where datediff('" + nowDate + "',created_at)<7 and finish_at!='' order by finish_at-created_at");
        }else if(type.equals(TestType.Mge)){
            compatilityJobWrapper.last("where datediff('" + nowDate + "',created_at)<7 and event_finish_at!='' order by event_finish_at-created_at");
        }

        List<CompatilityJob> resultList  = compatilityMapper.selectList(compatilityJobWrapper);
        int weekCount = resultList.size();
        int TP90Count =(int)Math.ceil(weekCount*0.9);
        int TP90Res = TP90Count;
        Date startTime = new Date();
        Date endTime = new Date();

        for (CompatilityJob compatilityJob : resultList) {
            startTime = compatilityJob.getCreatedAt();
            if (type.equals(TestType.UI)){
                endTime = compatilityJob.getFinishAt();

            }else if (type.equals(TestType.Mge)){
                endTime = compatilityJob.getEventFinishAt();
            }
            long diff = new ValueUtil().getDatePoor(endTime,startTime);
            if (TP90Res>0){
                TP90Res-=1;
                avgTP90Time+=diff;
            }
            avgTime += diff;
        }
        try{
            avgTime = avgTime / weekCount;
            avgTP90Time = avgTP90Time / TP90Count;
        }catch (Exception e){

        }





        //最快UI+tp90
        LambdaQueryWrapper<CompatilityJob> compatilityJobWrapper2 = new LambdaQueryWrapper<>();
        if (type.equals(TestType.UI)){
            compatilityJobWrapper2.last("where datediff('" + nowDate + "',created_at)<7 and first_finish!='' order by first_finish-created_at");
        }else if(type.equals(TestType.Mge)){
            compatilityJobWrapper2.last("where datediff('" + nowDate + "',created_at)<7 and event_first_finish!='' order by event_first_finish-created_at");
        }

        List<CompatilityJob> resultList2  = compatilityMapper.selectList(compatilityJobWrapper2);
        int weekCount2 = resultList.size();
        int TP90Count2 =(int)Math.ceil(weekCount*0.9);
        int TP90Res2 = TP90Count;
        for (CompatilityJob compatilityJob : resultList2) {
            startTime = compatilityJob.getCreatedAt();
            if (type.equals(TestType.UI)){
                endTime = compatilityJob.getFirstFinish();

            }else if (type.equals(TestType.Mge)){
                endTime = compatilityJob.getEventFirstFinish();
            }
            long diff = new ValueUtil().getDatePoor(endTime,startTime);
            if (TP90Res2>0){
                TP90Res2-=1;
                fistTP90Time+=diff;
            }
            fistTime += diff;
        }
        try{
            fistTime = fistTime / weekCount2;
            fistTP90Time = fistTP90Time / TP90Count2;
        }catch (Exception e){

        }
        HashMap re = new HashMap();

        re.put(type+"类型 avgTime",avgTime);
        re.put(type+"类型 avgTP90Time",avgTP90Time);
        re.put(type+"类型 fistAvgTime",fistTime);
        re.put(type+"类型 fistTP90Time",fistTP90Time);

        return re;
    }

    @SneakyThrows
    public static String printNowTime() {
        SimpleDateFormat sdf =   new SimpleDateFormat("HH:mm:ss.SSS");
        String startTime = sdf.format(new Date());
//        System.out.println(startTime);
        return startTime;
    }

    public long getTpData(ArrayList<Long> arrayList , double precent){
        try {
            int Count = arrayList.size();
            int TPData = (int)Math.ceil(Count*precent);
            Collections.sort(arrayList);
            int count = 0;
            long totalTime = 0;
            long avg = 0;
            for (long diff : arrayList) {
                count+=1;
                totalTime+=diff;
                if (count == TPData){
                    avg = totalTime/count;
                    break;
                }
            }
            return  avg;
        }catch (Exception e){
            return 0;
        }
    }




}