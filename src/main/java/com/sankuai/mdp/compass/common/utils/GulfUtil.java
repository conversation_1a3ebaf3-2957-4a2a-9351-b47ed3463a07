package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Slf4j
public class GulfUtil {

    private static final Logger logger = LoggerFactory.getLogger(GulfUtil.class);
    public static final String MGR_VERSION_API = "http://gulfstream.dataapp.dev.sankuai.com/api/getVersionByType?id=1&project=AIMT";

    public static JsonObject getTestVersion() {
        JsonObject result = null;
        try {
            String url = MGR_VERSION_API;
            JsonParser parser = new JsonParser();
            result = parser.parse(HttpUtil.vGet(url,TokenUtil.getToken("gulf_token"))).getAsJsonObject();
            if (null != result) {
                result = result.getAsJsonObject("data");
            }
        } catch (Exception e) {
            logger.error("create one", "读取API失败");
        } finally {
            logger.info(result.toString());
        }
        return result;
    }
}
