package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonObject;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.io.IOException;

/**
 * Created by l<PERSON><PERSON> on 2020/02/05.
 */
@Slf4j
public class AquamanUtil {
    private static final String PROD_URL = "https://dqa.sankuai.com/openApi/monitor/realTime/search";
    private static final String TEST_URL = "http://dqa.data.test.sankuai.com/openApi/monitor/realTime/search";
    private static final String ZERO_TEST_URL = "http://zerox.fe.test.sankuai.com/zerox/api/resultCallBack";
    private static final String ZERO_PROD_URL = "http://zerox.sankuai.com/zerox/api/resultCallBack";

    public JSONArray collectResult(String version, String build) throws IOException {
        JSONObject data = new JSONObject();
        JSONArray resultList = null;
        JSONObject response = null;
        try{
            data.put("jobId","");
            data.put("appName","group");
            data.put("appVersion",version);
            data.put("buildId",build);
            if (EnvUtil.isOnline()){
                response = HttpUtil.sendPostJson(PROD_URL,data);
            }else{
                response = HttpUtil.sendPostJson(TEST_URL,data);
            }
//            log.info("请求灵犀埋点结果：" + response.toString());
            resultList = response.getJSONObject("data").getJSONArray("list");
        }catch (Exception e){
            e.printStackTrace();
            log.info("请求灵犀埋点结果失败！");
        }
        return resultList;
    }

    public void sendResult(Integer jobId,Integer status,String reportUrl){
        JSONObject data = new JSONObject();
        try{
            data.put("id",jobId);
            data.put("resultStatus",status);
            data.put("reportUrl",reportUrl);
            if (EnvUtil.isOnline()){
                HttpUtil.sendPostJson(ZERO_PROD_URL,data);
            }else{
                HttpUtil.sendPostJson(ZERO_TEST_URL,data);
            }
            log.info("回调Zero-X请求结果： "+jobId+" success");
        }catch (Exception e){
            e.printStackTrace();
            log.info("回调Zero-X请求结果： "+jobId+" failed");
        }
    }
}
