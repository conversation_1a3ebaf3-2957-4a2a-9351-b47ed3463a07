package com.sankuai.mdp.compass.common.utils;

import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpJob;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.pomConsistency.entity.dto.JenkinsParams;
import com.sankuai.mdp.compass.robust.entity.RobustBuild;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import okhttp3.Credentials;
import com.google.gson.JsonObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.nio.charset.StandardCharsets;
import static org.yaml.snakeyaml.util.UriEncoder.encode;


/**
 * Created by xieyongrui on 2019/11/17.
 */
@Slf4j
public class JenkinsUtil {
    private static final String PROD_URL = "http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/aimeituan_dynamic_check/buildWithParameters?";
    private static final String GUIDE_LAYER_URL = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/guide_layer_auto_test/buildWithParameters";
    //    private static final String TEST_HOOK_URL = "http://172.18.76.220:8080/job/test_hook/buildWithParameters?";
    private static final String TEST_MGE_URL = "http://172.18.76.220:8080/job/mge_real_time_auto_test/buildWithParameters?";
    private static final String DynamicTeplate_URL = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/dynamic_check_with_virtual_devices/buildWithParameters";
    private static final String Robust_URL = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/Robustness/buildWithParameters";
    private static final String Robust_Pre_URL = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/RobustnessPreBuild/buildWithParameters";

    private static final String HYPEY_JUMP_URL_ANDROID = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/auto_screenshot_android/buildWithParameters";
    private static final String HYPER_JUMP_URL_iOS = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/auto_screenshot_ios/buildWithParameters";

    private static final String OREO_JOB_IOS = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/oreo_uitest_ios/buildWithParameters";
    private static final String OREO_JOB_ANDROID = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/oreo_uitest_android/buildWithParameters";

    private static final String POM_CHECK_JOB = "https://jenkins.sankuai.com/job/pom_relation_n/buildWithParameters";

    public void newDynamicTestJob(CompatilityJob dynamicJob) throws InterruptedException {

        //TODO：param参数多拼一个mockId
        //// TODO: 2021/3/19 在这里区分 机型、设备系统

        String jsonString = encode(dynamicJob.getMockIds().toString());
        String param = "id=" + dynamicJob.getId() + "&platform=" + dynamicJob.getPlatform() + "&type=" + dynamicJob.getType() + "&apk_versions=" + dynamicJob.getApkVersion() +
                "&apk_url=" + dynamicJob.getApkUrl() + "&device_os_versions=" + dynamicJob.getDevicesVersion() + "&group=" + dynamicJob.getBusiness() + "&dynamic=" + dynamicJob.getTemplateName() + "&mock=" + jsonString + "&token=" + TokenUtil.getToken("jenkins_token")
                + "&address=" + dynamicJob.getAddress();
        HttpUtil.vGet(PROD_URL + param);
    }

    @SneakyThrows
    public void UISmokeJob(CompatilityJob compatilityJob) {
        // 3.11：mockRule超长导致Jenkins提交失败，把mockRule(即jsonString)从URL中去掉，改为Jenkins脚本中从数据表读取mockRule
        // String jsonString = encode(compatilityJob.getMockRule().toString());
        // 7.15添加环境参数
        String env = "prod";
        if (!EnvUtil.isOnline()) {
            env = "test";
        }
        //安卓获取设备分辨率列表
        String androidScreenResolutions = new LionUtil().getValue("androidScreenResolutions");
        String param = "id=" + compatilityJob.getId() +
                "&platform=" + compatilityJob.getPlatform() +
                "&type=0" +
                "&apk_versions=" + compatilityJob.getApkVersion() +
                "&apk_url=" + compatilityJob.getApkUrl() +
                "&device_os_versions=" + compatilityJob.getDevicesVersion() +
                "&group=" + compatilityJob.getBusiness() +
                "&dynamic=" + compatilityJob.getTemplateName() +
                "&token=" + TokenUtil.getToken("jenkins_token") +
                "&address=" + compatilityJob.getAddress() +
                "&ipa_url=" + compatilityJob.getImeituanUrl() +
                "&screenResolutions=" + androidScreenResolutions + "&env=" + env;
        String credential = TokenUtil.getNewJenkinsToken();
        HttpUtil.vpostbyToken(DynamicTeplate_URL, param, credential);
    }

    @SneakyThrows
    public void newHyperJumpJob(HyperJumpJob hyperJumpJob) throws Exception {
        String platform = hyperJumpJob.getPlatform();
        String baseApkUrl = hyperJumpJob.getBaseApkUrl();
        String testApkUrl = hyperJumpJob.getTestApkUrl();
        String bg = hyperJumpJob.getBg();
        String bu = hyperJumpJob.getBu();
        String biz = hyperJumpJob.getBiz();
        String pageType = hyperJumpJob.getPageType();
        String pageDescription = hyperJumpJob.getPageDescription();
        String url = "";
        if (platform.equals("Android")) {
            url = HYPEY_JUMP_URL_ANDROID;
            if (baseApkUrl != null && !baseApkUrl.isEmpty()
                    && !baseApkUrl.matches("https://hyperloop-s3\\.sankuai\\.com/hpx-artifacts/.+\\.apk")) {
                return;
            }
            if (testApkUrl != null && !testApkUrl.isEmpty()
                    && !testApkUrl.matches("https://hyperloop-s3\\.sankuai\\.com/hpx-artifacts/.+\\.apk")) {
                return;
            }
        } else if (platform.equals("iOS")) {
            url = HYPER_JUMP_URL_iOS;
            if (baseApkUrl != null && !baseApkUrl.isEmpty()
                    && !baseApkUrl.matches("https://hyperloop-s3\\.sankuai\\.com/hyperloop-ipa/.+\\.ipa")) {
                return;
            }
            if (testApkUrl != null && !testApkUrl.isEmpty()
                    && !testApkUrl.matches("https://hyperloop-s3\\.sankuai\\.com/hyperloop-ipa/.+\\.ipa")) {
                return;
            }
        } else {
            throw new Exception("platform参数错误");
        }

        StringBuilder paramBuilder = new StringBuilder();
        if (baseApkUrl != null && !baseApkUrl.equals("null")) {
            paramBuilder.append("baseApkUrl=").append(baseApkUrl).append("&");
        }
        if (testApkUrl != null && !testApkUrl.equals("null")) {
            paramBuilder.append("testApkUrl=").append(testApkUrl).append("&");
        }
        if (bg != null && !bg.equals("null")) {
            paramBuilder.append("bg=").append(bg).append("&");
        }
        if (bu != null && !bu.equals("null")) {
            paramBuilder.append("bu=").append(bu).append("&");
        }
        if (biz != null && !biz.equals("null")) {
            paramBuilder.append("biz=").append(biz).append("&");
        }
        if (pageType != null && !pageType.equals("null")) {
            paramBuilder.append("pageType=").append(pageType).append("&");
        }
        if (pageDescription != null && !pageDescription.equals("null")) {
            paramBuilder.append("pageDescription=").append(pageDescription).append("&");
        }
        paramBuilder.append("token=").append(TokenUtil.getToken("jenkins_token"));

        String param = paramBuilder.toString();

        String credential = TokenUtil.getNewJenkinsToken();
        HttpUtil.vpostbyToken(url, param, credential);
    }

    public void newUfoJob(OreoJob oreoJob, String strategyMessage) throws Exception {
        String encodedAccountList = URLEncoder.encode(strategyMessage, String.valueOf(StandardCharsets.UTF_8));
        String oreoId = String.valueOf(oreoJob.getOreoId());
        String apkUrl = oreoJob.getApkUrl();
        String oreoDesc = oreoJob.getOreoDesc();
        String oreoUuid = oreoJob.getOreoUuid();
        StringBuilder paramBuilder = new StringBuilder();

        if ("".equals(oreoDesc) || oreoDesc == null) {
            oreoDesc = "Oreo配置变更";
        }
        if (apkUrl == null) {
            System.out.println(apkUrl);
            apkUrl = "";
        }
        String[] apkUrlList = apkUrl.split(",");
        String androidApkUrl = "";
        String iosApkUrl = "";
        if (apkUrl.contains(".apk")) {
            for (String str : apkUrlList) {
                if (str.endsWith(".apk")) {
                    androidApkUrl = str;
                }
            }
        }
        if (apkUrl.contains(".ipa")) {
            for (String str : apkUrlList) {
                if (str.endsWith(".ipa")) {
                    iosApkUrl = str;
                }
            }
        }
        paramBuilder.append("oreoUuid=").append(oreoUuid).append("&");
        paramBuilder.append("apkurl=").append(androidApkUrl).append("&");
        paramBuilder.append("oreoDesc=").append(oreoDesc).append("&");
        paramBuilder.append("accountList=").append(encodedAccountList).append("&");
        paramBuilder.append("oreoId=").append(oreoId);
        String credential = TokenUtil.getNewJenkinsToken();
        String param = paramBuilder.toString();
        HttpUtil.vpostbyToken(OREO_JOB_ANDROID, param, credential);
        String replacedString = param.replaceFirst("apkurl=[^&]*", "apkurl=" + iosApkUrl);
        HttpUtil.vpostbyToken(OREO_JOB_IOS, replacedString, credential);
    }


    @SneakyThrows
    public void eventSmokeJob(CompatilityJob compatilityJob) {
        // 7.15添加环境参数
        String env = "prod";
        if (!EnvUtil.isOnline()) {
            env = "test";
        }
        String jsonString = encode(compatilityJob.getMockRule().toString());
        String param = "id=" + compatilityJob.getId() + "&platform=" + compatilityJob.getPlatform() + "&type=1&apk_versions=" + compatilityJob.getApkVersion() +
                "&apk_url=" + compatilityJob.getApkUrl() + "&device_os_versions=" + compatilityJob.getDevicesVersion() + "&group=" + compatilityJob.getBusiness() + "&dynamic=" + compatilityJob.getTemplateName() + "&mock=" + jsonString + "&token=" + TokenUtil.getToken("jenkins_token")
                + "&address=" + compatilityJob.getAddress() + "&ipa_url=" + compatilityJob.getImeituanUrl() + "&env=" + env;
        String credential = TokenUtil.getNewJenkinsToken();
        HttpUtil.vpostbyToken(DynamicTeplate_URL, param, credential);
    }

    @SneakyThrows
    public void newGuideLayerTestJob(GuideLayerJob guideLayerJob) {
        String jsonString = encode(guideLayerJob.getMock().toString());
        String param = "id=" + guideLayerJob.getId() + "&mock=" + jsonString + "&token=" + TokenUtil.getToken("jenkins_token");
        String credential = TokenUtil.getNewJenkinsToken();
        HttpUtil.vpostbyToken(GUIDE_LAYER_URL, param, credential);
        log.info("触发引导浮层自动化测试");
    }

    public void newTestHookJob(JSONObject body) {
    }

    public void newMgeTestJob(JsonObject body) {
        String jobId = body.get("jobid").getAsString();
        String platform = body.get("os").getAsString();
        String apk_url = body.get("appUrl").getAsString();
        String apk_versions = body.get("version").getAsString();
        String env = body.get("env").getAsString();
        String param = "jobid=" + jobId + "&platform=" + platform + "&apk_url=" + apk_url + "&apk_versions=" + apk_versions + "&token=" + TokenUtil.getToken("jenkins_token") + "&env=" + env;
        String credential = Credentials.basic("ptqa_local_test", TokenUtil.getToken("local_jenkins_token"));
        HttpUtil.vGet(TEST_MGE_URL + param, credential);
        log.info("触发实时埋点测试");
    }

    @SneakyThrows
    public void robustJob(RobustBuild robustBuild, String mode, String appName) {
        if (EnvUtil.isOnline()) {
            //触发jenkins任务,mode=new:新触发测试，mode=retrySkipCase:重试未执行用例，mode=retryCombinationCrashCase重新执行发生聚合崩溃用例
            String platform = robustBuild.getPlatform();
            String apkUrl = robustBuild.getApkUrl();
            int id = robustBuild.getId();
            String credential = TokenUtil.getNewJenkinsToken();
            String param = "apk_url=" + apkUrl + "&platform=" + platform + "&robust_job_id=" + id + "&mode=" + mode + "&token=robustness" + "&appName=" + appName;
            HttpUtil.vpostbyToken(Robust_URL, param, credential);
        }
    }

    /**
     * 触发预处理job
     *
     * @param triggerId
     * @param platform
     */
    @SneakyThrows
    public void robustPreJob(String triggerId, String platform, String apkUrl, String mode, String env, String appName) {
        String credential = TokenUtil.getNewJenkinsToken();
        String param = "apk_url=" + apkUrl + "&platform=" + platform + "&trigger_id=" + triggerId + "&mode=" + mode + "&env=" + env + "&token=robustness" + "&appName=" + appName;
        HttpUtil.vpostbyToken(Robust_Pre_URL, param, credential);
    }

    @SneakyThrows
    public void dependencyCheckJob( JenkinsParams jenkinsParams) {
            String credential = TokenUtil.getNewJenkinsUserCentreToken();
            Map<String,String> paramsMap=new HashMap<>();
            paramsMap.put("PR_ID",jenkinsParams.getPrId());
            paramsMap.put("REPO_URL",jenkinsParams.getGitUrl());
            paramsMap.put("SOURCE_BRANCH",jenkinsParams.getBranch());
            paramsMap.put("PROJECT_NAME",jenkinsParams.getProjectName());
            paramsMap.put("REPO_NAME",jenkinsParams.getRepoName());
            paramsMap.put("EVENT_ID",jenkinsParams.getEventId());
            paramsMap.put("ACTION",jenkinsParams.getAction());
            paramsMap.put("CI_ID",jenkinsParams.getCiId());
            paramsMap.put("TOKEN",TokenUtil.getToken("jenkins_token"));
            paramsMap.put("POM_CHECK", "true");
            String params =buildParams(paramsMap);
            HttpUtil.vpostbyToken(POM_CHECK_JOB, params, credential);
    }
    @SneakyThrows
    public  String buildParams(Map<String, String> params) {
        StringBuilder paramBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (paramBuilder.length() != 0) {
                paramBuilder.append("&");
            }

            try {
                if (entry.getKey() != null && entry.getValue() != null) {
                    paramBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException e) {
                // 处理异常，例如记录日志或抛出自定义异常
                log.error("Error encoding URL parameters", e);
            }
        }
        return paramBuilder.toString();
    }
}
