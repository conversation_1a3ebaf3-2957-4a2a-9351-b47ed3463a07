package com.sankuai.mdp.compass.common.utils;

import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by skt on 2022/9/15.
 */
public class LionUtil {
    ConfigRepository config;
    public LionUtil() {
        config = Lion.getConfigRepository("com.sankuai.sigma.compass");
    }

    public String getValue(String key){
        String value = config.get(key);
//        new DxUtil().sendToPersionByCompass(key+"的值为："+value,"sunkangtong");
        return value;
    }
    public boolean getBooleanValue(String KEY) {
        try {
            return config.getBooleanValue(KEY);
        }catch (Exception e){
            return true;
        }
    }
    public List<String> getListValue(String key){return config.getList(key);}

}
