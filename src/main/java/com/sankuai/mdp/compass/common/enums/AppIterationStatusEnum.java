package com.sankuai.mdp.compass.common.enums;

/**
 * Created by skt on 2022/11/7.
 */
public enum AppIterationStatusEnum {
    NOT_STARTED(-1,"未开始"),
    OTHERS_PROCESS(0,"其他中"),
    DEVELOP_PROCESS(1,"开发中"),
    BASIC_INTEGRATE_PROCESS(2,"基础库集成中"),
    BASIC_ADAPTIVE_PROCESS(3,"基础库适配中"),
    BUILD_PROCESS(4,"构建中"),
    INTEGRATION_PROCESS(5,"集成中"),
    TEST_PROCESS(6,"测试中"),
    REGRESSION_PROCESS(7,"回归中"),
    REGRESSION_FINISH(8,"回归完成"),
    LOCK_PROCESS(9,"锁版中"),
    LOCK_FINISH(10,"已锁版"),
    GREY_PROCESS(11,"灰度中"),
    GREY_FINISH(12,"已灰度"),
    PUBLISH_PROCESS(13,"发布中"),
    PUBLISH_FINISH(14,"已发布"),
    FINISH(15,"已结束");


    private int Code;

    private String Text;

    AppIterationStatusEnum(){
    }

    public static AppIterationStatusEnum getByCode(Integer status) {
        switch (status){
            case 0:
                return OTHERS_PROCESS;
            case 1:
                return DEVELOP_PROCESS;
            case 2:
                return BASIC_INTEGRATE_PROCESS;
            case 3:
                return BASIC_ADAPTIVE_PROCESS;
            case 4:
                return BUILD_PROCESS;
            case 5:
                return INTEGRATION_PROCESS;
            case 6:
                return TEST_PROCESS;
            case 7:
                return REGRESSION_PROCESS;
            case 8:
                return REGRESSION_FINISH;
            case 9:
                return LOCK_PROCESS;
            case 10:
                return LOCK_FINISH;
            case 11:
                return GREY_PROCESS;
            case 12:
                return GREY_FINISH;
            case 13:
                return PUBLISH_PROCESS;
            case 14:
                return PUBLISH_FINISH;
            case 15:
                return FINISH;
            default:
                return NOT_STARTED;
        }
    }

    public static Integer getByText (String stage) {
        switch (stage){
            case "开发中":
                return 1;
            case "基础库集成中":
                return 2;
            case "基础库适配中":
                return 3;
            case "构建中":
                return 4;
            case "集成中":
                return 5;
            case "测试中":
                return 6;
            case "回归中":
                return 7;
            case "回归完成":
                return 8;
            case "锁版中":
                return 8;
            case "已锁版":
                return 9;
            case "灰度中":
                return 10;
            case "已灰度":
                return 11;
            case "发布中":
                return 12;
            case "已发布":
                return 13;
            case "已全量":
                return 14;
            case "未开始":
                return -1;

            default:
                return 0;
        }
    }

    AppIterationStatusEnum(int code, String text){
        this.Code=code;
        this.Text=text;
    }

    public int getCode() {
        return Code;
    }

    public void setCode(int code) {
        Code = code;
    }

    public String getText() {
        return Text;
    }

    public void setText(String text) {
        Text = text;
    }
}
