package com.sankuai.mdp.compass.common.utils;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.enums.TestType;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import lombok.SneakyThrows;
import org.codehaus.groovy.runtime.powerassert.SourceText;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Iterator;

/**
 * Created by skt on 2021/7/6.
 */
public class SubmitJobUtil {
    private static final String androidAppName = "com.sankuai.meituan";
    private static final String flavor = "meituanInternal";
    private static final String channel = "meituaninternaltest";
    private static final String reportURL = "https://conan.sankuai.com/auto-function/report/";

    private static final String jenkinsUrl = "http://aci.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E6%B5%8B%E8%AF%95/job/dynamic_check_with_virtual_devices/";
    private static final String CALLBACK_URL = "http://qaassist.sankuai.com/compass/api/collectDynamicTestResult";
    private static final String Dynamic_CALLBACK_PARAMS = "after=notifyResultCommon&notify=" + CALLBACK_URL;

    private static final String conanUrl = "https://conan.sankuai.com/ci/auto-function/appium";

    private static final String downUitest = "git clone --depth 1 ssh://*******************/ptqa/agroup-dynamic-auto-uitest.git";
    private static final String delUitest = "rm -rf agroup-dynamic-auto-uitest";


    private static final String andr_device_os_versions = "5,6,7,8,9,10";
    private static final String ios_device_os_versions = "10,11,12,13,14";

    private static final String andr_black_numbers = "52f34922,41bed3d57d34,20a51815,7N2SQL14CV000034,141de0b5,69DDU17224002427,5LM0216429000397,15bdd8e1,f8676a38";
    private static final String ios_black_numbers = "bca074fc8faa85b0edf54144200c7ebc54e31e3e,8703ef8ac2380fb78b44c9aa6a0df8bd015baae0,41349c9f05d87fffa4803d4e29f40011c51aa690,60aeb8e0176ec26a7a564c4b50ca7cec9334516b";

    private static final String conanKey = "2d7d4fb7-ec69-4b61-a310-a4f694fde9e8";
    private static final String misId = "sunkangtong";

    private static final String iosApk = "https://hyperloop-s3.sankuai.com/hyperloop-ipa/imeituan-11.10.204-92305.20210622113257.ipa";
    private static final String AndroidApk = "https://apptest.sankuai.com/download/aimeituan-release_11.10.400-132167.apk";

    private static final int count = 3;

    private static final int mgeJob = 1;
    private static final int uiJob = 0;

    private static final String  platformIOS = "iOS";
    private static final String  platformAndroid = "Android";





    public String dynamicSmokeJob(CompatilityJob compatilityJob, TestType testType){
        int type = compatilityJob.getType();
        String jobId = compatilityJob.getId().toString();
        String jobName = testType + jobId;

        String apkUrl = compatilityJob.getApkUrl();
        String ipaUrl = compatilityJob.getImeituanUrl();

        new CmdUtil().run("mkdir "+jobName);
        new CmdUtil().run(downUitest+" "+jobName);

        if(testType.equals(TestType.Mge)){
            new FileUtil().writeFile(jobName + "/test-config/case_config.txt","testCardLXPoint");
        }else if(testType.equals(TestType.UI)){
            new FileUtil().writeFile(jobName + "/test-config/case_config.txt","testCardUI\ntestCardOperation");
        }

        String mockRule = compatilityJob.getMockRule().toString();
        JSONArray caseGroups = groupingSmokeCase(mockRule,count);
        String reportIdIOS = dynamicSubmit(caseGroups, jobName, platformIOS, type, ipaUrl);
        String reportIdAndroid = dynamicSubmit(caseGroups, jobName, platformAndroid, type, apkUrl);

        String reportURL = (reportIdAndroid+reportIdIOS);
        reportURL = reportURL.substring(0,reportURL.length()-1);
        return reportURL;

//        if [[ '1' -eq $type ]]; then
//        curl -X PUT "$domain/compass/api/job/updateEventConanId?id=$id&eventConanId=${reportURL##*/}"
//        elif [[ '0' -eq $type ]]; then
//        curl -X PUT "$domain/compass/api/job/updataReport?id=$id&report=${reportURL##*/}"
//        fi


    }
    @SneakyThrows
    public String dynamicSubmit(JSONArray caseGroups, String jobName, String platform, int type, String qiangxianURL)  {
        StringUtil st = new StringUtil();
        if (st.isBlank(jobName))return "";
        FileUtil fileUtil = new FileUtil();
        CmdUtil cmdUtil = new CmdUtil();
        String reportId = "";
        int deviveCount = 0;
        if (mgeJob == type){
            deviveCount = 1;
        }else if(uiJob == type){
            deviveCount = 3;
        }
        fileUtil.delete(jobName+"/app.properties");
        if (platform.equals(platformAndroid)){
            cmdUtil.run("cp "+jobName+"/config/android.properties "+jobName+"/app.properties");
        }else{
            cmdUtil.run("cp "+jobName+"/config/iOS.properties "+jobName+"/app.properties");
        }
        for (Object caseGroup : caseGroups) {
            fileUtil.delete(jobName + "/test-config/mock_config.txt");
            new FileUtil().writeFile(jobName + "/test-config/mock_config.txt",caseGroup.toString());
            //压缩
            new CmdUtil().run("zip -qr "+jobName+ ".zip "+jobName);
            //上传case
            String testCaseName = uploadCase(jobName);
            //把该删的配置文件都删一下
            fileUtil.delete(jobName + "/test-config/mock_config.txt");
            fileUtil.delete(jobName+".zip");

            JsonObject respones =  submitJob(platform, deviveCount, testCaseName, qiangxianURL);
            String conanId = "";
            try{
                conanId = respones.get("jobName").getAsString();
            }catch (Exception e){
                conanId = "";
            }
            reportId+=conanId+"_";
        }
        return reportId;
    }

    /**
     * 将脚本上传，获得文件名称
     * @param jobNamePath 文件路径
     * @return caseName
     */
    public String uploadCase(String jobNamePath){
        String res = new CmdUtil().run("curl -F \"file=@"+jobNamePath+".zip\" https://conan.sankuai.com/ci/upload\\?type=case\\&misId=sunkangtong\\&conanKey=2d7d4fb7-ec69-4b61-a310-a4f694fde9e8");
        JsonObject r = new JsonParser().parse(res).getAsJsonObject();
        String fileName = r.get("fileName").getAsString();
        return fileName;
    }


    /**
     *
     * @param platform 选择平台 Android or iOS
     * @param deviceCount 占用机器数量
     * @param TestCaseName
     * @param apk 包链接
     * @return 云测任务id
     */
    public JsonObject submitJob(String platform, int deviceCount, String TestCaseName, String apk){
        String params = "";
        String response = "";
        JsonObject result = new JsonObject();
        if ("Android".equals(platform)){
            if ("" == apk){apk = AndroidApk;}
            params += Dynamic_CALLBACK_PARAMS + "&apk=" + apk + "&case=" + TestCaseName + "&platform=Android&alias=美团&type=smoketest&devicesVersion=" +andr_device_os_versions+ "&deviceCount=" + deviceCount + "&misId="+misId+"&conanKey="+conanKey;
        }else{
            if ("" == apk){apk = iosApk;}
            params += Dynamic_CALLBACK_PARAMS + "&apk=" + apk + "&case=" + TestCaseName + "&platform=iOS&alias=美团&type=smoketest&devicesVersion=" +ios_device_os_versions+ "&deviceCount=" + deviceCount + "&misId="+misId+"&conanKey="+conanKey;
        }
        try {
//            response = new HttpUtil().sendPost(conanUrl, params);
//            result =  new JsonParser().parse(response).getAsJsonObject();
            System.out.println(params);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            return result;
        }
    }


    public JSONArray groupingSmokeCase(String mockRule, int count) {
        JSONArray result = new JSONArray();
//        JSONObject jsonObject = new JSONObject(bodyJson.get("data").toString());
        JSONObject jsonObject = new JSONObject(mockRule);
        int groupCount = count;
        Iterator<String> iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String business = iterator.next();
            JSONObject templateCase = new JSONObject(jsonObject.getJSONArray(business).get(0).toString());
            String templateName = templateCase.getString("templateName");
            JSONArray jsonArray = templateCase.getJSONArray("mock");
            int caseCount = jsonArray.length();
            //如果原本case数就很少 || case数少于组数，不用分组
            if (caseCount <= 6 || caseCount <= groupCount) {
                result.put(jsonObject);
                return result;
            }
            //每个分组有{size}条case，normal类型的不算在内
            int size = jsonArray.length() / groupCount;
            if (size > 0) {
                for (int i = 0; i < caseCount; i++) {
                    JSONObject caseItem = jsonArray.getJSONObject(i);

                    if (i == 0) {
                        JSONObject groupItem = new JSONObject();
                        JSONArray businessItem = new JSONArray();
                        JSONArray mockItem = new JSONArray();
                        mockItem.put(caseItem);
                        JSONObject templateItem = new JSONObject();
                        templateItem.put("templateName",templateName);
                        templateItem.put("mock",mockItem);
                        businessItem.put(templateItem);
                        groupItem.put(business,businessItem);
                        result.put(groupItem);
                        continue;
                    }

                    //先计算当前case应该属于第几个分组
                    int iPosition = i / size;

                    //边界值
                    if (i % size == 0) {
                        if (iPosition == 0) {
                            result.getJSONObject(0).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                        } else {
                            result.getJSONObject(iPosition-1).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                        }
                    } else if (result.length() > iPosition) {    //该分组已创建
                        result.getJSONObject(iPosition).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                    } else {
                        if (result.length() == groupCount) {
                            //分组数已达到目标，剩余case插入最后一个分组即可
                            result.getJSONObject(iPosition-1).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                        } else {
                            //先创建分组
                            JSONObject groupItem = new JSONObject();
                            JSONArray businessItem = new JSONArray();
                            JSONArray mockItem = new JSONArray();
                            mockItem.put(jsonArray.getJSONObject(0));
                            mockItem.put(caseItem);
                            JSONObject templateItem = new JSONObject();
                            templateItem.put("templateName", templateName);
                            templateItem.put("mock", mockItem);
                            businessItem.put(templateItem);
                            groupItem.put(business, businessItem);
                            result.put(groupItem);
                        }
                    }

                }
                return result;
            }

        }
        result.put(jsonObject);
        return result;
    }



    public static void main(String[] args) throws InterruptedException {
//        CompatilityJob compatilityJob = new CompatilityJob();
//        compatilityJob.setType(1);
//        compatilityJob.setId(8888);
//        compatilityJob.setPlatform(platformIOS);
//        String mockRule = "{\"mine\":[{\"templateName\":\"mineOrderWithShipped\",\"mock\":[{\"type\":\"normal\",\"mockId\":343029},{\"type\":\"abnormal\",\"mockId\":343030},{\"type\":\"abnormal\",\"mockId\":343031},{\"type\":\"abnormal\",\"mockId\":343032},{\"type\":\"abnormal\",\"mockId\":343033},{\"type\":\"abnormal\",\"mockId\":343034},{\"type\":\"abnormal\",\"mockId\":343035},{\"type\":\"abnormal\",\"mockId\":343036},{\"type\":\"abnormal\",\"mockId\":343037},{\"type\":\"abnormal\",\"mockId\":343038},{\"type\":\"abnormal\",\"mockId\":343039},{\"type\":\"abnormal\",\"mockId\":343040},{\"type\":\"abnormal\",\"mockId\":343041},{\"type\":\"abnormal\",\"mockId\":343042},{\"type\":\"abnormal\",\"mockId\":343043},{\"type\":\"abnormal\",\"mockId\":343044},{\"type\":\"abnormal\",\"mockId\":343045},{\"type\":\"abnormal\",\"mockId\":343046},{\"type\":\"abnormal\",\"mockId\":343047},{\"type\":\"abnormal\",\"mockId\":343048},{\"type\":\"abnormal\",\"mockId\":343049},{\"type\":\"abnormal\",\"mockId\":343050}]}]}";
//        compatilityJob.setMockRule(mockRule);
//        new SubmitJobUtil().dynamicSmokeJob(compatilityJob);


        String a = "1234_2234_3234_";
        a = a .substring(0,a.length()-1);
        System.out.println(a);

    }


}
