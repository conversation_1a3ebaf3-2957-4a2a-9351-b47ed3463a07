package com.sankuai.mdp.compass.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * Created by xieyongrui on 2020/5/21.
 */
public class TTUtil {
    private static String createdUrl = "https://ticket.vip.sankuai.com/api/1.0/ticket/fast/create"; //线下url：http://ticket.ee.test.sankuai.com/api/1.0/ticket/fast/create
    private static String templateUrl = "https://cti.vip.sankuai.com/api/1.0/template/item/";

//    private static String createdUrl = "http://ticket.ee.test.sankuai.com/api/1.0/ticket/fast/create";
//    private static String templateUrl = "http://cti.ee.test.sankuai.com/api/1.0/template/item/";

    private static String USERNAME = "xieyongrui";

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();

    public static String createdTT(JsonObject bodyObj){
        MediaType MEDIA_TYPE_TEXT = MediaType.parse("application/json;charset=UTF-8");
        String body;
        if (bodyObj != null) {
            body = bodyObj.toString();
        } else {
            body = "";
        }
        Request request = new Request.Builder()
                .url(createdUrl)
                .addHeader("Authorization", TokenUtil.getToken("tt_token"))
                .addHeader("Content-Type", "application/json")
                .addHeader("USERNAME", USERNAME)
                .post(RequestBody.create(MEDIA_TYPE_TEXT, body))
                .build();
        String response = executeRemotePostCall(request);
        System.out.print(response);
        return response;
    }

    public static String executeRemotePostCall(Request request) {
        Response response = null;
        String content;
        try {
            response = client.newCall(request).execute();
            content = response.body().string();
        } catch (IOException e) {
            content = "{\"errors\": \"IOException\"}";
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return content;
    }

    public String getTemplate(String itemId) {
        Request request = new Request.Builder()
                .url(templateUrl+itemId)
                .addHeader("Authorization", TokenUtil.getToken("tt_token"))
                .addHeader("Content-Type", "application/json")
                .addHeader("USERNAME", USERNAME)
                .get()
                .build();
        String response = executeRemotePostCall(request);
        System.out.print(response);
        return response;
    }

    public void create(String name, String desc, String itemId){
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("itemId",itemId);
        jsonObject.addProperty("name",name);
        jsonObject.addProperty("desc",desc);
        jsonObject.addProperty("ticketType","事件");
        jsonObject.addProperty("sla","S4");
        jsonObject.addProperty("assigned","");
//        jsonObject.addProperty("permission","public");
        JsonArray cc = new JsonArray();
//        cc.add("xieyongrui");
//        jsonObject.add("cc", cc);
        createdTT(jsonObject);
    }
}
