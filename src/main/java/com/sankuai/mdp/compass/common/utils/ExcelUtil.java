package com.sankuai.mdp.compass.common.utils;

import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xieyongrui on 2020/2/21.
 */
public class ExcelUtil {
    private static String DEFAULT_SHEET_NAME = "默认名称";

    public static void main(String[] args ) {
        List<List<Object>> data = new ArrayList<>();
        List a = new ArrayList<>();
        a.add("wangming");
        a.add("32");
        a.add("beijing");
        List<Object> b = new ArrayList<>();
        b.add("lihua");
        b.add("30");
        b.add("shanghai");
        data.add(a);
        data.add(b);
        List<Object> cols = new ArrayList<>();
        cols.add("name");
        cols.add("age");
        cols.add("address");
        try {
            createAExcel(cols, data, "/Users/<USER>/Code/compass/test1.xlsx");
        } catch (Exception e){
            System.out.print(e);
        }
    }


    public static Boolean createAExcel(List<Object> cols, List<List<Object>> rows, String filePath) throws IOException {
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFCellStyle style = createCellStyle(wb);
        XSSFSheet sheet = wb.createSheet(DEFAULT_SHEET_NAME);
        Integer columnNumber = cols.size();

        //这里的是表格内像素的转换公式,例如第列宽24像素,第二列宽20像素等等..
        //设置列宽：http://blog.csdn.net/duqian42707/article/details/51491312

//        //合并第一行
//        CellRangeAddress region1 = new CellRangeAddress(0, 0, (short) 0, (short) 3); //参数1：起始行 参数2：终止行 参数3：起始列 参数4：终止列
//        sheet.addMergedRegion(region1);//合并单元格
//        XSSFCell cell_= sheet.createRow(0).createCell(0);
//        cell_.setCellStyle(style);
//        cell_.setCellValue("{可设置title}");
//        //输出表头,即第一行
        XSSFRow row = null;
        XSSFCell cell0_;
        XSSFRow row0 = sheet.createRow(0);
        for (int i = 0; i < columnNumber; i++) {
            XSSFCell currentCell = row0.createCell(i);
            currentCell.setCellStyle(style);
            String value = cols.get(i).toString();
            if ("" == value) {
                value = "null";
            }
            currentCell.setCellValue(value);
        }

        for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
            row = sheet.createRow(rowIndex + 1);
            List<Object> rowData = rows.get(rowIndex);
            Integer colNum = rowData.size();
            for (int colIndex = 0; colIndex < colNum; colIndex++) {
                cell0_ = row.createCell(colIndex);
                cell0_.setCellStyle(style);
                String value = rowData.get(colIndex).toString();
                if ("" == value) {
                    value = "null";
                }
                cell0_.setCellValue(value);
            }
        }

        try{
            FileOutputStream outputStream = new FileOutputStream(filePath);
            wb.write(outputStream);
            wb.close();
            outputStream.flush();
            outputStream.close();
            return true;
        } catch (FileNotFoundException e){
            System.err.println("获取不到位置");
            e.printStackTrace();
        } catch (IOException e){
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 设置导出Excel表格样式
     * @param workbook 表格
     * @return 样式
     */
    private static XSSFCellStyle createCellStyle(XSSFWorkbook workbook){
        XSSFFont font=workbook.createFont();
        //在对应的workbook中新建字体
        font.setFontName("微软雅黑");
        //字体微软雅黑
        font.setFontHeightInPoints((short)11);
        //设置字体大小
        XSSFCellStyle style=workbook.createCellStyle();
        //新建Cell字体
        style.setFont(font);
        return style;
    }

}
