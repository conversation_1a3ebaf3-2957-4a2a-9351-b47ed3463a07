package com.sankuai.mdp.compass.config;

import com.dianping.zebra.group.jdbc.GroupDataSource;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.beans.PropertyVetoException;

/**
 * Created by xieyongrui on 2020/1/2.
 */

@Configuration
public class DataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceConfig.class);
    private String JdbcRef;

    /**
     * 生成与spring-dao.xml对应的bean dataSource
     *
     * @return
     * @throws PropertyVetoException
     */
    @Bean(name = "dataSource")
    public DataSource dataSource(){
        GroupDataSource groupDataSource = new GroupDataSource();

        if(EnvUtil.isOnline()){
            JdbcRef = "qa_dashboard_product";
        }else{
            JdbcRef = "platformptqa_qa_dashboard_test";
        }
        logger.error("===========JdbcRef===========:" + JdbcRef);

        try {
            groupDataSource.setJdbcRef(JdbcRef);
//            groupDataSource.setExtraJdbcUrlParams("useSSL=false");
//            groupDataSource.setLazyInit(true);
            groupDataSource.setMinPoolSize(5);
            groupDataSource.setMaxPoolSize(20);
            groupDataSource.setInitialPoolSize(5);
            groupDataSource.setCheckoutTimeout(1000);
//            groupDataSource.setJdbcRefMaxInitialCount(3);
            groupDataSource.init();
        } catch (Exception e) {
            logger.error("======================",e);
        }
        return groupDataSource;
    }

}