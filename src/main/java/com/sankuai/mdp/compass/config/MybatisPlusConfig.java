package com.sankuai.mdp.compass.config;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer;
import org.apache.ibatis.plugin.Interceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.io.IOException;

@Configuration
public class MybatisPlusConfig {

    private static final Logger logger = LoggerFactory.getLogger(MybatisPlusConfig.class);

    //第2步：配置SqlSessionFactoryBean
    @Bean(name="zebraSqlSessionFactory")
    public MybatisSqlSessionFactoryBean sqlSessionFactory(@Qualifier("dataSource") DataSource groupDataSource) throws IOException {
        MybatisSqlSessionFactoryBean ssfb = new MybatisSqlSessionFactoryBean();
        ssfb.setDataSource(groupDataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        ssfb.setMapperLocations(resolver.getResources("classpath*:/mapper/*/*.xml"));

        MybatisConfiguration mc = new MybatisConfiguration();
        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        mc.setMapUnderscoreToCamelCase(true);// 数据库和java都是驼峰，就不需要
        ssfb.setConfiguration(mc);
        ssfb.setTypeAliasesPackage("com.sankuai.mdp.compass.entity,com.sankuai.mdp.compass.build.entity,com.sankuai.mdp.compass.conan.entity");
        //添加插件
        ssfb.setTypeEnumsPackage("com.sankuai.mdp.compass.riskSQLScan.enums");
        ssfb.setPlugins(new Interceptor[]{paginationInterceptor()});
        return ssfb;
    }

    //第3步：配置ZebraMapperScannerConfigurer
    @Bean
    public ZebraMapperScannerConfigurer mapperScannerConfigurer() throws IOException {
        ZebraMapperScannerConfigurer configurer = new ZebraMapperScannerConfigurer();
        configurer.setSqlSessionFactoryBeanName("zebraSqlSessionFactory");

        configurer.setBasePackage("com.sankuai.mdp.compass.mapper,com.sankuai.mdp.compass.build.mapper,com.sankuai.mdp.compass.conan.mapper");
        return configurer;
    }

    /**
     * 分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

}
