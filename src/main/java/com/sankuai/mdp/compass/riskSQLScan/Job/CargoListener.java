package com.sankuai.mdp.compass.riskSQLScan.Job;/* *************
 * @author: liuYang359
 * @date: 2023/11/21 20:53
 * @description:
 */

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.mdp.compass.riskSQLScan.service.CargoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@CraneConfiguration
public class CargoListener {
    @Autowired
    private CargoService cargoService;

    @Crane("com.sankuai.compass.riskSQLScan.cargoLinstener")
    public void getSwimlaneInfo(){
        cargoService.getSwimlaneInfo();
    }
}
