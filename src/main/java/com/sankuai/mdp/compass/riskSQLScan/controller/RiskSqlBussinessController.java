package com.sankuai.mdp.compass.riskSQLScan.controller;/* *************
 * @author: liuYang359
 * @date: 2023/9/28 15:41
 * @description:
 */

import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.DeleteDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlAuditOfflineTaskPO;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlAuditOfflineTaskService;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlScanBussinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/compass/api/riskSql/business")
public class RiskSqlBussinessController {
    @Autowired
    RiskSqlScanBussinessService riskSqlScanBussinessService;
    @Autowired
    RiskSqlAuditOfflineTaskService riskSqlAuditOfflineTaskService;
    @RequestMapping("/getBusinessByMis")
    public Resp getBusinessByMis(@RequestParam("mis") String mis){

        return Resp.success(riskSqlScanBussinessService.getListbyMis(mis));
    }
    @RequestMapping("/getSqlList")
    public Resp getALL(@RequestParam("mis") String mis,@RequestParam("id") int id){
        return Resp.success(riskSqlScanBussinessService.getAll(mis,id));
    }

    @RequestMapping("/getBusinessByMisAndId")
    public Resp getBusinessByMisAndId(@RequestParam("mis") String mis,@RequestParam("id") int id){
        return Resp.success(riskSqlScanBussinessService.getListbyMisAndId(mis,id));
    }
    @RequestMapping("/delete")
    public Resp deleteAppkey(@RequestParam("id") Integer id){

        return Resp.success(DeleteDTO.builder().deleteNum(riskSqlScanBussinessService.deleteAllSqlbyId(id)).build());
    }

    @RequestMapping("/businessDuplicate")
    public Resp businessDuplicate(@RequestParam("businessName") String name){
        return Resp.success(riskSqlScanBussinessService.businessDuplicate(name));
    }
    @RequestMapping("/businessDuplicateV2")
    public Resp businessDuplicateV2(@RequestParam("businessName") String name,@RequestParam("id") Integer id){
        return Resp.success(riskSqlScanBussinessService.businessDuplicateV2(name,id));
    }
    @RequestMapping("/createBusiness")
    public Resp createBusiness(@RequestParam("businessName") String name,@RequestParam("admins")String admins){
        return Resp.success(riskSqlScanBussinessService.createBusiness(name, admins));
    }

    @RequestMapping("/deleteBusiness")
    public Resp deleteBusiness(@RequestParam("id") Integer id){

        return Resp.success(DeleteDTO.builder().deleteNum(riskSqlScanBussinessService.deleteBusinessbyId(id)).build());
    }
    @RequestMapping("/updateBusiness")
    public Resp updateBusiness(@RequestParam("id") Integer id,@RequestParam("businessName") String businessName,@RequestParam("adminList") String adminList){

        return Resp.success(riskSqlScanBussinessService.updateBusinessById(id,businessName,adminList));
    }
    @RequestMapping("/test")
    public Resp test(){

        //riskSqlAuditOfflineTaskService.insert(RiskSqlAuditOfflineTaskPO.builder().createTime(new Date()).appkeyId(1).swimlane("com.sankuai").
              //  build());
        riskSqlAuditOfflineTaskService.delete(RiskSqlAuditOfflineTaskPO.builder().id(7).build());

                return Resp.success(riskSqlAuditOfflineTaskService.getAllTasks());
    }

}
