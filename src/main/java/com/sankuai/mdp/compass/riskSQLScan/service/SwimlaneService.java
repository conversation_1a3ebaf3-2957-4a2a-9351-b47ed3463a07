package com.sankuai.mdp.compass.riskSQLScan.service;

import com.sankuai.mdp.compass.riskSQLScan.entity.vo.SwimlaneInfoVO;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/6/7
 * @time: 上午9:39
 */
public interface SwimlaneService {
    List<String> getAppkeyByMis(String mis);

    List<SwimlaneInfoVO> getSwimlaneList(String mis,String appkey);

    Integer deleteSwimlane(Long id);

    Integer closeSwimlane(Long id);
}
