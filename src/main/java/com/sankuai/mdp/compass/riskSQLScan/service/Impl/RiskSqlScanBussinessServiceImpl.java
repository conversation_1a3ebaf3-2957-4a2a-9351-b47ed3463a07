package com.sankuai.mdp.compass.riskSQLScan.service.Impl;/* *************
 * @author: liuYang359
 * @date: 2023/10/9 15:28
 * @description:
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.RiskSqlScanListDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlPo;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanBusinessInfoPO;

import com.sankuai.mdp.compass.riskSQLScan.enums.DeleteTypeEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanDeletedTypeEnum;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanAppKeyMapper;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanBusinessInfoMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlScanAppkeyService;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlScanBussinessService;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class RiskSqlScanBussinessServiceImpl implements RiskSqlScanBussinessService {
    @Autowired
    RiskSqlScanBusinessInfoMapper riskSqlScanBusinessInfoMapper;
    @Autowired
    RiskSqlScanAppKeyMapper riskSqlScanAppKeyMapper;
    @Autowired
    RiskSqlScanAppkeyService riskSqlScanAppkeyService;

    @Autowired
    RiskSqlService riskSqlService;


    @Override
    public List<RiskSqlScanBusinessInfoPO> getListbyMis(String mis) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("admin_mis_list", mis);
        queryWrapper.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        return riskSqlScanBusinessInfoMapper.selectList(queryWrapper);
    }
    @Override
    public RiskSqlScanBusinessInfoPO getOnebyMisAndId(String mis, int id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("admin_mis_list", mis);
        queryWrapper.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        queryWrapper.eq("id", id);
        return riskSqlScanBusinessInfoMapper.selectOne(queryWrapper);
    }

    @Override
    public int deleteAllSqlbyId(int id) {/*
     * @param id:Appkeyid

     * @return: int
     * @author:
     * @date:
     * @description:
     */
        int deleteNum=0;
        try {
//            //业务软删
//            UpdateWrapper<RiskSqlScanBusinessInfoPO> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.eq("id", id);//作为条件
//            updateWrapper.set("deleted", DeleteTypeEnum.DELETED.getCode());//设置想要更新的字段
//            riskSqlScanBusinessInfoMapper.update(null, updateWrapper);
            //
            //sql 软删
            deleteNum= riskSqlService.deletebyAppkeyId(id);
            //appkey 软删  todo 不写事务，不用注解原因-后续需要设置唯一键

            riskSqlScanAppkeyService.deleteById(id);

        } catch (Exception E) {
            E.printStackTrace();
            return deleteNum;
        }
        return deleteNum;
    }

    @Override
    public List<RiskSqlScanListDTO> getAll(String MIS, int id) {
        List<RiskSqlScanListDTO> result = new ArrayList<>();
        // 0 表示查询所有仅查询接口使用，不做枚举。
        if (id == 0) {
            List<RiskSqlScanBusinessInfoPO> businessInfoPOList = getListbyMis(MIS);
            for (RiskSqlScanBusinessInfoPO index : businessInfoPOList) {
                List<RiskSqlScanAppKeyPO> appKeyList = riskSqlScanAppkeyService.selectByBusinessID(index.getId(), RiskSqlScanDeletedTypeEnum.ONLINE.getType());
                for (RiskSqlScanAppKeyPO appkey : appKeyList) {
                    List<RiskSqlPo> riskSqlPoList = riskSqlService.getsqlByBusinessAndAppKey(index.getId(), appkey.getId(), RiskSqlScanDeletedTypeEnum.ONLINE.getType());
                    if (riskSqlPoList != null)
                        result.add(new RiskSqlScanListDTO(index, appkey, riskSqlPoList));
                    else
                        result.add(new RiskSqlScanListDTO(index, appkey, null));
                }
//                if (appKeyList == null||appKeyList.size()==0) {
//                    result.add(new RiskSqlScanListDto(index, null, null));
//                }
            }

        } else {
            RiskSqlScanBusinessInfoPO businessInfoPO = getOnebyMisAndId(MIS, id);
            List<RiskSqlScanAppKeyPO> appKeyList = riskSqlScanAppkeyService.selectByBusinessID(businessInfoPO.getId(), RiskSqlScanDeletedTypeEnum.ONLINE.getType());
            for (RiskSqlScanAppKeyPO appkey : appKeyList) {
                List<RiskSqlPo> riskSqlPoList = riskSqlService.getsqlByBusinessAndAppKey(businessInfoPO.getId(), appkey.getId(), RiskSqlScanDeletedTypeEnum.ONLINE.getType());
                if (riskSqlPoList != null)
                    result.add(new RiskSqlScanListDTO(businessInfoPO, appkey, riskSqlPoList));
                else
                    result.add(new RiskSqlScanListDTO(businessInfoPO, appkey, null));
            }
//            if (appKeyList == null||appKeyList.size()==0) {
//                result.add(new RiskSqlScanListDto(businessInfoPO, null, null));
//            }
        }
        if(result!=null&&result.size()>1)
        {
            Collections.sort(result);
        }
        return result;
    }

    @Override
    public List<RiskSqlScanListDTO> getListbyMisAndId(String mis, int id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("admin_mis_list", mis);
        queryWrapper.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        if (id!=0)
        queryWrapper.eq("id", id);
        return riskSqlScanBusinessInfoMapper.selectList(queryWrapper);
    }

    @Override
    public boolean businessDuplicate(String name) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("business_name", name);
        queryWrapper.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        return riskSqlScanBusinessInfoMapper.selectList(queryWrapper).size()>0?false:true;

    }

    @Override
    public int createBusiness(String name, String admins) {
        RiskSqlScanBusinessInfoPO riskSqlScanBusinessInfoPO=RiskSqlScanBusinessInfoPO.builder()
                                .businessName(name).adminMisList(admins).deleted(DeleteTypeEnum.ONLINE.getCode())
                                .build();
        return riskSqlScanBusinessInfoMapper.insert(riskSqlScanBusinessInfoPO);
    }

    @Override
    public int deleteBusinessbyId(Integer id) {
        int deleteNum=0;
        try {

            riskSqlScanBusinessInfoMapper.updateById(RiskSqlScanBusinessInfoPO.builder().id(id).deleted(DeleteTypeEnum.DELETED.getCode()).build());
            deleteNum= riskSqlService.deletebyBusinessId(id);
            riskSqlScanAppkeyService.deleteByBusinessId(id);

        } catch (Exception E) {
            E.printStackTrace();
            return deleteNum;
        }
        return deleteNum;
    }

    @Override
    public boolean businessDuplicateV2(String name,Integer id) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("business_name", name);
        queryWrapper.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        List<RiskSqlScanBusinessInfoPO>resultList=riskSqlScanBusinessInfoMapper.selectList(queryWrapper);
        if(resultList.size()>1)
            return false;
        else if(resultList.size()==1)
            {
                RiskSqlScanBusinessInfoPO businessInfoPO = resultList.get(0);
                if (businessInfoPO.getId()==id) {
                    return true;
                } else {
                    return false;
                }
            }
        else if(resultList.size()==0)
        {
            return true;
        }
        else
            return false;

    }

    @Override
    public boolean updateBusinessById(Integer id, String businessName, String adminList) {
        try{
        riskSqlScanBusinessInfoMapper.updateById(RiskSqlScanBusinessInfoPO.builder().id(id).businessName(businessName).adminMisList(adminList).build());
        }
        catch (Exception e)
        {
            return false;
        }
        return true;
    }
}
