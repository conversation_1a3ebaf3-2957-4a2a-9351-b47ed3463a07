package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.RDSListDiffSqlByAppkeyActionRequest;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.RDSListDiffSqlByAppkeyActionResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/28
 * @time: 下午5:08
 */
@Component
public class DiffSqlInvoker extends AbstractRdsApiInvoker<RDSListDiffSqlByAppkeyActionRequest, List<RDSListDiffSqlByAppkeyActionResponse>> {
    @Override
    protected String getAction() {
        return "ListDiffSqlByAppkeyAction";
    }

    @Override
    protected List<RDSListDiffSqlByAppkeyActionResponse> convert2Model(String data) {
        return JSON.parseArray(data, RDSListDiffSqlByAppkeyActionResponse.class);
    }
}
