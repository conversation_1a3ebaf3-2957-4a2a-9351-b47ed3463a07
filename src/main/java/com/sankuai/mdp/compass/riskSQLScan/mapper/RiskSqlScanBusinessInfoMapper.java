package com.sankuai.mdp.compass.riskSQLScan.mapper;/* *************
 * @author: liuYang359
 * @date: 2023/9/26 11:01
 * @description:
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanBusinessInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface RiskSqlScanBusinessInfoMapper extends BaseMapper<RiskSqlScanBusinessInfoPO> {

    @Select("select business_name from risk_sql_scan_bussiness where id = #{businessId}")
    String selectBusinessLineByBusinessId(String businessId);
}
