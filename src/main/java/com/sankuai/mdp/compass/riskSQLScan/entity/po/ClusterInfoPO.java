package com.sankuai.mdp.compass.riskSQLScan.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("cluster_info")
public class ClusterInfoPO {
    @TableId(value = "id",type =  IdType.AUTO)
    Long id;

    @TableField(value = "cluster_name")
    private String clusterName;

    @TableField(value = "is_process")
    private Integer isProcess;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time")
    Date updateTime;

    @TableField(value = "appkey")
    private String appkey;
}
