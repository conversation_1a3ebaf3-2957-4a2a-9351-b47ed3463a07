package com.sankuai.mdp.compass.riskSQLScan.entity.po;/* *************
 * @author: liuYang359
 * @date: 2023/10/10 15:09
 * @description:
 */

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanDeletedTypeEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.yetus.audience.InterfaceStability;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Objects;

@Data
@Builder
@TableName("risk_sql_info")
public class RiskSqlPo {
    @TableId(value = "id",type =  IdType.AUTO)
    Integer id;
    @TableField("sql_template")
    String sqlTemplate;

    @TableField("risk_sql")
    String riskSql;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;

    @TableField("level")
    RiskSqlScanRiskLevelEnum level;
    @TableField("business_id")
    Integer BusinessId;
    @TableField("app_key_id")
    Integer AppKeyId;


    // @see com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanDeletedTypeEnum
    Integer deleted;

    String swimlane;

    @TableField(value = "risk_reason")
    String riskReason;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RiskSqlPo riskSqlPo = (RiskSqlPo) o;
        return id.equals(riskSqlPo.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
