package com.sankuai.mdp.compass.riskSQLScan.service;/* *************
 * @author: liuYang359
 * @date: 2023/12/27 19:39
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlAuditOfflineTaskPO;

import java.util.List;

public interface RiskSqlAuditOfflineTaskService {

    List<RiskSqlAuditOfflineTaskPO> getAllTasks();
    int insert(RiskSqlAuditOfflineTaskPO riskSqlAuditOfflineTaskPO);
    int  delete(RiskSqlAuditOfflineTaskPO riskSqlAuditOfflineTaskPO);
}
