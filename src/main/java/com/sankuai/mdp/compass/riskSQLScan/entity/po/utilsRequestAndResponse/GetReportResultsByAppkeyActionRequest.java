package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRDSRuleNameEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @author: niu<PERSON>ech<PERSON>
 * @date: 2024/5/29
 * @time: 上午11:37
 */
@Data
@Builder
public class GetReportResultsByAppkeyActionRequest {
    private String username;
    private String appkey;
    private int taskId;
    private boolean filterWhitelist;
    private int pageNum;
    private int pageSize;
    private RiskSqlScanRiskLevelEnum riskLevel;
    private RiskSqlScanRDSRuleNameEnum ruleName;
}
