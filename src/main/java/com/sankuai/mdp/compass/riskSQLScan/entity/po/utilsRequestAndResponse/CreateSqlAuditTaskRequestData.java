package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/28
 * @time: 下午5:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateSqlAuditTaskRequestData {
    private String appkey;
    private List<SQLSamples> sqlSamples;
}
