package com.sankuai.mdp.compass.riskSQLScan.controller;

import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.*;
import com.sankuai.mdp.compass.riskSQLScan.service.RdsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 下午4:55
 */
@RestController
@RequestMapping("/compass/api/riskSql/rds")
public class RdsController {
    @Autowired
    RdsService rdsService;

    @RequestMapping("/listFullSqlByAppkey")
    public Resp listFullSqlByAppkey(@RequestBody RDSListFullSqlByAppkeyActionRequest request) {
        return Resp.success(rdsService.listFullSqlByAppkey(request));
    }

    @RequestMapping("/listDiffSqlByAppkey")
    public Resp listDiffSqlByAppkey(@RequestBody RDSListDiffSqlByAppkeyActionRequest request) {
        return Resp.success(rdsService.listDiffSqlByAppkey(request));
    }
    @RequestMapping("/userCreateSqlAuditTask")
    public Resp createSqlAuditTask(@RequestBody UserCreateSqlAuditTaskByUserActionRequest request) {
        return Resp.success(rdsService.createSqlAuditTask(request));
    }
    @RequestMapping("/userGetSqlAuditTask")
    public Resp getSqlAuditTask(@RequestParam String username,@RequestParam int taskId) {
        return Resp.success(rdsService.getSqlAuditTask(username,taskId));
    }
    @RequestMapping("/userGetSqlAuditResult")
    public Resp getSqlAuditResult(@RequestParam String username,@RequestParam int taskId) {
        return Resp.success(rdsService.getSqlAuditResult(username,taskId));
    }
    @RequestMapping("/getReportByOrgAction")
    public Resp getReportByOrgAction(@RequestBody GetReportByOrgActionRequest request) {
        return Resp.success(rdsService.getReportByOrgAction(request));
    }
    @RequestMapping("/getReportResultByOrgAction")
    public Resp getReportResultByOrgAction(@RequestBody GetReportResultsByOrgActionRequest request) {
        return Resp.success(rdsService.getReportResultByOrgAction(request));
    }
    @RequestMapping("/getReportByAppkeyAction")
    public Resp getReportByAppkeyAction(@RequestBody GetReportByAppkeyActionRequest request) {
        return Resp.success(rdsService.getReportByAppkeyAction(request));
    }
    @RequestMapping("/getreportResultByAppkeyAction")
    public Resp getReportResultByAppkeyAction(@RequestBody GetReportResultsByAppkeyActionRequest request) {
        return Resp.success(rdsService.getReportResultByAppkeyAction(request));
    }

}