package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/29
 * @time: 上午10:33
 */
@Data
@Builder
public class GetReportByOrgActionRequest {
    private String userName;
    private int orgId;
    private String beginTime;
    private String endTime;
    private boolean filterWhitelist;
}
