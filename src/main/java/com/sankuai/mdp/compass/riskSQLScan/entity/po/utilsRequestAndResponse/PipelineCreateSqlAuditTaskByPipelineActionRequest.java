package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/28
 * @time: 下午5:24
 */
@Data
@Builder
public class PipelineCreateSqlAuditTaskByPipelineActionRequest {
    private String submitter;
    private String receiver;
    private int source;
    private String sourceLink;
    private List<CreateSqlAuditTaskRequestData> sqls;
}
