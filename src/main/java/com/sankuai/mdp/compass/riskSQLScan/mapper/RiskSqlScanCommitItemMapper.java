package com.sankuai.mdp.compass.riskSQLScan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanCommitItemPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqls;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午3:36
 */
@Mapper
public interface RiskSqlScanCommitItemMapper extends BaseMapper<RiskSqlScanCommitItemPO> {
    @Select("SELECT sqlTpl,riskSql FROM risk_sql_scan_commit_item WHERE id = #{commitId}")
    RiskSqls selectSqlTpl(Long commitId);
    @Select("SELECT id FROM risk_sql_scan_commit_item WHERE commitId = #{commitId} AND sqlTpl = #{sqlTpl} AND riskSql = #{riskSql} AND dbName = #{dbName} AND clusterName = #{clusterName}")
    Long selectID1(long commitId, String sqlTpl, String riskSql, String dbName, String clusterName);
}
