package com.sankuai.mdp.compass.riskSQLScan.service;

import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.riskSQLScan.entity.vo.ResultInfoVO;

import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/6/7
 * @time: 下午5:32
 */
public interface ResultService {
    List<String> getAppkeyByMis(String mis);

    List<String> getSwimlaneByMis(String mis);

    List<ResultInfoVO> getResultList(String mis, String appkey, String swimlane);

}
