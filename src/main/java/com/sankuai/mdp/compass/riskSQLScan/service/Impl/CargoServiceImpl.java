package com.sankuai.mdp.compass.riskSQLScan.service.Impl;/* *************
 * @author: liuYang359
 * @date: 2023/12/14 11:52
 * @description:
 */

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.CargoRequestDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.CargoResponseDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanSwimlaneInfoPO;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanAppKeyMapper;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanSwimlaneInfoMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.CargoService;
import jdk.nashorn.internal.scripts.JS;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;

import javax.xml.crypto.Data;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
public class CargoServiceImpl implements CargoService {

    @Autowired
    private RiskSqlScanSwimlaneInfoMapper riskSqlScanSwimlaneInfoMapper;
    @Autowired
    private RiskSqlScanAppKeyMapper riskSqlScanAppKeyMapper;

    @Override
    public void getSwimlaneInfo() {
        List<String> appkeyList = riskSqlScanAppKeyMapper.selectAppKeyList();

        for (String appkey : appkeyList) {
            try {
                HttpURLConnection connection = createHttpURLConnection(appkey);
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    handleResponse(connection);
                } else {
                    System.out.println("Request failed. Response Code: " + responseCode);
                }
                connection.disconnect();
            } catch (IOException e) {
                handleException(e);
            }
        }
    }

    private void handleResponse(HttpURLConnection connection) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            CargoResponseDTO cargoResponseDTO = JSON.parseObject(response.toString(), CargoResponseDTO.class);
            if (cargoResponseDTO != null && "success".equals(cargoResponseDTO.getStatus())) {
                processCargoResponseData(cargoResponseDTO.getData());
            }
        }
    }

    private void processCargoResponseData(List<CargoResponseDTO.CargoResponseData> cargoResponseDataList) {
        for (CargoResponseDTO.CargoResponseData cargoResponseData : cargoResponseDataList) {
            if (cargoResponseData.getMessage() == null || cargoResponseData.getMessage().getSwimlane() == null
                    || cargoResponseData.getMessage().getSwimlane().trim().isEmpty()) {
                continue;
            }
            RiskSqlScanSwimlaneInfoPO riskSqlScanSwimlaneInfo = getRiskSqlScanSwimlaneInfoPO(cargoResponseData);
            if ("deploy".equals(cargoResponseData.getMessage().getAction())) {
                handleDeployAction(riskSqlScanSwimlaneInfo);
            } else if ("delete".equals(cargoResponseData.getMessage().getAction())) {
                handleDeleteAction(riskSqlScanSwimlaneInfo);
            }
        }
    }

    private void handleDeployAction(RiskSqlScanSwimlaneInfoPO riskSqlScanSwimlaneInfo) {
        QueryWrapper<RiskSqlScanSwimlaneInfoPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_key", riskSqlScanSwimlaneInfo.getAppKey());
        queryWrapper.eq("swimlane", riskSqlScanSwimlaneInfo.getSwimlane());
        if (riskSqlScanSwimlaneInfoMapper.selectList(queryWrapper).isEmpty()) {
            riskSqlScanSwimlaneInfoMapper.insert(riskSqlScanSwimlaneInfo);
        } else {
            UpdateWrapper<RiskSqlScanSwimlaneInfoPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("app_key", riskSqlScanSwimlaneInfo.getAppKey());
            updateWrapper.eq("swimlane", riskSqlScanSwimlaneInfo.getSwimlane());
            updateWrapper.set("update_time", getFormatDate());
            updateWrapper.set("isActive", 1);
            riskSqlScanSwimlaneInfoMapper.update(riskSqlScanSwimlaneInfo, updateWrapper);
        }
    }

    private void handleDeleteAction(RiskSqlScanSwimlaneInfoPO riskSqlScanSwimlaneInfo) {
        UpdateWrapper<RiskSqlScanSwimlaneInfoPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("app_key", riskSqlScanSwimlaneInfo.getAppKey());
        updateWrapper.eq("swimlane", riskSqlScanSwimlaneInfo.getSwimlane());
        updateWrapper.eq("isActive",1);
        updateWrapper.set("deleteTime", getFormatDate());
        updateWrapper.set("isActive", 0);
        riskSqlScanSwimlaneInfoMapper.update(riskSqlScanSwimlaneInfo, updateWrapper);
    }

    private void handleException(IOException e) {
        e.printStackTrace();
        // handle exception, log error, etc.
    }

    private static RiskSqlScanSwimlaneInfoPO getRiskSqlScanSwimlaneInfoPO(CargoResponseDTO.CargoResponseData cargoResponseDTOS) {
        RiskSqlScanSwimlaneInfoPO riskSqlScanSwimlaneInfo = new RiskSqlScanSwimlaneInfoPO();
        riskSqlScanSwimlaneInfo.setSwimlane(cargoResponseDTOS.getMessage().getSwimlane());
        riskSqlScanSwimlaneInfo.setIsDeleted(0);
        riskSqlScanSwimlaneInfo.setIsActive(1);
        riskSqlScanSwimlaneInfo.setEnv(cargoResponseDTOS.getEnv());
        riskSqlScanSwimlaneInfo.setHostName(cargoResponseDTOS.getMessage().getHostname());
        riskSqlScanSwimlaneInfo.setAppKey(cargoResponseDTOS.getMessage().getAppkey());
        riskSqlScanSwimlaneInfo.setIp(cargoResponseDTOS.getMessage().getIp());
        riskSqlScanSwimlaneInfo.setArea(cargoResponseDTOS.getMessage().getArea());
        riskSqlScanSwimlaneInfo.setBranch(cargoResponseDTOS.getMessage().getBranch());
        riskSqlScanSwimlaneInfo.setTargetHostName(cargoResponseDTOS.getMessage().getHostname());
        riskSqlScanSwimlaneInfo.setMis(cargoResponseDTOS.getMessage().getOp_user());
        riskSqlScanSwimlaneInfo.setType(cargoResponseDTOS.getType());
        riskSqlScanSwimlaneInfo.setCreateTime(getFormatDate());
        riskSqlScanSwimlaneInfo.setUpdateTime(getFormatDate());
        riskSqlScanSwimlaneInfo.setDeleteTime(getFormatDate());
        return  riskSqlScanSwimlaneInfo;
    }
    private static  Date getFormatDate(){
        LocalDateTime now = LocalDateTime.now();
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }

    public HttpURLConnection createHttpURLConnection(String appkey) throws IOException {
        URL url = new URL("http://api.cargo.sankuai.com/stack?type=op_history&appkey="+appkey);

        // 打开连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法
        connection.setRequestMethod("GET");
        return connection;
    }
}