package com.sankuai.mdp.compass.riskSQLScan.utils.Pipeline;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 下午3:25
 */
@Slf4j
public abstract class AbstractPipelineApiInvoke<PARAM,RESULT> implements PipelineApiInvoker<PARAM,RESULT>  {
    public static final String DATE_GMT_FORMAT_PATTERN = "E, dd MMM yyyy HH:mm:ss 'GMT'";

    public static final String DATE_TIME_ZONE = "GMT";

    //下面的这些属性值最好放在项目的配置文件里
    private String path = "https://dev.sankuai.com/api/v2/open/pipeline";

    private String version = "v1";

    private String sign = "POST /api/v2/open/gateway %s"+"\n"+"%s";

    private String authorization = "MWS %s:%s";

    //todo  accessKey和secretKey填写你在平台上申请的
    private String accessKey = "4Ey3aB";

    private String secretKey = "gO6XC8b2YBHgyQ4uahTePzNa0P3JxQ9dMDHFLfeU8jYlR71N075cVCiqtwSfijDL";

    //protected abstract String getAction();

    protected abstract RESULT convert2Model(String data);

    @Override
    public RESULT execute(PARAM param) {
        try {
            PipelineApiRequest pipelineApiRequest = new PipelineApiRequest(version, new JSONObject(param));
            HttpResponse response = HttpUtil.createPost(path)
                    .addHeaders(buildHeader())
                    .body(new JSONObject(pipelineApiRequest).toString())
                    .execute();
            String httpResult = new String(response.bodyBytes());
            PipelineApiResponse pipelineApiResponse = PipelineApiResponse.instanceOf(httpResult);
            if (Objects.isNull(pipelineApiResponse) || pipelineApiResponse.getCode() != 0) {
                log.error("Pipeline请求返回值异常, param: {}, result: {}", param, httpResult);
                throw new RuntimeException("Pipeline请求失败");
            }
            if (StringUtils.isBlank(pipelineApiResponse.getData()) || StringUtils.equalsIgnoreCase(pipelineApiResponse.getData(), "{}")) {
                log.warn("Pipeline请求返回值为空,param: {}, result: {}",param, httpResult);
                return null;
            }
            return convert2Model(pipelineApiResponse.getData());
        } catch (Exception e) {
            log.error("Pipeline请求异常, param: {}",param, e);
            throw new RuntimeException("Pipeline请求异常");
        }
    }
    private Map<String, String> buildHeader() throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_GMT_FORMAT_PATTERN, Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone(DATE_TIME_ZONE));
        String date = dateFormat.format(new Date());

        String strToSign = String.format(sign,date);
        String signature = hmacSha1(strToSign, secretKey);
        String strToAuthorization = String.format(authorization, accessKey, signature);

        Map<String, String> httpHeaders = new HashMap<>();
        httpHeaders.put("Date", date);
        httpHeaders.put("Authorization", strToAuthorization);

        return httpHeaders;
    }
    private String hmacSha1(String dataStr, String keyStr) {
        SecretKeySpec signingKey = new SecretKeySpec(keyStr.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        Mac mac;
        try {
            mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
        } catch (Exception e) {
            log.error("Mac 初始化异常,dataStr={}", dataStr, e);
            throw new RuntimeException("pipeline.open.api初始化Mac异常");
        }
        byte[] bytes = mac.doFinal(dataStr.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(bytes);
    }
}
