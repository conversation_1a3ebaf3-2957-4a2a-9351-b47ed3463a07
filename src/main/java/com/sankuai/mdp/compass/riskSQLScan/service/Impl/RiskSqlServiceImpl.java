package com.sankuai.mdp.compass.riskSQLScan.service.Impl;/* *************
 * @author: liuYang359
 * @date: 2023/10/12 17:06
 * @description:
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlPo;
import com.sankuai.mdp.compass.riskSQLScan.enums.DeleteTypeEnum;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanSqlMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RiskSqlServiceImpl implements RiskSqlService {
    @Autowired
    RiskSqlScanSqlMapper riskSqlScanSqlMapper;
    @Override
    public List<RiskSqlPo> getsqlByBusinessAndAppKey(Integer BusinessId, Integer appKeyId,int deleted) {
        QueryWrapper<RiskSqlPo>RiskSqlPodao=new QueryWrapper<>();
        RiskSqlPodao.eq("business_id",BusinessId);
        RiskSqlPodao.eq("app_key_id",appKeyId);
        RiskSqlPodao.eq("deleted",deleted);
        return riskSqlScanSqlMapper.selectSqlList(appKeyId,deleted,BusinessId);
    }

    @Override
    public int deletebyAppkeyId(Integer appKeyId) {
        UpdateWrapper<RiskSqlPo> RiskSqlPodao = new UpdateWrapper<>();
        RiskSqlPodao.eq("app_key_id", appKeyId);
        RiskSqlPodao.set("deleted", DeleteTypeEnum.DELETED.getCode());
        RiskSqlPo temp=RiskSqlPo.builder().build();
        temp.setAppKeyId(appKeyId);
        temp.setDeleted(DeleteTypeEnum.DELETED.getCode());
        return   riskSqlScanSqlMapper.update(temp,RiskSqlPodao);
    }
    public int deletebyBusinessId(Integer appKeyId) {
        UpdateWrapper<RiskSqlPo> RiskSqlPodao = new UpdateWrapper<>();
        RiskSqlPodao.eq("business_id", appKeyId);
        RiskSqlPodao.set("deleted", DeleteTypeEnum.DELETED.getCode());
        RiskSqlPo temp=RiskSqlPo.builder().build();
        temp.setAppKeyId(appKeyId);
        temp.setDeleted(DeleteTypeEnum.DELETED.getCode());
        return   riskSqlScanSqlMapper.update(temp,RiskSqlPodao);
    }


}
