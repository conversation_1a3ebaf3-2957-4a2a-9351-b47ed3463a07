//package com.sankuai.mdp.compass.riskSQLScan.test;
//
///**
// * @description:
// * @author: niuji<PERSON><PERSON>
// * @date: 2024/6/17
// * @time: 上午11:03
// */
//
//import com.sankuai.mdp.compass.riskSQLScan.Job.RdsListener;
//import com.sankuai.mdp.compass.riskSQLScan.service.RdsListenerService;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.junit.MockitoJUnitRunner;
//import static org.mockito.Mockito.*;
//
//@RunWith(MockitoJUnitRunner.class)
//public class RdsListenerTest {
//
//    @Mock
//    private RdsListenerService rdsListenerService;
//
//    @InjectMocks
//    private RdsListener rdsListener;
//
//    /**
//     * 测试 getChangedSql 方法正常情况
//     */
//    @Test
//    public void testGetChangedSqlNormal() throws Throwable {
//        // arrange
//        Mockito.doNothing().when(rdsListenerService).getChangedSql();
//
//        // act
//        rdsListener.getChangedSql();
//
//        // assert
//        Mockito.verify(rdsListenerService, Mockito.times(1)).getChangedSql();
//    }
//
//    /**
//     * 测试 getChangedSql 方法异常情况
//     */
//    @Test(expected = Exception.class)
//    public void testGetChangedSqlException() throws Throwable {
//        // arrange
//        Mockito.doThrow(new Exception()).when(rdsListenerService).getChangedSql();
//
//        // act
//        rdsListener.getChangedSql();
//
//        // assert 是通过 expected 来完成的，如果方法抛出了异常，则测试通过
//    }
//    /**
//     * 测试 getTaskResult 方法正常情况
//     */
//    @Test
//    public void testGetTaskResultNormal() throws Throwable {
//        // arrange
//        doNothing().when(rdsListenerService).getTaskResult();
//
//        // act
//        rdsListener.getTaskResult();
//
//        // assert
//        verify(rdsListenerService, times(1)).getTaskResult();
//    }
//
//    /**
//     * 测试 getTaskResult 方法异常情况
//     */
//    @Test(expected = Exception.class)
//    public void testGetTaskResultException() throws Throwable {
//        // arrange
//        doThrow(new Exception()).when(rdsListenerService).getTaskResult();
//
//        // act
//        rdsListener.getTaskResult();
//
//        // assert 是通过 expected 来完成的，期望此次调用抛出异常
//    }
//}
//
