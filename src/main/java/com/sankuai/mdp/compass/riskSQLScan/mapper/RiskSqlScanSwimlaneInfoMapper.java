package com.sankuai.mdp.compass.riskSQLScan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanSwimlaneInfoPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.vo.SwimlaneInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午3:42
 */
@Mapper
public interface RiskSqlScanSwimlaneInfoMapper extends BaseMapper<RiskSqlScanSwimlaneInfoPO> {
    @Select("SELECT DISTINCT app_key FROM risk_sql_scan_swimlane_info")
    List<String> selectAllAppkeys();
    @Select("SELECT DISTINCT app_key FROM risk_sql_scan_swimlane_info WHERE mis = #{mis}")
    List<String> selectAppkeysByMis(String mis);
    @Select("SELECT id,app_key,swimlane,mis,isActive,create_time,update_time,deleteTime FROM risk_sql_scan_swimlane_info WHERE isDeleted = 0 order by isActive DESC,update_time DESC")
    List<SwimlaneInfoVO> selectAllSwimlaneInfo();
    @Select("SELECT id,app_key,swimlane,mis,isActive,create_time,update_time,deleteTime FROM risk_sql_scan_swimlane_info WHERE mis = #{mis} AND app_key = #{appkey} AND isDeleted = 0  order by isActive DESC,update_time DESC")
    List<SwimlaneInfoVO> selectSwimlaneInfoByMisAndAppkey(String mis,String appkey);
    @Select("SELECT id,app_key,swimlane,mis,isActive,create_time,update_time,deleteTime FROM risk_sql_scan_swimlane_info WHERE mis = #{mis} AND isDeleted = 0  order by isActive DESC,update_time DESC")
    List<SwimlaneInfoVO> selectSwimlaneInfoByMis(String mis);
    @Select("SELECT id,app_key,swimlane,mis,isActive,create_time,update_time,deleteTime FROM risk_sql_scan_swimlane_info WHERE app_key = #{appkey} AND isDeleted = 0  order by isActive DESC,update_time DESC")
    List<SwimlaneInfoVO> selectSwimlaneInfoByAppkey(String appkey);
    @Select("SELECT * FROM risk_sql_scan_swimlane_info WHERE isActive = 1 AND isDeleted = 0")
    List<RiskSqlScanSwimlaneInfoPO> selecctActive();
}
