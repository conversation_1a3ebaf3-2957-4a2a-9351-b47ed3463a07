package com.sankuai.mdp.compass.riskSQLScan.enums;/* *************
 * @author: liuYang359
 * @date: 2023/10/10 15:15
 * @description:
 */

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum RiskSqlScanRiskLevelEnum {
    HIGH(0,"高风险，必须改进"),
    MID(1,"中风险，建议改进"),
    LOW(2,"低风险，潜在问题"),
    NO(3,"无风险");
    RiskSqlScanRiskLevelEnum(int code,String message)
    {
        this.code=code;
        this.message=message;
    }
    @EnumValue
    private int code;
    private String message;
}
