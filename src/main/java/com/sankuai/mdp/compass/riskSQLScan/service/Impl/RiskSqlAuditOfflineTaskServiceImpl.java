package com.sankuai.mdp.compass.riskSQLScan.service.Impl;/* *************
 * @author: liuYang359
 * @date: 2023/12/27 19:39
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlAuditOfflineTaskPO;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlAuditOfflineTaskMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlAuditOfflineTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RiskSqlAuditOfflineTaskServiceImpl implements RiskSqlAuditOfflineTaskService {
    @Autowired
    RiskSqlAuditOfflineTaskMapper riskSqlAuditOfflineTaskMapper;

    public List<RiskSqlAuditOfflineTaskPO> getAllTasks() {
        return riskSqlAuditOfflineTaskMapper.selectList(null);
    }
    public  int  insert(RiskSqlAuditOfflineTaskPO riskSqlAuditOfflineTaskPO) {
        try{
        return riskSqlAuditOfflineTaskMapper.insert(riskSqlAuditOfflineTaskPO);}
        catch (Exception e)
            {
                return 0;
            }
    }
    public  int  delete(RiskSqlAuditOfflineTaskPO riskSqlAuditOfflineTaskPO) {
        return riskSqlAuditOfflineTaskMapper.deleteById(riskSqlAuditOfflineTaskPO.getId());
    }

}
