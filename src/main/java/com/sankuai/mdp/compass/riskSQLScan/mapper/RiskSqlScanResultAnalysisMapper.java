package com.sankuai.mdp.compass.riskSQLScan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanResultAnalysisPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.vo.ResultInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午3:38
 */
@Mapper
public interface RiskSqlScanResultAnalysisMapper extends BaseMapper<RiskSqlScanResultAnalysisPO> {
    @Select("SELECT DISTINCT appkey FROM risk_sql_scan_result_analysis")
    List<String> selectAllAppkey();
    @Select("SELECT DISTINCT appkey FROM risk_sql_scan_result_analysis WHERE mis = #{mis}")
    List<String> selectAppkeyByMis(String mis);
    @Select("SELECT DISTINCT swimlane FROM risk_sql_scan_result_analysis")
    List<String> selectAllSwimlane();
    @Select("SELECT DISTINCT swimlane FROM risk_sql_scan_result_analysis WHERE mis = #{mis}")
    List<String> selectSwimlaneByMis(String mis);
    @Select("SELECT id,appkey,swimlane,totalRiskNum,highRiskNum,mediumRiskNum,lowRiskNum," +
            "noRiskNum FROM risk_sql_scan_result_analysis")
    List<ResultInfoVO> selectAllResult();
    @Select("SELECT MIN(id) AS id,appkey,swimlane,SUM(totalRiskNum) AS totalRiskNum,SUM(highRiskNum) AS highRiskNum,SUM(mediumRiskNum) AS mediumRiskNum,SUM(lowRiskNum) AS lowRiskNum," +
            "SUM(noRiskNum) AS noRiskNum FROM risk_sql_scan_result_analysis WHERE appkey = #{appkey} GROUP BY appkey, swimlane")
    List<ResultInfoVO> selectResultByAppkey(String appkey);
    @Select("SELECT id,appkey,swimlane,totalRiskNum,highRiskNum,mediumRiskNum,lowRiskNum," +
            "noRiskNum FROM risk_sql_scan_result_analysis WHERE swimlane = #{swimlane}")
    List<ResultInfoVO> selectResultBySwimlane(String swimlane);
    @Select("SELECT id,appkey,swimlane,totalRiskNum,highRiskNum,mediumRiskNum,lowRiskNum," +
            "noRiskNum FROM risk_sql_scan_result_analysis WHERE appkey = #{appkey} AND swimlane = #{swimlane}")
    List<ResultInfoVO> selectResultByAppkeyAndSwimlane(String appkey, String swimlane);
    @Select("SELECT id,appkey,swimlane,totalRiskNum,highRiskNum,mediumRiskNum,lowRiskNum," +
            "noRiskNum FROM risk_sql_scan_result_analysis WHERE mis = #{mis}")
    List<ResultInfoVO> selectResultByMis(String mis);
    @Select("SELECT MIN(id) AS id,appkey,swimlane,SUM(totalRiskNum) AS totalRiskNum,SUM(highRiskNum) AS highRiskNum,SUM(mediumRiskNum) AS mediumRiskNum,SUM(lowRiskNum) AS lowRiskNum," +
            "SUM(noRiskNum) AS noRiskNum  FROM risk_sql_scan_result_analysis WHERE mis = #{mis} AND appkey = #{appkey} GROUP BY appkey, swimlane")
    List<ResultInfoVO> selectResultByMisAndAppkey(String mis, String appkey);
    @Select("SELECT id,appkey,swimlane,totalRiskNum,highRiskNum,mediumRiskNum,lowRiskNum," +
            "noRiskNum FROM risk_sql_scan_result_analysis WHERE mis = #{mis} AND swimlane = #{swimlane}")
    List<ResultInfoVO> selectResultByMisAndSwimlane(String mis, String swimlane);
    @Select("SELECT id,appkey,swimlane,totalRiskNum,highRiskNum,mediumRiskNum,lowRiskNum," +
            "noRiskNum FROM risk_sql_scan_result_analysis WHERE mis = #{mis} AND appkey = #{appkey} AND swimlane = #{swimlane}")
    List<ResultInfoVO> selectResultByMisAndAppkeyAndSwimlane(String mis, String appkey, String swimlane);
    @Select("SELECT id,taskId,appkey,swimlane,mis,totalRiskNum,totalHitWhitelistNum,highRiskNum,highHitWhitelistNum,mediumRiskNum,mediumHitWhitelistNum,lowRiskNum,lowHitWhitelistNum,noRiskNum,noHitWhitelistNum FROM risk_sql_scan_result_analysis WHERE id = #{id}")
    RiskSqlScanResultAnalysisPO selectByAnalysisId(Long id);
    @Select("SELECT id FROM risk_sql_scan_result_analysis WHERE TaskId = #{TaskId}")
    Long selectId(Long taskId);
    @Select("SELECT * FROM risk_sql_scan_result_analysis WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)")
    List<RiskSqlScanResultAnalysisPO> selectNew();
}
