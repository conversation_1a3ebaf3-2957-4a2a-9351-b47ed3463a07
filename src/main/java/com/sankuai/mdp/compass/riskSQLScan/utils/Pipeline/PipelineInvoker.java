package com.sankuai.mdp.compass.riskSQLScan.utils.Pipeline;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.PipelineCreateSqlAuditTaskByPipelineActionResponse;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.PipelineCreateSqlAuditTaskByPipelineActionRequest;

import java.util.List;


/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 下午4:14
 */
public class PipelineInvoker extends AbstractPipelineApiInvoke<PipelineCreateSqlAuditTaskByPipelineActionRequest, List<PipelineCreateSqlAuditTaskByPipelineActionResponse>>{
    @Override
    protected List<PipelineCreateSqlAuditTaskByPipelineActionResponse> convert2Model(String data) {
        return JSON.parseArray(data, PipelineCreateSqlAuditTaskByPipelineActionResponse.class);
    }
}
