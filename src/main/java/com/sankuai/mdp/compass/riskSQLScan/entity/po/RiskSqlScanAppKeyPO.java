package com.sankuai.mdp.compass.riskSQLScan.entity.po;/* *************
 * @author: liuYang359
 * @date: 2023/9/25 20:39
 * @description:
 */

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;


@Data
@Builder
@Getter
@Setter
@Generated
@TableName("risk_sql_scan_appkey")
public class RiskSqlScanAppKeyPO {
    @TableId(value = "id",type =  IdType.AUTO)
    private Integer id;
    private Integer businessId;

    private String appkey;

    private boolean onlineScanSwitch;

    private boolean offlineScanSwitch;

    private int deleted;


}
