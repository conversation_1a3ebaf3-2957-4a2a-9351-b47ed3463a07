package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/29
 * @time: 上午10:34
 */
@Data
public class GetReportByOrgActionResponse{
    private List<GetReportByOrgActionReponseData> data;

    @Data
    @Accessors(chain = true)
    public static class GetReportByOrgActionReponseData {
        private String week;
        private String weekStartTime;
        private String weekEndTime;
        private int totalNum;
        private int highRiskNum;
        private int mediumRiskNum;
        private int lowRiskNum;
        private int noRiskNum;
    }
}
