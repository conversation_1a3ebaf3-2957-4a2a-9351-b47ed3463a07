package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/28
 * @time: 下午4:41
 */
@Data
public class RDSListDiffSqlByAppkeyActionResponse  {
    private String appkey;
    private List<RDSListSqlByAppkeyActionReponseData> data;
}
