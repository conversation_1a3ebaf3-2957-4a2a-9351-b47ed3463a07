package com.sankuai.mdp.compass.riskSQLScan.utils.Pipeline;

import lombok.Data;
import cn.hutool.json.JSONObject;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/29
 * @time: 下午3:18
 */
@Data
public class PipelineApiResponse {
    public static PipelineApiResponse instanceOf(String json){
        if(json == null || !json.startsWith("{")){
            return null;
        }
        return new JSONObject(json).toBean(PipelineApiResponse.class);
    }
    /**
     * code : 0
     * userMessage : 操作成功
     * message : 操作成功
     * data :
     */
    private Integer code;
    private String userMessage;
    private String message;
    private String data;


}
