package com.sankuai.mdp.compass.riskSQLScan.service;/* *************
 * @author: liuYang359
 * @date: 2023/12/14 11:55
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.*;

import java.util.List;
public interface RdsService   {
    List<RDSListFullSqlByAppkeyActionResponse> listFullSqlByAppkey(RDSListFullSqlByAppkeyActionRequest request);

    List<RDSListDiffSqlByAppkeyActionResponse> listDiffSqlByAppkey(RDSListDiffSqlByAppkeyActionRequest request);

    UserCreateSqlAuditTaskByUserActionResponse createSqlAuditTask(UserCreateSqlAuditTaskByUserActionRequest request);

    UserGetSqlAuditTaskByUserActionResponse getSqlAuditTask(String username,int taskId);

    UserGetSqlAuditResultByUserActionResponse getSqlAuditResult(String username,int taskId);

    List<GetReportByOrgActionResponse> getReportByOrgAction(GetReportByOrgActionRequest request);

    List<GetReportResultsByOrgActionResponse> getReportResultByOrgAction(GetReportResultsByOrgActionRequest request);

    List<GetReportByAppkeyActionResponse> getReportByAppkeyAction(GetReportByAppkeyActionRequest request);

    List<GetReportResultsByAppkeyActionResponse> getReportResultByAppkeyAction(GetReportResultsByAppkeyActionRequest request);
}
