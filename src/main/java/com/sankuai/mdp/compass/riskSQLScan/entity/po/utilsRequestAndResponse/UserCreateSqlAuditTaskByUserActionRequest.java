package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/28
 * @time: 下午5:44
 */
@Data
public class UserCreateSqlAuditTaskByUserActionRequest {
    private String submitter;
    private String receiver;
    private List<CreateSqlAuditTaskRequestData> sqls;
}
