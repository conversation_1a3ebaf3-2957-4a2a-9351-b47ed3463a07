package com.sankuai.mdp.compass.riskSQLScan.service;/* *************
 * @author: liuYang359
 * @date: 2023/10/12 18:36
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.dto.RiskSqlScanAppKeyDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;

import java.util.List;

public interface RiskSqlScanAppkeyService {
   public List<RiskSqlScanAppKeyPO> selectByBusinessID(Integer businessId, Integer deleted);
   public List<RiskSqlScanAppKeyDTO> selectByAppkeyIdAndMis(Integer businessId, String mis);

   public int deleteById(Integer id);
   public int deleteByBusinessId(Integer id);

   boolean setSwitch(Integer appkeyId, Integer type, boolean staus);


   boolean duplicate(String appkey);


   boolean createAppKey(RiskSqlScanAppKeyPO appKey);
}
