package com.sankuai.mdp.compass.riskSQLScan.service.Impl;


import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanResultAnalysisPO;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanAppKeyMapper;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanBusinessInfoMapper;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanResultAnalysisMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.DxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Service
public class DxServiceImpl implements DxService {

    @Autowired
    private RiskSqlScanResultAnalysisMapper riskSqlScanResultAnalysisMapper;
    @Autowired
    private RiskSqlScanAppKeyMapper riskSqlScanAppKeyMapper;
    @Autowired
    private RiskSqlScanBusinessInfoMapper riskSqlScanBusinessInfoMapper;


    DxUtil dxUtil = new DxUtil();

    @Override
    public void sendSQLMessages() {
        List<RiskSqlScanResultAnalysisPO> riskSqlScanResultAnalyses = riskSqlScanResultAnalysisMapper.selectNew();
        if (riskSqlScanResultAnalyses.isEmpty()) {
            return;
        }
        // 按 mis 分组
        Map<String, List<RiskSqlScanResultAnalysisPO>> misGrouped = riskSqlScanResultAnalyses.stream()
                .collect(Collectors.groupingBy(RiskSqlScanResultAnalysisPO::getMis));
        // 遍历每个 mis
        for (Map.Entry<String, List<RiskSqlScanResultAnalysisPO>> misEntry : misGrouped.entrySet()) {
            String mis = misEntry.getKey();

            // 排除 mis 为 hotel.sonar 和 Cargo
            if ("hotel.sonar".equals(mis) || "Cargo".equals(mis)) {
                continue;
            }

            List<RiskSqlScanResultAnalysisPO> misGroup = misEntry.getValue();
            // 按 appkey 分组
            Map<String, List<RiskSqlScanResultAnalysisPO>> appkeyGrouped = misGroup.stream()
                    .collect(Collectors.groupingBy(RiskSqlScanResultAnalysisPO::getAppkey));

            // 组织消息
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("您管理的业务线上一周存在风险SQL：\n");

            boolean hasRisk = false;

            // 遍历每个 appkey
            for (Map.Entry<String, List<RiskSqlScanResultAnalysisPO>> appkeyEntry : appkeyGrouped.entrySet()) {
                String appkey = appkeyEntry.getKey();
                List<RiskSqlScanResultAnalysisPO> appkeyGroup = appkeyEntry.getValue();

                // 累加 highRiskNum, mediumRiskNum, lowHitWhitelistNum
                int totalHighRiskNum = appkeyGroup.stream().mapToInt(RiskSqlScanResultAnalysisPO::getHighRiskNum).sum();
                int totalMediumRiskNum = appkeyGroup.stream().mapToInt(RiskSqlScanResultAnalysisPO::getMediumRiskNum).sum();
                int totalLowRiskNum = appkeyGroup.stream().mapToInt(RiskSqlScanResultAnalysisPO::getLowRiskNum).sum();

                // 如果均为 0，则跳过
                if (totalHighRiskNum == 0 && totalMediumRiskNum == 0 && totalLowRiskNum == 0) {
                    continue;
                }

                hasRisk = true;

                // 查找业务线（假设有一个方法 findBusinessLineByAppkey）
                String businessLine = findBusinessLineByAppkey(appkey);

                // 组织业务线和 appkey 的消息
                messageBuilder.append(String.format(
                        "%s：\n%s：共%d条高风险SQL，%d条中风险SQL，%d条低风险SQL\n",
                        businessLine, appkey, totalHighRiskNum, totalMediumRiskNum, totalLowRiskNum
                ));
            }

            // 如果有风险数据，发送消息
            if (hasRisk) {
                // 在消息末尾添加链接
                messageBuilder.append("\n详情请查看：https://w.sankuai.com/ptqa/compass_new_web/index.html#/sqlRisk/riskList");
                dxUtil.sendToPersionByCompass(messageBuilder.toString(), mis);
            }
        }


    }

    private String findBusinessLineByAppkey(String appkey) {
        String businessId = riskSqlScanAppKeyMapper.selectBusinessIdByAppkey(appkey);
        return riskSqlScanBusinessInfoMapper.selectBusinessLineByBusinessId(businessId);
    }
}
