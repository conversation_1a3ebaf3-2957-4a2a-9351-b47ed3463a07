package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/29
 * @time: 上午11:30
 */
@Data
public class GetReportByAppkeyActionResponse {
    private List<ReportByAppkey> data;

    @Data
    @Accessors(chain = true)
    private static class ReportByAppkey {
        private Date time;
        private int taskId;
        private int totalNum;
        private int highRiskNum;
        private int mediumRiskNum;
        private int lowRiskNum;
        private int noRiskNum;
    }
}
