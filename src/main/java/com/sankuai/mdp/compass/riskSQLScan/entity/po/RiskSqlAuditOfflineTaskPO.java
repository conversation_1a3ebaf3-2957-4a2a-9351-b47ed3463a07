package com.sankuai.mdp.compass.riskSQLScan.entity.po;/* *************
 * @author: liuYang359
 * @date: 2023/12/27 16:52
 * @description:
 */

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Builder
@TableName("risk_sql_audit_offline_task")
public class RiskSqlAuditOfflineTaskPO {
    //主键
    @TableId(value = "id",type =  IdType.AUTO)
    Integer id;
    //泳道名称、唯一、
    @TableField(value = "swimlane")
    String swimlane;

    //逻辑删除
    @TableLogic(value = "0", delval = "id")
    @TableField(value = "isDeleted")
    Integer isDeleted;
    // 业务线 id
    @TableField(value = "businessId")
    Integer businessId;
    //业务 appkeyid
    @TableField(value = "appkeyId")
    Integer appkeyId;
    //业务名称
    @TableField(value = "businessNamne")
    String businessNamne;
    @TableField(value = "appkey")
    String  appkey;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "createTime")
    Date createTime;
    @TableField(value = "UpdateTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date UpdateTime;
    @TableField(value = "deleteTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date deleteTime;



}
