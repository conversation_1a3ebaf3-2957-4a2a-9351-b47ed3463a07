package com.sankuai.mdp.compass.riskSQLScan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanResultDataPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午3:39
 */
@Mapper
public interface RiskSqlScanResultDataMapper extends BaseMapper<RiskSqlScanResultDataPO> {
    @Select("SELECT id,resultAnalysisId,sqlText,appkey,dbName,riskLevel," +
            "hitWhitelist,dbId,useNum FROM risk_sql_scan_result_data WHERE resultAnalysisId = #{id}")
    List<RiskSqlScanResultDataPO> selectByResultAnalysisId(Long id);
    @Select("SELECT dbName FROM risk_sql_scan_result_data WHERE resultAnalysisId = #{id}")
    List<String> selectDBsByResultAnalysisId(Integer id);
    @Select("SELECT id FROM risk_sql_scan_result_data WHERE appkey = #{appkey} AND dbName = #{dbName} AND resultAnalysisId = #{analysisId}")
    List<Long> selectId(Long analysisId,String appkey, String dbName);

}
