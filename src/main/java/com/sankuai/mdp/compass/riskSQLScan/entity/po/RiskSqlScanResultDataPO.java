package com.sankuai.mdp.compass.riskSQLScan.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午3:07
 */
@Data
@TableName("risk_sql_scan_result_data")
@NoArgsConstructor
@AllArgsConstructor
public class RiskSqlScanResultDataPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "resultAnalysisId")
    private Long resultAnalysisId;
    @TableField(value = "sqlText")
    private String sqlText;
    @TableField(value = "appkey")
    private String appkey;
    @TableField(value = "dbName")
    private String dbName;
    @TableField(value = "riskLevel")
    private RiskSqlScanRiskLevelEnum riskLevel;
    @TableField(value = "hitWhitelist")
    private Boolean hitWhitelist;
    @TableField(value = "dbId")
    private Long dbId;
    @TableField(value = "useNum")
    private Long useNum;

}
