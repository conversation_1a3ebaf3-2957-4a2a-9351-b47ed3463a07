package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;/* *************
 * @author: liuYang359
 * @date: 2023/11/28 16:07
 * @description:
 */

import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;
@Data
public class RdsApiResponse {
    public static RdsApiResponse instanceOf(String json) {
        if (json == null || !json.startsWith("{")) {
            return null;
        }
        return new JSONObject(json).toBean(RdsApiResponse.class);
    }

    /**
     * code : 0
     * userMessage : 操作成功
     * message : 操作成功
     * data :
     * ctx : {"requestId":"xxx","sentryEventId":"None"}
     */
    private Integer code;
    private String userMessage;
    private String message;
    /**
     * RDS请求结果
     */
    private String data;
    private CtxBean ctx;

    @Data
    @Accessors(chain = true)
    public static class CtxBean {
        /**
         * requestId : xxx
         * sentryEventId : None
         */

        private String requestId;
        private String sentryEventId;
    }
}
