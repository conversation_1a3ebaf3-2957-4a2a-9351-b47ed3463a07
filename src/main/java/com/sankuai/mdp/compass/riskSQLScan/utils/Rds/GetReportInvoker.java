package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportByOrgActionRequest;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportByOrgActionResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/30
 * @time: 下午2:24
 */
@Component
public class GetReportInvoker extends AbstractRdsApiInvoker<GetReportByOrgActionRequest,List<GetReportByOrgActionResponse>>{

    @Override
    protected String getAction() {
        return "GetReportByOrgAction";
    }

    @Override
    protected List<GetReportByOrgActionResponse> convert2Model(String data) {
        return JSON.parseArray(data,GetReportByOrgActionResponse.class);
    }
}
