package com.sankuai.mdp.compass.riskSQLScan.enums;/* *************
 * @author: liuYang359
 * @date: 2023/9/23 18:49
 * @description:
 */

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum RiskSqlScanTaskStatusEnum {
    SUCCESS(0,"成功"),
    FAIL(1,"失败"),
    RUNNING(2,"运行中"),
    START(3,"就绪");

    RiskSqlScanTaskStatusEnum(int code, String message)
        {
            this.code=code;
            this.message=message;
        }
        @EnumValue
        private int code;
       private  String message;
}
