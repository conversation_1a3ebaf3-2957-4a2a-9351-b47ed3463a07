package com.sankuai.mdp.compass.riskSQLScan.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sankuai.mdp.compass.common.utils.LionUtil;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.*;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.*;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanTaskStatusEnum;
import com.sankuai.mdp.compass.riskSQLScan.mapper.*;
import com.sankuai.mdp.compass.riskSQLScan.service.ClusterInfoService;
import com.sankuai.mdp.compass.riskSQLScan.service.RdsListenerService;
import com.sankuai.mdp.compass.riskSQLScan.service.RdsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午4:00
 */
@Service
@Slf4j
public class RdsListenerServiceImpl implements RdsListenerService {
    private String submitter_1;
    private Set<String> riskReasonWhitelist;
    private int sqlLimit;
    @PostConstruct
    public void init() {
        LionUtil lionUtil = new LionUtil();
        String riskSqlScanTaskSubmitter = lionUtil.getValue("riskSqlScanTaskSubmitter");
        if(riskSqlScanTaskSubmitter!=null){
            submitter_1 = riskSqlScanTaskSubmitter;
        }else submitter_1= "liuyang359";
        int riskSqlScanSqlLimit = 1000;
        try {
            int parsedValue = Integer.parseInt(lionUtil.getValue("riskSqlScanSqlLimit"));
            if (parsedValue != 0) {
                riskSqlScanSqlLimit = parsedValue;
            }
        } catch (NumberFormatException e) {
            // 保持默认值
            log.info("Lion配置项 riskSqlScanSqlLimit 解析失败，使用默认值",e);
        }
        sqlLimit = riskSqlScanSqlLimit;
        // 从Lion获取白名单配置
        String whitelistConfig = lionUtil.getValue("riskSqlScanRiskReasonWhitelist");
        if (whitelistConfig != null && !whitelistConfig.isEmpty()) {
            this.riskReasonWhitelist = new HashSet<>(Arrays.asList(whitelistConfig.split(",")));
        } else {
            // 如果Lion中没有配置，使用本地的白名单
            this.riskReasonWhitelist = new HashSet<>(Arrays.asList(
                    // 可以为空，或者添加默认的白名单项
            ));
        }
    }




    @Autowired
    private RiskSqlScanSwimlaneInfoMapper riskSqlScanSwimlaneInfoMapper;
    @Autowired
    private RiskSqlScanCommitItemMapper riskSqlScanCommitItemMapper;
    @Autowired
    private RiskSqlScanCommitMapper riskSqlScanCommitMapper;
    @Autowired
    private RiskSqlScanTaskMapper riskSqlScanTaskMapper;
    @Autowired
    RiskSqlScanResultAnalysisMapper riskSqlScanResultAnalysisMapper;
    @Autowired
    private RiskSqlScanResultDataMapper riskSqlScanResultDataMapper;
    @Autowired
    private RiskSqlScanSqlMapper riskSqlScanSqlMapper;
    @Autowired
    private RiskSqlScanAppKeyMapper riskSqlScanAppKeyMapper;
    @Autowired
    private RdsService rdsService;
    @Autowired
    private ClusterInfoService clusterInfoService;

    List<RDSListFullSqlByAppkeyActionResponse> rdsListSqlByAppkeyActionResponses;

    long OldCommitId=0;

    @Override
    public void getChangedSql() {
        // 获取所有风险SQL扫描泳道信息列表
        List<RiskSqlScanSwimlaneInfoPO> riskSqlScanSwimlaneInfoPOS = riskSqlScanSwimlaneInfoMapper.selecctActive();
        if(riskSqlScanSwimlaneInfoPOS.isEmpty()){
            return;
        }
        for (RiskSqlScanSwimlaneInfoPO riskSqlScanSwimlaneInfoPO : riskSqlScanSwimlaneInfoPOS) {
            String appkeys = riskSqlScanSwimlaneInfoPO.getAppKey();
            String swimlane = riskSqlScanSwimlaneInfoPO.getSwimlane();
            String env = riskSqlScanSwimlaneInfoPO.getEnv();
            String receiver = riskSqlScanSwimlaneInfoPO.getMis();
            int maxRetries = 3;
            int retryCount = 0;
            long commitId = 0;
            // 保存变更的sql，返回commitId
            while (retryCount < maxRetries) {
                try {
                    // 保存变更的sql，返回commitId
                    commitId = saveChangedSql(riskSqlScanCommitMapper, rdsService, appkeys, swimlane, env);

                    if (commitId > 0) {
                        setTask(commitId, receiver, appkeys, swimlane);
                        for (RDSListFullSqlByAppkeyActionResponse response : rdsListSqlByAppkeyActionResponses) {
                            if (OldCommitId == 0) {
                                saveFullSqlSamples(riskSqlScanCommitItemMapper, response, commitId);
                            } else {
                                    saveDiffSqlSamples(riskSqlScanCommitItemMapper, response, commitId, OldCommitId);
                            }
                        }
                            // 如果所有操作都成功，跳出循环
                            break;

                    }

                    // 如果commitId <= 0 或者setTask失败，抛出异常以触发重试
                    if (commitId == 0) {
                        throw new RuntimeException("操作失败，commitId: " + commitId);
                    }
                } catch (Exception e) {
                    retryCount++;
                    if (retryCount < maxRetries) {
                        log.warn("第 {} 次尝试失败，准备重试。错误: {}", retryCount, e.getMessage());
                        try {
                            Thread.sleep(4000); // 等待5秒后重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.error("重试等待被中断", ie);
                        }
                    } else {
                        log.error("在 {} 次尝试后操作仍然失败", maxRetries, e);
                    }
                }
            }
        }
    }
    // 保存变更的sql，返回commitId
    private long saveChangedSql(RiskSqlScanCommitMapper riskSqlScanCommitMapper, RdsService rdsService, String appkeys, String swimlane, String env) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime letter = now.minusMinutes(60*24);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        String startTime = letter.format(formatter);
        String endTime = now.format(formatter);
        Date startDate = Date.from(letter.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());

        QueryWrapper<RiskSqlScanCommitPO> riskSqlScanCommitPOQueryWrapper = new QueryWrapper<>();
        riskSqlScanCommitPOQueryWrapper.eq("appkeys", appkeys);
        riskSqlScanCommitPOQueryWrapper.eq("swimlane", swimlane);
        riskSqlScanCommitPOQueryWrapper.orderByDesc("id"); // 按 id 降序排列
        riskSqlScanCommitPOQueryWrapper.last("LIMIT 1"); // 限制只返回一条记录
        RiskSqlScanCommitPO riskSqlScanCommitPOS = riskSqlScanCommitMapper.selectOne(riskSqlScanCommitPOQueryWrapper);

        if (riskSqlScanCommitPOS==null) {
            RDSListFullSqlByAppkeyActionRequest rdsListFullSqlByAppkeyActionRequest = new RDSListFullSqlByAppkeyActionRequest(appkeys, swimlane, startTime, endTime, sqlLimit);
            RiskSqlScanCommitPO riskSqlScanCommitPO = new RiskSqlScanCommitPO(null, appkeys, swimlane, startDate, endDate, 0);
            riskSqlScanCommitMapper.insert(riskSqlScanCommitPO);
            Long commitId=riskSqlScanCommitPO.getId();
            rdsListSqlByAppkeyActionResponses = rdsService.listFullSqlByAppkey(rdsListFullSqlByAppkeyActionRequest);
            if(rdsListSqlByAppkeyActionResponses.isEmpty()){
                return 0;
            }
            List<String> filteredClusters = clusterInfoService.getFilteredClusters(appkeys);
            for (RDSListFullSqlByAppkeyActionResponse rdsListFullSqlByAppkeyActionResponse : rdsListSqlByAppkeyActionResponses) {
                List<RDSListSqlByAppkeyActionReponseData> filteredData = rdsListFullSqlByAppkeyActionResponse.getData().stream()
                        .filter(data -> data.getSqlSamples().stream()
                                .anyMatch(sample -> !filteredClusters.contains(sample.getClusterName())))
                        .collect(Collectors.toList());
                rdsListFullSqlByAppkeyActionResponse.setData(filteredData);
                //saveFullSqlSamples(riskSqlScanCommitItemMapper, rdsListFullSqlByAppkeyActionResponse, commitId);
            }
            return commitId;
        }else if((endDate.getTime()-riskSqlScanCommitPOS.getUpdateTime().getTime())>1000*60*60*23){
            RDSListFullSqlByAppkeyActionRequest rdsListFullSqlByAppkeyActionRequest = new RDSListFullSqlByAppkeyActionRequest(appkeys, swimlane, startTime, endTime, sqlLimit);
            RiskSqlScanCommitPO riskSqlScanCommitPO = new RiskSqlScanCommitPO(null, appkeys, swimlane, startDate, endDate, 0);
            riskSqlScanCommitMapper.insert(riskSqlScanCommitPO);
            long commitId = riskSqlScanCommitPO.getId();
            rdsListSqlByAppkeyActionResponses = rdsService.listFullSqlByAppkey(rdsListFullSqlByAppkeyActionRequest);
            if (rdsListSqlByAppkeyActionResponses.isEmpty()) {
                return 0;
            }
            List<String> filteredClusters = clusterInfoService.getFilteredClusters(appkeys);
            for (RDSListFullSqlByAppkeyActionResponse rdsListFullSqlByAppkeyActionResponse : rdsListSqlByAppkeyActionResponses) {
                List<RDSListSqlByAppkeyActionReponseData> filteredData = rdsListFullSqlByAppkeyActionResponse.getData().stream()
                        .filter(data -> data.getSqlSamples().stream()
                                .anyMatch(sample -> !filteredClusters.contains(sample.getClusterName())))
                        .collect(Collectors.toList());
                rdsListFullSqlByAppkeyActionResponse.setData(filteredData);
                //saveFullSqlSamples(riskSqlScanCommitItemMapper, rdsListFullSqlByAppkeyActionResponse, commitId);
            }
            return commitId;
        }else {
            RDSListFullSqlByAppkeyActionRequest rdsListFullSqlByAppkeyActionRequest = new RDSListFullSqlByAppkeyActionRequest(appkeys, swimlane, startTime, endTime, sqlLimit);
            RiskSqlScanCommitPO riskSqlScanCommitPO = new RiskSqlScanCommitPO(null, appkeys, swimlane, startDate, endDate, 0);
            riskSqlScanCommitMapper.insert(riskSqlScanCommitPO);
            long commitId = riskSqlScanCommitPO.getId();
            rdsListSqlByAppkeyActionResponses = rdsService.listFullSqlByAppkey(rdsListFullSqlByAppkeyActionRequest);
            if (rdsListSqlByAppkeyActionResponses.isEmpty()) {
                return 0;
            }
            List<String> filteredClusters = clusterInfoService.getFilteredClusters(appkeys);
            for (RDSListFullSqlByAppkeyActionResponse rdsListFullSqlByAppkeyActionResponse : rdsListSqlByAppkeyActionResponses) {
                List<RDSListSqlByAppkeyActionReponseData> filteredData = rdsListFullSqlByAppkeyActionResponse.getData().stream()
                        .filter(data -> data.getSqlSamples().stream()
                                .anyMatch(sample -> !filteredClusters.contains(sample.getClusterName())))
                        .collect(Collectors.toList());
                rdsListFullSqlByAppkeyActionResponse.setData(filteredData);
                OldCommitId = riskSqlScanCommitPOS.getId();
                //saveDiffSqlSamples(riskSqlScanCommitItemMapper, rdsListFullSqlByAppkeyActionResponse, commitId, riskSqlScanCommitPOS.getId());
            }
            return commitId;
        }
    }
    // 保存"full"SQL样本
    private void saveFullSqlSamples(RiskSqlScanCommitItemMapper riskSqlScanCommitItemMapper,  RDSListFullSqlByAppkeyActionResponse rdsListSqlByAppkeyActionResponse, long commitId) {
        if (rdsListSqlByAppkeyActionResponse == null || rdsListSqlByAppkeyActionResponse.getData().isEmpty()) {
            return;
        }
        List<String> filteredClusters = clusterInfoService.getFilteredClusters(rdsListSqlByAppkeyActionResponse.getAppkey());
        for (RDSListSqlByAppkeyActionReponseData rdsListSqlByAppkeyActionReponseData : rdsListSqlByAppkeyActionResponse.getData()) {
            if (rdsListSqlByAppkeyActionReponseData.getSqlSamples().isEmpty()) {
                continue;
            }
            for (SQLSamples sqlSamples : rdsListSqlByAppkeyActionReponseData.getSqlSamples()) {
                if (!filteredClusters.contains(sqlSamples.getClusterName())) {
                    RiskSqlScanCommitItemPO riskSqlScanCommitItemPO = new RiskSqlScanCommitItemPO(null, commitId, rdsListSqlByAppkeyActionReponseData.getSqlTpl(), sqlSamples.getSql(), sqlSamples.getDbName(), sqlSamples.getClusterName());
                    riskSqlScanCommitItemMapper.insert(riskSqlScanCommitItemPO);
                }
            }
        }
    }
    //保存"diff"SQL样本
    private void saveDiffSqlSamples(RiskSqlScanCommitItemMapper riskSqlScanCommitItemMapper, RDSListFullSqlByAppkeyActionResponse rdsListSqlByAppkeyActionResponse, long commitId,long oldCommitId) {
        if (rdsListSqlByAppkeyActionResponse == null || rdsListSqlByAppkeyActionResponse.getData().isEmpty()) {
            return;
        }
        List<String> filteredClusters = clusterInfoService.getFilteredClusters(rdsListSqlByAppkeyActionResponse.getAppkey());
        for (RDSListSqlByAppkeyActionReponseData rdsListSqlByAppkeyActionReponseData : rdsListSqlByAppkeyActionResponse.getData()) {
            if (rdsListSqlByAppkeyActionReponseData.getSqlSamples().isEmpty()) {
                continue;
            }
            for (SQLSamples sqlSamples : rdsListSqlByAppkeyActionReponseData.getSqlSamples()) {
                if (!filteredClusters.contains(sqlSamples.getClusterName())) {
                    RiskSqlScanCommitItemPO riskSqlScanCommitItemPO = new RiskSqlScanCommitItemPO(null, commitId, rdsListSqlByAppkeyActionReponseData.getSqlTpl(), sqlSamples.getSql(), sqlSamples.getDbName(), sqlSamples.getClusterName());
                    if (riskSqlScanCommitItemMapper.selectID1(oldCommitId, rdsListSqlByAppkeyActionReponseData.getSqlTpl(), sqlSamples.getSql(), sqlSamples.getDbName(), sqlSamples.getClusterName()) == null) {
                        riskSqlScanCommitItemMapper.insert(riskSqlScanCommitItemPO);
                    }
                }
            }
        }
    }

    public void setTask(long commitId, String receiver, String appkeys, String swimlane) {
        QueryWrapper<RiskSqlScanTaskPO> riskSqlScanTaskPOQueryWrapper = new QueryWrapper<>();
        riskSqlScanTaskPOQueryWrapper.eq("commitId", commitId);
        riskSqlScanTaskPOQueryWrapper.eq("taskSatus", RiskSqlScanTaskStatusEnum.SUCCESS);
        List<RiskSqlScanTaskPO> riskSqlScanTaskPOS = riskSqlScanTaskMapper.selectList(riskSqlScanTaskPOQueryWrapper);
        if (riskSqlScanTaskPOS.isEmpty()) {
            // 构建请求数据
            UserCreateSqlAuditTaskByUserActionRequest userCreateSqlAuditTaskByUserActionRequest = new UserCreateSqlAuditTaskByUserActionRequest();
            userCreateSqlAuditTaskByUserActionRequest.setSubmitter(submitter_1);
            userCreateSqlAuditTaskByUserActionRequest.setReceiver(receiver);
            List<SQLSamples> sqlSamplesList = new ArrayList<>();
            if(rdsListSqlByAppkeyActionResponses.isEmpty()||rdsListSqlByAppkeyActionResponses.get(0).getData().isEmpty()){
                return;
            }
            for(RDSListSqlByAppkeyActionReponseData  listFullSqlByAppkeyActionData :rdsListSqlByAppkeyActionResponses.get(0).getData()){
                if(listFullSqlByAppkeyActionData.getSqlSamples().isEmpty()){
                    continue;
                }
                for(SQLSamples sqlSample1:listFullSqlByAppkeyActionData.getSqlSamples()){
                    SQLSamples sqlSample = new SQLSamples();
                    sqlSample.setSql(sqlSample1.getSql());
                    sqlSample.setDbName(sqlSample1.getDbName());
                    sqlSample.setClusterName(sqlSample1.getClusterName());
                    sqlSamplesList.add(sqlSample);
                }
            }
            CreateSqlAuditTaskRequestData createSqlAuditTaskRequestData = new CreateSqlAuditTaskRequestData();
            createSqlAuditTaskRequestData.setAppkey(appkeys);
            createSqlAuditTaskRequestData.setSqlSamples(sqlSamplesList);
            List<CreateSqlAuditTaskRequestData> createSqlAuditTaskRequestDataList = new ArrayList<>();
            createSqlAuditTaskRequestDataList.add(createSqlAuditTaskRequestData);
            userCreateSqlAuditTaskByUserActionRequest.setSqls(createSqlAuditTaskRequestDataList);
            // 发起请求
            UserCreateSqlAuditTaskByUserActionResponse sqlAuditTask = rdsService.createSqlAuditTask(userCreateSqlAuditTaskByUserActionRequest);
            if (sqlAuditTask == null) {
                return;
            }
            // 插入RiskSqlScanTaskPO数据
            LocalDateTime now = LocalDateTime.now();
            Date creatDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
            RiskSqlScanTaskPO riskSqlScanTaskPO = new RiskSqlScanTaskPO(null, commitId, RiskSqlScanTaskStatusEnum.RUNNING, appkeys, swimlane, submitter_1, receiver, (long) sqlAuditTask.getId(),creatDate);
            riskSqlScanTaskMapper.insert(riskSqlScanTaskPO);
        }

    }


    @Override
    @Transactional
    public void getTaskResult() {
        //读task表获得是否要获得结果的任务
        QueryWrapper<RiskSqlScanTaskPO> riskSqlScanTaskPOQueryWrapper=new QueryWrapper<>();
        riskSqlScanTaskPOQueryWrapper.eq("taskSatus", RiskSqlScanTaskStatusEnum.RUNNING);
        List<RiskSqlScanTaskPO> riskSqlScanTaskPOS = riskSqlScanTaskMapper.selectList(riskSqlScanTaskPOQueryWrapper);
        if(riskSqlScanTaskPOS.isEmpty()){
            return;
        }
        for (RiskSqlScanTaskPO riskSqlScanTaskPO : riskSqlScanTaskPOS) {
            Long taskId = riskSqlScanTaskPO.getTaskId();
            String appkey= riskSqlScanTaskPO.getAppkeys();
            String swimlane= riskSqlScanTaskPO.getSwimLane();
            UserGetSqlAuditTaskByUserActionResponse sqlAuditTask = rdsService.getSqlAuditTask(submitter_1, Math.toIntExact(taskId));
            if(sqlAuditTask==null){
                continue;
            }
                if(Objects.equals(sqlAuditTask.getStatus(), "success")){
                    //保存Analysis
                    RiskSqlScanResultAnalysisPO riskSqlScanResultAnalysisPO=new RiskSqlScanResultAnalysisPO();
                    riskSqlScanResultAnalysisPO.setTaskId(riskSqlScanTaskPO.getId());
                    riskSqlScanResultAnalysisPO.setAppkey(appkey);
                    riskSqlScanResultAnalysisPO.setSwimlane(swimlane);
                    riskSqlScanResultAnalysisPO.setMis(riskSqlScanTaskPO.getReceiver());
                    riskSqlScanResultAnalysisPO.setTotalRiskNum(sqlAuditTask.getResult().getTotalNum());
                    riskSqlScanResultAnalysisPO.setHighRiskNum(sqlAuditTask.getResult().getHighRiskNum());
                    riskSqlScanResultAnalysisPO.setMediumRiskNum(sqlAuditTask.getResult().getMiddleRiskNum());
                    riskSqlScanResultAnalysisPO.setLowRiskNum(sqlAuditTask.getResult().getLowRiskNum());
                    riskSqlScanResultAnalysisPO.setNoRiskNum(sqlAuditTask.getResult().getNoneRiskNum());
                    riskSqlScanResultAnalysisPO.setCreateTime(new Date());
                    riskSqlScanResultAnalysisMapper.insert(riskSqlScanResultAnalysisPO);
                    Long analysisId=riskSqlScanResultAnalysisPO.getId();
                    //保存到Risksql
                    if(riskSqlScanResultAnalysisPO.getHighRiskNum()!=0||riskSqlScanResultAnalysisPO.getMediumRiskNum()!=0||riskSqlScanResultAnalysisPO.getLowRiskNum()!=0) {
                        UserGetSqlAuditResultByUserActionResponse sqlAuditResult = rdsService.getSqlAuditResult(submitter_1, Math.toIntExact(taskId));
                        if(sqlAuditResult==null){
                            continue;
                        }
                        for (UserGetSqlAuditResultByUserActionResponse.RiskData sqlAuditResultData : sqlAuditResult.getData()) {
                            if(sqlAuditResultData==null|| Objects.equals(sqlAuditResultData.getRiskLevel(), "none")){
                                continue;
                            }
                            RiskSqlPo riskSqlPo =RiskSqlPo.builder().build();
                            String riskLevel_1="";
                            switch (sqlAuditResultData.getRiskLevel()){
                                case "high":
                                    riskLevel_1="high";
                                    riskSqlPo.setLevel(RiskSqlScanRiskLevelEnum.HIGH);
                                    break;
                                case "low":
                                    riskLevel_1="low";
                                    riskSqlPo.setLevel(RiskSqlScanRiskLevelEnum.LOW);
                                    break;
                                case "middle":
                                    riskLevel_1="middle";
                                    riskSqlPo.setLevel(RiskSqlScanRiskLevelEnum.MID);
                            }
                            riskSqlPo.setCreateTime(new Date());
                            riskSqlPo.setDeleted(0);
                            riskSqlPo.setSwimlane(swimlane);
                            List<Long> appkeyIds = riskSqlScanAppKeyMapper.selectId(appkey);
                            riskSqlPo.setAppKeyId(Math.toIntExact(appkeyIds.get(0)));
                            riskSqlPo.setSqlTemplate(sqlAuditResultData.getSqlTpl());
                            riskSqlPo.setRiskSql(sqlAuditResultData.getSql());
                            List<Long> businessId = riskSqlScanAppKeyMapper.selectBusinessId(appkeyIds.get(0));
                            if (!businessId.isEmpty()) {
                                riskSqlPo.setBusinessId(Math.toIntExact(businessId.get(0)));
                            }
                            boolean shouldInsert = true;
                            for (UserGetSqlAuditResultByUserActionResponse.RiskData.riskReason result : sqlAuditResultData.getResult()) {
                                if (result != null && Objects.equals(result.getRiskLevel(), riskLevel_1)) {
                                    String riskReason = result.getMessage();
                                    if (shouldSkipRiskReason(riskReason)) {
                                        shouldInsert = false;
                                        break;  // 如果风险原因在白名单中，跳过这条记录
                                    }
                                    riskSqlPo.setRiskReason(riskReason);
                                    break;  // 找到匹配项后立即退出循环
                                }
                            }

                            if (riskSqlPo.getLevel() != null && shouldInsert) {
                                riskSqlScanSqlMapper.insert(riskSqlPo);
                            }

                            RiskSqlScanResultDataPO riskSqlScanResultDataPO=new RiskSqlScanResultDataPO();
                            riskSqlScanResultDataPO.setResultAnalysisId(analysisId);
                            String dbName=sqlAuditResultData.getDbName();
                            riskSqlScanResultDataPO.setDbName(dbName);
                            riskSqlScanResultDataPO.setAppkey(appkey);
                            if(riskSqlScanResultDataMapper.selectId(analysisId,appkey,dbName).isEmpty()){
                                riskSqlScanResultDataMapper.insert(riskSqlScanResultDataPO);
                            }

                        }
                    }
                    UpdateWrapper<RiskSqlScanTaskPO> updateWrapper=new UpdateWrapper<>();
                    updateWrapper.eq("id",riskSqlScanTaskPO.getId());
                    updateWrapper.set("taskSatus",RiskSqlScanTaskStatusEnum.SUCCESS);
                    RiskSqlScanTaskPO riskSqlScanTaskPO1=new RiskSqlScanTaskPO();
                    riskSqlScanTaskPO1.setTaskSatus(RiskSqlScanTaskStatusEnum.SUCCESS);
                    riskSqlScanTaskPO1.setId(riskSqlScanTaskPO.getId());
                    riskSqlScanTaskMapper.update(riskSqlScanTaskPO1,updateWrapper);
                }else{
                    UpdateWrapper<RiskSqlScanTaskPO> updateWrapper=new UpdateWrapper<>();
                    updateWrapper.eq("id",riskSqlScanTaskPO.getId());
                    updateWrapper.set("taskSatus",RiskSqlScanTaskStatusEnum.FAIL);
                    RiskSqlScanTaskPO riskSqlScanTaskPO1=new RiskSqlScanTaskPO();
                    riskSqlScanTaskPO1.setTaskSatus(RiskSqlScanTaskStatusEnum.FAIL);
                    riskSqlScanTaskPO1.setId(riskSqlScanTaskPO.getId());
                    riskSqlScanTaskMapper.update(riskSqlScanTaskPO1,updateWrapper);
                    setTask(riskSqlScanTaskPO.getCommitId(),riskSqlScanTaskPO.getReceiver(),riskSqlScanTaskPO.getAppkeys(),riskSqlScanTaskPO.getSwimLane());
                }

        }
    }
    private boolean shouldSkipRiskReason(String riskReason) {
        // 如果白名单为空，不跳过任何风险原因
        if (riskReasonWhitelist.isEmpty()) {
            return false;
        }
        return riskReasonWhitelist.contains(riskReason);
    }


}
