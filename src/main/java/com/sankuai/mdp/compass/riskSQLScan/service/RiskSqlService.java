package com.sankuai.mdp.compass.riskSQLScan.service;/* *************
 * @author: liuYang359
 * @date: 2023/10/12 17:03
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlPo;

import java.util.List;

public interface RiskSqlService {
    public List<RiskSqlPo> getsqlByBusinessAndAppKey(Integer BusinessId,Integer appKeyId,int deleted);
    public int deletebyAppkeyId(Integer appKeyId);
    public int deletebyBusinessId(Integer businessId);
}
