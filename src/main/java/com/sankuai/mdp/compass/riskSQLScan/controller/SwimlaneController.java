package com.sankuai.mdp.compass.riskSQLScan.controller;

import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.riskSQLScan.service.SwimlaneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: niuji<PERSON><PERSON>
 * @date: 2024/6/7
 * @time: 上午9:09
 */
@RestController
@RequestMapping("/compass/api/riskSql/swimlane")
public class SwimlaneController {
    @Autowired
    private SwimlaneService swimlaneService;

    @RequestMapping("/getAppkeyByMis")
    public Resp getAppkeyByMis(@RequestParam String mis) {
        return Resp.success(swimlaneService.getAppkeyByMis(mis));
    }
    @RequestMapping("/getSwimlaneList")
    public Resp getSwimlaneList(@RequestParam String mis,@RequestParam String appkey) {
        return Resp.success(swimlaneService.getSwimlaneList(mis,appkey));
    }
    @RequestMapping("/delete")
    public Resp deleteSwimlane(@RequestParam Long id) {
        return Resp.success(swimlaneService.deleteSwimlane(id));
    }
    @RequestMapping("/close")
    public Resp closeSwimlane(@RequestParam Long id) {
        return Resp.success(swimlaneService.closeSwimlane(id));
    }
}
