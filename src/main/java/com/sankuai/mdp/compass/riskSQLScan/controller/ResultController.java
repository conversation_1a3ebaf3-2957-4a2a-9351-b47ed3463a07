package com.sankuai.mdp.compass.riskSQLScan.controller;

import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.riskSQLScan.service.Impl.RdsListenerServiceImpl;
import com.sankuai.mdp.compass.riskSQLScan.service.RdsListenerService;
import com.sankuai.mdp.compass.riskSQLScan.service.ResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/6/7
 * @time: 下午5:27
 */
@RestController
@RequestMapping("compass/api/riskSql/result")
public class ResultController {
    @Autowired
    private ResultService resultService;
    @Autowired
    private RdsListenerService rdsListenerService;

    @RequestMapping("/getAppkeyByMis")
    public Resp getAppkeyByMis(@RequestParam String mis) {
        return Resp.success(resultService.getAppkeyByMis(mis));
    }
    @RequestMapping("/getSwimlaneByMis")
    public Resp getSwimlaneByMis(@RequestParam String mis) {
        return Resp.success(resultService.getSwimlaneByMis(mis));
    }
    @RequestMapping("/getResultList")
    public Resp getResultByAppkey(@RequestParam String mis,@RequestParam String appkey,@RequestParam String swimlane) {
        return Resp.success(resultService.getResultList(mis,appkey,swimlane));
    }
}
