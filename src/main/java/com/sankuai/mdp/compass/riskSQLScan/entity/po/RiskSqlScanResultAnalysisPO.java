package com.sankuai.mdp.compass.riskSQLScan.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午2:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("risk_sql_scan_result_analysis")
public class RiskSqlScanResultAnalysisPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "taskId")
    private Long TaskId;
    @TableField(value = "appkey")
    private String appkey;
    @TableField(value = "swimlane")
    private String swimlane;
    @TableField(value = "mis")
    private String mis;
    @TableField(value = "totalRiskNum")
    private Integer totalRiskNum;
    @TableField(value = "totalHitWhitelistNum")
    private Integer totalHitWhitelistNum;
    @TableField(value = "highRiskNum")
    private Integer highRiskNum;
    @TableField(value = "highHitWhitelistNum")
    private Integer highHitWhitelistNum;
    @TableField(value = "mediumRiskNum")
    private Integer mediumRiskNum;
    @TableField(value = "mediumHitWhitelistNum")
    private Integer mediumHitWhitelistNum;
    @TableField(value = "lowRiskNum")
    private Integer lowRiskNum;
    @TableField(value = "lowHitWhitelistNum")
    private Integer lowHitWhitelistNum;
    @TableField(value = "noRiskNum")
    private Integer noRiskNum;
    @TableField(value = "noHitWhitelistNum")
    private Integer noHitWhitelistNum;
    @TableField(value = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
