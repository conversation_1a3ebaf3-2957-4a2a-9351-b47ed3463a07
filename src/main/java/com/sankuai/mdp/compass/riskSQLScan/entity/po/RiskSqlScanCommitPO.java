package com.sankuai.mdp.compass.riskSQLScan.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午2:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("risk_sql_scan_commit")
public class RiskSqlScanCommitPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "appkeys")
    private  String appkeys;
    @TableField(value = "swimlane")
    private  String swimlane;
    @TableField(value = "createTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
    @TableField(value = "updateTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date updateTime;
    @TableField(value = "deleted")
    Integer deleted;


}
