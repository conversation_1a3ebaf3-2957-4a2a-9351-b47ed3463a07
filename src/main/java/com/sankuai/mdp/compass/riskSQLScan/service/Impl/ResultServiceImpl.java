package com.sankuai.mdp.compass.riskSQLScan.service.Impl;

import com.sankuai.mdp.compass.riskSQLScan.entity.vo.ResultInfoVO;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanResultAnalysisMapper;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanResultDataMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.ResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/6/7
 * @time: 下午5:33
 */
@Service
public class ResultServiceImpl implements ResultService {
    @Autowired
    private RiskSqlScanResultAnalysisMapper riskSqlScanResultAnalysisMapper;
    @Autowired
    private RiskSqlScanResultDataMapper riskSqlScanResultDataMapper;
    @Override
    public List<String> getAppkeyByMis(String mis) {
        if(mis==null||mis.isEmpty()) {
            return riskSqlScanResultAnalysisMapper.selectAllAppkey();
        }else {
            return riskSqlScanResultAnalysisMapper.selectAppkeyByMis(mis);
        }
    }

    @Override
    public List<String> getSwimlaneByMis(String mis) {
        if (mis == null || mis.isEmpty()) {
            return riskSqlScanResultAnalysisMapper.selectAllSwimlane();
        } else {
            return riskSqlScanResultAnalysisMapper.selectSwimlaneByMis(mis);
        }
    }

    @Override
    public List<ResultInfoVO> getResultList(String mis, String appkey, String swimlane) {
       if(mis==null||mis.isEmpty()){
           if((appkey==null||appkey.isEmpty())&&(swimlane==null||swimlane.isEmpty())){
               return riskSqlScanResultAnalysisMapper.selectAllResult();
           }else if((appkey!=null)&&(swimlane==null||swimlane.isEmpty())){
               List<ResultInfoVO> resultInfoVOS = riskSqlScanResultAnalysisMapper.selectResultByAppkey(appkey);
               for(ResultInfoVO resultInfoVO : resultInfoVOS) {
                   List<String> rds=riskSqlScanResultDataMapper.selectDBsByResultAnalysisId(resultInfoVO.getId());
                   resultInfoVO.setDbs(rds);
                }
               return resultInfoVOS;
           }else if(appkey == null || appkey.isEmpty()) {
               return riskSqlScanResultAnalysisMapper.selectResultBySwimlane(swimlane);
           }else {
               return riskSqlScanResultAnalysisMapper.selectResultByAppkeyAndSwimlane(appkey,swimlane);
           }
       }else {
           if((appkey==null||appkey.isEmpty())&&(swimlane==null||swimlane.isEmpty())){
               return riskSqlScanResultAnalysisMapper.selectResultByMis(mis);
           }else if((appkey!=null)&&(swimlane==null||swimlane.isEmpty())){
               List<ResultInfoVO> resultInfoVOS = riskSqlScanResultAnalysisMapper.selectResultByMisAndAppkey(mis, appkey);
               for(ResultInfoVO resultInfoVO : resultInfoVOS) {
                   List<String> rds=riskSqlScanResultDataMapper.selectDBsByResultAnalysisId(resultInfoVO.getId());
                   resultInfoVO.setDbs(rds);
               }
               return resultInfoVOS;
           }else if(appkey == null || appkey.isEmpty()) {
               return riskSqlScanResultAnalysisMapper.selectResultByMisAndSwimlane(mis,swimlane);
           }else {
               return riskSqlScanResultAnalysisMapper.selectResultByMisAndAppkeyAndSwimlane(mis,appkey,swimlane);
           }
       }
    }
}
