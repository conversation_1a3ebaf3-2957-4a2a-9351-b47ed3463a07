package com.sankuai.mdp.compass.riskSQLScan.mapper;/* *************
 * @author: liuYang359
 * @date: 2023/10/12 20:54
 * @description:
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RiskSqlScanSqlMapper extends BaseMapper<RiskSqlPo> {
    @Select("SELECT * FROM risk_sql_info " +
            "WHERE app_key_id = #{appKeyId} AND deleted = #{deleted} AND business_id = #{BusinessId} AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) ORDER BY create_time DESC ")
    List<RiskSqlPo> selectSqlList(Integer appKeyId, int deleted,Integer BusinessId);
}
