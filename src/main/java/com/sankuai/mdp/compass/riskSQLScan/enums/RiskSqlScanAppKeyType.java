package com.sankuai.mdp.compass.riskSQLScan.enums;/* *************
 * @author: liuYang359
 * @date: 2023/9/26 11:43
 * @description:
 */

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.core.enums.IEnum;

public enum RiskSqlScanAppKeyType  {
    PROD(1, "线上巡检"),
    TEST(0,"线下巡检");
    RiskSqlScanAppKeyType(int code,String message)
    {
        this.type=code;
        this.message=message;
    }

    @EnumValue
    private final int type;
    private  String message;


}
