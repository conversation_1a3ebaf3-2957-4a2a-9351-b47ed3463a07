package com.sankuai.mdp.compass.riskSQLScan.enums;/* *************
 * @author: liuYang359
 * @date: 2023/10/25 11:54
 * @description:
 */

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum DeleteTypeEnum  {
    DELETED(1,"成功"),
    ONLINE(0,"线上态");

    DeleteTypeEnum(int code, String message)
    {
        this.code=code;
        this.message=message;
    }
    @EnumValue
    private int code;
    private  String message;
}
