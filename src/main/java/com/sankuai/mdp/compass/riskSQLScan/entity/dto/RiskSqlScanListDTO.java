package com.sankuai.mdp.compass.riskSQLScan.entity.dto;/* *************
 * @author: liuYang359
 * @date: 2023/10/10 15:04
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlPo;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanBusinessInfoPO;
import lombok.Data;

import java.util.List;

@Data
public class RiskSqlScanListDTO implements Comparable<RiskSqlScanListDTO>{
    public RiskSqlScanListDTO(RiskSqlScanBusinessInfoPO businessInfoPO, RiskSqlScanAppKeyPO appKey, List<RiskSqlPo> input) {
        id = businessInfoPO.getId();
        businessName = businessInfoPO.getBusinessName();
        if (appKey != null) {
            this.appKey = appKey.getAppkey();
            appKeyId = appKey.getId();
        }
        mis = businessInfoPO.getAdminMisList();
        if (input != null) {
            sum = input.size();

        } else {
            sum = 0;

        }
        riskSqlPoList = input;


    }
    @Override
    public int compareTo(RiskSqlScanListDTO o) {
        // TODO Auto-generated method stub

        int result=o.getSum()-this.sum;
        return result;
    }

    public RiskSqlScanListDTO() {
    }

    int id;

    String businessName;

    String appKey;

    Integer appKeyId;

    String mis;


    List<RiskSqlPo> riskSqlPoList;

    int sum;
}
