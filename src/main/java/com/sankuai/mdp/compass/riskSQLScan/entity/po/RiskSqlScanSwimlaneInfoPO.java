package com.sankuai.mdp.compass.riskSQLScan.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/31
 * @time: 上午10:06
 */
@Data
@TableName("risk_sql_scan_swimlane_info")
public class RiskSqlScanSwimlaneInfoPO {
    //主
    @TableId(value = "id",type = IdType.AUTO)
    Long id;
    @TableField(value = "swimlane")
    String swimlane;
    //逻辑删除
    @TableLogic(value = "0", delval = "id")
    @TableField(value = "isDeleted")
    Integer isDeleted;
    @TableField(value = "isActive")
    Integer isActive;
    @TableField(value = "env")
    String env;
    @TableField(value = "host_name")
    String hostName;
    @TableField(value = "app_key")
    String appKey;
    @TableField(value = "area")
    String area;
    @TableField(value = "branch")
    String branch;
    @TableField(value = "target_host_name")
    String targetHostName;
    @TableField(value = "ip")
    String ip;
    @TableField(value="mis")
    String mis;
    @TableField(value = "type")
    String type;
    @TableField(value = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
    @TableField(value = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date updateTime;
    @TableField(value = "deleteTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date deleteTime;

}
