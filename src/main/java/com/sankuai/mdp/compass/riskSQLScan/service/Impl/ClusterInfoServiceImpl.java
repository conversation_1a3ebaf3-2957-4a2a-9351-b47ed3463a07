package com.sankuai.mdp.compass.riskSQLScan.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.ClusterInfoPO;
import com.sankuai.mdp.compass.riskSQLScan.mapper.ClusterInfoMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.ClusterInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ClusterInfoServiceImpl implements ClusterInfoService {

    @Autowired
    private ClusterInfoMapper clusterInfoMapper;

    @Override
    @Transactional
    public void saveUnauthorizedCluster(List<String> clusterNames, String appkey) {
        if (clusterNames == null || clusterNames.isEmpty() || appkey == null || appkey.isEmpty()) {
            return;
        }

        // 1. clusterNames去重
        List<String> uniqueClusterNames = new ArrayList<>(new HashSet<>(clusterNames));

        Date now = new Date();

        for (String clusterName : uniqueClusterNames) {
            // 2. 检查集群是否已存在
            ClusterInfoPO existingCluster = clusterInfoMapper.selectOne(
                    new QueryWrapper<ClusterInfoPO>()
                            .eq("cluster_name", clusterName)
                            .eq("appkey", appkey)
            );

            if (existingCluster == null) {
                // 集群不存在，插入新记录
                ClusterInfoPO newCluster = new ClusterInfoPO();
                newCluster.setClusterName(clusterName);
                newCluster.setAppkey(appkey);
                newCluster.setIsProcess(0);
                newCluster.setCreateTime(now);
                newCluster.setUpdateTime(now);
                clusterInfoMapper.insert(newCluster);
            } else if (existingCluster.getIsProcess() == 1) {
                // 集群已存在且已处理，将IsProcess改回0
                existingCluster.setIsProcess(0);
                existingCluster.setUpdateTime(now);
                clusterInfoMapper.updateById(existingCluster);
            } else {
                // 集群已存在且IsProcess为0，只更新UpdateTime
                existingCluster.setUpdateTime(now);
                clusterInfoMapper.updateById(existingCluster);
            }
        }
    }

    @Override
    @Transactional
    public void updateProcessedClusters(String appkey) {
        ClusterInfoPO updateInfo = new ClusterInfoPO();
        updateInfo.setIsProcess(1);
        updateInfo.setUpdateTime(new Date());

        clusterInfoMapper.update(updateInfo,
                new QueryWrapper<ClusterInfoPO>()
                        .eq("is_process", 0)
                        .eq("appkey", appkey));
    }

    @Override
    public List<String> getFilteredClusters(String appkey) {
        QueryWrapper<ClusterInfoPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_process", 0)
                .eq("appkey", appkey);
        List<ClusterInfoPO> clusterInfoList = clusterInfoMapper.selectList(queryWrapper);
        return clusterInfoList.stream()
                .map(ClusterInfoPO::getClusterName)
                .collect(Collectors.toList());
    }
}
