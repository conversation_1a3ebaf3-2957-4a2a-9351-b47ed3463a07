package com.sankuai.mdp.compass.riskSQLScan.service.Impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanSwimlaneInfoPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.vo.SwimlaneInfoVO;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanSwimlaneInfoMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.SwimlaneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/6/7
 * @time: 上午9:40
 */
@Service
public class SwimlaneServiceImpl implements SwimlaneService {
    @Autowired
    private RiskSqlScanSwimlaneInfoMapper swimlaneInfoMapper;

    @Override
    public List<String> getAppkeyByMis(String mis){
        if (mis == null || mis.isEmpty()) {
            // 如果mis不存在，返回所有appkey列表，注意去重
            return swimlaneInfoMapper.selectAllAppkeys();
        } else {
            // 如果mis存在，返回对应的appkey列表
            return swimlaneInfoMapper.selectAppkeysByMis(mis);
        }
    }

    @Override
    public List<SwimlaneInfoVO> getSwimlaneList(String mis,String appkey) {
        if ((mis == null||mis.isEmpty())&&(appkey==null||appkey.isEmpty())) {
            return swimlaneInfoMapper.selectAllSwimlaneInfo();
        } else if (mis != null && !mis.isEmpty() && (appkey!=null&&!appkey.isEmpty())) {
            return swimlaneInfoMapper.selectSwimlaneInfoByMisAndAppkey(mis, appkey);
        } else if(mis != null && !mis.isEmpty()){
            return swimlaneInfoMapper.selectSwimlaneInfoByMis(mis);
        }else{
            return swimlaneInfoMapper.selectSwimlaneInfoByAppkey(appkey);
        }
    }

    @Override
    public Integer deleteSwimlane(Long id) {
        UpdateWrapper<RiskSqlScanSwimlaneInfoPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("isDeleted",1);
        updateWrapper.set("deleteTime",new Date());
        updateWrapper.set("isActive",0);
        RiskSqlScanSwimlaneInfoPO temp=new RiskSqlScanSwimlaneInfoPO();
        return swimlaneInfoMapper.update(temp,updateWrapper);
    }

    @Override
    public Integer closeSwimlane(Long id) {
        RiskSqlScanSwimlaneInfoPO riskSqlScanSwimlaneInfoPO = swimlaneInfoMapper.selectById(id);
        if (riskSqlScanSwimlaneInfoPO.getIsActive() == 1) {
            UpdateWrapper<RiskSqlScanSwimlaneInfoPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("isActive",0);
            RiskSqlScanSwimlaneInfoPO temp = new RiskSqlScanSwimlaneInfoPO();
            temp.setIsActive(0);
            temp.setId(id);
            return swimlaneInfoMapper.update(temp, updateWrapper);
        }else {
            UpdateWrapper<RiskSqlScanSwimlaneInfoPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("isActive",1);
            RiskSqlScanSwimlaneInfoPO temp = new RiskSqlScanSwimlaneInfoPO();
            temp.setIsActive(1);
            temp.setId(id);
            return swimlaneInfoMapper.update(temp, updateWrapper);
        }
    }

}
