package com.sankuai.mdp.compass.riskSQLScan.service.Impl;/* *************
 * @author: liuYang359
 * @date: 2023/12/14 11:53
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.*;
import com.sankuai.mdp.compass.riskSQLScan.service.RdsService;
import com.sankuai.mdp.compass.riskSQLScan.utils.Rds.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class RdsServiceImpl implements RdsService {

    @Autowired
    private FullSqlInvoker fullSqlInvoker;
    @Autowired
    private DiffSqlInvoker diffSqlInvoker;
    @Autowired
    private CreateSqlAuditTaskInvoker createSqlAuditTaskInvoker;
    @Autowired
    private GetSqlAuditTaskInvoker getSqlAuditTaskInvoker;
    @Autowired
    private GetSqlAuditResultInvoker getSqlAuditResultInvoker;
    @Autowired
    private GetReportInvoker getReportInvoker;
    @Autowired
    private GetReportByAppkeyInvoker getReportByAppkeyInvoker;
    @Autowired
    private GetReportResultByAppkeyInvoker getReportResultByAppkeyInvoker;
    @Autowired
    private GetReportResultInvoker getReportResultInvoker;

    @Override
    public List<RDSListFullSqlByAppkeyActionResponse> listFullSqlByAppkey(RDSListFullSqlByAppkeyActionRequest request) {
        if(request == null || request.getAppkeys().isEmpty() || request.getStartTime().isEmpty() || request.getEndTime().isEmpty()||request.getSqlLimit()==0) {
            return new ArrayList<>();
        }
        return fullSqlInvoker.execute(request);
    }

    @Override
    public List<RDSListDiffSqlByAppkeyActionResponse> listDiffSqlByAppkey(RDSListDiffSqlByAppkeyActionRequest request) {
        if(request==null|| request.getAppkeys().isEmpty()||request.getSqlLimit()==0||request.getSource()==null||request.getTarget()==null) {
            return new ArrayList<>();
        }
        return diffSqlInvoker.execute(request);
    }

    @Override
    public UserCreateSqlAuditTaskByUserActionResponse createSqlAuditTask(UserCreateSqlAuditTaskByUserActionRequest request) {
        if(request==null||request.getSubmitter().isEmpty()||request.getSqls().isEmpty()){
            return new UserCreateSqlAuditTaskByUserActionResponse();
        }
        return createSqlAuditTaskInvoker.execute(request);
    }

    @Override
    public UserGetSqlAuditTaskByUserActionResponse getSqlAuditTask(String username,int taskId) {
        if(username.isEmpty()||taskId==0){
            return new UserGetSqlAuditTaskByUserActionResponse();
        }
        return getSqlAuditTaskInvoker.execute(new UserGetSqlAuditTaskByUserActionRequest(username,taskId));
    }

    @Override
    public UserGetSqlAuditResultByUserActionResponse getSqlAuditResult(String username,int taskId) {
        if (username.isEmpty()||taskId==0){
            return new UserGetSqlAuditResultByUserActionResponse();
        }
        return getSqlAuditResultInvoker.execute(new UserGetSqlAuditResultByUserActionRequest(username,taskId));
    }

    @Override
    public List<GetReportByOrgActionResponse> getReportByOrgAction(GetReportByOrgActionRequest request) {
        if(request==null||request.getUserName().isEmpty()||request.getOrgId()==0){
            return  new ArrayList<>();
        }
        return getReportInvoker.execute(request);
    }

    @Override
    public List<GetReportResultsByOrgActionResponse> getReportResultByOrgAction(GetReportResultsByOrgActionRequest request) {
        if(request==null||request.getUsername().isEmpty()||request.getOrgId()==0||request.getWeek()==0){
            return new ArrayList<>();
        }
        return getReportResultInvoker.execute(request);
    }

    @Override
    public List<GetReportByAppkeyActionResponse> getReportByAppkeyAction(GetReportByAppkeyActionRequest request) {
        if(request==null||request.getUsername().isEmpty()||request.getAppkey().isEmpty()){
            return new ArrayList<>();
        }
        return getReportByAppkeyInvoker.execute(request);
    }

    @Override
    public List<GetReportResultsByAppkeyActionResponse> getReportResultByAppkeyAction(GetReportResultsByAppkeyActionRequest request) {
        if (request == null||request.getUsername().isEmpty()||request.getAppkey().isEmpty()||request.getTaskId()==0) {
            return new ArrayList<>();
        }
        return getReportResultByAppkeyInvoker.execute(request);
    }
}
