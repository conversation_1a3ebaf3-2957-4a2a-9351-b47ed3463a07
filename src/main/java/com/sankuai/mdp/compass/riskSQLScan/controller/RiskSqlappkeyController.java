package com.sankuai.mdp.compass.riskSQLScan.controller;/* *************
 * @author: liuYang359
 * @date: 2023/11/13 19:27
 * @description:
 */

import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.DeleteDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlScanAppkeyService;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/compass/api/riskSql/appkey")
public class RiskSqlappkeyController {
    @Autowired
    RiskSqlScanAppkeyService riskSqlScanAppkeyService;
    @Autowired
    RiskSqlService riskSqlService;

    @RequestMapping("/selectByBusinessIDAndMis")
    public Resp selectbybusinessID(@RequestParam("businessID") Integer businessId, @RequestParam("mis") String mis) {
        return Resp.success(riskSqlScanAppkeyService.selectByAppkeyIdAndMis(businessId, mis));
    }
    @RequestMapping("/setSwitch")
    public Resp setSwitch(@RequestParam("appkeyId") Integer appkeyId, @RequestParam("type") Integer type,@RequestParam("switchStaus") boolean staus) {
        return Resp.success(riskSqlScanAppkeyService.setSwitch(appkeyId,type,staus));
    }

    @RequestMapping("/deleteAppKeyById")
    public Resp deleteById(@RequestParam("appkeyId") Integer appkeyId) {
        riskSqlScanAppkeyService.deleteById(appkeyId);

        return Resp.success( DeleteDTO.builder().deleteNum(riskSqlService.deletebyAppkeyId(appkeyId)).build());
    }

    @RequestMapping("/appkeyDuplicate")
    public Resp appkeyDuplicate(@RequestParam("appkey") String appkey){
        return Resp.success(riskSqlScanAppkeyService.duplicate(appkey));
    }
    @RequestMapping("/createAppkey")
    public Resp createAppkey( @RequestBody RiskSqlScanAppKeyPO appkey){
        return Resp.success(riskSqlScanAppkeyService.createAppKey(appkey));
    }


}
