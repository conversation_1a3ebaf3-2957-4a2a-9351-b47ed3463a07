package com.sankuai.mdp.compass.riskSQLScan.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/31
 * @time: 上午11:07
 */
@Data
public class CargoResponseDTO {
    private List<CargoResponseData> data;
    private String errorMsg;
    private String status;


    @Data
    @Accessors(chain = true)
    public static class CargoResponseData {
        private String env;
        private String hostname;
        private CargoResponseDataMessage message;
        private Date timestamp;
        private String type;
        @Data
        @Accessors(chain = true)
        public static class CargoResponseDataMessage {
            private String action;
            private String action_zh;
            private String appkey;
            private String area;
            private String branch;
            private String cell;
            private String cloud_image_id;
            private double duration;
            private boolean enable_swimlane_two;
            private String error;
            private String grouptags;
            private String hostname;
            private String hosttype;
            private String ip;
            private String op_user;
            private String plustemplate;
            private String releasename;
            private String runner_uuid;
            private String stack_uuid;
            private String status;
            private String swimlane;
        }
    }
}
