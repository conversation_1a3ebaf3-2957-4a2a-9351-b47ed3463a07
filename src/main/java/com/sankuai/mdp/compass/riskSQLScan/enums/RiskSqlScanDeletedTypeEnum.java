package com.sankuai.mdp.compass.riskSQLScan.enums;/* *************
 * @author: liuYang359
 * @date: 2023/10/12 18:53
 * @description:
 */

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum RiskSqlScanDeletedTypeEnum {
    DELETED(1, "删除态"),
    ONLINE(0,"线上态");
    RiskSqlScanDeletedTypeEnum(int code,String message)
    {
        this.type=code;
        this.message=message;

    }


    @EnumValue
    private final int type;
    private  String message;


}
