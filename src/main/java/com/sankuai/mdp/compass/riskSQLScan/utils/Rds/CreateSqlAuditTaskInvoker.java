package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserCreateSqlAuditTaskByUserActionResponse;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserCreateSqlAuditTaskByUserActionRequest;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 下午4:39
 */
@Component
public class CreateSqlAuditTaskInvoker extends AbstractRdsApiInvoker<UserCreateSqlAuditTaskByUserActionRequest, UserCreateSqlAuditTaskByUserActionResponse> {
    @Override
    protected String getAction() {
        return "CreateSqlAuditTaskAction";
    }

    @Override
    protected UserCreateSqlAuditTaskByUserActionResponse convert2Model(String data) {
        return JSON.parseObject(data, UserCreateSqlAuditTaskByUserActionResponse.class);
    }
}
