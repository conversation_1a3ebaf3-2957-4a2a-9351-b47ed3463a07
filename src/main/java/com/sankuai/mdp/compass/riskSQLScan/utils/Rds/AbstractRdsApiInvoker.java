package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;
/* *************
 * @author: liuYang359
 * @date: 2023/11/28 16:11
 * @description:
 */

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserCreateSqlAuditTaskByUserActionRequest;
import com.sankuai.mdp.compass.riskSQLScan.service.ClusterInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public abstract class AbstractRdsApiInvoker<PARAM, RESULT> implements RdsApiInvoker<PARAM, RESULT> {

    public static final String DATE_GMT_FORMAT_PATTERN = "E, dd MMM yyyy HH:mm:ss 'GMT'";

    public static final String DATE_TIME_ZONE = "GMT";

    //下面的这些属性值最好放在项目的配置文件里
    private String path = "https://rdsopenapi-test-dba.vip.sankuai.com/api/v2/open/gateway";

    private String version = "v1";

    private String sign = "POST /api/v2/open/gateway %s"+"\n"+"%s";

    private String authorization = "MWS %s:%s";

    //todo  accessKey和secretKey填写你在平台上申请的
    private String accessKey = "n8qaAy";

    private String secretKey = "dpibm6tDQPXao7z29jf4wuVmqYcujshz5KfilWgA2dh4oJa5Sx6MwQYxyRFLH7nG";

    protected abstract String getAction();

    protected abstract RESULT convert2Model(String data);

    @Autowired
    private ClusterInfoService clusterInfoService;
    @Override
    public RESULT execute(PARAM param) {
        try {
            RdsApiRequest rdsApiRequest = new RdsApiRequest(getAction(), version, new JSONObject(param));
            HttpResponse response = HttpUtil.createPost(path)
                    .addHeaders(buildHeader())
                    .body(new JSONObject(rdsApiRequest).toString())
                    .execute();
            String httpResult = new String(response.bodyBytes());
            RdsApiResponse rdsApiResponse = RdsApiResponse.instanceOf(httpResult);
            if (Objects.isNull(rdsApiResponse) || rdsApiResponse.getCode() != 0) {
                log.error("RDS请求返回值异常, action: {}, param: {}, result: {}", getAction(), param, httpResult);
                // 在这里添加逻辑来处理无权访问集群的情况
                if (rdsApiResponse != null && rdsApiResponse.getCode() == 100105009) {
                    List<String> clusterNames = extractClusterName(rdsApiResponse.getUserMessage());
                    if (!clusterNames.isEmpty()) {
                        String appkey = extractAppkey(param);
                        clusterInfoService.saveUnauthorizedCluster(clusterNames, appkey);
                    }
                }
            }
            if (StringUtils.isBlank(rdsApiResponse.getData()) || StringUtils.equalsIgnoreCase(rdsApiResponse.getData(), "{}")) {
                log.warn("RDS请求返回值为空, action: {}, param: {}, result: {}", getAction(), param, httpResult);
                return null;
            }
            return convert2Model(rdsApiResponse.getData());
        } catch (Exception e) {
            log.error("RDS请求异常, action: {}, param: {}", getAction(), param, e);
            throw new RuntimeException("RDS请求异常");
        }
    }

    private Map<String, String> buildHeader() throws Exception {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_GMT_FORMAT_PATTERN, Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone(DATE_TIME_ZONE));
        String date = dateFormat.format(new Date());

        String strToSign = String.format(sign, getAction(), date);
        String signature = hmacSha1(strToSign, secretKey);
        String strToAuthorization = String.format(authorization, accessKey, signature);

        Map<String, String> httpHeaders = new HashMap<>();
        httpHeaders.put("Date", date);
        httpHeaders.put("Authorization", strToAuthorization);

        return httpHeaders;
    }

    private String hmacSha1(String dataStr, String keyStr) {
        SecretKeySpec signingKey = new SecretKeySpec(keyStr.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        Mac mac;
        try {
            mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
        } catch (Exception e) {
            log.error("Mac 初始化异常,dataStr={}", dataStr, e);
            throw new RuntimeException("rds.open.api初始化Mac异常");
        }
        byte[] bytes = mac.doFinal(dataStr.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(bytes);
    }
    private List<String> extractClusterName(String userMessage) {
        // 从userMessage中提取集群名称，例如："无权访问集群: shangouordercenter"
        List<String> clusterNames = new ArrayList<>();
        String[] parts = userMessage.split(":");
        if (parts.length > 1) {
            String[] clusters = parts[1].split(",");
            for (String cluster : clusters) {
                clusterNames.add(cluster.trim());
            }
        }
        return clusterNames;
    }
    private String extractAppkey(PARAM param) {
        if (param instanceof UserCreateSqlAuditTaskByUserActionRequest) {
            UserCreateSqlAuditTaskByUserActionRequest request = (UserCreateSqlAuditTaskByUserActionRequest) param;
            if (request.getSqls() != null && !request.getSqls().isEmpty()) {
                return request.getSqls().get(0).getAppkey();
            }
        }
        // 这里需要根据PARAM的具体类型来实现appkey的提取
        // 假设PARAM是一个Map或者是一个包含appkey字段的对象
        if (param instanceof Map) {
            return (String) ((Map) param).get("appkey");
        } else {
            // 如果PARAM是一个自定义对象，假设它有一个getAppkey方法
            try {
                return (String) param.getClass().getMethod("getAppkey").invoke(param);
            } catch (Exception e) {
                log.error("无法从param中提取appkey", e);
                return "com.sankuai.ds.be.http";
            }
        }
    }

}