//package com.sankuai.mdp.compass.riskSQLScan.test;
//
///**
// * @description:
// * @author: niujiechao
// * @date: 2024/5/30
// * @time: 上午11:12
// */
//
//import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.*;
//import com.sankuai.mdp.compass.riskSQLScan.service.Impl.RdsServiceImpl;
//import com.sankuai.mdp.compass.riskSQLScan.utils.Rds.*;
//import org.junit.Before;
//import org.junit.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.Mockito.*;
//import static org.junit.Assert.*;
//
//public class RdsServiceImplTest {
//
//    @InjectMocks
//    private RdsServiceImpl rdsService;
//
//    @Mocks
//    private FullSqlInvoker fullSqlInvoker;
//
//    @Mock
//    private DiffSqlInvoker diffSqlInvoker;
//
//    @Mock
//    private CreateSqlAuditTaskInvoker createSqlTaskInvoker;
//
//    @Mock
//    private GetSqlAuditTaskInvoker getSqlAuditTaskInvoker;
//
//    @Mock
//    private GetSqlAuditResultInvoker getSqlAuditResultInvoker;
//
//    @Mock
//    private GetReportResultByAppkeyInvoker getReportResultByAppkeyInvoker;
//
//    @Mock
//    private GetReportByAppkeyInvoker getReportByAppkeyInvoker;
//
//    @Mock
//    private GetReportInvoker getReportInvoker;
//
//    @Mock
//    private GetReportResultInvoker getReportResultInvoker;
//
//
//    @Before
//    public void setUp() {
//        MockitoAnnotations.initMocks(this);
//    }
//
//    /**
//     * 测试 listFullSqlByAppkey 方法，当输入参数为 null 时
//     */
//    @Test
//    public void testListFullSqlByAppkeyInputNull() throws Throwable {
//        // arrange
//        RDSListFullSqlByAppkeyActionRequest request = null;
//
//        // act
//        List<RDSListFullSqlByAppkeyActionResponse> result = rdsService.listFullSqlByAppkey(request);
//
//        // assert
//        assertTrue(result.isEmpty());
//    }
//
//    /**
//     * 测试 listFullSqlByAppkey 方法，当输入参数有效时
//     */
//    @Test
//    public void testListFullSqlByAppkeyValidInput() throws Throwable {
//        // arrange
//        RDSListFullSqlByAppkeyActionRequest request = mock(RDSListFullSqlByAppkeyActionRequest.class);
//        when(request.getAppkeys()).thenReturn("appkey1,appkey2");
//        when(request.getStartTime()).thenReturn("2023-01-01");
//        when(request.getEndTime()).thenReturn("2023-01-02");
//        when(request.getSqlLimit()).thenReturn(1);
//        List<RDSListFullSqlByAppkeyActionResponse> expectedResponse = new ArrayList<>();
//        when(fullSqlInvoker.execute(request)).thenReturn(expectedResponse);
//
//        // act
//        List<RDSListFullSqlByAppkeyActionResponse> result = rdsService.listFullSqlByAppkey(request);
//
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(fullSqlInvoker, times(1)).execute(request);
//    }
//    @Test
//    public void testListDiffSqlByAppkeyRequestIsNull() throws Throwable {
//        // arrange
//        RDSListDiffSqlByAppkeyActionRequest request = null;
//
//        // act
//        List<RDSListDiffSqlByAppkeyActionResponse> result = rdsService.listDiffSqlByAppkey(request);
//
//        // assert
//        assertNull(result);
//    }
//    /**
//     * 测试 listDiffSqlByAppkey 方法，当输入参数有效时
//     */
//    @Test
//    public void testListDiffSqlByAppkeyValidInput() throws Throwable {
//        // arrange
//        RDSListDiffSqlByAppkeyActionRequest request = mock(RDSListDiffSqlByAppkeyActionRequest.class);
//        when(request.getAppkeys()).thenReturn("appkey1,appkey2");
//        when(request.getSqlLimit()).thenReturn(1); // 确保 sqlLimit 大于 0
//        //when(request.getSource()).thenReturn(new RDSListDiffSqlByAppkeyActionRequest.SourcePayLoad()); // 确保 source 不为 null
//        //when(request.getTarget()).thenReturn(new RDSListDiffSqlByAppkeyActionRequest.TargetPayLoad());
//        List<RDSListDiffSqlByAppkeyActionResponse> expectedResponse = new ArrayList<>();
//        when(diffSqlInvoker.execute(request)).thenReturn(expectedResponse);
//
//        // act
//        List<RDSListDiffSqlByAppkeyActionResponse> result = rdsService.listDiffSqlByAppkey(request);
//
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(diffSqlInvoker, times(1)).execute(request);
//    }
//    @Test
//    public void testCreateSqlAuditTaskRequestIsNull() {
//        // arrange
//        UserCreateSqlAuditTaskByUserActionRequest request = mock(UserCreateSqlAuditTaskByUserActionRequest.class);
//        when(request.getSubmitter()).thenReturn("submitter");
//        List<CreateSqlAuditTaskRequestData> sqls = new ArrayList<>();
//        sqls.add(new CreateSqlAuditTaskRequestData()); // 添加一个非空的sqls元素
//        when(request.getSqls()).thenReturn(sqls);
//
//        List<UserCreateSqlAuditTaskByUserActionResponse> expected = new ArrayList<>();
//        when(createSqlTaskInvoker.execute(request)).thenReturn(expected);
//        // act
//        List<UserCreateSqlAuditTaskByUserActionResponse> result = rdsService.createSqlAuditTask(request);
//
//        // assert
//        assertEquals(expected, result);
//        verify(createSqlTaskInvoker, times(1)).execute(request);
//    }
//    @Test
//    public void testGetSqlAuditTaskValidInput() {
//        // arrange
//        String username = "user";
//        int taskId = 1;
//        List<UserGetSqlAuditTaskByUserActionResponse> expectedResponse = new ArrayList<>();
//        when(getSqlAuditTaskInvoker.execute(any(UserGetSqlAuditTaskByUserActionRequest.class))).thenReturn(expectedResponse);
//        // act
//        List<UserGetSqlAuditTaskByUserActionResponse> result = rdsService.getSqlAuditTask(username, taskId);
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(getSqlAuditTaskInvoker, times(1)).execute(any(UserGetSqlAuditTaskByUserActionRequest.class));
//    }
//    @Test
//    public void testGetSqlAuditResultValidInput() {
//        // arrange
//        String username = "user";
//        int taskId = 1;
//        List<UserGetSqlAuditResultByUserActionResponse> expectedResponse = new ArrayList<>();
//        when(getSqlAuditResultInvoker.execute(any(UserGetSqlAuditResultByUserActionRequest.class))).thenReturn(expectedResponse);
//        // act
//        List<UserGetSqlAuditResultByUserActionResponse> result = rdsService.getSqlAuditResult(username, taskId);
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(getSqlAuditResultInvoker, times(1)).execute(any(UserGetSqlAuditResultByUserActionRequest.class));
//    }
//    @Test
//    public void testGetReportByOrgActionValidInput() {
//        // arrange
//        GetReportByOrgActionRequest request = mock(GetReportByOrgActionRequest.class);
//        when(request.getUserName()).thenReturn("user");
//        when(request.getOrgId()).thenReturn(1);
//        List<GetReportByOrgActionResponse> expectedResponse = new ArrayList<>();
//        when(getReportInvoker.execute(any(GetReportByOrgActionRequest.class))).thenReturn(expectedResponse);
//        // act
//        List<GetReportByOrgActionResponse> result = rdsService.getReportByOrgAction(request);
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(getReportInvoker, times(1)).execute(any(GetReportByOrgActionRequest.class));
//    }
//    @Test
//    public void testGetReportResultByOrgActionValidInput() {
//        // arrange
//        GetReportResultsByOrgActionRequest request = mock(GetReportResultsByOrgActionRequest.class);
//        when(request.getUsername()).thenReturn("user");
//        when(request.getOrgId()).thenReturn(1);
//        when(request.getWeek()).thenReturn(1);
//        List<GetReportResultsByOrgActionResponse> expectedResponse = new ArrayList<>();
//        when(getReportResultInvoker.execute(any(GetReportResultsByOrgActionRequest.class))).thenReturn(expectedResponse);
//        // act
//        List<GetReportResultsByOrgActionResponse> result = rdsService.getReportResultByOrgAction(request);
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(getReportResultInvoker, times(1)).execute(any(GetReportResultsByOrgActionRequest.class));
//    }
//    @Test
//    public void testGetReportByAppkeyActionValidInput() {
//        // arrange
//        GetReportByAppkeyActionRequest request = mock(GetReportByAppkeyActionRequest.class);
//        when(request.getUsername()).thenReturn("user");
//        when(request.getAppkey()).thenReturn("appkey");
//        List<GetReportByAppkeyActionResponse> expectedResponse = new ArrayList<>();
//        when(getReportByAppkeyInvoker.execute(any(GetReportByAppkeyActionRequest.class))).thenReturn(expectedResponse);
//        // act
//        List<GetReportByAppkeyActionResponse> result = rdsService.getReportByAppkeyAction(request);
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(getReportByAppkeyInvoker, times(1)).execute(any(GetReportByAppkeyActionRequest.class));
//    }
//    @Test
//    public void testGetReportResultByAppkeyActionValidInput() {
//        // arrange
//        GetReportResultsByAppkeyActionRequest request = mock(GetReportResultsByAppkeyActionRequest.class);
//        when(request.getUsername()).thenReturn("validUsername");
//        when(request.getAppkey()).thenReturn("validAppkey");
//        when(request.getTaskId()).thenReturn(1);
//        List<GetReportResultsByAppkeyActionResponse> expectedResponse = new ArrayList<>();
//        when(getReportResultByAppkeyInvoker.execute(any(GetReportResultsByAppkeyActionRequest.class))).thenReturn(expectedResponse);
//        // act
//        List<GetReportResultsByAppkeyActionResponse> result = rdsService.getReportResultByAppkeyAction(request);
//        // assert
//        assertEquals(expectedResponse, result);
//        verify(getReportResultByAppkeyInvoker, times(1)).execute(any(GetReportResultsByAppkeyActionRequest.class));
//    }
//
//
//}
//
