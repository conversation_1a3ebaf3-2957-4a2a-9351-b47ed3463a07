package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/6/5
 * @time: 下午4:37
 */
@Data
@AllArgsConstructor
public class RDSListDiffSqlByAppkeyActionRequest {
    private String appkeys;
    private SourcePayLoad source;
    private TargetPayLoad target;
    private int sqlLimit;

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class SourcePayLoad {
        private String env;
        private String startTime;
        private String endTime;
        private String swimlane;
    }
    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class TargetPayLoad {
        private String env;
        private String startTime;
        private String endTime;
    }
}
