package com.sankuai.mdp.compass.riskSQLScan.service;/* *************
 * @author: liuYang359
 * @date: 2023/10/9 15:22
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.dto.RiskSqlScanListDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanBusinessInfoPO;

import java.util.List;

public interface RiskSqlScanBussinessService {
     List<RiskSqlScanBusinessInfoPO> getListbyMis(String MIS);

     int deleteAllSqlbyId(int id);
     RiskSqlScanBusinessInfoPO getOnebyMisAndId(String mis,int id);
     List<RiskSqlScanListDTO>getAll(String MIS, int id);

    List<RiskSqlScanListDTO> getListbyMisAndId(String mis, int id);

    boolean businessDuplicate(String name);

    int createBusiness(String name, String admins);

    int deleteBusinessbyId(Integer id);

    boolean businessDuplicateV2(String name,Integer id);

    boolean updateBusinessById(Integer id, String businessName, String adminList);
}
