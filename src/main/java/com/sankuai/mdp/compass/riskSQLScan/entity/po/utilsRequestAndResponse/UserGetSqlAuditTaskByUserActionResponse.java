package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanTaskStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/28
 * @time: 下午6:01
 */
@Data
public class UserGetSqlAuditTaskByUserActionResponse {
        private String appkey;
        private String beginAt;
        private CTRBody body;
        private String createdAt;
        private String dbType;
        private String endAt;
        private String form;
        private List<String> globalResults;
        private Integer id;
        private String log;
        private ArrayList<String> receivers;
        private CTRResult result;
        private String source;
        private String status;
        private String submitter;
        private String updatedAt;
        private String addTime;
        private String receiver;

        @Data
        public class CTRResult {
                private Integer criticalRiskNum;
                private Integer exceptNum;
                private Integer failedNum;
                private Integer highRiskNum;
                private Integer lowRiskNum;
                private Integer middleRiskNum;
                private Integer noneRiskNum;
                private Integer totalNum;
        }
        @Data
        private static class CTRBody {
                private List<String> codeSqlList;
                private String custom;
                private Integer day;
                private List<String> fileList;
                private List<SqlList> sqlList;
                private String swimlane;
                private List<String> textList;
                @Data
                private class SqlList {
                        private Integer databaseId;
                        private List<String> sqls;
                }
        }
}
