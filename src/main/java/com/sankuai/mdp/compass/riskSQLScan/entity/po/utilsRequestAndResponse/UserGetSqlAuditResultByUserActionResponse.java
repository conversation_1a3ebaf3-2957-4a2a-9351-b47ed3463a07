package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRDSRuleNameEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import jnr.ffi.annotations.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/28
 * @time: 下午6:01
 */
@Data
public class UserGetSqlAuditResultByUserActionResponse  {
        private Integer current;
        private String order;
        private Integer pageSize;
        private String sort;
        private Integer total;
        private TotalInfo totalInfo;
        private HighRiskInfo highRiskInfo;
        private MediumRiskInfo mediumRiskInfo;
        private LowRiskInfo lowRiskInfo;
        private NoRiskInfo noRiskInfo;
        private List<RiskData> data;
        private String submitter;
        private String receiver;
        private String source;

        @Data
        @Accessors(chain = true)
        public static class TotalInfo {
            private int num;
            private int hitWhitelistNum;
        }

        @Data
        @Accessors(chain = true)
        public static class HighRiskInfo {
            private int num;
            private int hitWhitelistNum;
        }

        @Data
        @Accessors(chain = true)
        public static class MediumRiskInfo {
            private int num;
            private int hitWhitelistNum;
        }

        @Data
        @Accessors(chain = true)
        public static class LowRiskInfo {
            private int num;
            private int hitWhitelistNum;
        }

        @Data
        @Accessors(chain = true)
        public static class NoRiskInfo {
            private int num;
            private int hitWhitelistNum;
        }
        @Data
        @Accessors(chain = true)
        public static class RiskData {
            private String beginAt;
            private Integer clusterId;
            private String clusterName;
            private String createdAt;
            private Integer databaseId;
            private String databaseName;
            private String endAt;
            private Integer flowId;
            private Integer id;
            private List<riskReason> result;
            private String riskLevel;
            private String sql;
            private Integer sqlNum;
            private String sqlTpl;
            private String status;
            private String updatedAt;
            private String appkey;
            private String dbName;
            private Integer dbId;
            private Integer useNum;
            private String sqlText;
            @Data
            @Accessors(chain = true)
            public static class riskReason {
                private String description;
                private String message;
                private String name;
                private String nameCn;
                private String riskLevel;
            }
        }


}
