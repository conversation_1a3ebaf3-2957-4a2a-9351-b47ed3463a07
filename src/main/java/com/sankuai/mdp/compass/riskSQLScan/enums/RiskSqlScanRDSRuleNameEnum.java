package com.sankuai.mdp.compass.riskSQLScan.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @description:
 * @author: niuji<PERSON><PERSON>
 * @date: 2024/5/29
 * @time: 下午2:17
 */
public enum RiskSqlScanRDSRuleNameEnum {
    EXPLAIN_TYPE_IS_ALL(0, "EXPLAIN_TYPE_IS_ALL"),
    EXPLAIN_TYPE_IS_INDEX(1, "EXPLAIN_TYPE_IS_INDEX"),
    EXPLAIN_TYPE_IS_EMPTY(2, "EXPLAIN_KEY_IS_EMPTY"),
    EXPLAIN_ROWS_TOO_BIG(3, "EXPLAIN_ROWS_TOO_BIG"),
    SELECT_WITHOUT_WHERE(4, "SELECT_WITHOUT_WHERE"),
    SELECT_WITH_ORDER_BY_RAND(5, "SELECT_WITH_ORDER_BY_RAND"),
    SELECT_WITH_GROUP_BY_CONSTANT(6, "SELECT_WITH_GROUP_BY_CONSTANT"),
    SELECT_WITH_ORDER_BY_CONSTANT(7, "SELECT_WITH_ORDER_BY_CONSTANT"),
    SELECT_ORDER_BY_WITH_MULTI_DIRECTION(8, "SELECT_ORDER_BY_WITH_MULTI_DIRECTION"),
    SELECT_WITH_UNION(9, "SELECT_WITH_UNION"),
    INSERT_WITHOUT_COLUMN(10, "INSERT_WITHOUT_COLUMN"),
    INSERT_COLUMN_NOT_MATCH(11, "INSERT_COLUMN_NOT_MATCH"),
    INSERT_TABLE_OR_COLUMN_NOT_EXIST(12, "INSERT_TABLE_OR_COLUMN_NOT_EXIST"),
    INSERT_COLUMN_NOT_NULL_AND_VALUE_IS_NULL(13, "INSERT_COLUMN_NOT_NULL_AND_VALUE_IS_NULL"),
    INSERT_TOO_MANY_VALUES(14, "INSERT_TOO_MANY_VALUES"),
    INSERT_WITH_SYSDATE(15, "INSERT_WITH_SYSDATE"),
    UPDATE_WITH_AND(16, "UPDATE_WITH_AND"),
    UPDATE_OR_DELETE_TOO_MANY_ROWS(17, "UPDATE_OR_DELETE_TOO_MANY_ROWS"),
    UPDATE_OR_DELTE_WITH_SUBQUERY(18, "UPDATE_OR_DELTE_WITH_SUBQUERY"),
    UPDATE_OR_DELETE_WITHOUT_WHERE(19, "UPDATE_OR_DELETE_WITHOUT_WHERE"),
    UPDATE_OR_DELETE_JOIN_TOO_MANY_TABLES(20, "UPDATE_OR_DELETE_JOIN_TOO_MANY_TABLES"),
    SELECT_JOIN_TOO_MANY_TABLES(21, "SELECT_JOIN_TOO_MANY_TABLES"),
    UPDATE_OR_DELETE_WITH_ORDER_BY(22, "UPDATE_OR_DELETE_WITH_ORDER_BY"),
    SELECT_WITH_OFFSET(23, "SELECT_WITH_OFFSET"),
    WHERE_NOT_IN_OR_NOT_LIKE(24, "WHERE_NOT_IN_OR_NOT_LIKE"),
    WHERE_IN_ELEMENT_SIZE(25, "WHERE_IN_ELEMENT_SIZE"),
    WHERE_INCLUDE_OR(26, "WHERE_INCLUDE_OR"),
    INSERT_TIMESTAMP_NULL_VALUE_WITH_DEFINE_NOT_NULL(27, "INSERT_TIMESTAMP_NULL_VALUE_WITH_DEFINE_NOT_NULL");

    @EnumValue
    private int code;
    private String ruleName;

    RiskSqlScanRDSRuleNameEnum(int code, String ruleName) {
        this.code = code;
        this.ruleName = ruleName;
    }
}
