package com.sankuai.mdp.compass.riskSQLScan.mapper;/* *************
 * @author: liuYang359
 * @date: 2023/9/27 11:36
 * @description:
 */

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RiskSqlScanAppKeyMapper extends BaseMapper<RiskSqlScanAppKeyPO> {
    @Select("SELECT id FROM risk_sql_scan_appkey WHERE appkey = #{appkey}")
    List<Long> selectId(String appkey);
    @Select("SELECT DISTINCT appkey FROM risk_sql_scan_appkey WHERE offline_scan_switch = 1")
    List<String> selectAppKeyList();
    @Select("SELECT appkey FROM risk_sql_scan_appkey WHERE id = #{id}")
    String selectAppkeyById(Integer id);
    @Select("SELECT business_id FROM risk_sql_scan_appkey WHERE id = #{id}")
    List<Long> selectBusinessId(Long id);
    @Select("SELECT business_id FROM risk_sql_scan_appkey WHERE appkey = #{appkey}")
    String selectBusinessIdByAppkey(String appkey);
}
