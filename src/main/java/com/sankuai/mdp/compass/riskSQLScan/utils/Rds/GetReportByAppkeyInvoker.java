package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportByAppkeyActionRequest;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportByAppkeyActionResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niu<PERSON><PERSON><PERSON>
 * @date: 2024/5/30
 * @time: 下午2:26
 */
@Component
public class GetReportByAppkeyInvoker extends AbstractRdsApiInvoker<GetReportByAppkeyActionRequest, List<GetReportByAppkeyActionResponse>>{
    @Override
    protected String getAction() {
        return "GetReportByAppkeyAction";
    }

    @Override
    protected List<GetReportByAppkeyActionResponse> convert2Model(String data) {
        return JSON.parseArray(data, GetReportByAppkeyActionResponse.class);
    }
}
