package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportResultsByAppkeyActionRequest;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportResultsByAppkeyActionResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/30
 * @time: 下午2:26
 */
@Component
public class GetReportResultByAppkeyInvoker extends AbstractRdsApiInvoker<GetReportResultsByAppkeyActionRequest,List<GetReportResultsByAppkeyActionResponse>>{
    @Override
    protected String getAction() {
        return "GetReportResultsByAppkeyAction";
    }

    @Override
    protected List<GetReportResultsByAppkeyActionResponse> convert2Model(String data) {
        return JSON.parseArray(data,GetReportResultsByAppkeyActionResponse.class);
    }
}
