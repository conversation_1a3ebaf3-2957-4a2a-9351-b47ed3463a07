package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserGetSqlAuditResultByUserActionResponse;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserGetSqlAuditResultByUserActionRequest;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 下午4:49
 */
@Component
public class GetSqlAuditResultInvoker extends AbstractRdsApiInvoker<UserGetSqlAuditResultByUserActionRequest, UserGetSqlAuditResultByUserActionResponse> {

    @Override
    protected String getAction() {
        return "GetSqlAuditResultAction";
    }

    @Override
    protected UserGetSqlAuditResultByUserActionResponse convert2Model(String data) {
        return JSON.parseObject(data, UserGetSqlAuditResultByUserActionResponse.class);

    }
}
