package com.sankuai.mdp.compass.riskSQLScan.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 下午2:44
 */
@Data
@AllArgsConstructor
@TableName("risk_sql_scan_commit_item")
public class RiskSqlScanCommitItemPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(value = "commitId")
    private Long commitId;
    @TableField(value = "sqlTpl")
    private String sqlTpl;
    @TableField(value = "riskSql")
    private String riskSql;
    @TableField(value = "dbName")
    private String dbName;
    @TableField(value = "clusterName")
    private String clusterName;
}
