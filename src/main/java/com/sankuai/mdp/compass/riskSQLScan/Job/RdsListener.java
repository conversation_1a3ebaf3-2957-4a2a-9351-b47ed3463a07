package com.sankuai.mdp.compass.riskSQLScan.Job;/* *************
 * @author: liuYang359
 * @date: 2023/11/28 15:49
 * @description:
 */

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.mdp.compass.riskSQLScan.service.RdsListenerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@CraneConfiguration
public class RdsListener {
    @Autowired
    private RdsListenerService rdsListenService;
    @Crane("com.sankuai.compass.riskSQLScan.rdsLinstener.getChangedSql")
    public void getChangedSql(){
        rdsListenService.getChangedSql();
    }
    @Crane("com.sankuai.compass.riskSQLScan.rdsLinstener.getTaskResult")
    public  void getTaskResult() {
        rdsListenService.getTaskResult();
    }

}
