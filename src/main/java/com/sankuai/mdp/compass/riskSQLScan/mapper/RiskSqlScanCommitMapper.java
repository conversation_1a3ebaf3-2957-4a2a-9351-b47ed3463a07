package com.sankuai.mdp.compass.riskSQLScan.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanCommitPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * @description:
 * @author: niuji<PERSON><PERSON>
 * @date: 2024/5/31
 * @time: 下午3:37
 */
@Mapper
public interface RiskSqlScanCommitMapper extends BaseMapper<RiskSqlScanCommitPO> {
    @Select("SELECT id FROM risk_sql_scan_commit WHERE  appkeys= #{appkeys} AND swimlane = #{swimlane}")
    Long selectId(String appkeys,String swimlane);
}
