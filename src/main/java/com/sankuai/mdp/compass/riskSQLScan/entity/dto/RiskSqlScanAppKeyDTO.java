package com.sankuai.mdp.compass.riskSQLScan.entity.dto;/* *************
 * @author: liuYang359
 * @date: 2023/11/15 15:51
 * @description:
 */

import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;
import lombok.Data;

@Data
public class RiskSqlScanAppKeyDTO {
    int id;
    String businessName;
    String appKey;
    Boolean offlineSwitch;
    Boolean onlineSwitch;
    public  RiskSqlScanAppKeyDTO(String businessName, RiskSqlScanAppKeyPO appKeyPO){
        this.businessName = businessName;
        this.id = appKeyPO.getId();
        this.appKey = appKeyPO.getAppkey();
        this.offlineSwitch = appKeyPO.isOfflineScanSwitch();
        this.onlineSwitch = appKeyPO.isOnlineScanSwitch();
    }
}

