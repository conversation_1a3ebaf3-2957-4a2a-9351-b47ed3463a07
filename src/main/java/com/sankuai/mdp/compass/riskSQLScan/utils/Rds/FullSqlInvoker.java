package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;/* *************
 * @author: liuYang359
 * @date: 2023/12/13 19:15
 * @description:
 */

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.RDSListFullSqlByAppkeyActionResponse;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.RDSListFullSqlByAppkeyActionRequest;
import org.springframework.stereotype.Component;


import java.util.List;
@Component
public class FullSqlInvoker extends AbstractRdsApiInvoker<RDSListFullSqlByAppkeyActionRequest, List<RDSListFullSqlByAppkeyActionResponse>> {
    @Override
    public String getAction() {
        return "ListFullSqlByAppkeyAction";
    }

    @Override
    public List<RDSListFullSqlByAppkeyActionResponse> convert2Model(String data) {
        return JSON.parseArray(data, RDSListFullSqlByAppkeyActionResponse.class);
    }



}
