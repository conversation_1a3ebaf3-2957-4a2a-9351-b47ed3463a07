package com.sankuai.mdp.compass.riskSQLScan.service.Impl;/* *************
 * @author: liuYang359
 * @date: 2023/10/12 18:35
 * @description:
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.RiskSqlScanAppKeyDTO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanAppKeyPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanBusinessInfoPO;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.RiskSqlScanSwimlaneInfoPO;
import com.sankuai.mdp.compass.riskSQLScan.enums.DeleteTypeEnum;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanAppKeyMapper;
import com.sankuai.mdp.compass.riskSQLScan.mapper.RiskSqlScanSwimlaneInfoMapper;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlScanAppkeyService;
import com.sankuai.mdp.compass.riskSQLScan.service.RiskSqlScanBussinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RiskSqlScanAppkeyServiceImpl implements RiskSqlScanAppkeyService {
    @Autowired
    RiskSqlScanAppKeyMapper riskSqlScanAppKeyMapper;
    @Autowired
    RiskSqlScanSwimlaneInfoMapper riskSqlScanSwimlaneInfoMapper;

    @Autowired
    RiskSqlScanBussinessService riskSqlScanBussinessService;

    public List<RiskSqlScanAppKeyPO> selectByBusinessID(Integer businessId, Integer deleted) {
        QueryWrapper<RiskSqlScanAppKeyPO> riskSqlScanAppKeyDao = new QueryWrapper<>();
        riskSqlScanAppKeyDao.eq("business_id", businessId);
        riskSqlScanAppKeyDao.eq("deleted", deleted);
        return riskSqlScanAppKeyMapper.selectList(riskSqlScanAppKeyDao);
    }

    @Override
    public List<RiskSqlScanAppKeyDTO> selectByAppkeyIdAndMis(Integer businessId, String mis) {
        List<RiskSqlScanAppKeyDTO> appKeys = new ArrayList<>();
        if (businessId != 0) {
            RiskSqlScanBusinessInfoPO businessInfoPO = riskSqlScanBussinessService.getOnebyMisAndId(mis, businessId);
            selectByBusinessID(businessId, DeleteTypeEnum.ONLINE.getCode()).forEach(index -> {
                        appKeys.add(new RiskSqlScanAppKeyDTO(businessInfoPO.getBusinessName(), index));
                    }
            );
        } else {
            riskSqlScanBussinessService.getListbyMis(mis).forEach(business -> {
                if (business.getId() != null) {
                    selectByBusinessID(business.getId(), DeleteTypeEnum.ONLINE.getCode()).forEach(appkey ->
                            appKeys.add(new RiskSqlScanAppKeyDTO(business.getBusinessName(), appkey))
                    );
                }
            });

        }
        return appKeys.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public int deleteById(Integer id) {
        UpdateWrapper<RiskSqlScanAppKeyPO> updateWrapperappkey = new UpdateWrapper<>();
        updateWrapperappkey.eq("id", id);//作为条件
        updateWrapperappkey.set("deleted", DeleteTypeEnum.DELETED.getCode());//设置想要更新的字段
        RiskSqlScanAppKeyPO temp = RiskSqlScanAppKeyPO.builder().build();
        temp.setId(id);
        temp.setDeleted(DeleteTypeEnum.DELETED.getCode());

        //关闭泳道
        String appkey=riskSqlScanAppKeyMapper.selectAppkeyById(id);
        UpdateWrapper<RiskSqlScanSwimlaneInfoPO> updateWrapperswimlane = new UpdateWrapper<>();
        updateWrapperswimlane.eq("appkey", appkey);
        updateWrapperswimlane.set("isActive", 0);
        RiskSqlScanSwimlaneInfoPO swimlaneInfoPO=new RiskSqlScanSwimlaneInfoPO();
        swimlaneInfoPO.setIsActive(0);
        swimlaneInfoPO.setAppKey(appkey);
        riskSqlScanSwimlaneInfoMapper.update(swimlaneInfoPO,updateWrapperswimlane);

        return riskSqlScanAppKeyMapper.update(temp, updateWrapperappkey);
    }

    @Override
    public int deleteByBusinessId(Integer id) {
        UpdateWrapper<RiskSqlScanAppKeyPO> updateWrapperappkey = new UpdateWrapper<>();
        updateWrapperappkey.eq("business_id", id);
        updateWrapperappkey.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        updateWrapperappkey.set("deleted", DeleteTypeEnum.DELETED.getCode());//设置想要更新的字段
        RiskSqlScanAppKeyPO temp = RiskSqlScanAppKeyPO.builder().build();
        temp.setDeleted(DeleteTypeEnum.DELETED.getCode());
        return riskSqlScanAppKeyMapper.update(temp, updateWrapperappkey);
    }

    @Override
    public boolean setSwitch(Integer appkeyId, Integer type, boolean staus) {
        try {

            RiskSqlScanAppKeyPO value=riskSqlScanAppKeyMapper.selectById(appkeyId);
            //小坑。必须查一次，否则初始化的时候 boolean 默认为 false 如果字段原来是 true 就会被修改
            if (type == 0) {
              value.setOfflineScanSwitch(staus);
            } else if (type == 1)
            {
                value.setOnlineScanSwitch(staus);
            }
            riskSqlScanAppKeyMapper.updateById(value);
        } catch (Exception E) {
            E.printStackTrace();
        }
        return staus;
    }

    @Override
    public boolean duplicate(String appkey) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("appkey", appkey);
        queryWrapper.eq("deleted", DeleteTypeEnum.ONLINE.getCode());
        return riskSqlScanAppKeyMapper.selectList(queryWrapper).size()>0?false:true;
    }

    @Override
    public boolean createAppKey(RiskSqlScanAppKeyPO appKey) {
        try {
            riskSqlScanAppKeyMapper.insert(appKey);
        }
       catch (Exception E)
       {
           return false;
       }
        return true;
    }

}
