package com.sankuai.mdp.compass.riskSQLScan.entity.po;/* *************
 * @author: liuYang359
 * @date: 2023/9/23 18:29
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import org.json.JSONObject;

import java.util.List;

@Data
@Builder
@TableName("risk_sql_scan_bussiness")
public class RiskSqlScanBusinessInfoPO {
    @TableId(value = "id",type =  IdType.AUTO)
    Integer id;
    //业务名称
    String businessName;
    // 负责人列表
    String adminMisList;
    private int deleted;


    public String toJsonString(){
    return JSONObject.valueToString(this);
    }

}
