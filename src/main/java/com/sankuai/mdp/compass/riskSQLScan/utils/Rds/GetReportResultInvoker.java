package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportResultsByOrgActionRequest;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.GetReportResultsByOrgActionResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/30
 * @time: 下午2:25
 */
@Component
public class GetReportResultInvoker extends AbstractRdsApiInvoker<GetReportResultsByOrgActionRequest, List<GetReportResultsByOrgActionResponse>> {

    @Override
    protected String getAction() {
        return "GetReportResultsByOrgAction";
    }

    @Override
    protected List<GetReportResultsByOrgActionResponse> convert2Model(String data) {
        return JSON.parseArray(data, GetReportResultsByOrgActionResponse.class);
    }
}
