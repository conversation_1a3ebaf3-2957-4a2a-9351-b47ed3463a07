package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRDSRuleNameEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 上午11:37
 */
@Data
public class GetReportResultsByAppkeyActionResponse {

        private int total;
        private int current;
        private int pageSize;
        private List<ReportResultsByAppkey> data;

        @Data
        @Accessors(chain = true)
        private static class ReportResultsByAppkey {
            private int id;
            private RiskSqlScanRiskLevelEnum riskLevel;
            private RiskSqlScanRDSRuleNameEnum ruleName;
            private String sqlTpl;
            private String sqlText;
            private String clusterName;
            private String dbName;
            private boolean filterWhitelist;
        }
}
