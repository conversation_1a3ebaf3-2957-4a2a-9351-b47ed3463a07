package com.sankuai.mdp.compass.riskSQLScan.utils.Rds;

import com.alibaba.fastjson.JSON;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserGetSqlAuditTaskByUserActionResponse;
import com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse.UserGetSqlAuditTaskByUserActionRequest;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/29
 * @time: 下午4:45
 */
@Component
public class GetSqlAuditTaskInvoker extends AbstractRdsApiInvoker<UserGetSqlAuditTaskByUserActionRequest, UserGetSqlAuditTaskByUserActionResponse> {
    @Override
    protected String getAction() {
        return "GetSqlAuditTaskAction";
    }

    @Override
    protected UserGetSqlAuditTaskByUserActionResponse convert2Model(String data) {
        return JSON.parseObject(data, UserGetSqlAuditTaskByUserActionResponse.class);
    }

}
