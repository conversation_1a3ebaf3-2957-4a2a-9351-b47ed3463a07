package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @author: ni<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/5/29
 * @time: 上午11:30
 */
@Data
@Builder
public class GetReportByAppkeyActionRequest {
    private String username;
    private String appkey;
    private String beginTime;
    private String endTime;
    private boolean filterWhiteList;
}
