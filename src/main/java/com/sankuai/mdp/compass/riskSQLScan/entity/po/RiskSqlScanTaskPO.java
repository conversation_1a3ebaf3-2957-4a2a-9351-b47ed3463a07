package com.sankuai.mdp.compass.riskSQLScan.entity.po;/* *************
 * @author: liuYang359
 * @date: 2023/9/23 18:38
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanTaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("risk_sql_scan_task")
public class RiskSqlScanTaskPO {
    @TableId(value = "id",type =  IdType.AUTO)
    private Long id;
    @TableField(value = "commitId")
    private Long commitId;
    @TableField(value = "taskSatus")
    private RiskSqlScanTaskStatusEnum taskSatus;
    @TableField(value = "appkeys")
    private String appkeys;
    @TableField(value = "swimLane")
    private String swimLane;
    @TableField(value = "submitter")
    private String submitter;
    @TableField(value = "receiver")
    private String receiver;
    @TableField(value = "taskId")
    private Long taskId;
    @TableField(value = "createTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
 }
