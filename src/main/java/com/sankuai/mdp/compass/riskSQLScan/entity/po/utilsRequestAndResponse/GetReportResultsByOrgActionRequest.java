package com.sankuai.mdp.compass.riskSQLScan.entity.po.utilsRequestAndResponse;

import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRDSRuleNameEnum;
import com.sankuai.mdp.compass.riskSQLScan.enums.RiskSqlScanRiskLevelEnum;
import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @author: niu<PERSON>ech<PERSON>
 * @date: 2024/5/29
 * @time: 上午11:07
 */
@Data
@Builder
public class GetReportResultsByOrgActionRequest {
    private String username;
    private int orgId;
    private int week;
    private String beginTime;
    private String endTime;
    private boolean filterWhiteList;
    private int current;
    private int pageSize;
    private RiskSqlScanRiskLevelEnum riskLevel;
    private RiskSqlScanRDSRuleNameEnum ruleName;
}
