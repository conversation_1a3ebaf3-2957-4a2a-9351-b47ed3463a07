package com.sankuai.mdp.compass.ssoinfo.controller;
/* *************
 * @author: liuYang359
 * @date: 2023/7/31 16:04 下午
 * @description:
 */

import com.sankuai.mdp.compass.common.utils.LionUtil;
import com.sankuai.mdp.compass.common.utils.TokenUtil;
import com.sankuai.mdp.compass.ssoinfo.service.SsoInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.IOException;


@RestController
@RequestMapping("/compass/api/ssoinfo")
@Slf4j
public class SsoInfoController {/*

 * @author: liuyang359
 * @date: 2023/7/31 16:34
 * @description:sso 转发接口
 */
    @Autowired
    SsoInfoService ssoInfoService;

    @PostMapping("/getinfo")

    public String getSsoAccessToken(String clientId, String clientSecret, String env, String userName, String password) throws IOException {/*
     * @param clientId:
     * @param client_secret:
     * @param env:环境 prod 线上 test 线下
     * @param userName: 用户名，线上需要用虚拟账号
     * @param password: 密码： 线上需要用虚拟账号，否则需要验证码或者动态码。
     * @return: java.lang.String： token &cookie
     * @author: liuyang359
     * @date: 2023/7/31 16:37
     * @description: 根据客户端id，等返回鉴权的access-token。

     */
        //LionUtil  lion 工具类
        //kms  TokenUtil.getToken(Z)
        return ssoInfoService.getAccessToken(clientId,clientSecret,env,userName,password);

    }



}
