package com.sankuai.mdp.compass.ssoinfo.utils;/* *************
 * @author: liuYang359
 * @date: 2023/8/3 19:39
 * @description:
 */

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ListUtils {
    public static Map<String, String> listToMap(List<String> params) {
        return params.stream()
                .map(param -> param.split(":"))
                .collect(Collectors.toMap(
                        array -> array[0],
                        array -> array.length > 1 ? array[1] : "",
                        (v1, v2) -> v1.length()>v2.length() ?v1:v2
                ));
    }
}
