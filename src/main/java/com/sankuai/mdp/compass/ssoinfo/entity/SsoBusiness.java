package com.sankuai.mdp.compass.ssoinfo.entity;/* *************
 * @author: liuYang359
 * @date: 2023/8/2 20:53
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("sso_business")
public class SsoBusiness {

    @TableId(value = "id",type =  IdType.AUTO)
    private Integer id;
    private String  businessName;

    private String accessToken;

    private int evn;



}
