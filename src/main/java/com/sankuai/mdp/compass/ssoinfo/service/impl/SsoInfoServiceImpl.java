package com.sankuai.mdp.compass.ssoinfo.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonObject;


import com.sankuai.it.sso.sdk.utils.HttpUtil;
import com.sankuai.mdp.compass.common.utils.LionUtil;
import com.sankuai.mdp.compass.common.utils.TokenUtil;
import com.sankuai.mdp.compass.ssoinfo.entity.Env;
import com.sankuai.mdp.compass.ssoinfo.entity.SsoBusiness;
import com.sankuai.mdp.compass.ssoinfo.mapper.SsoBusinessMapper;
import com.sankuai.mdp.compass.ssoinfo.service.SsoInfoService;
import com.sankuai.mdp.compass.ssoinfo.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * SSO获取鉴权信息服务实现类
 */
@Service
@Slf4j
public class SsoInfoServiceImpl implements SsoInfoService {

    /**
     * 正式环境URL
     */
    private static final String PRO_URL = "https://sso.vip.sankuai.com/sson/api/auth";

    /**
     * 测试环境URL
     */
    private static final String TEST_URL = "https://ssosv.it.test.sankuai.com/sson/api/auth";

    /**
     * 请求路径
     */
    private static final String PATH = "/sson/api/auth";

    /**
     * 请求成功状态码
     */
    private static final int SUCCESS = 200;

    /**
     * 环境类型：正式环境
     */
    private static final String PROD = "PROD";

    @Autowired
    SsoBusinessMapper ssoBusinessMapper;
    @Override
    public String getAccessToken(String clientId, String clientSecret, String env, String userName, String password) {
    /**
      @param clientId:客户端ID @see
         * 线上申请地址：https://open.sankuai.com/app/list
         * 线下申请地址：http://openplatform.it.test.sankuai.com/developer/app/list
     * @param clientSecret:同id  线上线下都不可混用
     * @param env:环境类型
     * @param userName:用户名
     * @param password:密码 ： 测试环境默认Ceshi123
     * @return: java.lang.String ：Access-Token
     * @author: liuyang359
     * @date: 2023/7/31 15:59
     * @description:
     */
        // 获取签名后的请求头
        String[] headers = HttpUtil.getSignedHeaders("POST", PATH, clientId, clientSecret);
        // 构造请求体
        JsonObject httpBody = new JsonObject();
        httpBody.addProperty("loginName", userName);
        httpBody.addProperty("password", password);
        String response = "";
        // 根据环境类型选择不同的URL进行请求
        if (env != null && env.equals(PROD))
            response = HttpUtil.doPost(PRO_URL, httpBody.toString(), String.class, headers);
        else
            response = HttpUtil.doPost(TEST_URL, httpBody.toString(), String.class, headers);
        // 解析响应结果
        JSONObject result = JSON.parseObject(response);
        // 判断请求是否成功
        if (result.getIntValue("code") == SUCCESS) {
            // 返回访问令牌
            return result.getJSONObject("data").getString("accessToken");
        }
        return null;
    }

    @Override
    public void updateBussiness(SsoBusiness ssoBusiness) {
        QueryWrapper<SsoBusiness>queryWrapper =new QueryWrapper<>();
        queryWrapper.eq("business_name",ssoBusiness.getBusinessName());

        SsoBusiness temp=ssoBusinessMapper.selectOne(queryWrapper);
        if(temp==null)
        ssoBusinessMapper.insert(ssoBusiness);
        else {
            ssoBusiness.setId(temp.getId());
            ssoBusinessMapper.updateById(ssoBusiness);
        }

    }
    @Override
    public void autoUpdtateToken()
        {
            LionUtil lionUtil =new LionUtil();
            List<String> businessLionList = lionUtil.getListValue("ssobusinesslist");
            Map<String,String> businessKmsMap= ListUtils.listToMap(businessLionList);
            businessKmsMap.entrySet().stream().forEach(
                    entry->{
                        SsoBusiness  SsoBusinessEntry =new SsoBusiness();
                        SsoBusinessEntry.setBusinessName(entry.getKey());
                        String token = TokenUtil.getToken(entry.getKey());
                        String clintId=token.split(":")[0];
                        String clientSecret=token.split(":")[1];
                        String env=token.split(":")[2];
                        String username=token.split(":")[3];
                        String password=token.split(":")[4];
                        String accessToken="";
                        accessToken=this.getAccessToken(clintId,clientSecret,env,username,password);
                        SsoBusinessEntry.setAccessToken(accessToken);
                        if (Env.PROD.toString().equals(env))
                            SsoBusinessEntry.setEvn(Env.PROD.getCode());
                        else
                            SsoBusinessEntry.setEvn(Env.TEST.getCode());
                        updateBussiness(SsoBusinessEntry);


                    }
            );
        }


}