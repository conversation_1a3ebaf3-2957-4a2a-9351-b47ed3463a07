package com.sankuai.mdp.compass.ssoinfo.service;/* *************
 * @author: liuYang359
 * @date: 2022/3/23 6:46 下午
 * @description:
 */

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.ssoinfo.entity.SsoBusiness;

import java.io.IOException;
import java.util.List;

public interface SsoInfoService {
    String getAccessToken(String clientId, String clientSecret, String env, String userName, String password) throws IOException;

    void updateBussiness(SsoBusiness ssoBusiness);
     void autoUpdtateToken();



}