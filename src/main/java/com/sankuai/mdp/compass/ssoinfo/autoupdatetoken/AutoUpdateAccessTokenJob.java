package com.sankuai.mdp.compass.ssoinfo.autoupdatetoken;/* *************
 * @author: liuYang359
 * @date: 2023/8/2 21:22
 * @description:
 */

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.mdp.compass.common.utils.LionUtil;
import com.sankuai.mdp.compass.common.utils.TokenUtil;
import com.sankuai.mdp.compass.mge.controller.MgeTestJobController;
import com.sankuai.mdp.compass.ssoinfo.entity.Env;
import com.sankuai.mdp.compass.ssoinfo.entity.SsoBusiness;
import com.sankuai.mdp.compass.ssoinfo.service.SsoInfoService;
import com.sankuai.mdp.compass.ssoinfo.utils.ListUtils;
import org.python.core.AstList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@CraneConfiguration

public class AutoUpdateAccessTokenJob {


    private static final Logger logger = LoggerFactory.getLogger(MgeTestJobController.class);

    @Autowired
    private SsoInfoService ssoInfoService;



    @Crane("autoUpdtateSsoToken")
    public void updateAccessTokenJob(){
        ssoInfoService.autoUpdtateToken();

}
}
