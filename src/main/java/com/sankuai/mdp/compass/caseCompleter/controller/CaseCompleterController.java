package com.sankuai.mdp.compass.caseCompleter.controller;

import com.sankuai.mdp.compass.caseCompleter.entity.PO.CaseCompleterBranchInfoPO;
import com.sankuai.mdp.compass.caseCompleter.service.CaseCompleterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @auther bock
 * @date 2024-11-07
 */
@RestController
@RequestMapping("/compass/api/caseCompleter")
public class CaseCompleterController {

    @Autowired
    private CaseCompleterService caseCompleterService;


    @GetMapping("/branch/list")
    public List<String> getBranches(String type,String status){
        return caseCompleterService.getBranches(type,status)
                .stream()
                .map(CaseCompleterBranchInfoPO::getBranch)
                .collect(Collectors.toList());
    }

    @GetMapping("/branch/list/conflicted")
    public List<String> getConflictedBranches(String appkey){
        return caseCompleterService.getBranches(appkey)
                .stream()
                .map(CaseCompleterBranchInfoPO::getBranch)
                .collect(Collectors.toList());
    }

    @PostMapping("/branch/update")
    public void updateBranch(@RequestBody CaseCompleterBranchInfoPO caseCompleterBranchInfo){
        caseCompleterService.update(caseCompleterBranchInfo);
    }
}
