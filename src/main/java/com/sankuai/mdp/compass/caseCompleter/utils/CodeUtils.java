package com.sankuai.mdp.compass.caseCompleter.utils;

import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.inf.patriot.common.Base64Utils;
import com.sankuai.inf.patriot.common.JsonUtils;
import com.sankuai.inf.patriot.config.Constants;
import com.sankuai.inf.patriot.token.ISignature;
import com.sankuai.inf.patriot.token.SignatureType;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.CodeCreateBranchDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.CodeCreatePrDTO;
import com.sankuai.mdp.compass.common.utils.GsonUtil;
import com.sankuai.oceanus.http.internal.HttpHeader;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * @auther bock
 * @date 2024-10-29
 */
public class CodeUtils {
    private static final String REMOTE_APPKEY = "com.sankuai.devtools.gateway.service";
    private static final String STS_TOKEN = "STS-TOKEN";
    private static final String CODE_HOST_TEST = "https://dev-api.vip.sankuai.com/mcode";
    private static final String CLIENT_ID = "git_compass";
    private static final String CLIENT_SECRET = "Prod1234!";

    private static final String CLIENT_APPKEY = "com.sankuai.sigma.compass";
    // 步骤4.1 跨环境调用一节有介绍： KMS_SECRET_KEY 从要调用环境的 KMS 中获取，秘钥名称是 auth_client_com.sankuai.devtools.gateway.service
    // 请注意，如果按照此 DEMO 中实现的算法，从 KMS 获取秘钥的的时候需要去掉外层的 [" "]，只保留中间的字符串
    private static final String KMS_SECRET_KEY;

    static {
        try {
            String temp = Kms.getByName(CLIENT_APPKEY,"auth_client_com.sankuai.devtools.gateway.service");
            System.out.println(temp);
            KMS_SECRET_KEY=temp.substring(2,temp.length()-2);
        } catch (KmsResultNullException e) {
            throw new RuntimeException(e);
        }
    }

    public static String doCodePost(String url,String body) throws Exception {
        // 使用目标环境的 IAM 帐号密码
        String token = IAMSTSUtilEnv.getIAMToken(CLIENT_ID, CLIENT_SECRET);
        Map<String, String> headers = new HashMap<>(3);
        headers.put(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, REMOTE_APPKEY);
        headers.put(HttpHeader.OCEANUS_AUTH_HEADER, getOceanusSignature(KMS_SECRET_KEY, CLIENT_APPKEY, REMOTE_APPKEY, null));
        headers.put(STS_TOKEN, token);

        return CommonHttpUtil.doRequest(url,body , "POST", headers);
    }

    public static String doCodeGet(String url) throws Exception {
        // 使用目标环境的 IAM 帐号密码
        String token = IAMSTSUtilEnv.getIAMToken(CLIENT_ID, CLIENT_SECRET);
        Map<String, String> headers = new HashMap<>(3);
        headers.put(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, REMOTE_APPKEY);
        headers.put(HttpHeader.OCEANUS_AUTH_HEADER, getOceanusSignature(KMS_SECRET_KEY, CLIENT_APPKEY, REMOTE_APPKEY, null));
        headers.put(STS_TOKEN, token);

        return CommonHttpUtil.doRequest(url,null , "GET", headers);
    }

    public static String createBranch(String branchName,String startPoint,String projectKey,String slug) throws Exception {
        String url = MessageFormat.format("{0}/rest/api/2.0/projects/{1}/repos/{2}/branches",CODE_HOST_TEST,projectKey,slug);
        CodeCreateBranchDTO codeCreateBranchDTO = new CodeCreateBranchDTO()
                .setName(branchName)
                .setStartPoint(startPoint);

        return doCodePost(url, GsonUtil.toJson(codeCreateBranchDTO));
    }

    public static String merge(String prId,String projectKey,String slug) throws Exception {
        String url = MessageFormat.format("{0}/rest/api/2.0/projects/{1}/repos/{2}/pull-requests/{3}/merge",CODE_HOST_TEST,projectKey,slug,prId);
        return doCodePost(url,null);
    }

    public static String createPr(CodeCreatePrDTO codeCreatePrDTO,String projectKey,String slug) throws Exception {
        String url = MessageFormat.format("{0}/rest/api/2.0/projects/{1}/repos/{2}/pull-requests",CODE_HOST_TEST,projectKey,slug);
        return doCodePost(url,GsonUtil.toJson(codeCreatePrDTO));
    }
    public static String getMergeStatus(String projectKey,String slug,String id) throws Exception {
        String url = MessageFormat.format("{0}/rest/api/2.0/projects/{1}/repos/{2}/pull-requests/{3}/merge",CODE_HOST_TEST,projectKey,slug,id);
        return doCodeGet(url);
    }

    /**
     * Oceanus Http 服务加签
     */
    public static String getOceanusSignature(String token, String clientAppkey, String remoteAppkey, Long time)
            throws JsonProcessingException {
        SignatureType type = SignatureType.HMAX_SHA1;
        ISignature signature = type.getSignature();
        Map<String, String> m1 = new HashMap<>(4);
        m1.put(Constants.ALGORITHM_KEY, type.getType());
        m1.put(Constants.TYPE_KEY, "patriot");

        m1.put(Constants.TIME_KEY, String.valueOf(time == null ? System.currentTimeMillis() : time));

        String s1 = Base64Utils.base64Encode(JsonUtils.mapToJson(m1));

        Map<String, String> m2 = new TreeMap<>();
        m2.put(Constants.NAMESPACE_KEY, clientAppkey);
        m2.put(Constants.NAME_KEY, remoteAppkey);
        String s2 = Base64Utils.base64Encode(JsonUtils.mapToJson(m2));
        return s1 + "." + s2 + "." + signature.signature(token, s2);
    }


}
