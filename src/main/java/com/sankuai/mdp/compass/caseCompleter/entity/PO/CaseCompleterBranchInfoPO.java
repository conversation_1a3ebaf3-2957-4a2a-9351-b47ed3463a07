package com.sankuai.mdp.compass.caseCompleter.entity.PO;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * @auther bock
 * @date 2024-10-29
 */
@Data
@TableName("case_completer_branch_info")
@Accessors(chain = true)
public class CaseCompleterBranchInfoPO {

    @TableId(value = "id", type = IdType.AUTO)
    Long id;

    @TableField("branch")
    String branch;

    @TableField("app_key")
    String appKey;

    @TableField("type")
    String type;

    @TableField("status")
    String status;

    @TableField("pr_id")
    String prId;

    @TableField("create_time")
    Timestamp createTime;

    @TableField("update_time")
    Timestamp updateTime;

    @TableField("delete_time")
    String deleteTime;

    @TableLogic(value = "0", delval = "id")
    @TableField(value = "is_deleted")
    Integer isDeleted;
}
