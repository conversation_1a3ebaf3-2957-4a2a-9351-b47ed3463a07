package com.sankuai.mdp.compass.caseCompleter.utils;

import com.sankuai.inf.auth.api.exception.AuthSecurityException;
import com.sankuai.inf.auth.api.model.AuthToken;
import com.sankuai.inf.auth.api.model.SignParam;
import com.sankuai.inf.sts.api.model.STSRequest;
import com.sankuai.inf.sts.api.service.ISTSService;
import com.sankuai.inf.sts.api.service.STSServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class IAMSTSUtilEnv {

    /**
     * STS票据统一由 com.sankuai.devtools.gateway.service 服务管理
     */
    private static final String STS_TOKEN_APPKEY = "com.sankuai.devtools.gateway.service";

    private static ISTSService stsServiceForIAM;

    static {
        // 用于申请IAM类型帐号票据：https://km.sankuai.com/page/1230540993
        stsServiceForIAM = STSServiceFactory.create(STSRequest.Builder.newSTSRequest()
                                                          .withAppKey(STS_TOKEN_APPKEY)
                                                          .withKeyName("IAMTicket")
                                                            // 这里的 keyName 不允许修改
                                                          // 指定从线上签票，如果当前host环境为线下，就是跨环境签发
                                                    			//（由sdk自行完成处理，用户只要明确知道自己需要获取什么环境的票据即可）
                                                          .onlineRequest() .signAction()
                                                          .build());
    }

    /**
     * IAM 帐号票据签发：通过IAM帐号密码换票据
     *
     * @param clientId     IAM帐号
     * @param clientSecret IAM密码
     * @return 签发的IAM票据
     */
    public static String getIAMToken(String clientId, String clientSecret) throws AuthSecurityException {
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return null;
        }
        SignParam signParam = SignParam.Builder.newSignParam()
              .withClientId(clientId)
              .withExtension("iampwd", clientSecret)
              .build();

        AuthToken token = stsServiceForIAM.sign(signParam);
        return token.getAt();
    }
}