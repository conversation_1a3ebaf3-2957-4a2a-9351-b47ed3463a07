package com.sankuai.mdp.compass.caseCompleter.service;

import com.sankuai.mdp.compass.caseCompleter.entity.PO.CaseCompleterBranchInfoPO;
import java.util.List;

/**
 * @ author bock
 * @ date 2024-10-16
 */
public interface CaseCompleterService {
    void scanSwimlane();

    void createTask() throws Exception;

    void generate() throws Exception;

    void manageTask() throws Exception;

    void merge() throws Exception;

    List<CaseCompleterBranchInfoPO> getBranches(String type, String status);

    List<CaseCompleterBranchInfoPO> getBranches(String appkey);

    void update(CaseCompleterBranchInfoPO caseCompleterBranchInfo);

    List<CaseCompleterBranchInfoPO> getConflictedBranches();
}
