package com.sankuai.mdp.compass.caseCompleter.utils;

import com.google.gson.reflect.TypeToken;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.BaseResponseDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.GenerateRequestDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.GenerateResponseDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.StopTaskRequestDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.TestXTaskInfoDTO;
import com.sankuai.mdp.compass.common.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * @auther bock
 * @date 2024-10-11
 */
@Slf4j
public class TestXUtil {
    // 定义常量URL
    private static final String DOMAIN_URL = "http://pipe.vip.sankuai.com";
    private static final String GET_TASK_URL = "http://pipe.vip.sankuai.com/repeater/open/api/query/task";
    private static final String STOP_TASK_URL = "http://pipe.vip.sankuai.com/repeater/open/api/task/stop";
    private static final String GENERATE_TASK_URL = "http://pipe.vip.sankuai.com/repeater/open/api/case/suggest/generate";
    private static final String CREATE_TASK_URL = "http://pipe.vip.sankuai.com/repeater/open/api/record/start";


    /**
     * 根据泳道创建任务
     * @param task 任务对象
     * @return 基础响应DTO
     */
    public static BaseResponseDTO<TestXTaskInfoDTO> createTaskBySwimLane(TestXTaskInfoDTO task) throws Exception {
        return GsonUtil.fromJson(
                HttpUtil.doPost(CREATE_TASK_URL, GsonUtil.toJson(task)),
                new TypeToken<BaseResponseDTO<TestXTaskInfoDTO>>() {
                }.getType());
    }


    public static BaseResponseDTO<TestXTaskInfoDTO> restartTask(TestXTaskInfoDTO task) throws Exception {
        return GsonUtil.fromJson(
                HttpUtil.doPost(CREATE_TASK_URL, GsonUtil.toJson(task)),
                new TypeToken<BaseResponseDTO<TestXTaskInfoDTO>>() {
                }.getType());
    }


    /**
     * 生成所有任务
     * @param swimlane 泳道
     * @param repo     仓库
     * @return 基础响应DTO
     */
    public static BaseResponseDTO<GenerateResponseDTO> generateAll(String swimlane, String repo,String branch) throws Exception {
        boolean finish = false;
        int start = 0;
        BaseResponseDTO<GenerateResponseDTO> resp = null;
        while (!finish) {
            resp = generate(swimlane, repo,branch, start, 200);
            if (resp==null || resp.getData() == null) {
                break;
            }
            finish = resp.getData().getFinish();
            start += 200;
        }

        return resp;
    }

    /**
     * 生成指定范围的任务
     *
     * @param swimlane 泳道
     * @param repo     仓库
     * @param start    起始位置
     * @param size     大小
     * @return 基础响应DTO
     */
    public static BaseResponseDTO<GenerateResponseDTO> generate(String swimlane, String repo,String branch, Integer start, Integer size) throws Exception {
        TestXTaskInfoDTO task = getTaskBySwimlane(swimlane).getData();
        //TODO 异常待处理，泳道任务终止后将查不到任务
        if (task == null) {
            return null;
        }

        GenerateRequestDTO generateRequestDTO = new GenerateRequestDTO()
                .setTaskId(task.getId())
                .setStart(start)
                .setSize(size)
                .setTestRepo(repo)
                .setTestBranch(branch)
                //TODO 枚举待创建
                .setCaseMode("SINGLE_MOCK")
                .setUserName(task.getUserName());

        return GsonUtil.fromJson(
                HttpUtil.doPost(GENERATE_TASK_URL, GsonUtil.toJson(generateRequestDTO)),
                new TypeToken<BaseResponseDTO<GenerateResponseDTO>>() {
                }.getType());
    }

    /**
     * 根据泳道停止任务
     *
     * @param swimlane 泳道
     * @return 基础响应DTO
     */
    public static BaseResponseDTO stopTaskBySwimLane(String swimlane) throws Exception {
        TestXTaskInfoDTO task = getTaskBySwimlane(swimlane).getData();
        StopTaskRequestDTO stopTaskRequestDTO = new StopTaskRequestDTO()
                .setId(task.getId())
                .setUserName(task.getUserName());
        return stopTaskByIdAndUserName(stopTaskRequestDTO);
    }


    /**
     * 根据泳道获取任务
     *
     * @param swimlane 泳道
     * @return 基础响应DTO
     */
    public static BaseResponseDTO<TestXTaskInfoDTO> getTaskBySwimlane(String swimlane) throws Exception {
        HashMap<String, String> map = new HashMap<>();
        map.put("swimlane", swimlane);

        return GsonUtil.fromJson(
                HttpUtil.doGet(UrlBuilder.buildUrl(GET_TASK_URL, map)),
                new TypeToken<BaseResponseDTO<TestXTaskInfoDTO>>() {
                }.getType());
    }

    /**
     * 根据录制任务Id获取任务
     *
     * @param taskId 任务id
     * @return 基础响应DTO
     */
    public static BaseResponseDTO<TestXTaskInfoDTO> getTaskByTaskId(Integer taskId) throws Exception {
        HashMap<String, Integer> map = new HashMap<>();
        map.put("taskId", taskId);

        return GsonUtil.fromJson(
                HttpUtil.doGet(UrlBuilder.buildUrl(GET_TASK_URL, map)),
                new TypeToken<BaseResponseDTO<TestXTaskInfoDTO>>() {
                }.getType());
    }

    /**
     * 根据ID和用户名停止任务
     *
     * @param stopTaskRequestDTO 停止任务请求DTO
     * @return 基础响应DTO
     */
    public static BaseResponseDTO stopTaskByIdAndUserName(StopTaskRequestDTO stopTaskRequestDTO) throws Exception {
        return GsonUtil.fromJson(
                HttpUtil.doPost(STOP_TASK_URL, GsonUtil.toJson(stopTaskRequestDTO)),
                BaseResponseDTO.class);
    }
}
