package com.sankuai.mdp.compass.caseCompleter.utils;

import com.sankuai.inf.auth.api.exception.AuthSecurityException;
import com.sankuai.inf.auth.api.model.AuthToken;
import com.sankuai.inf.auth.api.model.SignParam;
import com.sankuai.inf.sts.api.model.STSRequest;
import com.sankuai.inf.sts.api.service.ISTSService;
import com.sankuai.inf.sts.api.service.STSServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class IAMSTSUtil {

    /**
     * STS票据统一由 com.sankuai.devtools.gateway.service 服务管理
     */
    private static final String STS_TOKEN_APPKEY = "com.sankuai.devtools.gateway.service";

    private static ISTSService stsServiceForIAM;

    static {
        // 用于申请IAM类型帐号票据：https://km.sankuai.com/page/1230540993
        stsServiceForIAM = STSServiceFactory.create(STSRequest.Builder.newSTSRequest()
                                                          .withAppKey(STS_TOKEN_APPKEY)
                                                          .withKeyName("IAMTicket")// 这里的 keyName 不允许修改
                                                          .signAction()
                                                          .build());
    }

    /**
     * IAM 帐号票据签发：通过IAM帐号密码换票据
     *
     * @param clientId     IAM帐号
     * @param clientSecret IAM密码
     * @return 签发的IAM票据
     */
    public static String getIAMToken(String clientId, String clientSecret) throws AuthSecurityException {
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return null;
        }
        SignParam signParam = SignParam.Builder.newSignParam()
              .withClientId(clientId)
              .withExtension("iampwd", clientSecret)
              .build();

        AuthToken token = stsServiceForIAM.sign(signParam);
        return token.getAt();
    }
}