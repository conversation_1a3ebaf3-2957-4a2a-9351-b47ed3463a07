package com.sankuai.mdp.compass.caseCompleter.utils;

import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class UrlBuilder {

    public static String buildUrl(String baseUrl, Object params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        boolean isFirst = true;

        for (Field field : params.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(params);
                if (value != null) {
                    if (isFirst) {
                        urlBuilder.append("?");
                        isFirst = false;
                    } else {
                        urlBuilder.append("&");
                    }
                    urlBuilder.append(encodeParam(field.getName()))
                            .append("=")
                            .append(encodeParam(value.toString()));
                }
            } catch (IllegalAccessException e) {
                throw new IllegalArgumentException("params error");
            }
        }

        return urlBuilder.toString();
    }

    public static String buildUrl(String baseUrl, Map<String, String> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            boolean isFirst = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (isFirst) {
                    isFirst = false;
                } else {
                    urlBuilder.append("&");
                }
                urlBuilder.append(encodeParam(entry.getKey()))
                        .append("=")
                        .append(encodeParam(entry.getValue()));
            }
        }
        return urlBuilder.toString();
    }

    private static String encodeParam(String param) {
        try {
            return URLEncoder.encode(param, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            throw new RuntimeException("Failed to encode parameter", e);
        }
    }
}
