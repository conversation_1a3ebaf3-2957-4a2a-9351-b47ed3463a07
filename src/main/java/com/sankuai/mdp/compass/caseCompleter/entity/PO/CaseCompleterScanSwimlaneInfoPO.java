package com.sankuai.mdp.compass.caseCompleter.entity.PO;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.*;
import jnr.ffi.annotations.In;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @description:
 * @author: niujiechao
 * @date: 2024/5/31
 * @time: 上午10:06
 */
@Data
@TableName("case_completer_scan_swimlane_info")
@Accessors(chain = true)
public class CaseCompleterScanSwimlaneInfoPO {
    //主
    @TableId(value = "id",type = IdType.AUTO)
    Long id;

    @TableField(value = "swimlane")
    String swimlane;

    @TableField(value = "task_id")
    Integer taskId;

    @TableLogic(value = "0", delval = "id")
    @TableField(value = "is_deleted")
    Integer isDeleted;

    @TableField(value = "is_active")
    Integer isActive;

    @TableField(value = "env")
    String env;

    @TableField(value = "status")
    String status;

    @TableField(value = "retry_count")
    Integer retryCount;

    @TableField(value = "app_key")
    String appkey;

    @TableField(value = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Timestamp createTime;

    @TableField(value = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Timestamp updateTime;

    @TableField(value = "delete_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    String deleteTime;

}
