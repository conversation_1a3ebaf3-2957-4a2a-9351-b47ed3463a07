package com.sankuai.mdp.compass.caseCompleter.utils;

import com.sankuai.oceanus.http.client.apache.OceanusHttpProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.EntityBuilder;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.URI;
import java.net.UnknownHostException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CommonHttpUtil {

    public static final int CONNECT_TIMEOUT = 10000;

    public static final int SOCKET_TIMEOUT = 10000;

    public static final int RETRY_TIME = 3;

    public static final String UTF8 = "UTF-8";

    /**
     * 全局连接池对象
     */
    private static final PoolingHttpClientConnectionManager CONN_MANAGER = new PoolingHttpClientConnectionManager();

    private static CloseableHttpClient httpClient;

    /*
     * 静态代码块配置连接池信息
     */
    static {
        // 设置最大连接数
        CONN_MANAGER.setMaxTotal(1000);
        // 设置每个连接的路由数
        CONN_MANAGER.setDefaultMaxPerRoute(10);
        IdleConnectionMonitorThread staleMonitor = new IdleConnectionMonitorThread(CONN_MANAGER);
        staleMonitor.start();
        httpClient = createHttpClient();
    }

    /**
     * 获取Http客户端连接对象
     *
     * @return Http客户端连接对象
     */
    public static CloseableHttpClient createHttpClient() {
        // 创建Http请求配置参数
        RequestConfig requestConfig = RequestConfig.custom()
              // 获取连接超时时间
              .setConnectionRequestTimeout(CONNECT_TIMEOUT)
              // 请求超时时间
              .setConnectTimeout(CONNECT_TIMEOUT)
              // 响应超时时间
              .setSocketTimeout(SOCKET_TIMEOUT).build();

        /*
         * 测出超时重试机制为了防止超时不生效而设置
         *  如果直接放回false,不重试
         *  这里会根据情况进行判断是否重试
         */
        HttpRequestRetryHandler retry = (exception, executionCount, context) -> {
            if (executionCount >= RETRY_TIME) {// 如果已经重试了3次，就放弃
                return false;
            }
            if (exception instanceof NoHttpResponseException) {// 如果服务器丢掉了连接，那么就重试
                return true;
            }
            if (exception instanceof SSLHandshakeException) {// 不要重试SSL握手异常
                return false;
            }
            if (exception instanceof InterruptedIOException) {// 超时
                return true;
            }
            if (exception instanceof UnknownHostException) {// 目标服务器不可达
                return false;
            }
            if (exception instanceof ConnectTimeoutException) {// 连接被拒绝
                return false;
            }
            if (exception instanceof SSLException) {// ssl握手异常
                return false;
            }
            HttpClientContext clientContext = HttpClientContext.adapt(context);
            HttpRequest request = clientContext.getRequest();
            // 如果请求是幂等的，就再次尝试
            return !(request instanceof HttpEntityEnclosingRequest);
        };

        ConnectionKeepAliveStrategy myStrategy = (response, context) -> {
            HeaderElementIterator it = new BasicHeaderElementIterator(response.headerIterator(HTTP.CONN_KEEP_ALIVE));
            while (it.hasNext()) {
                HeaderElement he = it.nextElement();
                String param = he.getName();
                String value = he.getValue();
                if (value != null && "timeout".equalsIgnoreCase(param)) {
                    return Long.parseLong(value) * 1000;
                }
            }
            return 30 * 1000;
        };

        // 创建httpClient
        return HttpClients.custom()
              // 把请求相关的超时信息设置到连接客户端
              .setDefaultRequestConfig(requestConfig)
              // 把请求重试设置到连接客户端
              .setRetryHandler(retry)
              // 配置连接池管理对象
              .setConnectionManager(CONN_MANAGER)
              //keep alive strategy
              .setKeepAliveStrategy(myStrategy)
              // OceanusHttpProcessor 参考：https://km.sankuai.com/page/56541963
              .addInterceptorFirst((HttpRequestInterceptor) new OceanusHttpProcessor()).build();
    }

    private static CloseableHttpClient getHttpClient() {
        return httpClient;
    }

    private static synchronized void resetHttpClient() {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        httpClient = createHttpClient();
    }

    public static String doRequest(String url, String body, String method, Map<String, String> headers)
          throws Exception {
        HttpEntityEnclosingRequestBase httpRequest = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return method;
            }
        };
        httpRequest.setProtocolVersion(HttpVersion.HTTP_1_1);
        httpRequest.setURI(URI.create(url));

        // Set header
        headers.forEach(httpRequest::addHeader);
        httpRequest.addHeader("Accept-Charset", UTF8);
        httpRequest.addHeader("Content-type", "application/json");

        // Set body
        if (StringUtils.isNotBlank(body)) {
            HttpEntity entity = EntityBuilder.create()
                  .setText(body)
                  .setContentType(ContentType.create("Content-Type", UTF8))
                  .build();
            httpRequest.setEntity(entity);
        }

        // 获取客户端连接对象
        CloseableHttpClient httpClient = getHttpClient();
        CloseableHttpResponse response = null;

        try {
            // 执行请求
            response = httpClient.execute(httpRequest);
            // 获取响应实体
            HttpEntity entity = response.getEntity();
            int httpCode = response.getStatusLine().getStatusCode();
            if (httpCode < HttpStatus.SC_OK || httpCode >= HttpStatus.SC_BAD_REQUEST) {
                throw new RuntimeException(response.getEntity() != null ?
                                                 EntityUtils.toString(response.getEntity(), UTF8) :
                                                 response.toString());
            }
            // 获取响应信息
            return EntityUtils.toString(entity, UTF8);
        } catch (IllegalStateException e) {
            resetHttpClient();
            throw e;
        } finally {
            if (null != response) {
                try {
                    EntityUtils.consume(response.getEntity());
                    response.close();
                } catch (IOException e) {
                    log.error("释放链接错误", e);
                }
            }
        }
    }

    // 清理空闲未断开的 Connect
    public static class IdleConnectionMonitorThread extends Thread {

        private final HttpClientConnectionManager connMgr;

        private volatile boolean shutdown;

        public IdleConnectionMonitorThread(PoolingHttpClientConnectionManager connMgr) {
            super();
            this.connMgr = connMgr;
        }

        @Override
        public void run() {
            try {
                while (!shutdown) {
                    synchronized (this) {
                        wait(10000);
                        connMgr.closeExpiredConnections();
                        connMgr.closeIdleConnections(60, TimeUnit.SECONDS);
                    }
                }
            } catch (InterruptedException ex) {
                shutdown();
            }
        }

        public void shutdown() {
            shutdown = true;
            synchronized (this) {
                notifyAll();
            }
        }
    }
}