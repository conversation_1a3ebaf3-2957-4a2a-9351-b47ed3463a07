package com.sankuai.mdp.compass.caseCompleter.entity.DTO;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @auther bock
 * @date 2024-10-31
 */
@Data
@Accessors(chain = true)
public class CodeCreatePrDTO {
    Boolean deleteSourceRefAfterMerge;
    Ref fromRef;
    String title;
    Ref toRef;

    @Data
    @Accessors(chain = true)
    public static class Ref{
        String displayId;
        String id;
        Repository repository;

        @Data
        @Accessors(chain = true)
        public static class Repository{
            Project project;
            String slug;

            @Data
            @Accessors(chain = true)
            public static class Project{
                String key;
            }
        }
    }

}



