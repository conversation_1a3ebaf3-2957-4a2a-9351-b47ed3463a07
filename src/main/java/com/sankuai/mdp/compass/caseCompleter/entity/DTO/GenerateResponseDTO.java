package com.sankuai.mdp.compass.caseCompleter.entity.DTO;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @auther bock
 * @date 2024-10-12
 */
@Data
@Accessors(chain = true)
public class GenerateResponseDTO {
    // pr链接
    private String prUrl;
    // pr编号
    private Integer prId;
    // 推荐用例是否已全部生成
    private Boolean finish;
    // 本次生成用例数量
    private Integer caseCount;
    // 推荐用例总数
    private Integer caseTotalCount;
}
