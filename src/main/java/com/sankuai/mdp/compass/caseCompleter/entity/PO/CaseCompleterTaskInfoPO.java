package com.sankuai.mdp.compass.caseCompleter.entity.PO;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.mdp.compass.caseCompleter.entity.Plugin;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @auther bock
 * @date 2024-10-18
 */
@Data
@TableName("case_completer_task_info")
@Accessors(chain = true)
public class CaseCompleterTaskInfoPO {
    //主
    @TableId(value = "id",type = IdType.AUTO)
    Long id;

    @TableField(value = "swimlane")
    String swimlane;

    @TableField(value = "task_id")
    Integer taskId;

    @TableLogic(value = "0", delval = "id")
    @TableField(value = "is_deleted")
    Integer isDeleted;

    @TableField(value = "status")
    String status;

    @TableField(value = "show_name")
    String showName;

    @TableField(value = "plugin")
    String plugin;

    @TableField(value = "appkey_list")
    String appkeyList;

    @TableField(value = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Timestamp createTime;

    @TableField(value = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Timestamp updateTime;

    @TableField(value = "delete_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    String deleteTime;
}
