package com.sankuai.mdp.compass.caseCompleter.entity.DTO;


import com.sankuai.mdp.compass.caseCompleter.entity.Plugin;
import com.sankuai.mdp.compass.caseCompleter.entity.RecordMachineModel;
import com.sankuai.mdp.compass.caseCompleter.caseCompleterEnums.EnvType;
import com.sankuai.mdp.compass.caseCompleter.caseCompleterEnums.RecordType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TestXTaskInfoDTO {
    /**
     * 创建任务
     */
    private RecordType recordType;
    private String showName;
    private EnvType env;
    private String userName;
    private List<String> teamMemberList;
    private List<String> appkeyList;
    private Integer recordTimeLimit;
    private String swimlane;
    private List<RecordMachineModel> recordMachineModels;
    private Integer id;
    private List<Plugin> plugins;

    /**
     * 查询任务
     */
    private String updateTime;
    private List<Object> pluginConfigs;
    private String caseFilterType;
    private String incCaseFilter;
    private String createTime;
    private String progress;
    private String triggerType;
    private String stackUuid;
    private String status;
}