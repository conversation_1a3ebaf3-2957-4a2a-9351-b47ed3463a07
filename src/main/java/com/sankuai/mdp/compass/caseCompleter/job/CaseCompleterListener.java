package com.sankuai.mdp.compass.caseCompleter.job;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.mdp.compass.caseCompleter.service.CaseCompleterService;
import com.sankuai.mdp.compass.caseCompleter.utils.CodeUtils;
import org.springframework.beans.factory.annotation.Autowired;

@CraneConfiguration
public class CaseCompleterListener {
    @Autowired
    private CaseCompleterService caseCompleterService;

    @Crane("com.sankuai.mdp.compass.caseCompleter.job.scanSwimlane")
    public void scanSwimlane(){
        caseCompleterService.scanSwimlane();
    }

    @Crane("com.sankuai.mdp.compass.caseCompleter.job.createTask")
    public void createTask() throws Exception {
        caseCompleterService.createTask();
    }

    @Crane("com.sankuai.mdp.compass.caseCompleter.job.generate")
    public void generate() throws Exception {
        caseCompleterService.generate();
    }

    @Crane("com.sankuai.mdp.compass.caseCompleter.job.manageTask")
    public void manageTask() throws Exception {
        caseCompleterService.manageTask();
    }

    @Crane("com.sankuai.mdp.compass.caseCompleter.job.merge")
    public void merge() throws Exception {
        caseCompleterService.merge();
    }



}
