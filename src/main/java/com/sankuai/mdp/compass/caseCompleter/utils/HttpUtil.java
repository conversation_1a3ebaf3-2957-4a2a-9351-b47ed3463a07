package com.sankuai.mdp.compass.caseCompleter.utils;

import com.sankuai.oceanus.http.client.apache.OceanusHttpProcessor;
import com.sankuai.oceanus.http.internal.HttpHeader;
import groovy.util.logging.Slf4j;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpUtil {
    //注意是强转为HttpRequestInterceptor而不是HttpResponseInterceptor
    private static final HttpClientBuilder BUILDER = HttpClients.custom().addInterceptorFirst((HttpRequestInterceptor) new OceanusHttpProcessor());

    // 每次请求时指定remoteAppkey,remoteAppkey是服务端appkey，也就是你想调用的服务的appkey
    private static final String REMOTE_APPKEY = "com.sankuai.cd.repeater";

    public static String doGet(String url) throws Exception {
        // httpclient使用完后记得关闭
        CloseableHttpClient client = BUILDER.build();
        HttpGet request = new HttpGet(url);
        request.setHeader(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, REMOTE_APPKEY);

        try (CloseableHttpResponse response = client.execute(request)) {
            return EntityUtils.toString(response.getEntity());
        } finally {
            client.close();
        }
    }

    public static String doPost(String url, String body) throws Exception {
        // httpclient使用完后记得关闭
        CloseableHttpClient client = BUILDER.build();
        HttpPost request = new HttpPost(url);
        request.setHeader(HttpHeader.OCEANUS_RMOTE_APPKEY_HEADER, REMOTE_APPKEY);
        // 设置 Content-Type 为 application/json
        request.setHeader("Content-Type", "application/json");
        StringEntity entity = new StringEntity(body, StandardCharsets.UTF_8);
        entity.setContentType("application/json");
        request.setEntity(entity);
        try (CloseableHttpResponse response = client.execute(request)) {
            return EntityUtils.toString(response.getEntity());
        } finally {
            client.close();
        }
    }


}
