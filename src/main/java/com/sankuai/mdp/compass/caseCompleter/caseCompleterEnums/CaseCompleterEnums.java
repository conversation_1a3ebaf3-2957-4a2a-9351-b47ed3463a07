package com.sankuai.mdp.compass.caseCompleter.caseCompleterEnums;

public final class CaseCompleterEnums {
    //泳道状态
    public static final String SWIMLANE_PENDING = "PENDING";
    public static final String SWIMLANE_RECORDING = "RECORDING";
    public static final String SWIMLANE_COMPLETED = "COMPLETED";
    public static final String SWIMLANE_FAILED = "FAILED";

    //录制任务状态
    public static final String TASK_START = "START";
    public static final String TASK_PENDING = "PENDING";
    public static final String TASK_RUNNING = "RUNNING";
    public static final String TASK_STOP = "STOP";
    public static final String TASK_ERROR = "ERROR";

    //分支类型
    public static final String BRANCH_TEST = "TEST";
    public static final String BRANCH_ONLINE = "ONLINE";

    //分支状态
    public static final String BRANCH_TEST_NEW = "TEST_NEW";
    public static final String BRANCH_TEST_PENDING = "TEST_PENDING";
    public static final String BRANCH_TEST_MERGING = "TEST_MERGING";
    public static final String BRANCH_TEST_MERGED = "TEST_MERGED";
    public static final String BRANCH_TEST_CONFLICTED = "TEST_CONFLICTED";
    public static final String BRANCH_TEST_RUNNING = "TEST_RUNNING";
    public static final String BRANCH_TEST_FAILED = "TEST_FAILED";
    public static final String BRANCH_TEST_SUCCESS = "TEST_SUCCESS";


    // 私有构造函数，防止实例化
    private CaseCompleterEnums() {}
}