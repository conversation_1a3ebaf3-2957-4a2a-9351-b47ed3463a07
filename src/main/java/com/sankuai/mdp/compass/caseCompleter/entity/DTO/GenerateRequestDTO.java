package com.sankuai.mdp.compass.caseCompleter.entity.DTO;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @auther bock
 * @date 2024-10-12
 */
@Data
@Accessors(chain = true)
public class GenerateRequestDTO {

    private Integer taskId;

    private Integer start;

    private Integer size;

    private String testRepo;

    private String testBranch;

    private String caseMode;

    private String userName;
}