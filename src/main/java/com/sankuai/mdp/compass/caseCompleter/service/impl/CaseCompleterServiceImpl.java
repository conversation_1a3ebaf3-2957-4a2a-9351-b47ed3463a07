package com.sankuai.mdp.compass.caseCompleter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dianping.lion.client.Lion;
import com.google.gson.JsonParser;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.BaseResponseDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.CodeCreatePrDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.GenerateResponseDTO;
import com.sankuai.mdp.compass.caseCompleter.entity.PO.CaseCompleterBranchInfoPO;
import com.sankuai.mdp.compass.caseCompleter.entity.PO.CaseCompleterScanSwimlaneInfoPO;
import com.sankuai.mdp.compass.caseCompleter.entity.PO.CaseCompleterTaskInfoPO;
import com.sankuai.mdp.compass.caseCompleter.entity.DTO.TestXTaskInfoDTO;
import com.sankuai.mdp.compass.caseCompleter.caseCompleterEnums.EnvType;
import com.sankuai.mdp.compass.caseCompleter.caseCompleterEnums.RecordType;
import com.sankuai.mdp.compass.caseCompleter.caseCompleterEnums.CaseCompleterEnums;
import com.sankuai.mdp.compass.caseCompleter.mapper.CaseCompleterBranchInfoMapper;
import com.sankuai.mdp.compass.caseCompleter.mapper.CaseCompleterScanSwimlaneInfoMapper;
import com.sankuai.mdp.compass.caseCompleter.mapper.CaseCompleterTaskInfoMapper;
import com.sankuai.mdp.compass.caseCompleter.service.CaseCompleterService;
import com.sankuai.mdp.compass.caseCompleter.utils.CodeUtils;
import com.sankuai.mdp.compass.caseCompleter.utils.TestXUtil;
import com.sankuai.mdp.compass.common.utils.GsonUtil;
import com.sankuai.mdp.compass.riskSQLScan.entity.dto.CargoResponseDTO;
import groovy.util.logging.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ author bock
 * @ date 2024-10-16
 */
@Service
@Slf4j
public class CaseCompleterServiceImpl implements CaseCompleterService {
    @Autowired
    private CaseCompleterScanSwimlaneInfoMapper caseCompleterScanSwimlaneInfoMapper;
    @Autowired
    private CaseCompleterTaskInfoMapper caseCompleterTaskInfoMapper;
    @Autowired
    private CaseCompleterBranchInfoMapper caseCompleterBranchInfoMapper;

    private final String appkey="com.sankuai.sigma.compass";

    private final String repo=Lion.getConfigRepository(appkey).get("caseCompleterRepository");
    private final String projectKey=Lion.getConfigRepository(appkey).get("caseCompleterProjectKey");
    private final String slug=Lion.getConfigRepository(appkey).get("caseCompleterSlug");
    private final String user=Lion.getConfigRepository(appkey).get("caseCompleterUser");
    private final Integer maxRetryCount=Lion.getConfigRepository(appkey).getIntValue("caseCompleterMaxRetryCount");

    private static final Logger LOGGER=  LoggerFactory.getLogger(CaseCompleterServiceImpl.class);


    /**
     * 扫描管理泳道
     * deploy 状态下创建：
     *      表中已存在 ：更新字段 isActive=1 ；status=pending；retryCount=0；;updateTime?
     *      表中不存在:  插入泳道信息 和 分支信息（分支表中对应的appkey、不存在）
     * delete 状态
     * 更新字段 isActive=0
     * */
    @Override
    public void scanSwimlane() {
        List<String> appkeyList= Lion.getConfigRepository(this.appkey).getList("caseCompleterAppKey");
        for (String appkey : appkeyList) {
            try {
                HttpURLConnection connection = createHttpURLConnection(appkey);
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    //创建online分支
                    createBranch(CaseCompleterEnums.BRANCH_ONLINE,appkey,null);
                    //创建泳道
                    handleResponse(connection);
                } else {
                    LOGGER.warn("Request failed. Response Code: {}", responseCode);
                }
                connection.disconnect();
            } catch (Exception e) {
                handleException(e);
            }
        }
    }


    /**
     *  流程
     *  查询表中所有的泳道，更新符合条件的泳道信息，并做对应操作
     *       条件：
     *       基础条件 活跃（isActive=1）+待录制（pending）
     *       外加条件
     *       (1) +未达到最大重试次数（retryCount<=maxRetryCount）
     *           创建录制任务
     *           -〉创建失败-〉更新信息（retryCount、updateTime
     *           -〉创建成功-〉更新信息（taskId、status、updateTime）
     *       (2) +达到最大重试次数（retryCount>maxRetryCount）
     *           将泳道状态改为录制失败
     * */
    @Override
//    @Transactional
    //TODO 事务
    public void createTask() throws Exception {
        LambdaQueryWrapper<CaseCompleterScanSwimlaneInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsActive,1);
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getStatus, CaseCompleterEnums.SWIMLANE_PENDING);
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsDeleted,0);
        List<CaseCompleterScanSwimlaneInfoPO> caseCompleterScanSwimlaneInfos = caseCompleterScanSwimlaneInfoMapper.selectList(queryWrapper);
        for (CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo : caseCompleterScanSwimlaneInfos) {
            if(caseCompleterScanSwimlaneInfo.getRetryCount()<=maxRetryCount){
                createTask(caseCompleterScanSwimlaneInfo);
            }else {
                caseCompleterScanSwimlaneInfo.setStatus(CaseCompleterEnums.SWIMLANE_FAILED);
                caseCompleterScanSwimlaneInfo.setUpdateTime(getFormatDate());
                caseCompleterScanSwimlaneInfoMapper.updateById(caseCompleterScanSwimlaneInfo);
            }
        }
    }


    /**
     *  流程
     *  查询表中所有的泳道，更新符合条件的泳道信息，并做对应操作
     *       条件：不活跃（isActive=0）+录制中（recording）
     *          生成推荐用例-〉停止录制任务，更新录制任务信息（status、updateTime）-〉更新泳道状态为录制成功（status、updateTime）
     * */
    @Override
    //TODO 事务
    public void generate() throws Exception {
        LambdaQueryWrapper<CaseCompleterScanSwimlaneInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsActive,0);
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getStatus, CaseCompleterEnums.SWIMLANE_RECORDING);
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsDeleted,0);
        List<CaseCompleterScanSwimlaneInfoPO> caseCompleterScanSwimlaneInfos = caseCompleterScanSwimlaneInfoMapper.selectList(queryWrapper);

        for (CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo : caseCompleterScanSwimlaneInfos) {
            //生成所有用例至对应远程仓库分支
            BaseResponseDTO<GenerateResponseDTO> resp = TestXUtil.generateAll(
                    caseCompleterScanSwimlaneInfo.getSwimlane(),
                    repo,
                    getBranchAllName(CaseCompleterEnums.BRANCH_TEST,getBranchName(CaseCompleterEnums.BRANCH_TEST,null,caseCompleterScanSwimlaneInfo.getTaskId())));

            LOGGER.info("用例生成响应信息：{}", resp);

            //更新分支信息
            LambdaUpdateWrapper<CaseCompleterBranchInfoPO> branchUpdateWrapper=new UpdateWrapper<CaseCompleterBranchInfoPO>().lambda();
            branchUpdateWrapper.eq(CaseCompleterBranchInfoPO::getBranch,getBranchName(CaseCompleterEnums.BRANCH_TEST,null,caseCompleterScanSwimlaneInfo.getTaskId()));
            branchUpdateWrapper.eq(CaseCompleterBranchInfoPO::getType, CaseCompleterEnums.BRANCH_TEST);
            branchUpdateWrapper.eq(CaseCompleterBranchInfoPO::getStatus, CaseCompleterEnums.BRANCH_TEST_NEW);
            branchUpdateWrapper.eq(CaseCompleterBranchInfoPO::getIsDeleted,0);
            //泳道状态调整
            if("success".equals(resp.getMsg())){
                caseCompleterScanSwimlaneInfo.setStatus(CaseCompleterEnums.SWIMLANE_COMPLETED);
                branchUpdateWrapper.set(CaseCompleterBranchInfoPO::getStatus, CaseCompleterEnums.BRANCH_TEST_PENDING);
                branchUpdateWrapper.set(CaseCompleterBranchInfoPO::getPrId,resp.getData().getPrId());
            }else {
                //TODO 生成失败？重试？
                //已知若没有录制到case的任务，TestX会返回fail
                caseCompleterScanSwimlaneInfo.setStatus(CaseCompleterEnums.SWIMLANE_FAILED);
                //生成失败分支状态直接改为失败状态
                branchUpdateWrapper.set(CaseCompleterBranchInfoPO::getStatus, CaseCompleterEnums.SWIMLANE_FAILED);

            }

            caseCompleterBranchInfoMapper.update(new CaseCompleterBranchInfoPO(),branchUpdateWrapper);

            //停止录制任务
            //暂定自旋5次解决，如果超过重试次数，重新等待下一次定时任务调用。
            int count=0;
            while (count<5 && !"success".equals(TestXUtil.stopTaskBySwimLane(caseCompleterScanSwimlaneInfo.getSwimlane()).getMsg())){
                count++;
                Thread.sleep(50);
            }

            if(count >= 5){
                return;
            }

            //更新录制任务信息
            LambdaUpdateWrapper<CaseCompleterTaskInfoPO> taskInfoUpdateWrapper = new UpdateWrapper<CaseCompleterTaskInfoPO>().lambda();
            taskInfoUpdateWrapper.eq(CaseCompleterTaskInfoPO::getTaskId,caseCompleterScanSwimlaneInfo.getTaskId());
            taskInfoUpdateWrapper.eq(CaseCompleterTaskInfoPO::getIsDeleted,0);
            taskInfoUpdateWrapper.set(CaseCompleterTaskInfoPO::getStatus, CaseCompleterEnums.TASK_STOP);
            taskInfoUpdateWrapper.set(CaseCompleterTaskInfoPO::getUpdateTime,getFormatDate());
            caseCompleterTaskInfoMapper.update(new CaseCompleterTaskInfoPO(),taskInfoUpdateWrapper);

            //更新泳道信息
            caseCompleterScanSwimlaneInfo.setUpdateTime(getFormatDate());
            caseCompleterScanSwimlaneInfoMapper.updateById(caseCompleterScanSwimlaneInfo);

        }
    }
    /**
     * 更新start和running状态的录制任务
     * 如果返回状态为error就更新关联泳道信息
     * */
    @Override
    public void manageTask() throws Exception {
        LambdaQueryWrapper<CaseCompleterTaskInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseCompleterTaskInfoPO::getStatus, CaseCompleterEnums.TASK_START)
                .or().eq(CaseCompleterTaskInfoPO::getStatus, CaseCompleterEnums.TASK_RUNNING)
                .or().eq(CaseCompleterTaskInfoPO::getStatus, CaseCompleterEnums.TASK_PENDING);
        queryWrapper.eq(CaseCompleterTaskInfoPO::getIsDeleted, 0);
        List<CaseCompleterTaskInfoPO> caseCompleterTaskInfos = caseCompleterTaskInfoMapper.selectList(queryWrapper);

        for (CaseCompleterTaskInfoPO caseCompleterTaskInfo : caseCompleterTaskInfos) {
            String status = TestXUtil.getTaskByTaskId(caseCompleterTaskInfo.getTaskId()).getData().getStatus();
            if(!caseCompleterTaskInfo.getStatus().equals(status)){
                //状态改变
                // start-〉running | error
                // or running->  error
                caseCompleterTaskInfo.setStatus(status);
                LOGGER.info("录制任务状态更新：{}",caseCompleterTaskInfo);
                caseCompleterTaskInfoMapper.updateById(caseCompleterTaskInfo);

                //如果是error，变更泳道信息
                if(CaseCompleterEnums.TASK_ERROR.equals(status)){
                    LambdaQueryWrapper<CaseCompleterScanSwimlaneInfoPO> swimlaneQueryWrapper=new LambdaQueryWrapper<>();
                    swimlaneQueryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getSwimlane,caseCompleterTaskInfo.getSwimlane());
                    swimlaneQueryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsDeleted,0);
                    CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo = caseCompleterScanSwimlaneInfoMapper.selectOne(swimlaneQueryWrapper);

                    caseCompleterScanSwimlaneInfo.setRetryCount(caseCompleterScanSwimlaneInfo.getRetryCount()+1);
                    caseCompleterScanSwimlaneInfo.setUpdateTime(getFormatDate());
                    LOGGER.info("录制任务异常，泳道状态更新：{}",caseCompleterScanSwimlaneInfo);
                    caseCompleterScanSwimlaneInfoMapper.updateById(caseCompleterScanSwimlaneInfo);
                }
            }
        }

    }

    @Override
    public void merge() throws Exception {
        LambdaQueryWrapper<CaseCompleterBranchInfoPO> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseCompleterBranchInfoPO::getType, CaseCompleterEnums.BRANCH_TEST);
        queryWrapper.eq(CaseCompleterBranchInfoPO::getIsDeleted, 0);
        queryWrapper.orderByAsc(CaseCompleterBranchInfoPO::getCreateTime);
        Integer intervalSeconds=Lion.getConfigRepository(appkey).getIntValue("caseCompleterIntervalSeconds");

        List<CaseCompleterBranchInfoPO> branches = caseCompleterBranchInfoMapper.selectList(queryWrapper);
        for (CaseCompleterBranchInfoPO branch : branches) {
            if(CaseCompleterEnums.BRANCH_TEST_PENDING.equals(branch.getStatus())){
                //合并test pr
                String mergeRes = CodeUtils.merge(branch.getPrId(),projectKey,slug);
                LOGGER.info(mergeRes);
                //更改test分支信息
                branch.setStatus(CaseCompleterEnums.BRANCH_TEST_MERGED);
                branch.setPrId(null);
                caseCompleterBranchInfoMapper.updateById(branch);
            } else if (CaseCompleterEnums.BRANCH_TEST_MERGED.equals(branch.getStatus())
                    && hasExpired(branch.getCreateTime(),getFormatDate(),intervalSeconds)){
                //合并至online 或 failed 分支，先创建pr，再merge
                //创建pr
                CodeCreatePrDTO codeCreatePrDTO = getCodeCreatePrDTO(branch);
                String createPrRes = CodeUtils.createPr(codeCreatePrDTO,projectKey,slug);
                LOGGER.info(createPrRes);

                String prId= new JsonParser().parse(createPrRes).getAsJsonObject().get("id").getAsString();

                //合并pr
                String mergeResponse = CodeUtils.merge(prId,projectKey,slug);
                LOGGER.info(mergeResponse);

                //获取合并结果
                String mergeRes = CodeUtils.getMergeStatus(projectKey, slug, prId);
                String conflicted = new JsonParser().parse(mergeRes).getAsJsonObject().get("conflicted").getAsString();
                if("true".equals(conflicted)){
                    //合并冲突
                    branch.setStatus(CaseCompleterEnums.BRANCH_TEST_CONFLICTED);
                    caseCompleterBranchInfoMapper.updateById(branch);
                    return;
                }
                //删除表中分支
                //手动逻辑删除
                branch.setIsDeleted(1);
                branch.setDeleteTime(getFormatDate().toString());
                caseCompleterBranchInfoMapper.updateById(branch);
            }
        }

    }




    @Override
    public List<CaseCompleterBranchInfoPO> getBranches(String type, String status) {
        LambdaQueryWrapper<CaseCompleterBranchInfoPO> wrapper = new LambdaQueryWrapper<CaseCompleterBranchInfoPO>()
                .eq(CaseCompleterBranchInfoPO::getType, type)
                .eq(CaseCompleterBranchInfoPO::getIsDeleted,0);
        if(status!=null){
            wrapper.eq(CaseCompleterBranchInfoPO::getStatus, status);
        }
        return caseCompleterBranchInfoMapper.selectList(wrapper);
    }

    @Override
    public List<CaseCompleterBranchInfoPO> getBranches(String appkey) {
        if(appkey.isEmpty()){
            return Collections.emptyList();
        }
        return caseCompleterBranchInfoMapper.selectList(
                new LambdaQueryWrapper<CaseCompleterBranchInfoPO>()
                        .eq(CaseCompleterBranchInfoPO::getType, CaseCompleterEnums.BRANCH_TEST)
                        .eq(CaseCompleterBranchInfoPO::getAppKey, appkey)
                        .eq(CaseCompleterBranchInfoPO::getStatus, CaseCompleterEnums.BRANCH_TEST_CONFLICTED)
                        .eq(CaseCompleterBranchInfoPO::getIsDeleted,0)
        );

    }

    @Override
    public List<CaseCompleterBranchInfoPO> getConflictedBranches() {
        return caseCompleterBranchInfoMapper.selectList(new LambdaQueryWrapper<CaseCompleterBranchInfoPO>()
                .eq(CaseCompleterBranchInfoPO::getType, CaseCompleterEnums.BRANCH_TEST)
                .eq(CaseCompleterBranchInfoPO::getStatus, CaseCompleterEnums.BRANCH_TEST_CONFLICTED)
                .eq(CaseCompleterBranchInfoPO::getIsDeleted,0)
        );
    }

    @Override
    public void update(CaseCompleterBranchInfoPO caseCompleterBranchInfo) {
        caseCompleterBranchInfoMapper.update(
                caseCompleterBranchInfo,
                new UpdateWrapper<CaseCompleterBranchInfoPO>().lambda()
                        .eq(CaseCompleterBranchInfoPO::getIsDeleted,0)
                        .eq(CaseCompleterBranchInfoPO::getBranch,caseCompleterBranchInfo.getBranch())
                        .set(CaseCompleterBranchInfoPO::getUpdateTime,getFormatDate())
        );
    }

    private boolean hasExpired(Timestamp beginTime,Timestamp endTime,long intervalSeconds){
        long millisecondsBetween = endTime.getTime() - beginTime.getTime();
        long daysBetween = TimeUnit.MILLISECONDS.toSeconds(millisecondsBetween);
        return daysBetween>intervalSeconds;
    }


    /**
     * 创建test-》online的prDTO
     * */
    private CodeCreatePrDTO getCodeCreatePrDTO(CaseCompleterBranchInfoPO caseCompleterBranchInfo){
        //创建pr
        String toRefDisplayId=getBranchName(CaseCompleterEnums.BRANCH_ONLINE,caseCompleterBranchInfo.getAppKey(),null);;
        String toRefId=getBranchAllName(CaseCompleterEnums.BRANCH_ONLINE,toRefDisplayId);;

        return  new CodeCreatePrDTO()
                //合并后删除源分支
                .setDeleteSourceRefAfterMerge(true)
                .setTitle(caseCompleterBranchInfo.getBranch()+"-MergeFor-"+caseCompleterBranchInfo.getStatus())
                .setFromRef(new CodeCreatePrDTO.Ref()
                        .setDisplayId(caseCompleterBranchInfo.getBranch())
                        .setId(getBranchAllName(CaseCompleterEnums.BRANCH_TEST,caseCompleterBranchInfo.getBranch()))
                        .setRepository(new CodeCreatePrDTO.Ref.Repository()
                                .setProject(new CodeCreatePrDTO.Ref.Repository.Project()
                                        .setKey(projectKey))
                                .setSlug(slug)))
                .setToRef(new CodeCreatePrDTO.Ref()
                        .setDisplayId(toRefDisplayId)
                        .setId(toRefId));
    }

    private String getBranchAllName(String type,String displayName){
        if(CaseCompleterEnums.BRANCH_TEST.equals(type)){
            return "test/"+displayName;
        }else if (CaseCompleterEnums.BRANCH_ONLINE.equals(type)){
            return "online/"+displayName;
        }
        return null;
    }


    private void createBranch(String type,String appkey, Integer taskId) throws Exception {
        //TODO 创建分支失败异常处理
        if(appkey==null || appkey.isEmpty()) {
            return;
        }
        CaseCompleterBranchInfoPO caseCompleterBranchInfo = new CaseCompleterBranchInfoPO()
                .setAppKey(appkey)
                .setCreateTime(getFormatDate())
                .setUpdateTime(getFormatDate());

        String res=null;
        if(CaseCompleterEnums.BRANCH_ONLINE.equals(type)){
            caseCompleterBranchInfo
                    .setBranch(getBranchName(type,appkey,null))
                    .setType(CaseCompleterEnums.BRANCH_ONLINE);

            //重复分支创建判断,定时任务会重复创建插入online分支
            //TODO 重复代码待优化
            LambdaQueryWrapper<CaseCompleterBranchInfoPO> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(CaseCompleterBranchInfoPO::getBranch,caseCompleterBranchInfo.getBranch());
            queryWrapper.eq(CaseCompleterBranchInfoPO::getAppKey,caseCompleterBranchInfo.getAppKey());
            queryWrapper.eq(CaseCompleterBranchInfoPO::getIsDeleted,0);

            if(caseCompleterBranchInfoMapper.selectOne(queryWrapper) != null){
                return;
            }

            res = CodeUtils.createBranch(
                    getBranchAllName(CaseCompleterEnums.BRANCH_ONLINE,caseCompleterBranchInfo.getBranch()),
                    "master",
                    projectKey,
                    slug);
        }else if(CaseCompleterEnums.BRANCH_TEST.equals(type)){
            caseCompleterBranchInfo
                    .setBranch(getBranchName(type,null,taskId))
                    .setType(CaseCompleterEnums.BRANCH_TEST)
                    .setStatus(CaseCompleterEnums.BRANCH_TEST_NEW);

            //重复分支创建判断
            LambdaQueryWrapper<CaseCompleterBranchInfoPO> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(CaseCompleterBranchInfoPO::getBranch,caseCompleterBranchInfo.getBranch());
            queryWrapper.eq(CaseCompleterBranchInfoPO::getAppKey,caseCompleterBranchInfo.getAppKey());
            queryWrapper.eq(CaseCompleterBranchInfoPO::getIsDeleted,0);

            if(caseCompleterBranchInfoMapper.selectOne(queryWrapper) != null){
                return;
            }
            res=CodeUtils.createBranch(
                    getBranchAllName(CaseCompleterEnums.BRANCH_TEST,caseCompleterBranchInfo.getBranch()),
                    getBranchAllName(CaseCompleterEnums.BRANCH_ONLINE,getBranchName(CaseCompleterEnums.BRANCH_ONLINE,appkey,null)),
                    projectKey,
                    slug);
        }

        LOGGER.info(res);
        caseCompleterBranchInfoMapper.insert(caseCompleterBranchInfo);
    }

    private String getBranchName(String type,String appkey,Integer taskId){
        if(CaseCompleterEnums.BRANCH_TEST.equals(type)){
            return taskId + "_test";
        }else if (CaseCompleterEnums.BRANCH_ONLINE.equals(type)){
            return appkey + "_online";
        }

        return  null;
    }


    private void createTask(CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo) throws Exception {

        TestXTaskInfoDTO task = getTestXTaskInfoDTO(caseCompleterScanSwimlaneInfo);
        BaseResponseDTO<TestXTaskInfoDTO> resp = TestXUtil.createTaskBySwimLane(task);

        if("success".equals(resp.getMsg())){
            //成功，插入录制任务表,并更新对应泳道状态
            String pluginsJson = GsonUtil.toJson(resp.getData().getPlugins());
            String appkeyListJson = GsonUtil.toJson(resp.getData().getAppkeyList());

            CaseCompleterTaskInfoPO caseCompleterTaskInfo = new CaseCompleterTaskInfoPO()
                    .setTaskId(resp.getData().getId())
                    .setSwimlane(resp.getData().getSwimlane())
                    .setShowName(resp.getData().getShowName())
                    .setStatus(CaseCompleterEnums.TASK_START)
                    .setPlugin(pluginsJson == null || pluginsJson.isEmpty() || "null".equals(pluginsJson) ? "[]" : pluginsJson)
                    .setAppkeyList(appkeyListJson == null|| appkeyListJson.isEmpty() || "null".equals(appkeyListJson) ? "[]" : appkeyListJson);

            LOGGER.info("创建录制任务信息：{}",caseCompleterTaskInfo);

            caseCompleterTaskInfoMapper.insert(caseCompleterTaskInfo);
            //重启任务逻辑-弃用
//            if(Objects.isNull(caseCompleterScanSwimlaneInfo.getTaskId())){
//                //录制任务不存在
//                caseCompleterTaskInfoMapper.insert(caseCompleterTaskInfo);
//            }else {
//                UpdateWrapper<CaseCompleterTaskInfoPO> updateWrapper = new UpdateWrapper<>();
//                updateWrapper.eq("task_id",caseCompleterTaskInfo.getTaskId());
//                updateWrapper.eq("swimlane",caseCompleterTaskInfo.getSwimlane());
//                caseCompleterTaskInfoMapper.update(caseCompleterTaskInfo,updateWrapper);
//            }
            createBranch(CaseCompleterEnums.BRANCH_TEST,caseCompleterScanSwimlaneInfo.getAppkey(), resp.getData().getId() );

            caseCompleterScanSwimlaneInfo.setTaskId(resp.getData().getId());
            caseCompleterScanSwimlaneInfo.setStatus(CaseCompleterEnums.SWIMLANE_RECORDING);
        }else{
            //失败
            caseCompleterScanSwimlaneInfo.setRetryCount(caseCompleterScanSwimlaneInfo.getRetryCount()+1);
        }

        caseCompleterScanSwimlaneInfo.setUpdateTime(getFormatDate());
        caseCompleterScanSwimlaneInfoMapper.updateById(caseCompleterScanSwimlaneInfo);
    }


    private TestXTaskInfoDTO getTestXTaskInfoDTO(CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo){
        return new TestXTaskInfoDTO()
                //如果录制任务id不为null 那么就调用重启录制任务接口，复用原来的录制任务
//                .setId(caseCompleterScanSwimlaneInfo.getTaskId())
                .setRecordType(RecordType.SWIMLANE_RECORD)
                //TODO 任务命名规则待定
                .setShowName("CaseCompleterTask")
                .setAppkeyList(Collections.singletonList(caseCompleterScanSwimlaneInfo.getAppkey()))
                .setEnv(EnvType.TEST)
                .setSwimlane(caseCompleterScanSwimlaneInfo.getSwimlane())
                .setUserName(user)
                .setTeamMemberList(Collections.singletonList(user));
    }


    private void handleResponse(HttpURLConnection connection) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            LOGGER.warn(response.toString());
            CargoResponseDTO cargoResponseDTO = JSON.parseObject(response.toString(), CargoResponseDTO.class);
            if (cargoResponseDTO != null && "success".equals(cargoResponseDTO.getStatus())) {
                processCargoResponseData(cargoResponseDTO.getData());
            }
        }
    }
    private void processCargoResponseData(List<CargoResponseDTO.CargoResponseData> cargoResponseDataList) {
        for (CargoResponseDTO.CargoResponseData cargoResponseData : cargoResponseDataList) {
            CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo = getCaseCompleterScanSwimlaneInfoPO(cargoResponseData);
            LOGGER.info("扫描泳道信息:{}",caseCompleterScanSwimlaneInfo);
            if ("deploy".equals(cargoResponseData.getMessage().getAction())) {
                handleDeployAction(caseCompleterScanSwimlaneInfo);
            } else if ("delete".equals(cargoResponseData.getMessage().getAction())) {
                handleDeleteAction(caseCompleterScanSwimlaneInfo);
            }
        }
    }

    private void handleDeployAction(CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo) {
        LambdaQueryWrapper<CaseCompleterScanSwimlaneInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getAppkey, caseCompleterScanSwimlaneInfo.getAppkey());
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getSwimlane, caseCompleterScanSwimlaneInfo.getSwimlane());
        queryWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsDeleted,0);

        CaseCompleterScanSwimlaneInfoPO existingSwimlaneInfo = caseCompleterScanSwimlaneInfoMapper.selectOne(queryWrapper);
        if (Objects.isNull(existingSwimlaneInfo)) {
            caseCompleterScanSwimlaneInfoMapper.insert(caseCompleterScanSwimlaneInfo);
        } else if (CaseCompleterEnums.SWIMLANE_COMPLETED.equals(existingSwimlaneInfo.getStatus()) || CaseCompleterEnums.SWIMLANE_FAILED.equals(existingSwimlaneInfo.getStatus())){
            existingSwimlaneInfo
                    .setIsActive(1)
                    .setStatus(CaseCompleterEnums.SWIMLANE_PENDING)
                    .setRetryCount(0)
                    .setUpdateTime(getFormatDate());
            caseCompleterScanSwimlaneInfoMapper.updateById(existingSwimlaneInfo);
        }
    }

    public void handleDeleteAction(CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo) {
        LambdaUpdateWrapper<CaseCompleterScanSwimlaneInfoPO> updateWrapper = new UpdateWrapper<CaseCompleterScanSwimlaneInfoPO>().lambda();
        updateWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getAppkey, caseCompleterScanSwimlaneInfo.getAppkey());
        updateWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getSwimlane, caseCompleterScanSwimlaneInfo.getSwimlane());
        updateWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsActive,1);
        updateWrapper.eq(CaseCompleterScanSwimlaneInfoPO::getIsDeleted,0);
        updateWrapper.set(CaseCompleterScanSwimlaneInfoPO::getUpdateTime,getFormatDate());
        updateWrapper.set(CaseCompleterScanSwimlaneInfoPO::getIsActive,0);
        caseCompleterScanSwimlaneInfoMapper.update(new CaseCompleterScanSwimlaneInfoPO(), updateWrapper);
    }

    private static CaseCompleterScanSwimlaneInfoPO getCaseCompleterScanSwimlaneInfoPO(CargoResponseDTO.CargoResponseData cargoResponseDTOS) {
        CaseCompleterScanSwimlaneInfoPO caseCompleterScanSwimlaneInfo = new CaseCompleterScanSwimlaneInfoPO()
                .setSwimlane(cargoResponseDTOS.getMessage().getSwimlane())
                .setEnv(cargoResponseDTOS.getEnv())
                .setIsActive(1)
                .setStatus(CaseCompleterEnums.SWIMLANE_PENDING)
                .setCreateTime(getFormatDate())
                .setUpdateTime(getFormatDate())
                .setAppkey(cargoResponseDTOS.getMessage().getAppkey());

        //主干泳道处理
        if(caseCompleterScanSwimlaneInfo.getSwimlane().isEmpty()){
            caseCompleterScanSwimlaneInfo.setSwimlane("主干泳道");
            caseCompleterScanSwimlaneInfo.setIsActive(0);
        }
        return caseCompleterScanSwimlaneInfo;
    }

    private void handleException(Exception e) {
        //TODO 添加详细日志
        e.printStackTrace();
    }

    private static Timestamp getFormatDate(){
        LocalDateTime now = LocalDateTime.now();
        return  Timestamp.valueOf(now);
    }

    public HttpURLConnection createHttpURLConnection(String appkey) throws IOException {
        URL url = new URL("http://api.cargo.sankuai.com/stack?type=op_history&appkey="+appkey);

        // 打开连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法
        connection.setRequestMethod("GET");
        return connection;
    }

}
