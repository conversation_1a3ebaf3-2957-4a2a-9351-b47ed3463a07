package com.sankuai.mdp.compass.getAPK.controller;

import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.getAPK.service.IGetTargetBranchService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by dongheng on 2020/10/10
 */
@RestController
@RequestMapping("compass/api/getBranch")
public class GetTargetBranchController extends BaseController {
    @Autowired
    IGetTargetBranchService getTargetBranchService;

    @GetMapping("/getTargetBranches")
    public String getBranch(@RequestParam(value = "projectType") String projectType, @RequestParam(required = false,defaultValue = "default")String  processType){
        int process = 0;
        if (processType.equals("default")){
            process = 5;
        }else {
            process = Integer.parseInt(processType);
        }
        return getTargetBranchService.getBranch(projectType,process).toString();
    }
}
