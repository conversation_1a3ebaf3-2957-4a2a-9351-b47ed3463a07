package com.sankuai.mdp.compass.getAPK.Utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import com.sankuai.mdp.compass.getAPK.service.IGetTargetBranchService;
import com.sankuai.mdp.compass.getAPK.service.impl.GetTargetBranchServiceImpl;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;

/**
 * Created by dongheng on 2020/10/23
 */
public class BuildParamsUtil {
    IGetTargetBranchService getTargetBranchService = new GetTargetBranchServiceImpl();

    //根据currentBranch，更新分支
    private void updateBranch(ApkInfo apkInfo){
        String projectType="";
        if ("Android".equals(apkInfo.getOs())){
            projectType="AIMT";
        }else if("iOS".equals(apkInfo.getOs())){
            projectType="IPHONE";
        }
        JsonObject branch= getTargetBranchService.getBranch(projectType);
        JsonArray data = branch.get("data").getAsJsonArray();
        //1代表取下一个阶段的分支并且同时有下一个阶段的分支的话，取第下一个阶段的分支，否则取当前分支
        if(null!=data) {
            if(data.size()>1){
                int type = apkInfo.getCurrentBranch();
                apkInfo.setBranch(compareBranch(data,type));
            }else {
                apkInfo.setBranch(data.get(0).getAsString());
            }
        }
    }
    private String urlEncodeUtil(String url) throws IOException {
        return URLEncoder.encode(url, "UTF-8");
    }

    public String compareBranch(JsonArray data, int type){
        String branch0 = data.get(0).getAsString();
        String branch1 = data.get(1).getAsString();
        String version0 = "";
        String version1 = "";
        for (int i = 0; i < branch0.length(); i++) {
            if (Character.isDigit(branch0.charAt(i))) {
                version0 += branch0.charAt(i);
            }
        }
        for (int i = 0; i < branch1.length(); i++) {
            if (Character.isDigit(branch1.charAt(i))) {
                version1 += branch1.charAt(i);
            }
        }
        if (type == 1){
            return Integer.valueOf(version0)  < Integer.valueOf(version1) ? branch0 : branch1;
        }else {
            return Integer.valueOf(version0)  < Integer.valueOf(version1) ? branch1 : branch0;
        }
    }


    public String buildParams(ApkInfo apkInfo) throws IOException {
        StringBuilder params = new StringBuilder();
        if (null!=apkInfo.getOs()&&!apkInfo.getOs().isEmpty()){
            params.append("os="+urlEncodeUtil(apkInfo.getOs()));
            params.append("&");
        }
        if (null!=apkInfo.getAppName()&&!apkInfo.getAppName().isEmpty()){
            params.append("appName="+urlEncodeUtil(apkInfo.getAppName()));
            params.append("&");
        }
        if (null!=apkInfo.getComponentName()&&!apkInfo.getComponentName().isEmpty()){
            params.append("componentName="+urlEncodeUtil(apkInfo.getComponentName()));
            params.append("&");
        }
        if (null!=apkInfo.getBuildTypeName()&&!apkInfo.getBuildTypeName().isEmpty()){
            params.append("buildTypeName="+urlEncodeUtil(apkInfo.getBuildTypeName()));
            params.append("&");
        }else {
            //Android默认分组：Release 打包，iOS默认分组：组件集成
            if ("Android".equals(apkInfo.getOs())){
                apkInfo.setBuildTypeName("Release 打包");
                params.append("buildTypeName="+urlEncodeUtil(apkInfo.getBuildTypeName()));
            }else if("iOS".equals(apkInfo.getOs())){
                apkInfo.setBuildTypeName("组件集成打包");
                params.append("buildTypeName="+urlEncodeUtil(apkInfo.getBuildTypeName()));
            }
            params.append("&");
        }
        if (null!=apkInfo.getBuildNum()&&!apkInfo.getBuildNum().isEmpty()){
            params.append("buildNum="+urlEncodeUtil(apkInfo.getBuildNum()));
            params.append("&");
        }
        if(null!=apkInfo.getCurrentBranch()) {
            //更新当前分支
            updateBranch(apkInfo);
        }
        if (null!=apkInfo.getBranch()&&!apkInfo.getBranch().isEmpty()){
            params.append("branch="+urlEncodeUtil(apkInfo.getBranch()));
            params.append("&");
        }
        if (null!=apkInfo.getVersion()&&!apkInfo.getVersion().isEmpty()){
            params.append("version="+urlEncodeUtil(apkInfo.getVersion()));
            params.append("&");
        }
        if (null!=apkInfo.getStatus()&&!apkInfo.getStatus().isEmpty()){
            params.append("status="+urlEncodeUtil(apkInfo.getStatus()));
            params.append("&");
        }else {
            params.append("status="+urlEncodeUtil("成功"));
            params.append("&");
        }
        if (null!=apkInfo.getMisId()&&!apkInfo.getMisId().isEmpty()){
            params.append("misId="+urlEncodeUtil(apkInfo.getMisId()));
            params.append("&");
        }
        if (null!=apkInfo.getStartTime()&&!apkInfo.getStartTime().isEmpty()){
            params.append("startTime="+urlEncodeUtil(apkInfo.getStartTime()));
            params.append("&");
        }
        if (null!=apkInfo.getEndTime()&&!apkInfo.getEndTime().isEmpty()){
            params.append("endTime="+urlEncodeUtil(apkInfo.getEndTime()));
            params.append("&");
        }
        if (null!=apkInfo.getPageNum()&&!apkInfo.getPageNum().isEmpty()){
            params.append("pageNum="+urlEncodeUtil(apkInfo.getPageNum()));
            params.append("&");
        }else {
            apkInfo.setPageNum("0");
            params.append("pageNum="+urlEncodeUtil("0"));
            params.append("&");
        }
        if (null!=apkInfo.getPageSize()&&!apkInfo.getPageSize().isEmpty()){
            params.append("pageSize="+urlEncodeUtil(apkInfo.getPageSize()));
            params.append("&");
        }else {
            apkInfo.setPageSize("20");
            params.append("pageSize="+urlEncodeUtil(apkInfo.getPageSize()));
            params.append("&");
        }
        return params.toString();
    }
}
