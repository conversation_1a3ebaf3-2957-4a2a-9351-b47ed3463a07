package com.sankuai.mdp.compass.getAPK.service.impl;

import com.dianping.lion.client.util.Json;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.HttpUtil;
import com.sankuai.mdp.compass.common.utils.SigmaUtil;
import com.sankuai.mdp.compass.common.utils.StringUtil;
import com.sankuai.mdp.compass.getAPK.service.IGetTargetBranchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Created by dongheng on 2020/10/10
 */
@Slf4j
@Service
public class GetTargetBranchServiceImpl implements IGetTargetBranchService {
    public String url="http://gulfstream.dataapp.dev.sankuai.com/api/getRunningEventByProject";

    public JsonObject getBranch(String projectType){
        String platform = "";
        if (projectType.equals("AIMT")){
            platform = "Android";
        }else if (projectType.equals("IPHONE")){
            platform = "iOS";
        }else {
            platform = projectType;
        }
        String re = "";
        for (int PROCESS = 5; PROCESS < 10; PROCESS++) {
            re = new SigmaUtil().getTargetBranches(PROCESS,platform);
            if (!StringUtil.isBlank(re)) break;
        }
        JsonArray branchResult = new JsonArray();
        branchResult.add(re);
        JsonObject dataResult = new JsonObject();
        dataResult.add("data", branchResult);
        return dataResult;
    }

    public JsonObject getBranch(String projectType, int processType){

        String platform = "";
        if (projectType.equals("AIMT")){
            platform = "Android";
        }else if (projectType.equals("IPHONE")){
            platform = "iOS";
        }else {
            platform = projectType;
        }
        String  re = new SigmaUtil().getTargetBranches(processType,platform);
        JsonArray branchResult = new JsonArray();
        branchResult.add(re);
        JsonObject dataResult = new JsonObject();
        dataResult.add("data", branchResult);
        return dataResult;
//        dataResult.add("data", branchResult);
//        String result = HttpUtil.vGet(url + "?project=" + projectType, "Basic YWRtaW46cGFzcw==");
//        JsonObject jsonResult = new JsonParser().parse(result).getAsJsonObject();
//        JsonArray data = jsonResult.get("data").getAsJsonArray();
//        JsonArray branchResult = new JsonArray();
//        for (int i=0;i<data.size();i++){
//            JsonObject jsonObject = data.get(i).getAsJsonObject();
//            if (!jsonObject.get("branch").getAsString().equals("develop")){
//                if(jsonObject.get("type").getAsJsonObject().get("id").getAsInt()<2) {
//                    branchResult.add(jsonObject.get("branch").getAsString());
//                }
//            }
//        }
//        JsonObject dataResult = new JsonObject();
//        dataResult.add("data", branchResult);
//        return dataResult;
    }
}
