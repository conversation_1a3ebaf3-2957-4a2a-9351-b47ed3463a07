package com.sankuai.mdp.compass.getAPK.service;

import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;

/**
 * Created by dongheng on 2020/9/10
 */
public interface IGetApkDataService {
    JsonObject getApkUrl(ApkInfo apkInfo);
    JsonObject getSourceIPA(ApkInfo apkInfo);
    String getFormalApk(String os,String currentVersion);
    String getPlist(String downloadUrl);
    String getLatestFullReleaseApkUrlFromSigma(String os, String style) throws Exception;
    String getLatestRegressionTestApkUrlFromSigma(String os, String version) throws Exception;
}
