package com.sankuai.mdp.compass.getAPK.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.HttpUtil;
import com.sankuai.mdp.compass.getAPK.Utils.BuildParamsUtil;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import com.sankuai.mdp.compass.getAPK.service.IGetApkDataService;
import com.sankuai.mdp.compass.getAPK.service.IGetTargetBranchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by dongheng on 2020/9/10
 */
@Slf4j
@Service
public class GetApkDataServiceImpl implements IGetApkDataService {
    public String getApkListUrl = "http://hpx.sankuai.com/api/open/getTaskList";
    public String getApkUrl = "http://hpx.sankuai.com/api/open/getAppPackageInfo";
    public String getFormalApkInfos = "http://10.178.141.63:8080/indicator/api/codeDiff/getFormalApkInfos";
    private static final String SECRET_ID = "com.sankuai.sigma.compass";
    private static final String SECRET_KEY = "e3b68c6eb61d157fe6a2f6f789e7c150";
    private static final String BASE_URL = "http://sigma.sankuai.com";
    private static final String ANDROID_ENDPOINT = "/api/open/package/list/1";
    private static final String IOS_ENDPOINT = "/api/open/package/list/2";
    private static final String ANDROID_LATEST_FULL_RELEASE = "/api/open/fullRelease/latestPackage/1";
    private static final String IOS_LATEST_FULL_RELEASE = "/api/open/fullRelease/latestPackage/2";

    @Autowired
    IGetTargetBranchService getTargetBranchService;

    @Override
    public JsonObject getApkUrl(ApkInfo apkInfo) {
        String arch64 = "";
        if (apkInfo.getArch64()!= null){
            arch64 = apkInfo.getArch64();
        }
        log.info("apkInfo :" + apkInfo.toString());
        JsonObject result = new JsonObject();
        BuildParamsUtil buildParamsUtil = new BuildParamsUtil();
        //os和appName是必传参数
        if (apkInfo.getOs() != null && apkInfo.getAppName() != null) {
            String resultUrl = "";
            String versionName = "";
            try {
                String params = buildParamsUtil.buildParams(apkInfo);
                String param = "";
                //将末尾的“&”去除掉
                if (null != params && !params.isEmpty()) {
                    param = params.substring(0, params.length() - 1);
                }
                log.info("param: " + param);
                //获取整个apkUrlList
                String apkListData = HttpUtil.vGet(getApkListUrl + "?" + param, null);
                JsonObject jsonApkListData = new JsonParser().parse(apkListData).getAsJsonObject();
                if (jsonApkListData.get("data")==null){
                    return result;
                }
                JsonArray data = jsonApkListData.get("data").getAsJsonArray();
                String flavorDefault = null;
                String channelDefault = null;
                if ("Android".equals(apkInfo.getOs())) {
                    if ("Release 打包".equals(apkInfo.getBuildTypeName())) {
                        flavorDefault = "meituanInternal";
                        channelDefault = "meituaninternaltest";
                    } else if ("功能测试包-Release包".equals(apkInfo.getBuildTypeName())) {
                        channelDefault = "meituaninternaltest";
                    } else if ("Debug 打包".equals(apkInfo.getBuildTypeName())) {
                        flavorDefault = "speedCompilation";
                    } else if ("全量打包".equals(apkInfo.getBuildTypeName())) {
                        flavorDefault = "meituan";
                        channelDefault = "meituan";
                    }
                }
                if (null == apkInfo.getFlavor() || apkInfo.getFlavor().isEmpty()) {
                    apkInfo.setFlavor(flavorDefault);
                }
                if (null == apkInfo.getChannel() || apkInfo.getChannel().isEmpty()) {
                    apkInfo.setChannel(channelDefault);
                }
                JsonObject items = null;
                boolean channelAndFlavor = false;
                //从列表里筛选合适的测试包，从第一个开始选
                for (int i = 0; i < Integer.valueOf(apkInfo.getPageSize()); i++) {
                    //items是指每条测试包
                    items = data.get(i).getAsJsonObject();
                    if (null != items && items.size() != 0) {
                        JsonArray paramInfos = items.get("paramInfo").getAsJsonArray();
                        channelAndFlavor = isChannelAndFlavor(apkInfo.getChannel(), apkInfo.getFlavor(), paramInfos, apkInfo);
                        versionName = items.get("versionName").getAsString();
                    }
                    if (channelAndFlavor) {
                        break;
                    }
                }
//              item也不为空，并且符合channel与flavor值
                if (null != items && channelAndFlavor) {
                    String buildNumber = items.get("buildNum").getAsString();
                    String os = apkInfo.getOs();
                    String appName = apkInfo.getAppName();
                    resultUrl = specificApkUrl(os, appName, buildNumber, apkInfo, arch64);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            result.addProperty("appUrl", resultUrl);
            result.addProperty("versionName",versionName);
        }
        return result;
    }

    //判断是否符合channel与flavor的值
    private boolean isChannelAndFlavor(String channelDefault, String flavorDefault, JsonArray paramInfos, ApkInfo apkInfo) {
        boolean flavor = false;
        boolean channel = false;
        for (int j = 0; j < paramInfos.size(); j++) {
            JsonObject paramInfo = paramInfos.get(j).getAsJsonObject();
            log.info("paramInfo :" + paramInfo);
            //channel和flavor都不为空或者null
            if (null != apkInfo.getChannel() && !apkInfo.getChannel().isEmpty()
                    && null != apkInfo.getFlavor() && !apkInfo.getFlavor().isEmpty()) {
                if (paramInfo.get("paramKey").getAsString().equals("flavor")) {
                    if (paramInfo.get("paramValue").getAsString().equals(flavorDefault)) {
                        flavor = true;
                    }
                } else if (paramInfo.get("paramKey").getAsString().equals("channel")) {
                    if (paramInfo.get("paramValue").getAsString().equals(channelDefault)) {
                        channel = true;
                    }
                }
            }
            //flavor为null或者空
            else if (null != apkInfo.getChannel() && !apkInfo.getChannel().isEmpty()
                    && (null == apkInfo.getFlavor() || !apkInfo.getFlavor().isEmpty())) {
                flavor = true;
                if (paramInfo.get("paramKey").getAsString().equals("channel")) {
                    if (paramInfo.get("paramValue").getAsString().equals(channelDefault)) {
                        channel = true;
                    }
                }
            }
            //channel为null或者空
            else if ((null == apkInfo.getChannel() || apkInfo.getChannel().isEmpty())
                    && null != apkInfo.getFlavor() && !apkInfo.getFlavor().isEmpty()) {
                if (paramInfo.get("paramKey").getAsString().equals("flavor")) {
                    if (paramInfo.get("paramValue").getAsString().equals(flavorDefault)) {
                        flavor = true;
                    }
                }
                channel = true;
            }
            //channel与flavor都为null或者空
            else {
                return true;
            }
            if (channel && flavor) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取同源内测包
     * @param apkInfo
     * @return
     */
    @Override
    public JsonObject getSourceIPA(ApkInfo apkInfo) {
        log.info("apkInfo :" + apkInfo.toString());
        JsonObject result = new JsonObject();
        if (apkInfo.getOs() != null && apkInfo.getBuildNum() != null && apkInfo.getAppName() != null) {
            try {
                String param = "os=" + apkInfo.getOs() + "&" + "appName=" + apkInfo.getAppName() + "&" + "buildNumber=" + apkInfo.getBuildNum();
                String apkListData = HttpUtil.vGet(getApkUrl + "?" + param, null);
                JsonObject jsonApkListData = new JsonParser().parse(apkListData).getAsJsonObject();
//                log.info("前一次请求结果: "+jsonApkListData);
                if (jsonApkListData.has("data") && jsonApkListData.get("data") != null) {
                    JsonObject data = jsonApkListData.get("data").getAsJsonObject();
                    if (data.has("sourcePackageInfo") && data.get("sourcePackageInfo") != null) {
                        JsonArray sourcePackageInfos = data.get("sourcePackageInfo").getAsJsonArray();
                        JsonObject sourcePackageInfo = sourcePackageInfos.get(0).getAsJsonObject();
                        String buildNum = sourcePackageInfo.get("buildNum").getAsString();
//                        log.info("buildNum:"+buildNum);
                        apkInfo.setBuildNum(buildNum);
                    }
                } else {
                    return null;
                }
                String appUrl =specificApkUrl(apkInfo.getOs(), apkInfo.getAppName(), apkInfo.getBuildNum(), apkInfo, "");
                result.addProperty("appUrl", appUrl);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 获取全量包下载Url
     * @param os
     * @param currentVersion
     * @return
     */
    @Override
    public String getFormalApk(String os, String currentVersion) {
        JsonObject result = new JsonObject();
        String buildNumber = getLastLatestFormalApkBuildNumber(os,currentVersion);
        if (buildNumber.equals("-1")){
            return result.toString();
        }
        StringBuilder appName = new StringBuilder();
        if (os.equals("Android")){
            appName.append("com.sankuai.meituan");
        }else if (os.equals("iOS")){
            appName.append("imeituan");
        }
        String apkUrl = specificApkUrl(os, appName.toString(), buildNumber, null, "");
        result.addProperty("appUrl", apkUrl);
        return result.toString();
    }

    /**
     * 获取上个版本全量包的build号
     * @param os
     * @param currentVersion
     * @return
     */
    public String getLastLatestFormalApkBuildNumber(String os,String currentVersion) {
        String formalApkInfoList = HttpUtil.vGet(getFormalApkInfos+"?os="+os);
        log.info("历史全量版本有："+formalApkInfoList);
        JsonArray formalApkInfos = new JsonParser().parse(formalApkInfoList).getAsJsonArray();
        String[] currentVersionArray = currentVersion.split("\\.");
        String buildNumber = "-1";
        for (JsonElement formalApkInfo : formalApkInfos) {
            JsonObject asJson = formalApkInfo.getAsJsonObject();
            String lastVersion = asJson.get("formalVersion").getAsString();
            String[] lastVersionArray = lastVersion.split("\\.");
            boolean rightVersion = judgeLastVerison(currentVersionArray, lastVersionArray);
            if (rightVersion&&asJson.get("buildNumber")!=null) {
                buildNumber = asJson.get("buildNumber").getAsString();
                break;
            }
        }
        return buildNumber;
    }

    /**
     * 判断是不是上个全量版本
     */
    private boolean judgeLastVerison(String[] currentVersionArray, String[] lastVersionArray) {
        if (Integer.valueOf(currentVersionArray[0])>Integer.valueOf(lastVersionArray[0])) {
            return true;
        }
        if(Integer.valueOf(currentVersionArray[0])==Integer.valueOf(lastVersionArray[0])){
            if (Integer.valueOf(currentVersionArray[1])>Integer.valueOf(lastVersionArray[1])) {
                return true;
            }
            if (Integer.valueOf(currentVersionArray[1])==Integer.valueOf(lastVersionArray[1])){
                if (currentVersionArray[2].startsWith("4")){
                    Integer diff = Integer.valueOf(currentVersionArray[2]) - Integer.valueOf(lastVersionArray[2]);
                    if (diff>0&&diff.toString().length() == 3) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获得具体的测试包信息
     * @param os
     * @param appName
     * @param buildNumber
     * @param apkInfo
     * @return
     */
    public String specificApkUrl(String os, String appName, String buildNumber,ApkInfo apkInfo, String arch64) {
        String resultUrl = "";
        String appPackageInfoParam = "os=" + os + "&" + "appName=" + appName + "&" + "buildNumber=" + buildNumber;
        log.info("appPackageInfoParam: " + appPackageInfoParam);
        String apkData = HttpUtil.vGet(getApkUrl + "?" + appPackageInfoParam, null);
        if (null != apkData && !apkData.isEmpty()) {
            JsonObject jsonApkData = new JsonParser().parse(apkData).getAsJsonObject();
            JsonObject jsonData = jsonApkData.get("data").getAsJsonObject();
            if (null != jsonData) {
                JsonObject artifactInfo = jsonData.get("artifactInfo").getAsJsonObject();
                if (null != artifactInfo) {
                    if (os.equals("Android")) {
                        if (artifactInfo.has("archives_download_url")) {
                            JsonObject archivesDownloadUrl = artifactInfo.get("archives_download_url").getAsJsonObject();
                            if (arch64 != ""){
                                try{
                                    if (null != archivesDownloadUrl.get("apk_aarch64")){
                                        resultUrl = archivesDownloadUrl.get("apk_aarch64").getAsString();
                                    }
                                }catch (Exception e){
                                    resultUrl = "";
                                }
                            }else{
                                resultUrl = archivesDownloadUrl.get("apk").getAsString();
                            }

                        } else {
                            if (artifactInfo.has("apk")) {
                                resultUrl = artifactInfo.get("apk").getAsString();
                            } else if (artifactInfo.has("app_download_url")) {
                                resultUrl = artifactInfo.get("app_download_url").getAsString();
                            }
                        }
                    } else if (os.equals("iOS")) {
                        //virtual值为1或者build类型名中包含「模拟」，取模拟器包，app_path是包链接
                        if (apkInfo!=null&&(null != apkInfo.getVirtual() && apkInfo.getVirtual() == 1) || apkInfo.getBuildTypeName().contains("模拟")) {
                            resultUrl = artifactInfo.get("app_path").getAsString();
                        } else {
                            resultUrl = artifactInfo.get("app_download_url").getAsString();
                        }
                    }
                }
            }
        }
        return resultUrl;
    }

    public String getPlist(String downloadUrl) {
        String param = "";
        if(downloadUrl == null || downloadUrl.isEmpty()){
            return null;
        }
        if (downloadUrl.contains(".apk")){
            return downloadUrl;
        }
        else if (downloadUrl.contains(".ipa")){
            // 去掉 URL 前缀部分
            String fileName = downloadUrl.substring(downloadUrl.lastIndexOf("/") + 1); // imeituan-12.28.200-246151.20241227160210.ipa
            // 按 "-" 分割文件名
            String[] parts = fileName.split("-");
            // 提取需要的部分
            String appName = parts[0];              // imeituan
            String version = parts[1];             // 12.28.200
            String buildNum = parts[2].split("\\.")[0]; // 246151 (排除时间戳部分)
            param += "os=" + "iOS" + "&";
            param += "appName=" + appName + "&";
            param += "version=" + version + "&";
            param += "buildName=" + buildNum;
            try{
                String apkListData = HttpUtil.vGet(getApkListUrl + "?" + param, null);
                if(apkListData == null || apkListData.isEmpty()){
                    log.error("apkListData is null");
                    String plist = "";
                    return plist;
                }
                JSONObject hpxList = JSON.parseObject(apkListData);
                JSONArray stepArray = hpxList.getJSONArray("data").getJSONObject(0).getJSONArray("stepInfo");
                if(hpxList.getJSONArray("data").size() > 1){
                    param = param.replace("buildName=", "buildNum=");  // 替换 buildName 为 buildNum
                    apkListData = HttpUtil.vGet(getApkListUrl + "?" + param, null);
                    hpxList = JSON.parseObject(apkListData);
                    stepArray = hpxList.getJSONArray("data").getJSONObject(0).getJSONArray("stepInfo");
                }
                String plist = "";
                for (int i = 0; i < stepArray.size(); i++) {
                    String artifact = stepArray.getJSONObject(i).getString("artifact");
                    if (artifact != null && artifact.contains("安装地址")) {
                        plist = JSONArray.parseArray(artifact).getJSONObject(0).getString("value");
                        break;
                    }
                }
                return plist;
            }
            catch (Exception e){
                log.error("An error occurred", e);
                String plist = "";
                return plist;
            }

        }
        else{
            return "安装包下载路径不符合要求，必须以 ipa 或者 apk 结尾";
        }
    }

    /**
     * 构建鉴权头信息
     *
     * @param uri    请求的 URI（路径部分）
     * @param method HTTP 方法
     * @return 包含 Date、Authorization 和 Content-Type 的头信息 Map
     * @throws Exception 处理过程中可能抛出的异常
     */
    private static Map<String, String> buildHeaders(String uri, String method) throws Exception {
        // 生成当前 GMT 时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String gmtTime = dateFormat.format(new Date());

        // 构建待签名的字符串
        String stringToSign = method + " " + uri + "\n" + gmtTime;

        // 计算 HMAC-SHA1 签名
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(secretKeySpec);
        byte[] rawHmac = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));

        // Base64 编码
        String signature = Base64.getEncoder().encodeToString(rawHmac).replaceAll("\\r|\\n", "");

        // 构建 Authorization 头
        String authorization = "MWS " + SECRET_ID + ":" + signature;

        // 构建头信息 Map
        Map<String, String> headers = new HashMap<>();
        headers.put("Date", gmtTime);
        headers.put("Authorization", authorization);
        headers.put("Content-Type", "application/json;charset=UTF-8");

        return headers;
    }

    /**
     * 构建带有查询参数的完整 URL
     *
     * @param baseUrl 基础 URL
     * @param params  查询参数键值对
     * @return 完整的 URL
     * @throws Exception 处理过程中可能抛出的异常
     */
    private static String buildUrlWithParams(String baseUrl, Map<String, String> params) throws Exception {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");
        List<String> paramList = new ArrayList<>();

        for (Map.Entry<String, String> entry : params.entrySet()) {
            String encodedKey = URLEncoder.encode(entry.getKey(), "UTF-8");
            String encodedValue = URLEncoder.encode(entry.getValue(), "UTF-8");
            paramList.add(encodedKey + "=" + encodedValue);
        }

        urlBuilder.append(String.join("&", paramList));
        return urlBuilder.toString();
    }

    /**
     * 发送带有头信息的 GET 请求，并解析响应为 JSONObject
     *
     * @param urlStr URL 字符串
     * @param headers 头信息 Map
     * @return 响应的 JSONObject
     * @throws Exception 处理过程中可能抛出的异常
     */
    private static JSONObject sendGetRequest(String urlStr, Map<String, String> headers) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            connection.setRequestMethod("GET");

            // 添加头信息
            for (Map.Entry<String, String> headerEntry : headers.entrySet()) {
                connection.setRequestProperty(headerEntry.getKey(), headerEntry.getValue());
            }

            // 设置连接和读取超时（可根据需要调整）
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            // 获取响应码
            int responseCode = connection.getResponseCode();

            // 读取响应
            BufferedReader reader;
            if (responseCode == HttpURLConnection.HTTP_OK) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8));
            }

            StringBuilder responseBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                responseBuilder.append(line);
            }

            reader.close();

            String responseBody = responseBuilder.toString();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 解析响应为 JSONObject
                return JSON.parseObject(responseBody);
            } else {
                // 处理错误响应
                throw new RuntimeException("请求失败，状态码：" + responseCode + ", 响应内容：" + responseBody);
            }

        } finally {
            connection.disconnect();
        }
    }

    // 具体文档见: https://km.sankuai.com/collabpage/2684832637
    public String getLatestFullReleaseApkUrlFromSigma(String os, String style) throws Exception {
        Map<String, String> headers = null;
        String urlWithParams = "";
        String targetUrl = "";
        int flag = 0;
        if(Objects.equals(os, "iOS") || Objects.equals(os, "IOS")){
            // 构建鉴权头
            headers = buildHeaders(IOS_LATEST_FULL_RELEASE, "GET");
            Map<String, String> params = new LinkedHashMap<>();
            params.put("alias", style);
            params.put("pipeLineBuildResult", "全量发布");
            // 构建完整的 URL
            urlWithParams = buildUrlWithParams(BASE_URL + IOS_LATEST_FULL_RELEASE, params);
        }
        else if(Objects.equals(os, "Android") || Objects.equals(os, "android")){
            flag = 1;
            headers = buildHeaders(ANDROID_LATEST_FULL_RELEASE, "GET");
            Map<String, String> params = new LinkedHashMap<>();
            params.put("alias", style);
            params.put("pipeLineBuildResult", "全量发布");
            // 构建完整的 URL
            urlWithParams = buildUrlWithParams(BASE_URL + ANDROID_LATEST_FULL_RELEASE, params);
        }
        else{
            return "os 对应字段写错，请检查";
        }

        // 发送 GET 请求并获取响应
        JSONObject responseJson = sendGetRequest(urlWithParams, headers);
        // 打印响应
        if (responseJson == null || !responseJson.get("code").equals("0000") || responseJson.getJSONObject("data") == null) {
            log.warn("请求 sigma 接口出错！");
            return "sigma 接口返回异常";
        }
        else{
            log.info(responseJson.toJSONString());
            if(flag == 0){
                targetUrl = responseJson.getJSONObject("data").get("packageInfo").toString();
            }
            else {
                targetUrl = responseJson.getJSONObject("data").getJSONObject("artifactInfo")
                        .get("app_download_url_aarch64").toString();
            }
        }
        return targetUrl;
    }

    // 具体文档见: https://km.sankuai.com/collabpage/2684832637
    public String getLatestRegressionTestApkUrlFromSigma(String os, String version) throws Exception {
        Map<String, String> headers = null;
        String urlWithParams = "";
        String targetUrl = "";
        int flag = 0;
        if(Objects.equals(os, "iOS") || Objects.equals(os, "IOS")){
            // 构建鉴权头
            headers = buildHeaders(IOS_ENDPOINT, "GET");
            Map<String, String> params = new LinkedHashMap<>();
            params.put("buildTypes", "回归测试包");
            params.put("version", version);
            params.put("featureType","106");
            // 构建完整的 URL
            urlWithParams = buildUrlWithParams(BASE_URL + IOS_ENDPOINT, params);
        }
        else if(Objects.equals(os, "Android") || Objects.equals(os, "android")){
            flag = 1;
            headers = buildHeaders(ANDROID_ENDPOINT, "GET");
            Map<String, String> params = new LinkedHashMap<>();
            params.put("buildTypes", "回归测试包");
            params.put("version", version);
            params.put("featureType","106");
            // 构建完整的 URL
            urlWithParams = buildUrlWithParams(BASE_URL + ANDROID_ENDPOINT, params);
        }
        else{
            return "os 对应字段写错，请检查";
        }

        // 发送 GET 请求并获取响应
        JSONObject responseJson = sendGetRequest(urlWithParams, headers);
        // 打印响应
        if (responseJson == null || !responseJson.get("code").equals("0000") || responseJson.getJSONObject("data") == null) {
            log.warn("请求 sigma 接口出错！");
            return "sigma 接口返回异常";
        }
        else{
            log.info(responseJson.toJSONString());
            JSONArray ansArray = responseJson.getJSONObject("data").getJSONArray("records");
            if(flag == 0){
                for (int i = 0; i < ansArray.size(); i++) {
                    if (ansArray.getJSONObject(i).getString("releaseTypeList").equals("测试包")) {
                        targetUrl = ansArray.getJSONObject(i).getString("packageInfo");
                        break;
                    }
                }
            }
            else {
                for (int i = 0; i < ansArray.size(); i++) {
                    if (ansArray.getJSONObject(i).getString("releaseTypeList").equals("测试包")) {
                        targetUrl = ansArray.getJSONObject(i).getJSONObject("artifactInfo").get("app_download_url_aarch64").toString();
                        break;
                    }
                }
            }
        }
        return targetUrl;
    }

}
