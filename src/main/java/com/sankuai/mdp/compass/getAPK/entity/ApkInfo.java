package com.sankuai.mdp.compass.getAPK.entity;

import lombok.Data;

/**
 * Created by dongheng on 2020/9/10
 */
@Data
public class ApkInfo {
    public String os;
    public String appName;//包英文名
    public String componentName;
    public String buildTypeName;//分组,比如:Release 打包、Debug 打包
    public String buildNum;
    public String branch;
    public String version;
    public String status;//是否完成
    public String misId;//构建人
    public String startTime;
    public String endTime;
    public String pageNum;
    public String pageSize;
    public String flavor; //
    public Integer virtual;//模拟器打包
    public String channel;//渠道号
    public String apkType;
    public Integer currentBranch = 0;//是否当前测试分支
    public Integer sourceIPA;//是否获取其同源内测包
    public String arch64; //
    public int processType = 0; //


}
