package com.sankuai.mdp.compass.getAPK.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.utils.HpxUtil;
import com.sankuai.mdp.compass.common.utils.HttpUtil;
import com.sankuai.mdp.compass.common.utils.SigmaUtil;
import com.sankuai.mdp.compass.common.utils.StringUtil;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import com.sankuai.mdp.compass.getAPK.service.IGetApkDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by dongheng on 2020/9/10
 */
@RestController
@RequestMapping("compass/api/getApk")
public class GetApkDataController extends BaseController {
    @Autowired
    IGetApkDataService getApkDataService;

    @GetMapping("/getApkUrl")
    public String getApkData(ApkInfo apkInfo) {
        apkInfo.setArch64("1");
        SigmaUtil sigmaUtil = new SigmaUtil();
        int currentBranch = apkInfo.getCurrentBranch();
        String platform = apkInfo.getOs();
        int processType = apkInfo.getProcessType();
        String appName = apkInfo.getAppName();
        if (StringUtil.isBlank(appName) || !appName.equals("youxuan")) {
            appName = "meituan";
        }
        String branch = sigmaUtil.getBranch(currentBranch, platform, processType, appName);
        if (StringUtil.isBlank(appName) || !appName.equals("youxuan")) {
            apkInfo.setCurrentBranch(null);
            apkInfo.setBranch(branch);
            if (null != apkInfo.getSourceIPA() && apkInfo.getSourceIPA() == 1) {
                return getApkDataService.getSourceIPA(apkInfo).toString();
            }
            return getApkDataService.getApkUrl(apkInfo).toString();
        } else {
            //走优选的逻辑
            //直接用hpx方法获取对应分支的包\//appName=iyouxuan&branch=release/6.37.0&buildTypeName=抢鲜包（QA、UI、RD、PM）

            String resultUrl = "";
            String ext = "";
            JsonObject resultJson = new JsonObject();
            try {
                if (platform.equals("Android")) {
                    String response = new HpxUtil().getBranchNewestPackageInfo("aiyouxuan", branch, "抢鲜包（QA、UI、RD、PM）");
                    resultJson = new JsonParser().parse(response).getAsJsonObject();
                    if (!resultJson.get("data").isJsonNull()){
                        resultUrl = resultJson.get("data").getAsJsonObject().get("artifactInfo").getAsJsonObject().get("app_download_url_aarch64").getAsString();
                    }else {
                        ext = resultJson.get("error").toString();
                    }
                } else {
                    String response = new HpxUtil().getBranchNewestPackageInfo("iyouxuan", branch, "抢鲜包（QA、UI、RD、PM）");
                    resultJson = new JsonParser().parse(response).getAsJsonObject();
                    if (!resultJson.get("data").isJsonNull()){
                        resultUrl = resultJson.get("data").getAsJsonObject().get("artifactInfo").getAsJsonObject().get("app_download_url").getAsString();
                    }else {
                        ext = resultJson.get("error").toString();
                    }
                }
            }catch (Exception e){
                resultUrl = "";
                ext=e.toString();

            }
            JsonObject result = new JsonObject();
            result.addProperty("appUrl", resultUrl);
            result.addProperty("versionName", branch);
            if (!StringUtil.isBlank(ext)) result.addProperty("ext",ext);
            return result.toString();
        }
    }

    @GetMapping("/getLastLatestFormalApkUrl")
    public String getLastFormalApkUrl(@RequestParam(value = "os") String os, @RequestParam(value = "currentVersion") String currentVersion) {
        return getApkDataService.getFormalApk(os, currentVersion);
    }

    @GetMapping("/getPlist")
    public String getPlist(String downloadUrl) {
        return getApkDataService.getPlist(downloadUrl);
    }

    @GetMapping("/getLatestFullReleaseApkUrlFromSigma")
    public String getLatestFullReleaseApkUrlFromSigma(String os , String style) throws Exception {
        return getApkDataService.getLatestFullReleaseApkUrlFromSigma(os,style);
    }

    @GetMapping("/getLatestRegressionTestApkUrlFromSigma")
    public String getLatestRegressionTestApkUrlFromSigma(String os , String version) throws Exception {
        return getApkDataService.getLatestRegressionTestApkUrlFromSigma(os, version);
    }
}
