package com.sankuai.mdp.compass.aiTestAgent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.aiTestAgent.entity.AiTestAgent;
import com.sankuai.mdp.compass.aiTestAgent.mapper.AiTestAgentMapper;
import com.sankuai.mdp.compass.aiTestAgent.service.AiTestAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.Calendar;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.HashMap;
import java.util.Set;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.Collections;

/**
 * AiTestAgentService实现类
 * 实现对ai_test_agent表的业务操作
 */
@Service
public class AiTestAgentServiceImpl extends ServiceImpl<AiTestAgentMapper, AiTestAgent> implements AiTestAgentService {

    private static final Logger logger = LoggerFactory.getLogger(AiTestAgentServiceImpl.class);

    @Autowired
    private AiTestAgentMapper baseMapper;

    /**
     * 根据设备ID查询最新的检查记录
     * 
     * @param deviceId 设备ID
     * @return 最新的检查记录，如果不存在则返回null
     */
    @Override
    public AiTestAgent getLatestByDeviceId(String deviceId) {
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestAgent::getDeviceId, deviceId)
                    .orderByDesc(AiTestAgent::getInspectionTime)
                    .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据时间范围查询检查记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的检查记录列表
     */
    @Override
    public List<AiTestAgent> getByTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(startTime != null, AiTestAgent::getInspectionTime, startTime)
                    .le(endTime != null, AiTestAgent::getInspectionTime, endTime)
                    .orderByDesc(AiTestAgent::getInspectionTime);
        return list(queryWrapper);
    }
    
    /**
     * 添加检查记录
     * 
     * @param aiTestAgent 检查记录实体
     * @return 是否添加成功
     */
    @Override
    public boolean addTestAgent(AiTestAgent aiTestAgent) {
        // 设置默认创建时间
        if (aiTestAgent.getCreatedAt() == null) {
            aiTestAgent.setCreatedAt(new Date());
        }
        if (aiTestAgent.getAddTime() == null) {
            aiTestAgent.setAddTime(new Date());
        }
        if (aiTestAgent.getUpdateTime() == null) {
            aiTestAgent.setUpdateTime(new Date());
        }
        return save(aiTestAgent);
    }
    
    /**
     * 根据ID查询检查记录
     * 
     * @param inspectionId 检查记录ID
     * @return 检查记录实体
     */
    @Override
    public AiTestAgent getTestAgentById(Long inspectionId) {
        return getById(inspectionId);
    }
    
    /**
     * 分页查询检查记录
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param excludeLogLevel 要排除的日志级别（可选）
     * @return 分页结果
     */
    @Override
    public IPage<AiTestAgent> pageTestAgents(Page<AiTestAgent> page, String deviceId, String excludeLogLevel) {
        QueryWrapper<AiTestAgent> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_ID", deviceId);
        }
        
        // 如果指定了要排除的日志级别，添加条件
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            queryWrapper.ne("LOG_LEVEL", excludeLogLevel);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc("Created_At");
        
        return baseMapper.selectPage(page, queryWrapper);
    }
    
    /**
     * 保留原有的方法以保持兼容性
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @return 分页结果
     */
    @Override
    public IPage<AiTestAgent> pageTestAgents(Page<AiTestAgent> page, String deviceId) {
        // 调用新方法，默认不排除任何日志级别
        return pageTestAgents(page, deviceId, null);
    }
    
    /**
     * 更新检查记录
     * 
     * @param aiTestAgent 检查记录实体
     * @return 是否更新成功
     */
    @Override
    public boolean updateTestAgent(AiTestAgent aiTestAgent) {
        // 设置更新时间
        aiTestAgent.setUpdateTime(new Date());
        return updateById(aiTestAgent);
    }
    
    /**
     * 根据ID删除检查记录
     * 
     * @param inspectionId 检查记录ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteTestAgent(Long inspectionId) {
        return removeById(inspectionId);
    }
    
    /**
     * 根据设备ID删除检查记录
     * 
     * @param deviceId 设备ID
     * @return 删除的记录数
     */
    @Override
    public int deleteByDeviceId(String deviceId) {
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestAgent::getDeviceId, deviceId);
        return count(queryWrapper) > 0 ? baseMapper.delete(queryWrapper) : 0;
    }
    
    @Override
    public Integer newTestAgent(AiTestAgent aiTestAgent) {
        baseMapper.insert(aiTestAgent);
        return aiTestAgent.getInspectionId().intValue();
    }

    @Override
    public List<Map<String, Object>> getRecentDevices(Date startDate) {
        // 创建查询条件，筛选创建时间大于等于startDate的记录
        QueryWrapper<AiTestAgent> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("Created_At", startDate);
        queryWrapper.orderByDesc("Created_At");
        queryWrapper.select("Device_ID", "MAX(Created_At) as lastInspectionTime");
        queryWrapper.groupBy("Device_ID");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> device : result) {
            // 将数据库字段名转换为驼峰命名
            if (device.containsKey("Device_ID")) {
                device.put("deviceId", device.get("Device_ID"));
                device.remove("Device_ID");
            }
            if (device.containsKey("lastInspectionTime")) {
                device.put("lastInspectionTime", device.get("lastInspectionTime"));
            }
        }
        
        return result;
    }

    /**
     * 获取指定日期之后有检查记录的所有设备，排除指定日志级别
     *
     * @param startDate 开始日期
     * @param excludeLogLevel 要排除的日志级别
     * @return 设备列表，每个设备包含设备ID和最后检查时间
     */
    @Override
    public List<Map<String, Object>> getRecentDevices(Date startDate, String excludeLogLevel) {
        // 创建查询条件，筛选创建时间大于等于startDate的记录，排除指定日志级别
        QueryWrapper<AiTestAgent> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("Created_At", startDate);
        
        // 如果指定了要排除的日志级别，添加条件
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            queryWrapper.ne("LOG_LEVEL", excludeLogLevel);
        }
        
        queryWrapper.orderByDesc("Created_At");
        queryWrapper.select("Device_ID", "MAX(Created_At) as lastInspectionTime");
        queryWrapper.groupBy("Device_ID");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> device : result) {
            // 将数据库字段名转换为驼峰命名
            if (device.containsKey("Device_ID")) {
                device.put("deviceId", device.get("Device_ID"));
                device.remove("Device_ID");
            }
            if (device.containsKey("lastInspectionTime")) {
                device.put("lastInspectionTime", device.get("lastInspectionTime"));
            }
        }
        
        return result;
    }

    /**
     * 获取所有设备的最新心跳日志
     *
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志
     * @return 心跳日志列表，每个设备的最新心跳日志
     */
    @Override
    public List<Map<String, Object>> getLatestHeartbeatLogs(boolean onlyDevice2250) {
        // 创建查询条件，筛选日志级别为"HEART"的记录
        QueryWrapper<AiTestAgent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("LOG_LEVEL", "HEART");
        queryWrapper.orderByDesc("Created_At");
        
        // 选择需要的字段
        queryWrapper.select("Device_ID", "Remarks", "Created_At", "Inspection_Time");
        
        // 执行查询
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        
        // 处理结果，确保字段名称符合驼峰命名规范
        for (Map<String, Object> log : result) {
            // 将数据库字段名转换为驼峰命名
            if (log.containsKey("Device_ID")) {
                log.put("deviceId", log.get("Device_ID"));
                log.remove("Device_ID");
            }
            if (log.containsKey("Remarks")) {
                log.put("remarks", log.get("Remarks"));
                log.remove("Remarks");
            }
            if (log.containsKey("Created_At")) {
                log.put("createdAt", log.get("Created_At"));
                log.remove("Created_At");
            }
            if (log.containsKey("Inspection_Time")) {
                log.put("inspectionTime", log.get("Inspection_Time"));
                log.remove("Inspection_Time");
            }
        }

        // 如果 onlyDevice2250 为 true，过滤出以"【MBP-PVFCQ0LQ62-2250】"开头的 HEART 日志
        if (onlyDevice2250) {
            List<Map<String, Object>> filteredResult = new ArrayList<>();
            for (Map<String, Object> log : result) {
                String remarks = (String) log.get("remarks");
                if (remarks != null && remarks.contains("【MBP-PVFCQ0LQ62-2250】")) {
                    filteredResult.add(log);
                }
            }
            return filteredResult;
        }

        return result;
    }

    /**
     * 清理检查记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @return 删除的记录数
     */
    @Override
    @Async
    @Transactional
    public int cleanupTestAgentLogs(int keepRecords) {
        // 默认排除HEART级别的日志
        return cleanupTestAgentLogs(keepRecords, "HEART");
    }
    
    /**
     * 清理检查记录，只保留最新的指定数量记录
     * 使用异步方式执行，不阻塞其他操作
     * 
     * @param keepRecords 要保留的记录数量
     * @param excludeLogLevel 要排除的日志级别，不会被清理
     * @return 删除的记录数
     */
    @Override
    @Async
    @Transactional
    public int cleanupTestAgentLogs(int keepRecords, String excludeLogLevel) {
        // 获取总记录数（排除指定日志级别）
        long totalCount;

        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            LambdaQueryWrapper<AiTestAgent> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.ne(AiTestAgent::getLogLevel, excludeLogLevel);
            totalCount = count(countWrapper);
        } else {
            totalCount = count();
        }

        // 如果总记录数小于等于保留数量，不需要清理
        if (totalCount <= keepRecords) {
            return 0;
        }

        // 计算需要删除的记录数
        long deleteCount = totalCount - keepRecords;

        // 查询要保留的最小ID（按创建时间排序，保留最新的记录）
        QueryWrapper<AiTestAgent> queryWrapper = new QueryWrapper<>();

        // 排除指定日志级别
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            queryWrapper.ne("LOG_LEVEL", excludeLogLevel);
        }
        
        queryWrapper.select("MIN(Inspection_ID) as minId")
                   .orderByDesc("Created_At")
                   .last("LIMIT " + keepRecords);
        
        Map<String, Object> result = getMap(queryWrapper);
        if (result == null || !result.containsKey("minId")) {
            return 0;
        }

        Long minIdToKeep = Long.valueOf(result.get("minId").toString());
        
        // 删除ID小于minIdToKeep的记录，并排除指定日志级别
        LambdaQueryWrapper<AiTestAgent> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.lt(AiTestAgent::getInspectionId, minIdToKeep);
        
        // 排除指定日志级别
        if (excludeLogLevel != null && !excludeLogLevel.isEmpty()) {
            deleteWrapper.ne(AiTestAgent::getLogLevel, excludeLogLevel);
        }
        
        // 执行删除操作
        int deletedCount = baseMapper.delete(deleteWrapper);
        
        // 返回删除的记录数
        return deletedCount;
    }

    /**
     * 高级查询接口，支持多条件组合查询
     * 
     * @param page 分页参数
     * @param deviceId 设备ID（可选）
     * @param logLevel 日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param remarks 备注内容模糊查询（可选）
     * @param sortAsc 是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @Override
    public IPage<AiTestAgent> advancedQueryTestAgents(Page<AiTestAgent> page, String deviceId, 
            String logLevel, Date startTime, Date endTime, String remarks, Boolean sortAsc) {
        QueryWrapper<AiTestAgent> queryWrapper = new QueryWrapper<>();
        
        // 如果指定了设备ID，添加条件
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq("Device_ID", deviceId);
        }
        
        // 如果指定了日志级别，添加条件
        if (logLevel != null && !logLevel.isEmpty()) {
            queryWrapper.eq("LOG_LEVEL", logLevel);
        }
        
        // 添加时间范围条件
        if (startTime != null) {
            queryWrapper.ge("Inspection_Time", startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le("Inspection_Time", endTime);
        }
        
        // 添加备注模糊查询条件
        if (remarks != null && !remarks.isEmpty()) {
            queryWrapper.like("Remarks", remarks);
        }
        
        // 根据sortAsc参数决定排序方式
        if (sortAsc != null && sortAsc) {
            queryWrapper.orderByAsc("Inspection_Time"); // 正序排序
        } else {
            queryWrapper.orderByDesc("Inspection_Time"); // 默认倒序排序
        }
        
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据图片路径查询记录及其设备前N条INFO级别日志
     * 
     * @param imagePath 图片路径
     * @param infoLogCount 要获取的INFO级别日志数量
     * @return 包含图片记录和对应设备的INFO日志的结果集
     */
    @Override
    public Map<String, Object> getImageRelatedRecords(String imagePath, int infoLogCount) {
        Map<String, Object> result = new HashMap<>();
        // 查询包含此图片路径的所有记录，按 createAt 降序排列，取最新一条
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestAgent::getImagePath, imagePath)
                    .orderByDesc(AiTestAgent::getCreatedAt)
                    .last("LIMIT 1");
        AiTestAgent imageRecord = getOne(queryWrapper);
        result.put("imageRecord", imageRecord);
        // 如果找不到记录，直接返回
        if (imageRecord == null) {
            result.put("infoLogs", Collections.emptyList());
            return result;
        }
        // 获取该记录的设备ID
        String deviceId = imageRecord.getDeviceId();
        result.put("deviceId", deviceId);
        // 获取创建时间
        Date createdAt = imageRecord.getCreatedAt();
        // 查询此设备在该记录之前的N条INFO级别日志
        LambdaQueryWrapper<AiTestAgent> infoLogsWrapper = new LambdaQueryWrapper<>();
        infoLogsWrapper.eq(AiTestAgent::getDeviceId, deviceId)
                       .eq(AiTestAgent::getLogLevel, "INFO")
                       .lt(createdAt != null, AiTestAgent::getCreatedAt, createdAt)
                       .orderByDesc(AiTestAgent::getCreatedAt)
                       .last("LIMIT " + infoLogCount);
        List<AiTestAgent> infoLogs = list(infoLogsWrapper);
        // 将结果按时间升序排序
        Collections.sort(infoLogs, Comparator.comparing(AiTestAgent::getCreatedAt));
        result.put("infoLogs", infoLogs);
        result.put("count", infoLogs.size());
        result.put("requestedCount", infoLogCount);
        return result;
    }

    /**
     * 批量条件删除检查记录
     * 支持按日志级别、图片路径、设备ID、时间范围等条件组合删除
     *
     * @param logLevel 日志级别（可选）
     * @param deviceId 设备ID（可选）
     * @param hasImagePath 是否有图片路径（可选）
     * @param emptyImagePath 图片路径是否为空（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 删除记录数限制，防止大量删除
     * @param skipSafetyCheck 是否跳过安全检查
     * @return 删除的记录数
     */
    @Override
    @Transactional
    public int batchDeleteTestAgents(String logLevel, String deviceId, Boolean hasImagePath, 
                                     Boolean emptyImagePath, Date startTime, Date endTime, 
                                     Integer limit, Boolean skipSafetyCheck) {
        
        logger.info("开始批量删除AiTestAgent操作，条件：logLevel={}, deviceId={}, hasImagePath={}, emptyImagePath={}, " +
                   "startTime={}, endTime={}, limit={}, skipSafetyCheck={}", 
                   logLevel, deviceId, hasImagePath, emptyImagePath, startTime, endTime, limit, skipSafetyCheck);
        
        // 构建条件查询器
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加各种条件
        if (logLevel != null && !logLevel.isEmpty()) {
            queryWrapper.eq(AiTestAgent::getLogLevel, logLevel);
        }
        
        if (deviceId != null && !deviceId.isEmpty()) {
            queryWrapper.eq(AiTestAgent::getDeviceId, deviceId);
        }
        
        // 处理图片路径条件
        if (Boolean.TRUE.equals(hasImagePath)) {
            queryWrapper.isNotNull(AiTestAgent::getImagePath)
                       .ne(AiTestAgent::getImagePath, "");
        }
        
        if (Boolean.TRUE.equals(emptyImagePath)) {
            queryWrapper.and(wrapper -> wrapper
                             .isNull(AiTestAgent::getImagePath)
                             .or()
                             .eq(AiTestAgent::getImagePath, ""));
        }
        
        // 处理时间范围
        if (startTime != null) {
            queryWrapper.ge(AiTestAgent::getCreatedAt, startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le(AiTestAgent::getCreatedAt, endTime);
        }
        
        // 如果设置了限制数量
        if (limit != null && limit > 0) {
            // 首先查询符合条件的记录总数
            long totalCount = count(queryWrapper);
            
            // 检查是否超过了限制
            if (totalCount > limit && !Boolean.TRUE.equals(skipSafetyCheck)) {
                logger.warn("符合删除条件的记录数({})超过了限制({}), 由于未跳过安全检查，删除操作已取消", totalCount, limit);
                throw new IllegalArgumentException("符合删除条件的记录数(" + totalCount + ")超过了限制(" + limit + "), 删除操作已取消。如需继续删除，请设置skipSafetyCheck=true");
            }
            
            // 如果记录数超过限制但跳过了安全检查，记录警告日志
            if (totalCount > limit) {
                logger.warn("符合删除条件的记录数({})超过了限制({}), 但已跳过安全检查，继续执行删除操作", totalCount, limit);
            }
            
            // 按照创建时间降序排序，保留最新的记录
            if (totalCount > limit && Boolean.TRUE.equals(skipSafetyCheck)) {
                // 查找出要保留的记录的最早时间，通过查询结果并排序
                Page<AiTestAgent> page = new Page<>(1, limit);
                page.setDesc("Created_At"); // 按时间降序排序，保留最新的记录
                
                Page<AiTestAgent> latestRecords = baseMapper.selectPage(page, queryWrapper);
                if (latestRecords.getRecords().size() > 0) {
                    // 获取符合条件的limit条最新记录中的最早时间
                    Date earliestTimeToKeep = latestRecords.getRecords()
                                                       .get(latestRecords.getRecords().size() - 1)
                                                       .getCreatedAt();
                    
                    // 修改查询条件，只删除早于该时间的记录
                    queryWrapper.lt(AiTestAgent::getCreatedAt, earliestTimeToKeep);
                }
            }
        }
        
        // 执行删除操作，并返回删除的记录数
        int deletedCount = baseMapper.delete(queryWrapper);
        logger.info("批量删除AiTestAgent操作完成，共删除{}条记录", deletedCount);
        
        return deletedCount;
    }

    /**
     * 标记或取消标记指定时间范围内特定设备和备注内容的ERROR日志为误报(FP - False Positive)
     * 仅处理 imagePath 不为空的应用问题日志
     *
     * @param deviceId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param remarksKeyword 备注中的关键字
     * @param isFalsePositive 是否标记为误报 (true: 标记, false: 取消标记)
     * @return 更新的记录数
     */
    @Override
    @Transactional
    public int markFalsePositive(String deviceId, Date startTime, Date endTime, String remarksKeyword, boolean isFalsePositive) {
        logger.info("开始{}误报标记操作：设备={}, 时间范围=[{} - {}], 关键字={}, 标记={}", 
                   (isFalsePositive ? "添加" : "移除"), deviceId, startTime, endTime, remarksKeyword, isFalsePositive);
        
        // 构建基础查询条件
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestAgent::getDeviceId, deviceId)
                    .eq(AiTestAgent::getLogLevel, "ERROR") // 只处理ERROR级别的日志
                    .isNotNull(AiTestAgent::getImagePath) // 只处理应用问题（有图片路径）
                    .ne(AiTestAgent::getImagePath, "")      // 图片路径不为空字符串
                    .ge(AiTestAgent::getCreatedAt, startTime)
                    .le(AiTestAgent::getCreatedAt, endTime)
                    .like(AiTestAgent::getRemarks, remarksKeyword); // 备注包含关键字
        
        int updatedCount = 0;
        String fpPrefix = "FP "; // 误报标记前缀
        
        if (isFalsePositive) {
            // 标记为误报：在备注前面添加 "FP "
            // 1. 查询出所有符合条件且备注不以 "FP " 开头的记录
            queryWrapper.notLike(AiTestAgent::getRemarks, fpPrefix + "%" ); // 使用 not like 'FP %'
            List<AiTestAgent> recordsToMark = baseMapper.selectList(queryWrapper);
            
            if (recordsToMark.isEmpty()) {
                logger.info("没有需要标记为误报的记录");
                return 0;
            }
            
            logger.info("查询到 {} 条需要标记为误报的记录", recordsToMark.size());
            
            // 2. 逐条更新备注信息
            for (AiTestAgent record : recordsToMark) {
                String originalRemarks = record.getRemarks();
                record.setRemarks(fpPrefix + originalRemarks);
                record.setUpdateTime(new Date()); // 更新修改时间
                if (baseMapper.updateById(record) > 0) {
                    updatedCount++;
                }
            }
            logger.info("成功标记 {} 条记录为误报", updatedCount);
            
        } else {
            // 取消标记误报：移除备注前面的 "FP "
            // 1. 查询出所有符合条件且备注以 "FP " 开头的记录
            queryWrapper.likeRight(AiTestAgent::getRemarks, fpPrefix); // 使用 like 'FP %'
            List<AiTestAgent> recordsToUnmark = baseMapper.selectList(queryWrapper);
            
            if (recordsToUnmark.isEmpty()) {
                logger.info("没有需要取消标记误报的记录");
                return 0;
            }
            
            logger.info("查询到 {} 条需要取消标记误报的记录", recordsToUnmark.size());
            
            // 2. 逐条更新备注信息
            for (AiTestAgent record : recordsToUnmark) {
                String originalRemarks = record.getRemarks();
                // 移除前缀 "FP "
                record.setRemarks(originalRemarks.substring(fpPrefix.length()));
                record.setUpdateTime(new Date()); // 更新修改时间
                if (baseMapper.updateById(record) > 0) {
                    updatedCount++;
                }
            }
            logger.info("成功取消标记 {} 条记录的误报状态", updatedCount);
        }
        
        return updatedCount;
    }

    /**
     * 根据设备ID和轮次号查询所有日志
     * 按创建时间从早到晚排序
     *
     * @param deviceId 设备ID
     * @param roundNum 轮次号
     * @return 符合条件的日志列表，按创建时间升序排序
     */
    @Override
    public List<AiTestAgent> getLogsByDeviceIdAndRoundNum(String deviceId, Long roundNum) {
        logger.info("查询设备ID为{}，轮次号为{}的所有日志", deviceId, roundNum);
        
        LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiTestAgent::getDeviceId, deviceId)
                    .eq(AiTestAgent::getRoundNum, roundNum)
                    .orderByAsc(AiTestAgent::getCreatedAt); // 按创建时间升序排序（从早到晚）
        
        List<AiTestAgent> logs = list(queryWrapper);
        logger.info("查询到{}条符合条件的日志记录", logs.size());
        
        return logs;
    }
} 