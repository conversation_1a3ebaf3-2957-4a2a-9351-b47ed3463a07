package com.sankuai.mdp.compass.aiTestAgent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.mdp.compass.category.util.MultiFormatDateDeserializer;

import java.io.Serializable;
import java.util.Date;

/**
 * AiTestAgent实体类
 * 对应云端数据库表ai_test_agent
 */
@TableName("ai_test_agent")
public class AiTestAgent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    @TableId(value = "Inspection_ID", type = IdType.AUTO)
    private Long inspectionId;

    /**
     * 检查时间
     */
    @TableField("Inspection_Time")
    @JsonDeserialize(using = MultiFormatDateDeserializer.class)
    private Date inspectionTime;

    /**
     * 设备ID
     */
    @TableField("Device_ID")
    private String deviceId;

    /**
     * 图片路径
     */
    @TableField("Image_Path")
    private String imagePath;

    /**
     * 备注信息
     */
    @TableField("Remarks")
    private String remarks;

    /**
     * 日志级别
     */
    @TableField("LOG_LEVEL")
    private String logLevel;

    /**
     * 创建时间戳
     */
    @TableField("Created_At")
    @JsonDeserialize(using = MultiFormatDateDeserializer.class)
    private Date createdAt;

    /**
     * 添加时间
     */
    @TableField("Add_Time")
    @JsonDeserialize(using = MultiFormatDateDeserializer.class)
    private Date addTime;

    /**
     * 更新时间，会在记录更新时自动更新
     */
    @TableField("Update_Time")
    @JsonDeserialize(using = MultiFormatDateDeserializer.class)
    private Date updateTime;

    /**
     * 轮次号
     */
    @TableField("Round_Num")
    private Long roundNum;
    
    // Getter and Setter 方法
    public Long getInspectionId() {
        return inspectionId;
    }

    public void setInspectionId(Long inspectionId) {
        this.inspectionId = inspectionId;
    }

    public Date getInspectionTime() {
        return inspectionTime;
    }

    public void setInspectionTime(Date inspectionTime) {
        this.inspectionTime = inspectionTime;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(Long roundNum) {
        this.roundNum = roundNum;
    }
} 