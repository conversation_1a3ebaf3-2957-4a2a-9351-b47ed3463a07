package com.sankuai.mdp.compass.aiTestAgent.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sankuai.mdp.compass.aiTestAgent.entity.AiTestAgent;
import com.sankuai.mdp.compass.aiTestAgent.service.AiTestAgentService;
import com.sankuai.mdp.compass.aiTestRecord.entity.AiTestRecord;
import com.sankuai.mdp.compass.aiTestRecord.service.AiTestRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.Comparator;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.mdp.compass.category.util.MultiFormatDateDeserializer;

/**
 * AiTestAgent控制器
 * 提供RESTful API接口，用于对AiTestAgent数据的CRUD操作
 * 同时提供设备统计相关接口
 */
@RestController
@RequestMapping("compass/api/aiTestAgent")
public class AiTestAgentController {

    private static final Logger logger = LoggerFactory.getLogger(AiTestAgentController.class);

    // 数据库字段长度常量
    private static final int MAX_DEVICE_ID_LENGTH = 100;
    private static final int MAX_IMAGE_PATH_LENGTH = 500;
    private static final int MAX_REMARKS_LENGTH = 2000;
    private static final int MAX_LOG_LEVEL_LENGTH = 10;

    @Autowired
    private AiTestAgentService aiTestAgentService;
    
    @Autowired
    private AiTestRecordService aiTestRecordService;

    /**
     * 创建检查记录
     *
     * @param aiTestAgent 检查记录实体
     * @return 创建结果
     */
    @PostMapping("/add")
    public ResponseEntity<Map<String, Object>> createTestAgent(@RequestBody AiTestAgent aiTestAgent) {
        Map<String, Object> result = new HashMap<>();

        // 数据校验
        Map<String, String> validationErrors = validateTestAgentData(aiTestAgent);
        if (!validationErrors.isEmpty()) {
            result.put("success", false);
            result.put("message", "数据校验失败");
            result.put("errors", validationErrors);
            logger.warn("AiTestAgent记录数据校验失败: {}, 数据: {}", validationErrors, aiTestAgent);
            return ResponseEntity.badRequest().body(result);
        }

        // 设置创建时间
        Date date = new Date();
        if (aiTestAgent.getCreatedAt() == null) {
            aiTestAgent.setCreatedAt(date);
        }
        if (aiTestAgent.getAddTime() == null) {
            aiTestAgent.setAddTime(date);
        }
        if (aiTestAgent.getUpdateTime() == null) {
            aiTestAgent.setUpdateTime(date);
        }
        if (aiTestAgent.getInspectionTime() == null) {
            aiTestAgent.setInspectionTime(date);
        }

        boolean success = false;
        try {
            success = aiTestAgentService.addTestAgent(aiTestAgent);
        } catch (Exception e) {
            logger.error("添加AiTestAgent记录失败，数据: {}, 异常: {}", aiTestAgent, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "添加AiTestAgent记录失败: " + e.getMessage());
            result.put("failedData", aiTestAgent);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }

        if (success) {
            logger.info("添加AiTestAgent记录成功, ID: {}, 设备ID: {}", aiTestAgent.getInspectionId(), aiTestAgent.getDeviceId());
            result.put("success", true);
            result.put("message", "添加AiTestAgent记录成功");
            result.put("data", aiTestAgent);
            return ResponseEntity.ok(result);
        } else {
            logger.error("添加AiTestAgent记录失败，数据: {}", aiTestAgent);
            result.put("success", false);
            result.put("message", "添加AiTestAgent记录失败");
            result.put("failedData", aiTestAgent);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 校验检查记录数据
     * 
     * @param aiTestAgent 检查记录实体
     * @return 校验错误信息，如果没有错误则返回空Map
     */
    private Map<String, String> validateTestAgentData(AiTestAgent aiTestAgent) {
        Map<String, String> errors = new HashMap<>();

        // 检查必填字段
        if (aiTestAgent.getDeviceId() == null || aiTestAgent.getDeviceId().trim().isEmpty()) {
            errors.put("deviceId", "设备ID不能为空");
        } else if (aiTestAgent.getDeviceId().length() > MAX_DEVICE_ID_LENGTH) {
            errors.put("deviceId", "设备ID长度不能超过" + MAX_DEVICE_ID_LENGTH + "个字符");
        }

        // 图片路径可以为空，但如果不为空需要检查长度
        if (aiTestAgent.getImagePath() != null && !aiTestAgent.getImagePath().trim().isEmpty()
                && aiTestAgent.getImagePath().length() > MAX_IMAGE_PATH_LENGTH) {
            errors.put("imagePath", "图片路径长度不能超过" + MAX_IMAGE_PATH_LENGTH + "个字符");
        }

        // 检查可选字段长度
        if (aiTestAgent.getRemarks() != null && aiTestAgent.getRemarks().length() > MAX_REMARKS_LENGTH) {
            errors.put("remarks", "备注信息长度不能超过" + MAX_REMARKS_LENGTH + "个字符");
        }

        if (aiTestAgent.getLogLevel() != null && aiTestAgent.getLogLevel().length() > MAX_LOG_LEVEL_LENGTH) {
            errors.put("logLevel", "日志级别长度不能超过" + MAX_LOG_LEVEL_LENGTH + "个字符");
        }

        return errors;
    }

    /**
     * 根据ID查询检查记录
     *
     * @param inspectionId 检查记录ID
     * @return 检查记录
     */
    @GetMapping("/find/{inspectionId}")
    public ResponseEntity<Map<String, Object>> getTestAgentById(@PathVariable Long inspectionId) {
        Map<String, Object> result = new HashMap<>();
        AiTestAgent aiTestAgent = aiTestAgentService.getTestAgentById(inspectionId);

        if (aiTestAgent != null) {
            result.put("success", true);
            result.put("data", aiTestAgent);
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "未找到ID为" + inspectionId + "的AiTestAgent记录");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 分页查询检查记录
     * 默认排除日志级别为"HEART"的记录
     *
     * @param pageNum          页码
     * @param pageSize         每页大小
     * @param deviceId         设备ID（可选）
     * @param includeHeartbeat 是否包含心跳日志（可选，默认不包含）
     * @return 分页结果
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> pageTestAgents(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false, defaultValue = "false") Boolean includeHeartbeat) {

        // 默认排除"HEART"级别的日志，除非明确指定包含
        String logLevel = includeHeartbeat ? null : "!HEART";

        // 重定向到新的统一查询接口
        return queryTestAgents(pageNum, pageSize, deviceId, logLevel, null, null, null, false);
    }

    /**
     * 根据设备ID查询最新的检查记录
     *
     * @param deviceId 设备ID
     * @return 最新的检查记录
     */
    @GetMapping("/device/latest")
    public ResponseEntity<Map<String, Object>> getLatestByDeviceId(@RequestParam String deviceId) {
        Map<String, Object> result = new HashMap<>();
        AiTestAgent aiTestAgent = aiTestAgentService.getLatestByDeviceId(deviceId);

        if (aiTestAgent != null) {
            result.put("success", true);
            result.put("data", aiTestAgent);
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "未找到设备ID为" + deviceId + "的AiTestAgent记录");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 根据时间范围查询检查记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 检查记录列表
     */
    @GetMapping("/time-range")
    public ResponseEntity<Map<String, Object>> getByTimeRange(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        // 重定向到新的统一查询接口，默认不分页
        return queryTestAgents(1, 1000, null, null, startTime, endTime, null, false);
    }

    /**
     * 更新检查记录
     *
     * @param inspectionId 检查记录ID
     * @param aiTestAgent   检查记录实体
     * @return 更新结果
     */
    @PutMapping("/update/{inspectionId}")
    public ResponseEntity<Map<String, Object>> updateTestAgent(
            @PathVariable Long inspectionId,
            @RequestBody AiTestAgent aiTestAgent) {

        Map<String, Object> result = new HashMap<>();
        aiTestAgent.setInspectionId(inspectionId);

        // 设置更新时间
        aiTestAgent.setUpdateTime(new Date());

        boolean success = aiTestAgentService.updateTestAgent(aiTestAgent);

        if (success) {
            result.put("success", true);
            result.put("message", "更新AiTestAgent记录成功");
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "更新AiTestAgent记录失败");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 根据ID删除检查记录
     *
     * @param inspectionId 检查记录ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{inspectionId}")
    public ResponseEntity<Map<String, Object>> deleteTestAgent(@PathVariable Long inspectionId) {
        Map<String, Object> result = new HashMap<>();
        boolean success = aiTestAgentService.deleteTestAgent(inspectionId);

        if (success) {
            result.put("success", true);
            result.put("message", "删除AiTestAgent记录成功");
            return ResponseEntity.ok(result);
        } else {
            result.put("success", false);
            result.put("message", "删除AiTestAgent记录失败，可能记录不存在");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
    }

    /**
     * 根据设备ID删除检查记录
     *
     * @param deviceId 设备ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> deleteByDeviceId(@PathVariable String deviceId) {
        Map<String, Object> result = new HashMap<>();
        int count = aiTestAgentService.deleteByDeviceId(deviceId);

        result.put("success", true);
        result.put("message", "成功删除" + count + "条AiTestAgent记录");
        result.put("count", count);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取近n天内有检查记录的所有设备列表
     * 通过Created_At时间戳筛选近n天的记录，提取并去重设备ID
     * 排除特殊设备ID "global"和日志级别为"HEART"的记录
     *
     * @param days 查询的天数范围，默认为3天
     * @return 设备列表，包含设备ID和最后检查时间
     */
    @GetMapping("/recent-devices")
    public ResponseEntity<Map<String, Object>> getRecentDevices(
            @RequestParam(required = false, defaultValue = "3") Integer days) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (days <= 0) {
            result.put("success", false);
            result.put("message", "查询天数必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        // 计算n天前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date daysAgo = calendar.getTime();

        // 获取近n天的设备列表，排除"global"设备和"HEART"级别的日志
        List<Map<String, Object>> devices = aiTestAgentService.getRecentDevices(daysAgo, "HEART");

        // 过滤掉deviceId为"global"的记录
        devices.removeIf(device -> "global".equals(device.get("deviceId")));

        result.put("success", true);
        result.put("data", devices);
        result.put("count", devices.size());
        result.put("days", days);
        result.put("message", "成功获取近" + days + "天内的设备列表(不含global和心跳日志)");

        return ResponseEntity.ok(result);
    }

    /**
     * 获取各设备最新的心跳日志
     * 查询日志级别为"HEART"的最新记录，并按设备分组
     * 将remarks中的"|"替换为换行符，便于前端展示
     *
     * @param onlyDevice2250 是否只处理设备编号为2250的HEART日志，默认为true
     * @return 设备心跳日志列表
     */
    @GetMapping("/heartbeat-logs")
    public ResponseEntity<Map<String, Object>> getHeartbeatLogs(
            @RequestParam(required = false, defaultValue = "true") Boolean onlyDevice2250) {
        Map<String, Object> result = new HashMap<>();

        // 获取所有设备的最新心跳日志
        List<Map<String, Object>> heartbeatLogs = aiTestAgentService.getLatestHeartbeatLogs(onlyDevice2250);

        // 处理remarks字段，将"|"替换为换行符
        for (Map<String, Object> log : heartbeatLogs) {
            if (log.containsKey("remarks")) {
                String remarks = (String) log.get("remarks");
                if (remarks != null) {
                    // 将"|"替换为换行符
                    remarks = remarks.replace(" | ", "\n");
                    log.put("remarks", remarks);
                }
            }
        }

        result.put("success", true);
        result.put("data", heartbeatLogs);
        result.put("count", heartbeatLogs.size());
        result.put("message", "成功获取设备心跳日志" + (onlyDevice2250 ? "（仅设备2250）" : ""));

        return ResponseEntity.ok(result);
    }

    /**
     * 清理检查记录，只保留最新的10万条记录
     * 使用异步方式执行，不阻塞其他操作
     * 默认不清理HEART级别的日志
     * 
     * @return 清理操作的结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupTestAgentLogs() {
        Map<String, Object> result = new HashMap<>();

        // 保留的记录数量
        final int KEEP_RECORDS = 100000;
        // 排除的日志级别
        final String EXCLUDE_LOG_LEVEL = "HEART";

        // 获取总记录数（排除HEART级别）
        LambdaQueryWrapper<AiTestAgent> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.ne(AiTestAgent::getLogLevel, EXCLUDE_LOG_LEVEL);
        long totalCount = aiTestAgentService.count(countWrapper);

        if (totalCount <= KEEP_RECORDS) {
            result.put("success", true);
            result.put("message", "当前非心跳日志记录数(" + totalCount + ")未超过保留阈值(" + KEEP_RECORDS + ")，无需清理");
            result.put("totalCount", totalCount);
            result.put("keepRecords", KEEP_RECORDS);
            result.put("excludeLogLevel", EXCLUDE_LOG_LEVEL);
            result.put("deletedCount", 0);
            return ResponseEntity.ok(result);
        }

        // 异步执行清理操作，排除HEART级别的日志
        int deletedCount = aiTestAgentService.cleanupTestAgentLogs(KEEP_RECORDS, EXCLUDE_LOG_LEVEL);

        result.put("success", true);
        result.put("message", "清理操作已启动，将异步删除旧记录，保留最新的" + KEEP_RECORDS + "条非心跳日志记录");
        result.put("totalCount", totalCount);
        result.put("keepRecords", KEEP_RECORDS);
        result.put("excludeLogLevel", EXCLUDE_LOG_LEVEL);
        result.put("estimatedDeleteCount", totalCount - KEEP_RECORDS);

        return ResponseEntity.ok(result);
    }

    /**
     * 高级查询接口
     * 支持多条件组合查询：设备ID、日志级别、时间范围、备注内容模糊匹配
     * 支持分页和排序控制
     *
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @param deviceId  设备ID（可选）
     * @param logLevel  日志级别（可选）
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @param remarks   备注内容模糊查询（可选）
     * @param sortAsc   是否按时间正序排序（可选，默认为false，即倒序）
     * @return 分页结果
     */
    @GetMapping("/query")
    public ResponseEntity<Map<String, Object>> queryTestAgents(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) String logLevel,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false) String remarks,
            @RequestParam(required = false, defaultValue = "false") Boolean sortAsc) {

        Map<String, Object> result = new HashMap<>();
        Page<AiTestAgent> page = new Page<>(pageNum, pageSize);

        // 调用服务层方法，传入所有查询参数
        IPage<AiTestAgent> pageResult = aiTestAgentService.advancedQueryTestAgents(
                page, deviceId, logLevel, startTime, endTime, remarks, sortAsc);

        result.put("success", true);
        result.put("data", pageResult);

        // 添加查询参数到结果中，方便前端了解当前查询条件
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("pageNum", pageNum);
        queryParams.put("pageSize", pageSize);
        queryParams.put("deviceId", deviceId);
        queryParams.put("logLevel", logLevel);
        queryParams.put("startTime", startTime);
        queryParams.put("endTime", endTime);
        queryParams.put("remarks", remarks);
        queryParams.put("sortAsc", sortAsc);
        result.put("queryParams", queryParams);

        return ResponseEntity.ok(result);
    }

    /**
     * 根据图片路径查询相关记录
     * 先根据图片路径查找对应的记录，然后获取该记录所属的设备ID，
     * 再查询该设备ID在此记录之前的N条INFO级别日志记录。
     * 
     * @param imagePath 图片路径
     * @param count     要查询的INFO级别日志数量，默认为5条
     * @return 包含图片记录和对应设备的INFO日志的结果集
     */
    @GetMapping("/image-related-records")
    public ResponseEntity<Map<String, Object>> getImageRelatedRecords(
            @RequestParam String imagePath,
            @RequestParam(required = false, defaultValue = "5") Integer count) {

        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (imagePath == null || imagePath.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "图片路径不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        if (count <= 0) {
            result.put("success", false);
            result.put("message", "查询数量必须大于0");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            // 调用服务层方法获取相关记录
            Map<String, Object> relatedRecords = aiTestAgentService.getImageRelatedRecords(imagePath, count);

            // 检查是否找到对应图片记录
            if (relatedRecords.get("imageRecord") == null) {
                result.put("success", false);
                result.put("message", "未找到包含该图片路径的记录");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            result.put("success", true);
            result.put("data", relatedRecords);
            result.put("message", "成功获取图片相关记录");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取图片相关记录失败，图片路径: {}, 异常: {}", imagePath, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取图片相关记录失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 批量条件删除检查记录
     * 支持按日志级别、图片路径、设备ID、时间范围等条件组合删除
     *
     * @param deleteParams 包含删除条件的参数对象
     * @return 删除结果
     */
    @PostMapping("/batch-delete")
    public ResponseEntity<Map<String, Object>> batchDeleteTestAgents(@RequestBody Map<String, Object> deleteParams) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从参数中获取各种条件
            String logLevel = (String) deleteParams.get("logLevel");
            String deviceId = (String) deleteParams.get("deviceId");
            Boolean hasImagePath = (Boolean) deleteParams.get("hasImagePath");
            Boolean emptyImagePath = (Boolean) deleteParams.get("emptyImagePath");
            Date startTime = null;
            Date endTime = null;

            // 处理日期参数
            if (deleteParams.containsKey("startTime") && deleteParams.get("startTime") != null) {
                if (deleteParams.get("startTime") instanceof Date) {
                    startTime = (Date) deleteParams.get("startTime");
                } else {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        startTime = sdf.parse(deleteParams.get("startTime").toString());
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "无效的开始时间格式，请使用 yyyy-MM-dd HH:mm:ss");
                        return ResponseEntity.badRequest().body(result);
                    }
                }
            }

            if (deleteParams.containsKey("endTime") && deleteParams.get("endTime") != null) {
                if (deleteParams.get("endTime") instanceof Date) {
                    endTime = (Date) deleteParams.get("endTime");
                } else {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        endTime = sdf.parse(deleteParams.get("endTime").toString());
                    } catch (Exception e) {
                        result.put("success", false);
                        result.put("message", "无效的结束时间格式，请使用 yyyy-MM-dd HH:mm:ss");
                        return ResponseEntity.badRequest().body(result);
                    }
                }
            }

            // 参数校验，至少需要一个删除条件
            if (logLevel == null && deviceId == null && hasImagePath == null &&
                    emptyImagePath == null && startTime == null && endTime == null) {
                result.put("success", false);
                result.put("message", "至少需要提供一个删除条件");
                return ResponseEntity.badRequest().body(result);
            }

            // 添加安全限制，避免误删除大量数据
            Boolean skipSafetyCheck = (Boolean) deleteParams.getOrDefault("skipSafetyCheck", false);
            Integer limit = (Integer) deleteParams.getOrDefault("limit", 1000);

            if (limit > 10000 && !skipSafetyCheck) {
                result.put("success", false);
                result.put("message", "为保护数据安全，单次批量删除记录数不能超过10000条。如需删除更多记录，请分批操作或设置skipSafetyCheck=true");
                return ResponseEntity.badRequest().body(result);
            }

            // 调用服务层批量删除方法
            int deletedCount = aiTestAgentService.batchDeleteTestAgents(
                    logLevel, deviceId, hasImagePath, emptyImagePath, startTime, endTime, limit, skipSafetyCheck);

            result.put("success", true);
            result.put("message", "批量删除成功，共删除" + deletedCount + "条记录");
            result.put("count", deletedCount);

            // 添加删除条件到结果
            Map<String, Object> conditions = new HashMap<>(deleteParams);
            conditions.remove("skipSafetyCheck");
            result.put("conditions", conditions);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("批量删除AiTestAgent记录失败: {}, 条件: {}", e.getMessage(), deleteParams, e);
            result.put("success", false);
            result.put("message", "批量删除AiTestAgent记录失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 标记或取消标记指定时间范围内特定设备和备注内容的ERROR日志为误报(FP - False Positive)
     * 仅处理 imagePath 不为空的应用问题日志
     *
     * @param markRequest 包含标记信息的请求体
     * @return 操作结果，包含成功与否及更新的记录数
     */
    @PostMapping("/mark-fp")
    public ResponseEntity<Map<String, Object>> markFalsePositive(@RequestBody MarkFalsePositiveRequest markRequest) {
        Map<String, Object> result = new HashMap<>();

        // 参数校验
        if (markRequest == null || markRequest.getDeviceId() == null || markRequest.getDeviceId().trim().isEmpty() ||
                markRequest.getRemarksKeyword() == null || markRequest.getRemarksKeyword().trim().isEmpty() ||
                markRequest.getStartTime() == null || markRequest.getEndTime() == null
                || markRequest.getIsFalsePositive() == null) {
            result.put("success", false);
            result.put("message", "缺少必要的参数: deviceId, startTime, endTime, remarksKeyword, isFalsePositive");
            return ResponseEntity.badRequest().body(result);
        }

        if (markRequest.getEndTime().before(markRequest.getStartTime())) {
            result.put("success", false);
            result.put("message", "结束时间不能早于开始时间");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            int updatedCount = aiTestAgentService.markFalsePositive(
                    markRequest.getDeviceId(),
                    markRequest.getStartTime(),
                    markRequest.getEndTime(),
                    markRequest.getRemarksKeyword(),
                    markRequest.getIsFalsePositive());

            result.put("success", true);
            result.put("message",
                    (markRequest.getIsFalsePositive() ? "标记" : "取消标记") + "误报操作成功，共更新 " + updatedCount + " 条记录");
            result.put("updatedCount", updatedCount);
            result.put("requestParams", markRequest); // 返回请求参数以供确认
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("标记/取消标记误报失败: {}, 请求参数: {}", e.getMessage(), markRequest, e);
            result.put("success", false);
            result.put("message", "标记/取消标记误报失败: " + e.getMessage());
            result.put("requestParams", markRequest);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 根据设备ID和轮次号查询所有日志
     * 按创建时间从早到晚排序
     *
     * @param deviceId 设备ID
     * @param roundNum 轮次号
     * @return 符合条件的日志列表
     */
    @GetMapping("/device-round-logs")
    public ResponseEntity<Map<String, Object>> getLogsByDeviceIdAndRoundNum(
            @RequestParam String deviceId,
            @RequestParam Long roundNum) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (deviceId == null || deviceId.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "设备ID不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        if (roundNum == null) {
            result.put("success", false);
            result.put("message", "轮次号不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            List<AiTestAgent> logs = aiTestAgentService.getLogsByDeviceIdAndRoundNum(deviceId, roundNum);

            result.put("success", true);
            result.put("data", logs);
            result.put("count", logs.size());
            result.put("deviceId", deviceId);
            result.put("roundNum", roundNum);
            result.put("message", "成功获取设备ID为" + deviceId + "，轮次号为" + roundNum + "的所有日志");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("查询设备日志失败，设备ID: {}, 轮次号: {}, 异常: {}", deviceId, roundNum, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询设备日志失败: " + e.getMessage());
            result.put("deviceId", deviceId);
            result.put("roundNum", roundNum);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 聚合同一轮次的数据，按时间顺序排序，返回便于前端展示的数据结构
     * 
     * @param roundNum 轮次号
     * @param deviceId 设备ID（可选）
     * @return 聚合后的数据结构，按设备分组，每个设备的日志按时间排序
     */
    @GetMapping("/round-aggregated-data")
    public ResponseEntity<Map<String, Object>> getAggregatedRoundData(
            @RequestParam Long roundNum,
            @RequestParam(required = false) String deviceId) {
        Map<String, Object> result = new HashMap<>();

        // 参数验证
        if (roundNum == null) {
            result.put("success", false);
            result.put("message", "轮次号不能为空");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            // 构建查询条件
            LambdaQueryWrapper<AiTestAgent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiTestAgent::getRoundNum, roundNum);
            
            // 如果指定了设备ID，则添加设备ID条件
            if (deviceId != null && !deviceId.trim().isEmpty()) {
                queryWrapper.eq(AiTestAgent::getDeviceId, deviceId);
            }
            
            // 按创建时间升序排序
            queryWrapper.orderByAsc(AiTestAgent::getCreatedAt);
            
            // 查询所有符合条件的数据
            List<AiTestAgent> allLogs = aiTestAgentService.list(queryWrapper);
            
            // 按设备ID分组
            Map<String, List<AiTestAgent>> deviceGroupedLogs = allLogs.stream()
                .collect(Collectors.groupingBy(AiTestAgent::getDeviceId));
            
            // 构建便于前端展示的数据结构
            List<Map<String, Object>> deviceDataList = new ArrayList<>();
            int totalLogCount = 0;
            
            for (Map.Entry<String, List<AiTestAgent>> entry : deviceGroupedLogs.entrySet()) {
                String currentDeviceId = entry.getKey();
                List<AiTestAgent> deviceLogs = entry.getValue();
                
                // 按创建时间排序（确保时间顺序）
                deviceLogs.sort(Comparator.comparing(AiTestAgent::getCreatedAt));
                
                // 统计各类型日志数量
                Map<String, Integer> logLevelCounts = deviceLogs.stream()
                    .collect(Collectors.groupingBy(
                        log -> log.getLogLevel() != null ? log.getLogLevel() : "UNKNOWN",
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                    ));
                
                // 获取时间范围
                Date startTime = deviceLogs.isEmpty() ? null : deviceLogs.get(0).getCreatedAt();
                Date endTime = deviceLogs.isEmpty() ? null : deviceLogs.get(deviceLogs.size() - 1).getCreatedAt();
                
                // 构建设备数据
                Map<String, Object> deviceData = new HashMap<>();
                deviceData.put("deviceId", currentDeviceId);
                deviceData.put("logCount", deviceLogs.size());
                deviceData.put("logLevelCounts", logLevelCounts);
                deviceData.put("startTime", startTime);
                deviceData.put("endTime", endTime);
                deviceData.put("logs", deviceLogs);
                
                deviceDataList.add(deviceData);
                totalLogCount += deviceLogs.size();
            }
            
            // 按设备ID排序
            deviceDataList.sort(Comparator.comparing(device -> (String) device.get("deviceId")));
            
            // 构建统计信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("roundNum", roundNum);
            statistics.put("deviceCount", deviceGroupedLogs.size());
            statistics.put("totalLogCount", totalLogCount);
            
            // 聚合所有设备的日志级别统计
            Map<String, Integer> overallLogLevelCounts = allLogs.stream()
                .collect(Collectors.groupingBy(
                    log -> log.getLogLevel() != null ? log.getLogLevel() : "UNKNOWN",
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
            statistics.put("overallLogLevelCounts", overallLogLevelCounts);
            
            // 时间范围统计
            if (!allLogs.isEmpty()) {
                statistics.put("roundStartTime", allLogs.get(0).getCreatedAt());
                statistics.put("roundEndTime", allLogs.get(allLogs.size() - 1).getCreatedAt());
            }
            
            // 查询该轮次的prompt信息
            try {
                List<AiTestRecord> promptRecords = aiTestRecordService.getByRoundNum(roundNum.intValue());
                if (!promptRecords.isEmpty()) {
                    // 构建prompt信息映射
                    Map<String, Object> promptInfo = new HashMap<>();
                    for (AiTestRecord record : promptRecords) {
                        Map<String, Object> promptData = new HashMap<>();
                        promptData.put("promptText", record.getPromptText());
                        promptData.put("misId", record.getMisId());
                        promptData.put("processId", record.getProcessId());
                        promptData.put("deviceId", record.getDeviceId());
                        promptData.put("remark", record.getRemark());
                        promptData.put("createdAt", record.getCreatedAt());
                        promptInfo.put("record_" + record.getId(), promptData);
                    }
                    statistics.put("promptInfo", promptInfo);
                    statistics.put("promptCount", promptRecords.size());
                } else {
                    statistics.put("promptInfo", null);
                    statistics.put("promptCount", 0);
                }
            } catch (Exception e) {
                logger.error("查询轮次号 {} 的prompt信息失败: {}", roundNum, e.getMessage(), e);
                statistics.put("promptInfo", null);
                statistics.put("promptCount", 0);
                statistics.put("promptError", "查询prompt信息失败: " + e.getMessage());
            }
            
            result.put("success", true);
            result.put("data", deviceDataList);
            result.put("statistics", statistics);
            result.put("message", "成功聚合轮次号为" + roundNum + "的数据" + 
                    (deviceId != null ? "（设备ID: " + deviceId + "）" : ""));

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("聚合轮次数据失败，轮次号: {}, 设备ID: {}, 异常: {}", roundNum, deviceId, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "聚合轮次数据失败: " + e.getMessage());
            result.put("roundNum", roundNum);
            result.put("deviceId", deviceId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    // 内部类，用于接收 /mark-fp 接口的请求体
    static class MarkFalsePositiveRequest {
        private String deviceId;
        @JsonDeserialize(using = MultiFormatDateDeserializer.class)
        private Date startTime;
        @JsonDeserialize(using = MultiFormatDateDeserializer.class)
        private Date endTime;
        private String remarksKeyword;
        private Boolean isFalsePositive;

        // Getters and Setters
        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public String getRemarksKeyword() {
            return remarksKeyword;
        }

        public void setRemarksKeyword(String remarksKeyword) {
            this.remarksKeyword = remarksKeyword;
        }

        public Boolean getIsFalsePositive() {
            return isFalsePositive;
        }

        public void setIsFalsePositive(Boolean isFalsePositive) {
            this.isFalsePositive = isFalsePositive;
        }

        @Override
        public String toString() {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return "MarkFalsePositiveRequest{" +
                    "deviceId='" + deviceId + '\'' +
                    ", startTime=" + (startTime != null ? sdf.format(startTime) : "null") +
                    ", endTime=" + (endTime != null ? sdf.format(endTime) : "null") +
                    ", remarksKeyword='" + remarksKeyword + '\'' +
                    ", isFalsePositive=" + isFalsePositive +
                    '}';
        }
    }
} 