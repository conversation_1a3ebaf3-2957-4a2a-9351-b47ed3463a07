package com.sankuai.mdp.compass.controller;

import com.google.gson.JsonArray;
import com.sankuai.mdp.compass.service.OcrService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Created by cuijie12 on 2025/02/06.
 */
@RestController
@RequestMapping("/compass/api/ocr")
public class OcrController {
    private final OcrService ocrService;

    public OcrController(OcrService ocrService) {
        this.ocrService = ocrService;
    }

    @GetMapping("/getTextAndLocation")
    String getTextAndLocationFromAI(String PicUrl) {
        return ocrService.getTextAndLocationFromAI(PicUrl).toString();
    }

    @GetMapping("/getUIBug")
    public ResponseEntity<?> recognizeP0UIBugByOnePic(@RequestParam String PicUrl) {
        try {
            String[] results = ocrService.recognizeP0UIBugByOnePic(PicUrl);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error processing image: " + e.getMessage());
        }
    }

    @GetMapping("/getUiPageParseRequest")
    String getUiPageParseRequest(@RequestParam String PicUrl) {
        return ocrService.UiPageParseRequest(PicUrl);
    }

    @GetMapping("/getUiPageParseResult")
    String getUiPageParseResult(@RequestParam String PicUrl) {
        return ocrService.recognizeUiPageParse(PicUrl);
    }
}
