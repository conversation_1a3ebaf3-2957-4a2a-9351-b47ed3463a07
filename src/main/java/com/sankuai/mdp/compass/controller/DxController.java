package com.sankuai.mdp.compass.controller;

import com.sankuai.mdp.compass.common.utils.DxUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("compass/api/dx")
public class DxController {

    @Autowired
    private DxUtil dxUtil;

    /**
     * 发送消息给个人
     * @param message 消息内容
     * @param receivers 接收人的mis号数组
     * @return 发送结果
     */
    @PostMapping("/send/individual")
    public String sendMessageToIndividual(@RequestParam String message, 
                                        @RequestParam String[] receivers) {
        try {
            return dxUtil.sendToIndividualByCompassAuto(message, receivers);
        } catch (Exception e) {
            log.error("发送个人消息失败: message={}, receivers={}", message, receivers, e);
            return "发送失败：" + e.getMessage();
        }
    }

    /**
     * 发送消息给群组
     * @param message 消息内容
     * @param roomId 群组ID
     * @return 发送结果
     */
    @PostMapping("/send/group")
    public String sendMessageToGroup(@RequestParam String message,
                                   @RequestParam long roomId) {
        try {
            return dxUtil.sendToGroupByCompass(message, roomId);
        } catch (Exception e) {
            log.error("发送群组消息失败: message={}, roomId={}", message, roomId, e);
            return "发送失败：" + e.getMessage();
        }
    }

    /**
     * KingKong 公众号：发送消息给个人
     * @param message 消息内容
     * @param receivers 接收人的mis号数组
     * @return 发送结果
     */
    @PostMapping("/send/individual-kingkong")
    public String sendMessageToIndividualByKingKong(@RequestParam String message,
                                                    @RequestParam String[] receivers) {
        try {
            return dxUtil.sendToIndividualByKingKong(message, receivers);
        } catch (Exception e) {
            log.error("KingKong发送个人消息失败: message={}, receivers={}", message, receivers, e);
            return "发送失败：" + e.getMessage();
        }
    }

    /**
     * KingKong 公众号：发送消息给群组
     * @param message 消息内容
     * @param roomId 群组ID
     * @return 发送结果
     */
    @PostMapping("/send/group-kingkong")
    public String sendMessageToGroupByKingKong(@RequestParam String message,
                                               @RequestParam long roomId) {
        try {
            return dxUtil.sendToGroupByKingKong(message, roomId);
        } catch (Exception e) {
            log.error("KingKong发送群组消息失败: message={}, roomId={}", message, roomId, e);
            return "发送失败：" + e.getMessage();
        }
    }
} 