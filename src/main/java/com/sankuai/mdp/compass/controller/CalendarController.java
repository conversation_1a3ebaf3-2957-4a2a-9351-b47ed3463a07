package com.sankuai.mdp.compass.controller;

import com.sankuai.mdp.compass.common.BaseController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;

import static com.caucho.hessian.io.HessianInputFactory.log;

@RestController
@RequestMapping("/compass/api/calendar")
public class CalendarController extends BaseController {
    // 用于转换 UTC 时间格式到北京时间
    public static String convertDateTime(String utcTime) throws Exception {
        // 假设 UTC 时间格式为 "yyyyMMdd'T'HHmmss'Z'"
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        inputFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        outputFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 明确设置为北京时间

        Date date = inputFormat.parse(utcTime);
        return outputFormat.format(date);
    }

    // 处理 iCalendar 字符串并提取信息
    public static List<String> parseCalendar(String calendarContent) {
        List<String> result = new ArrayList<>();

        // 使用正则表达式提取 DTSTART, DTEND, SUMMARY, UID（版本和名称）
        String eventPattern = "BEGIN:VEVENT(.*?)END:VEVENT";
        Pattern pattern = Pattern.compile(eventPattern, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(calendarContent);

        while (matcher.find()) {
            String eventContent = matcher.group(1);
            // 提取 DTSTART 时间
            String startTimePattern = "DTSTART:(\\d{8}T\\d{6}Z)";
            Matcher startTimeMatcher = Pattern.compile(startTimePattern).matcher(eventContent);

            // 提取 DTEND 时间
            String endTimePattern = "DTEND:(\\d{8}T\\d{6}Z)";
            Matcher endTimeMatcher = Pattern.compile(endTimePattern).matcher(eventContent);

            if (startTimeMatcher.find() && endTimeMatcher.find()) {
                try {
                    String utcStartTime = startTimeMatcher.group(1);
                    String utcEndTime = endTimeMatcher.group(1);
                    String formattedStartTime = convertDateTime(utcStartTime); // 转换为本地时间格式
                    String formattedEndTime = convertDateTime(utcEndTime); // 转换为本地时间格式

                    // 提取 SUMMARY 中的时间、版本和状态
                    String summaryPattern = "SUMMARY:(\\d{2}:\\d{2}) (\\d+\\.\\d+\\.\\d+)\\s*(\\S+)";
                    Matcher summaryMatcher = Pattern.compile(summaryPattern).matcher(eventContent);
                    if (summaryMatcher.find()) {
                        String time = summaryMatcher.group(1); // 21:00 等时间部分
                        String version = summaryMatcher.group(2); // 12.29.400 版本号
                        String status = summaryMatcher.group(3); // 集成等状态
                        StringBuilder finalStatus = new StringBuilder();
                        for (int i = 0; i < status.length(); i++) {
                            char c = status.charAt(i);
                            // 判断字符是否是汉字（Unicode 范围：\u4e00-\u9fa5）
                            if (c >= '一' && c <= '龥') {
                                finalStatus.append(c);
                            } else {
                                break;
                            }
                        }
                        // 格式化最终输出：开始时间 | 结束时间 | 版本 | 状态
                        String resultString = formattedStartTime + "|" + formattedEndTime + "|" + version + "|"
                                + finalStatus.toString();
                        System.out.println(resultString);
                        result.add(resultString);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return result;
    }

    public static String getCurrentStage(List<String> versionStages, LocalDateTime currentTime) {
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 迭代版本阶段列表
        for (String stage : versionStages) {
            String[] stageParts = stage.split("\\|"); // 分割每一行
            if (stageParts.length >= 4) { // 确保有足够的字段：开始时间|结束时间|版本|状态
                try {
                    // 获取开始时间和结束时间
                    LocalDateTime startTime = LocalDateTime.parse(stageParts[0], formatter);
                    LocalDateTime endTime = LocalDateTime.parse(stageParts[1], formatter);

                    // 判断当前时间是否处于这个时间范围内
                    if ((currentTime.isEqual(startTime) || currentTime.isAfter(startTime)) &&
                            (currentTime.isEqual(endTime) || currentTime.isBefore(endTime))) {
                        return stage; // 返回完整的阶段信息
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 如果没有匹配到，则返回未知阶段
        return "未知阶段";
    }

    public static String findNearestRegressionTest(List<String> dataList) {
        // 定义时间格式，与数据中时间格式保持一致
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 过滤出包含"回归测试"的记录
        List<String> regressionTests = dataList.stream()
                .filter(s -> s.contains("回归测试"))
                .collect(Collectors.toList());

        // 首先检查当前时间是否在任何回归测试阶段内
        for (String test : regressionTests) {
            String[] parts = test.split("\\|");
            if (parts.length >= 4) { // 确保有足够的字段：开始时间|结束时间|版本|状态
                try {
                    LocalDateTime startTime = LocalDateTime.parse(parts[0], formatter);
                    LocalDateTime endTime = LocalDateTime.parse(parts[1], formatter);

                    // 如果当前时间在这个回归测试阶段内
                    if ((now.isEqual(startTime) || now.isAfter(startTime)) &&
                            (now.isEqual(endTime) || now.isBefore(endTime))) {
                        return test; // 返回当前回归测试阶段
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        // 如果当前时间不在任何回归测试阶段内，找最近的之前的回归测试
        return regressionTests.stream()
                .filter(s -> {
                    String[] parts = s.split("\\|");
                    if (parts.length >= 4) {
                        try {
                            LocalDateTime endTime = LocalDateTime.parse(parts[1], formatter);
                            // 只考虑已经结束的回归测试（即之前的）
                            return endTime.isBefore(now);
                        } catch (Exception e) {
                            return false;
                        }
                    }
                    return false;
                })
                // 找出结束时间与当前时间最近的记录
                .min(Comparator.comparingLong(s -> {
                    String[] parts = s.split("\\|");
                    LocalDateTime endTime = LocalDateTime.parse(parts[1], formatter);
                    // 返回结束时间与当前时间之间的差值（以秒为单位）
                    return ChronoUnit.SECONDS.between(endTime, now);
                }))
                // 如果找不到符合条件的记录，则返回 null
                .orElse(null);
    }

    public static String findPreviousRegressionTest(List<String> dataList) {
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime now = LocalDateTime.now();

        // 过滤出包含"回归测试"的记录，并按开始时间排序
        List<String> regressionTests = dataList.stream()
                .filter(s -> s.contains("回归测试"))
                .filter(s -> s.split("\\|").length >= 4) // 确保数据格式正确
                .sorted(Comparator.comparing(str -> {
                    String dateStr = str.split("\\|")[0]; // 使用开始时间排序
                    return LocalDateTime.parse(dateStr, formatter);
                }))
                .collect(Collectors.toList());

        // 如果回归测试记录少于2个，返回null
        if (regressionTests.size() < 2) {
            return null;
        }

        // 首先找到当前最近的回归测试
        String nearestRegressionTest = findNearestRegressionTest(dataList);
        if (nearestRegressionTest == null) {
            return null;
        }

        // 在排序后的列表中找到最近回归测试的索引
        int nearestIndex = -1;
        for (int i = 0; i < regressionTests.size(); i++) {
            if (regressionTests.get(i).equals(nearestRegressionTest)) {
                nearestIndex = i;
                break;
            }
        }

        // 返回上一个回归测试记录
        return nearestIndex > 0 ? regressionTests.get(nearestIndex - 1) : null;
    }

    // 提取公共逻辑：通过 appId 获取数据并解析成 List<String>
    private List<String> getEventData(String id) throws Exception {
        String url = "https://sigma.sankuai.com/api/v1/open/calendar?appId=" + id;
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("GET");

        // 读取响应内容
        try (InputStream is = conn.getInputStream();
                BufferedReader in = new BufferedReader(new InputStreamReader(is))) {
            StringBuilder data = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                data.append(line);
            }
            return parseCalendar(data.toString());
        }
    }

    @GetMapping("/currentEvent")
    public String findCurrentEvent(String id) throws Exception {
        // 调用公共方法获取数据
        List<String> eventData = getEventData(id);
        LocalDateTime currentTime = LocalDateTime.now();

        // 调用特定的业务逻辑，获取当前时间所在的阶段
        String currentStage = getCurrentStage(eventData, currentTime);
        return currentStage;
    }

    @GetMapping("/nearestRegressionTest")
    public String nearestRegressionTest(String id) throws Exception {
        // 调用公共方法获取数据
        List<String> eventData = getEventData(id);
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 对 List 进行排序，按照开始时间从小到大（数据格式：开始时间|结束时间|版本|状态）
        eventData.sort(Comparator.comparing(str -> {
            // 按照 "|" 分割字符串，并取第一个部分（开始时间字符串）
            String dateStr = str.split("\\|")[0];
            // 转换为 LocalDateTime 对象
            return LocalDateTime.parse(dateStr, formatter);
        }));

        // 调用特定的业务逻辑，获取离当前时间最近的回归测试阶段
        String nearestRegressionTest = findNearestRegressionTest(eventData);
        return nearestRegressionTest;
    }

    @GetMapping("/previousRegressionTest")
    public String previousRegressionTest(@RequestParam String id) throws Exception {
        List<String> eventData = getEventData(id);
        return findPreviousRegressionTest(eventData);
    }

    @GetMapping("/fullCalendar")
    public String getFullCalendar(@RequestParam String id) throws Exception {
        // 调用公共方法获取数据
        List<String> eventData = getEventData(id);

        // 对 List 进行排序，按照开始时间从小到大（数据格式：开始时间|结束时间|版本|状态）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        eventData.sort(Comparator.comparing(str -> {
            String dateStr = str.split("\\|")[0]; // 使用开始时间排序
            return LocalDateTime.parse(dateStr, formatter);
        }));

        // 将每个元素添加换行符，并连接成一个字符串
        return String.join("\n", eventData);
    }
}
