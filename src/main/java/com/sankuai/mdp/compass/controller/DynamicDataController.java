package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicData;
import com.sankuai.mdp.compass.service.DynamicDataService;
import org.json.JSONArray;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by xieyongrui on 2019/11/21.
 */

@RestController
@RequestMapping("/compass/api/data")
public class DynamicDataController extends BaseController{
    @Autowired
    private DynamicDataService dynamicDataService;

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, DynamicData dynamicData)  {
        IPage<DynamicData> dynamicDataIPage = this.dynamicDataService.list(request,dynamicData);
        if (dynamicDataIPage != null) {
            return getDataTable(dynamicDataIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/add")
    public Boolean add(@RequestBody String body) {
        return dynamicDataService.add(new JsonParser().parse(body).getAsJsonObject());
    }

    public DynamicData search(String template){

        return dynamicDataService.find(template);
    }
    @PostMapping("/updateApi")
    public int updateApi(@RequestParam int id, @RequestBody String body) {
        return dynamicDataService.updateMbcProperty(id,new JsonParser().parse(body).getAsJsonObject(),"apiData");
    }
    @PostMapping("/updateTemplate")
    public int updateTemplateData(@RequestParam int id, @RequestBody String body) {
        return dynamicDataService.updateMbcProperty(id,new JsonParser().parse(body).getAsJsonObject(),"templateData");
    }

    @GetMapping("/groupingSmokeCase")
    public String groupingSmokeCase(@RequestBody() JSONObject body) {
        return dynamicDataService.groupingSmokeCase(body.toString());
    }
}
