package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.AbnormalUIList;
import com.sankuai.mdp.compass.entity.AbnormalUi;
import com.sankuai.mdp.compass.entity.JobDetail;
import com.sankuai.mdp.compass.entity.LayoutMgeDetail;
import com.sankuai.mdp.compass.service.JobDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by xieyongrui on 2019/11/9.
 */
@RestController
@RequestMapping("/compass/api/jobDetail")
public class JobDetailController extends BaseController {
    @Autowired
    private JobDetailService jobDetailService;

    @GetMapping("/list")
    public Map<String, Object> getJobDetails(QueryRequest request, JobDetail jobDetail) {

        IPage<JobDetail> JobDetailsJobIPage = this.jobDetailService.list(request, jobDetail);
        if (JobDetailsJobIPage != null) {
            return getDataTable(JobDetailsJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/mgeList")
    public Map<String, Object> getMgeList(@RequestParam("jobId") String jobId ) {

        IPage<LayoutMgeDetail> JobDetailsJobIPage = this.jobDetailService.getMgeList(jobId);
        if (JobDetailsJobIPage != null) {
            return getDataTable(JobDetailsJobIPage);
        } else {
            return null;
        }
    }

    @PutMapping("add")
    public void add(JobDetail jobDetail) {
        jobDetailService.add(jobDetail);
    }

    @RequestMapping("/abnormal")
    public Map<String, Object> getAbnormalUIList(QueryRequest request, JobDetail jobDetail) {
        IPage<AbnormalUi> JobDetailsJobIPage = this.jobDetailService.abnormalList(request, jobDetail ,"abnormal");
        if (JobDetailsJobIPage != null) {
            return getDataTable(JobDetailsJobIPage);
        } else {
            return null;
        }
    }
    @RequestMapping("/abnormal/v2")
    public Map<String, Object> getAbnormalUIListv2(QueryRequest request, JobDetail jobDetail) {
        IPage<AbnormalUIList> JobDetailsJobIPage = this.jobDetailService.abnormalListv2(request, jobDetail ,"abnormal");
        if (JobDetailsJobIPage != null) {
            return getDataTable(JobDetailsJobIPage);
        } else {
            return null;
        }
    }
    @RequestMapping("/normal")
    public Map<String, Object> getNormalUIList(QueryRequest request, JobDetail jobDetail) {

        IPage<JobDetail> JobDetailsJobIPage = this.jobDetailService.list(request, jobDetail ,"normal");
        if (JobDetailsJobIPage != null) {
            return getDataTable(JobDetailsJobIPage);
        } else {
            return null;
        }
    }

    @RequestMapping("/operation")
    public Map<String, Object> getOperationList(QueryRequest request, JobDetail jobDetail) {

        IPage<JobDetail> JobDetailsJobIPage = this.jobDetailService.list(request, jobDetail ,"operation");
        if (JobDetailsJobIPage != null) {
            return getDataTable(JobDetailsJobIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/reportBug")
    public Boolean reportBug(@RequestBody String body) {
        return this.jobDetailService.reportBug(new JsonParser().parse(body).getAsJsonObject());
    }



}
