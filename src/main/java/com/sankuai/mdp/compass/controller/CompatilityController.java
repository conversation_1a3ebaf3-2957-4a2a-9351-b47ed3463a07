package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicTemplateData;
import com.sankuai.mdp.compass.service.*;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Created by xieyongru<PERSON> on 2019/11/10.
 */
@RestController
@RequestMapping("/compass/api/job")
public class CompatilityController extends BaseController {
    @Autowired
    private CompatilityService compatilityService;
    @Autowired
    private OcrService ocrService;

    @Autowired
    private DynamicProcessService dynamicProcessService;

    @Autowired
    private DynamicTemplateDataService dynamicTemplateDataService;

    @GetMapping("/search")
    public CompatilityJob search(@RequestParam("id") Integer id) {

        return compatilityService.find(id);
    }

    @GetMapping("/getJobInfo")
    public CompatilityJob getJobInfo(CompatilityJob compatilityJob) {
        return compatilityService.getJobInfo(compatilityJob);
    }

    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, CompatilityJob compatilityJob) {
        IPage<CompatilityJob> CompatilityJobIPage = this.compatilityService.list(request, compatilityJob);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/list/aggregate")
    public Map<String, Object> jobAggregateList(QueryRequest request, CompatilityJob compatilityJob) {
        IPage<CompatilityJob> CompatilityJobIPage = this.compatilityService.aggList(request, compatilityJob);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }


    @GetMapping("/list/listAggregation")
    public Map<String, Object> listAggregation(QueryRequest request, CompatilityJob compatilityJob) {
        IPage<CompatilityJob> CompatilityJobIPage = this.compatilityService.listAggregation(request, compatilityJob);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/list/listAggregationSecond")
    public Map<String, Object> listAggregationSecond(QueryRequest request, CompatilityJob compatilityJob) {
        IPage<CompatilityJob> CompatilityJobIPage = this.compatilityService.listAggregationSecond(request, compatilityJob);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/listProcess")
    public Map<String, Object> listProcess(@RequestParam("processId") String processId, @RequestParam("currentPage") String currentPage, @RequestParam("type") Integer testType) {
        IPage<CompatilityJob> CompatilityJobIPage = this.compatilityService.listProcess(processId, currentPage, testType);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/new")
    public Map<String, Object> createV2(CompatilityJob dynamicJob) {
        JsonObject jsonObject = compatilityService.createV2(dynamicJob);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        try {
            System.out.println(map.get("success"));
            if (map.get("success").equals(true)) {
                dynamicProcessService.updateProcess(dynamicJob);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    @PostMapping("/recognizeUIBug")
    public Map<String, Object> recognizeUIBug(String picUrl) {
        Map<String, Object> map = new HashMap<>();
        String[] result = ocrService.recognizeUIBugByOnePic(picUrl);
        map.put("result", result[0]);
        map.put("mergedUrl", result[1]);
        if (result[0].isEmpty() || result[1].isEmpty()) {
            map.put("success", false);
        } else {
            map.put("success", true);
        }
        return map;
    }

    @PostMapping("/smoke")
    public Map<String, Object> createSmokeTest(CompatilityJob compatilityJob) {
        /* 07.15取消自动测试，只保留手动测试
        JSONObject jsonObject = compatilityService.createSmokeJob(compatilityJob);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
         */
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("msg", "不触发自动测试");
        map.put("success", true);
        map.put("reportUrl", "http://w.sankuai.com/ptqa/compass_new_web/index.html#/dlautotest/jobList");
        return map;
    }

    @PostMapping("/smokeByMbc")
    public Map<String, Object> createSmokeTestByMbc(CompatilityJob compatilityJob) {
        JSONObject jsonObject = compatilityService.createSmokeJob(compatilityJob);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        return map;
    }

    @PostMapping("/smokeByCompass")
    public Map<String, Object> createSmokeTestByCompass(@RequestBody String body, CompatilityJob compatilityJob) {
        JsonObject request = new JsonParser().parse(body).getAsJsonObject();
        System.out.println(request);
        JsonObject r = request.get("payload").getAsJsonObject();
        compatilityJob.setCreatedBy(r.get("createdBy").getAsString());
        compatilityJob.setMockData(r.get("templateData").getAsString());
        compatilityJob.setType(r.get("type").getAsInt());
        compatilityJob.setTemplateName(r.get("templateName").getAsString());
        compatilityJob.setBusiness(r.get("business").getAsString());
        compatilityJob.setCaseName(r.get("caseName").getAsString());
        JsonElement apkUrlJson = r.get("apkUrl");
        JsonElement ipaUrlJson = r.get("imeituanUrl");
        String apkUrl = "";
        String imeituanUrl = "";
        if (apkUrlJson != null) {
            apkUrl = apkUrlJson.getAsString();
        }
        if (ipaUrlJson != null) {
            imeituanUrl = ipaUrlJson.getAsString();
        }
        compatilityJob.setApkUrl(apkUrl);
        compatilityJob.setImeituanUrl(imeituanUrl);

        JSONObject jsonObject = compatilityService.createSmokeJob(compatilityJob);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        return map;
    }

    @PutMapping("/updateCheckStatus")
    public void updateCheckStatus(CompatilityJob compatilityJob) {
        compatilityService.updateCheckStatus(compatilityJob);
    }

    @PutMapping("/updateJobId")
    public void updateJobId(CompatilityJob compatilityJob) {
        compatilityService.updateJobId(compatilityJob);
    }

    @PutMapping("/updataReport")
    public void updateReport(CompatilityJob compatilityJob) {
        compatilityService.updateReport(compatilityJob);
    }

    @PutMapping("/updateEventConanId")
    public void updateEventConanId(CompatilityJob compatilityJob) {
        compatilityService.updateEventConanId(compatilityJob);
    }

    @PutMapping("updateJobStatus")
    public void updateJobStatus(CompatilityJob compatilityJob) {
        compatilityService.updateStatusById(compatilityJob);
    }

    @PutMapping("updateJobStatusByJenkinsId")
    public void updateJobStatusByJenkinsId(CompatilityJob compatilityJob) {
        compatilityService.updateStatusByJenkinsId(compatilityJob);
    }


    @PutMapping("/updateFinishTime")
    public void updateFinishTime(CompatilityJob compatilityJob) {
        compatilityService.updateFinishTime(compatilityJob);
    }

    @PutMapping("/updateEventFinishTime")
    public void updateEventFinishTime(CompatilityJob compatilityJob) {
        compatilityService.updateEventFinishTime(compatilityJob);
    }

    @PutMapping("/updateCheckTime")
    public void updateCheckTime(CompatilityJob compatilityJob) {
        compatilityService.updateCheckTime(compatilityJob);
    }

    @GetMapping("/getEventId")
    public String getEventIdByJobId(@RequestParam("jobId") Integer jobId, @RequestParam("templateName") String templateName) {
        return compatilityService.getEventId(jobId, templateName);
    }

    @PostMapping("/updateFailedReason")
    public int changeFailedReason(int id, String failedReason) {
        return compatilityService.changeFailedReason(id, failedReason);
    }

    @PostMapping("/newCreate")
    public void newCreate(CompatilityJob compatilityJob) {
        System.out.println(compatilityJob);
//        return compatilityService.changeFailedReason(id,failedReason);
    }

    @GetMapping("/setAutoResult")
    public void setAutoResult(@RequestParam("jobId") int jobId, @RequestParam("result") String result) {
        compatilityService.updateAutoResult(jobId, result);
    }

    @GetMapping("/cancel")
    public Resp cancel(@RequestParam("jobId") int jobId) {
        return compatilityService.cancel(jobId);
    }

    @GetMapping("/weekReport")
    public HashMap weekReport() {
        return compatilityService.weekReport();
    }

    // 上传动态布局模板数据
    @PostMapping("/uploadTemplateData")
    public Map<String, Object> uploadTemplateData(DynamicTemplateData dynamicTemplateData) {
        JSONObject jsonObject = dynamicTemplateDataService.uploadData(dynamicTemplateData);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        return map;
    }

    // 查询动态布局模板数据
    @GetMapping("/getTemplateData")
    public Map<String, Object> getTemplateData(QueryRequest request, DynamicTemplateData dynamicTemplateData) {
        IPage<DynamicTemplateData> dynamicTemplateDataIPage = dynamicTemplateDataService.getTemplateData(request, dynamicTemplateData);
        if (dynamicTemplateDataIPage != null) {
            return getDataTable(dynamicTemplateDataIPage);
        } else {
            return null;
        }
    }
}
