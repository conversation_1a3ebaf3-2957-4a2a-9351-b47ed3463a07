package com.sankuai.mdp.compass.controller;

import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by xieyongrui on 2020/9/1.
 */

@Slf4j
@Validated
@RestController
@RequestMapping("/compass/api/user")
public class UserController extends BaseController{
    @GetMapping("/current")
    public Resp userInfo(@RequestHeader(name = "access-token") String ssoid) {
//        String a = "eAGFkCtLBFEYhjkismySScaJuxOG831zLt-Y3B1XjF6CYJE9l4n6BwyuRbQsTNOkuLBB8NL2NxgsimAwqpMsGzQs4oiY7Q8vz_PW2Ozd_TULH5_ei0mM9dzphFubkJkPnQGDZHlXeC6shJRARpwbbqTI01y3H1jQ3PBm3fptv0t6ETW1MplpKUWWpUQcsZ22Oh0ErZfC_vn4dhI3GP47TD9KC1PLX0X_6DNeGY0Pnj_ifRbVZ1bXsh3ng-BlMCxvzl4PL95Oe-XloLzqzU2He6PjZuMXLljtT-yExZAog05xDkgaNEktlAIDVZZypPgWKEBFKk2QQ7oZSocVWL1gIiWs8yZxYHWXKDFeo5TfcQ5mTA**eAEFwYERADAEBLCVWv-KcTjsP0KTTLRZazHQ1bL2Rh7OIRkL53b67NUM8VAMZPZY3cpyVX5B0xGP";
        com.sankuai.meituan.auth.vo.User user = UserUtils.getUser(ssoid);
//        if(null == user){

//            User userDefault = new User();
//            userDefault.setLogin("QA");
//            return Resp.success(userDefault);
//        }
        return Resp.success(user);
    }
}
