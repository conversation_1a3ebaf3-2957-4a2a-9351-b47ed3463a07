package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.MSSUtil;
import com.sankuai.mdp.compass.entity.DynamicSdkUiDiff;
import com.sankuai.mdp.compass.service.DynamicSdkUiDiffService;
import net.minidev.json.JSONObject;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/compass/api/diff")
public class DynamicSdkXmlPicController extends BaseController {

    @Autowired
    private DynamicSdkUiDiffService dynamicSdkPicService;

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, @RequestParam("reportId") Integer reportId) {
        IPage<DynamicSdkUiDiff> dynamicDataIPage = dynamicSdkPicService.list(request, reportId);
        if (dynamicDataIPage != null) {
            return getDataTable(dynamicDataIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/errorList")
    public Map<String, Object> errorList(QueryRequest request, @RequestParam("reportId") Integer reportId) {
        IPage<DynamicSdkUiDiff> dynamicDataIPage = dynamicSdkPicService.errorList(request, reportId);
        if (dynamicDataIPage != null) {
            return getDataTable(dynamicDataIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/localpicdiff")
    public String LocalPicDiff(@RequestParam("jobId") Integer jenkinsId, @RequestParam("platform") String platform) {
        return dynamicSdkPicService.startLocalPicDiff(jenkinsId, platform);
    }

    @PostMapping("/PicUrlDiff")
    public String PicUrlDiff(@RequestBody() JSONObject body) {
        String platform = body.get("platform").toString();
        String testPicMapStr = body.get("testPicMapStr").toString();
        Integer jenkinsId = body.getAsNumber("jenkinsId").intValue();
        String isUpdate = body.getAsString("update");
        return dynamicSdkPicService.startPicUrlDiff(jenkinsId, platform, testPicMapStr, Boolean.parseBoolean(isUpdate));
    }

    @PostMapping("/createBug")
    public String createBug(@RequestBody net.sf.json.JSONObject body){
        JSONArray buglist = body.getJSONArray("buglist");
        if (buglist.size()==0){
            return null;
        }
        return dynamicSdkPicService.createBug(buglist);
    }

}
