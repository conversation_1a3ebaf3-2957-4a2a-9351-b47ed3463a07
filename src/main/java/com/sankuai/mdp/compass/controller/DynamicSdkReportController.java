package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicSdkReport;
import com.sankuai.mdp.compass.service.DynamicSdkReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/diff")
public class DynamicSdkReportController extends BaseController {

    @Autowired
    private DynamicSdkReportService dynamicReportService;

    @GetMapping("/reportList")
    public Map<String, Object> list(QueryRequest request) {
        IPage<DynamicSdkReport> dynamicReportIPage = dynamicReportService.list(request);
        if (dynamicReportIPage != null) {
            return getDataTable(dynamicReportIPage);
        } else {
            return null;
        }
    }
}
