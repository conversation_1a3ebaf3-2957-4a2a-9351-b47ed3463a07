package com.sankuai.mdp.compass.controller;

import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.JenkinsJob;
import com.sankuai.mdp.compass.service.JenkinsJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by xieyongrui on 2019/11/21.
 */

@RestController
@RequestMapping("compass/api/jenkinsJob")
public class JenkinsJobController {
    @Autowired
    private JenkinsJobService jenkinsJobService;

    @PutMapping("/add")
    public String create(<PERSON><PERSON>ob jenkinsJob) {
        return jenkinsJobService.add(jenkinsJob);
    }

    @PostMapping("/updateJobStartTime")
    public void updateJobStartTime(<PERSON><PERSON><PERSON> jenkinsJob) { jenkinsJobService.updateJobStartTime(jenkinsJob); }

    @PutMapping("/updateSubmitFinishTime")
    public void updateSubmitFinishTime(<PERSON><PERSON>ob jenkinsJob){
        jenkinsJobService.updateSubmitFinishTime(jenkinsJob);
    }

    @PutMapping("/updateCaseRunTime")
    public void updateCaseRunTime(JenkinsJob jenkinsJob){
        jenkinsJobService.updateCaseRunTime(jenkinsJob);
    }

    @PutMapping("/updateCaseFinishTime")
    public void updateCaseFinishTime(JenkinsJob jenkinsJob){
        jenkinsJobService.updateCaseFinishTime(jenkinsJob);
    }

    @PutMapping("/updateJobFinishTime")
    public void updateJobFinishTime(JenkinsJob jenkinsJob){
        jenkinsJobService.updateJobFinishTime(jenkinsJob);
    }

    @PutMapping("/delete")
    public void delete(JenkinsJob jenkinsJob) {
        jenkinsJobService.delete(jenkinsJob);
    }

}
