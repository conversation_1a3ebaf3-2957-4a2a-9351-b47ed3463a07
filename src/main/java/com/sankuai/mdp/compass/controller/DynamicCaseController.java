package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicCase;
import com.sankuai.mdp.compass.entity.DynamicPage;
import com.sankuai.mdp.compass.service.DynamicCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by sunkangtong on 2021/3/9.
 */

@RestController
@RequestMapping("/compass/api/case")
public class DynamicCaseController extends BaseController{
    @Autowired
    private DynamicCaseService dynamicCaseService;

    @PostMapping("/add")
    public Map<String, Object> add(@RequestBody String body, DynamicCase dynamicCase) {
        JsonObject jsonObject = dynamicCaseService.add(new JsonParser().parse(body).getAsJsonObject(),dynamicCase);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        return map;
    }


    @GetMapping("/caseList")
    public Map<String, Object> caseList(QueryRequest request, DynamicCase dynamicCase) {
        IPage<DynamicCase> DynamicCaseIPage = this.dynamicCaseService.caseList(request, dynamicCase);
        if (DynamicCaseIPage != null) {
            return getDataTable(DynamicCaseIPage);
        } else {
            return null;
        }
    }


    @GetMapping("/childList")
    public Map<String, Object> childList(@RequestParam("templateName") String templateName) {
        IPage<DynamicCase> DynamicCaseIPage = this.dynamicCaseService.childList(templateName);
        if (DynamicCaseIPage != null) {
            return getDataTable(DynamicCaseIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/pageNameList")
    public List<String> pageNameList() {
        return this.dynamicCaseService.pageNameList();
    }


}
