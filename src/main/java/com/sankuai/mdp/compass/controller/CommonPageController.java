package com.sankuai.mdp.compass.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dianping.zebra.util.StringUtils;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.robust.entity.CommonPage;
import com.sankuai.mdp.compass.service.CommonPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/compass/api/commonPage")
public class CommonPageController extends BaseController{
    @Autowired
    private CommonPageService commonPageService;

    @GetMapping("/getAllAppName")
    public List<String> getAllAppName() {
        return commonPageService.getAllAppName();
    }

    /**
     * 根据业务线、业务组、模块、页面描述、页面类型获取schema列表
     * 
     * @param bg 事业群
     * @param bu 事业部
     * @param biz 业务模块
     * @param pageDescription 页面描述
     * @param pageType 页面类型
     * @return 符合要求的schema列表
     */
    @GetMapping({"/getSchema"})
    public Map<String, String> getSchema(@RequestParam(value = "bg", required = false) String bg,
            @RequestParam(value = "bu", required = false) String bu,
            @RequestParam(value = "biz", required = false) String biz,
            @RequestParam(value = "pageDescription", required = false) String pageDescription,
            @RequestParam(value = "pageType", required = false) String pageType) {
        return commonPageService.getSchema(bg, bu, biz, pageDescription, pageType);
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, CommonPage commonPage) {
        IPage<CommonPage> CompatilityJobIPage = this.commonPageService.list(request, commonPage);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/update")
    public Resp update(CommonPage commonPage) {
        return commonPageService.update(commonPage);
    }

    @PostMapping("/delete")
    public Resp delete(CommonPage commonPage) {
        return commonPageService.delete(commonPage);
    }

    @PostMapping("/add")
    public Resp add(CommonPage commonPage) {
        return commonPageService.add(commonPage);
    }

    @PostMapping("/batchAdd")
    public Resp batchAdd(@RequestBody CommonPage[] commonPages) {
        for (CommonPage commonPage : commonPages) {
            commonPageService.add(commonPage);
        }
        return Resp.success();
    }

    @GetMapping("/getAllPageDescription")
    public HashMap<String, Integer> getAllPageDescription() {
        return commonPageService.getAllPageDescription();
    }

    @GetMapping("/getAllBg")
    public HashMap<String, Integer> getAllBg() {return commonPageService.getAllBg();
    }
    @GetMapping("/getAllBu")
    public HashMap<String, Integer> getAllBu() {return commonPageService.getAllBu();
    }
    @GetMapping("/getAllBiz")
    public HashMap<String, Integer> getAllBiz() {return commonPageService.getAllBiz();
    }
    @GetMapping("/getAllPageType")
    public HashMap<String, Integer> getAllPageType() {return commonPageService.getAllPageType();
    }
}
