package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.entity.DynamicIgnore;
import com.sankuai.mdp.compass.entity.DynamicPage;
import com.sankuai.mdp.compass.entity.FailedReason;
import com.sankuai.mdp.compass.mapper.DynamicIgnoreMapper;
import com.sankuai.mdp.compass.mapper.DynamicPageMapper;
import com.sankuai.mdp.compass.service.CustomService;
import com.sankuai.mdp.compass.service.DynamicTemplateService;
import com.sankuai.mdp.compass.service.OcrService;
import net.minidev.json.JSONObject;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * Created by xieyongrui on 2019/11/16.
 */
@RestController
@RequestMapping("/compass/api")
public class CustomController extends BaseController {
    @Autowired
    private CustomService customService;

    @Autowired
    private DynamicTemplateService dynamicTemplateService;

    @Autowired
    private OcrService ocrService;

    @Autowired
    private DynamicIgnoreMapper dynamicIgnoreMapper;

    @Autowired
    private DynamicPageMapper dynamicPageMapper;

    private JenkinsUtil jenkinsUtil = new JenkinsUtil();

    @PostMapping("/addIgnoreName")
    public boolean addIgnoreName(@RequestParam("name") String name) {
        DynamicIgnore dynamicIgnore = new DynamicIgnore();
        dynamicIgnore.setName(name);
        dynamicIgnoreMapper.insert(dynamicIgnore);
        return true;
    }

    @PostMapping("/addDynamicPage")
    public boolean addDynamicPage(@RequestBody JSONObject body) {
        String module = body.getAsString("module");
        String moduleCn = body.getAsString("module_cn");
        String imeituanUrl = body.getAsString("imeituan_url");
        String apiData = body.getAsString("api_data");
        String listPath = body.getAsString("list_path");
        String apiPath = body.getAsString("api_path");
        DynamicPage dynamicPage = new DynamicPage();
        dynamicPage.setApiPath(apiPath);
        dynamicPage.setListPath(listPath);
        dynamicPage.setApiData(apiData);
        dynamicPage.setImeituanUrl(imeituanUrl);
        dynamicPage.setModuleCn(moduleCn);
        dynamicPage.setModule(module);
        dynamicPageMapper.insert(dynamicPage);
        return true;
    }

    @GetMapping("/module")
    public Set<String> module(@RequestParam("address") String address) {
        return customService.getModule(address);
    }

    @GetMapping("/template")
    public List<String> template(@RequestParam("module") String module, String address) {
        return customService.getTemplate(module, address);
    }

    @GetMapping("/business")
    public String business(@RequestParam("module") String module, String address) {
        return customService.getBusinessEnNameByCnName(module, address);
    }

    @GetMapping("/business/list")
    public Map<String, String> business2(@RequestParam("address") String address) {
        return customService.getBusinessInfo(address);
    }

    @GetMapping("/get/mockData")
    public String getMockData(@RequestParam("mockId") Integer mockId) {
        return customService.getMockData(mockId);
    }

    // 云测回调接口
    @PostMapping("/collectDynamicTestResult")
    public String collectDynamicTestResult(@RequestBody JSONObject body) throws Exception {
        return dynamicTemplateService.collectDynamicTestResult(body);
    }

    @PostMapping("json/key/list")
    public String keyList(@RequestBody() String body) {
        return customService.getJsonKey(new JsonParser().parse(body).getAsJsonObject()).toString();
    }

    @GetMapping("json/key/original")
    public Map<String, Object> getOriginalPath(@RequestParam("templateName") String templateName) {
        JsonObject jsonObject = customService.getOriginalKey(templateName);
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        return map;
    }

    @PostMapping("/sql/submit")
    public Boolean submitSql(@RequestBody() JsonObject body) {
        return customService.submitSql(body);
    }

    @GetMapping("/testPicDiff")
    public String testPicDiff() {
        ComUtil comUtil = new ComUtil();
        String DIFF = "/opt/meituan/script/diff/ssim.py";
        String picUrl = "http://p1.meituan.net/ptautotest/2fe04b5ec1b4aca082df8273b2348fa5150914.png";
        String basePic = "http://p0.meituan.net/ptautotest/3197dbfbb7dec1334e95d7c615026ea5212499.png";
        String resultPicPath = System.getProperty("user.dir") + "/" + "diff.png";
        String result = comUtil.doPython(DIFF, picUrl + " " + basePic + " " + resultPicPath);
        try {
            FileUtil fileUtil = new FileUtil();
            fileUtil.delete(resultPicPath);
            return result;
        } catch (NoSuchFieldError e) {
            return e.getMessage();
        }
    }

    @GetMapping("/listFailedReason")
    public List<String> dynamicFailedReason() {
        return dynamicTemplateService.failedReasonForDynamicReportList();
    }

    @PostMapping("/failedReason/DynamicSubmit")
    public Boolean dynamicSubmit(FailedReason failedReason) {
        dynamicTemplateService.failedReasonAdd(failedReason);
        return true;
    }

    @PostMapping("/ocrNew")
    public String getOcrNewText(@RequestParam("url") String url) {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.getTextFromAI(url).toString();
    }

    @PostMapping("/ocrNewLocation")
    public String getOcrNewTextLocation(@RequestParam("url") String url) {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.getTextAndLocationFromAI(url).toString();
    }


    @GetMapping("/parserImageForXray")
    public String parserImageForXray(@RequestParam("imageUrl") String imageUrl) {
        OcrUtil ocrUtil = new OcrUtil();
        String xrayResult = ocrUtil.get0crMessage(imageUrl);
        return xrayResult;
    }

    @GetMapping("/getCardData")
    public String getMbcCardData(@RequestParam("templateName") String templateName,
            @RequestParam("business") String business) {
        MbcUtil mbcUtil = new MbcUtil();
        return mbcUtil.getCardData(templateName, business, "");
    }

    @PostMapping("/getMockRule")
    public Map<String, Object> getMockRule(@RequestBody String body) {
        JsonObject jsonObject = customService.getMockRule(new JsonParser().parse(body).getAsJsonObject());
        Map<String, Object> map = new HashMap<String, Object>();
        Gson gson = new Gson();
        map = gson.fromJson(jsonObject.toString(), map.getClass());
        return map;
    }

    @PostMapping("/ocrUIBug")
    public String recognizeUIBugByOcr(@RequestParam("url") String url) {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.recognizeUIBug(url);
    }

    /**
     * 通过AI识别UI bug
     *
     * @param url 识别的图片URL
     * @return 识别结果
     */
    @PostMapping("/ocrUIBugByAI")
    public String recognizeUIBugByAI(@RequestParam("url") String url) {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.recognizeUIBugByAI(url);
    }

    /**
     * 通过AI检测图片是否模糊
     *
     * @param url 检测的图片URL
     * @return 检测结果
     */
    @PostMapping("/blurDetect")
    public String blurDetect(@RequestParam("url") String url) {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.blurDetect(url);
    }

    /**
     * 通过AI检测图片是否模糊
     *
     * @param url 检测的图片URL
     * @param imageType 图片类型
     * @param type 检测类型
     * @param lineThreshold 线框阈值
     * @return 检测结果
     */
    @PostMapping("/blurDetectByAI")
    public String blurDetectByAI(@RequestParam("url") String url, @RequestParam("imageType") String imageType,
            @RequestParam("type") String type, @RequestParam("lineThreshold") String lineThreshold) throws TException {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.blurDetectByAI(url, Integer.parseInt(imageType), type, Float.parseFloat(lineThreshold));
    }

    @PostMapping("/ocr/diff")
    public String ocrDiff(@RequestParam("burl") String burl, @RequestParam("rurl") String rurl,
            @RequestParam("compareModel") String compareModel, @RequestParam("cutModel") String cutModel,
            @RequestParam("lineConfidence") String lineConfidence, @RequestParam("ignoreArea") String ignoreArea)
            throws TException {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.diff(burl, rurl, compareModel, cutModel, lineConfidence, ignoreArea);
    }

    @GetMapping("/getLinglong")
    public String getLinglong(@RequestParam("moduleId") Integer moduleId, String platform, String version) {
        JsonObject req = customService.getLingLongData(moduleId, platform, version);
        return req.toString();
    }

    /**
     * platform:0-ios,1-android
     */
    @GetMapping("/getCategoryData")
    public String getCategory(int platform, String version) {
        String req = customService.getCategoryData(platform, version);
        return req;
    }

    @GetMapping("/getCategoryList")
    public String getCategoryList(int platform, String version, String cities) {
        String req = customService.getCategoryList(platform, version, cities);
        return req;
    }

    @PostMapping("/uiPageParse")
    public String recognizeUiPageParseByOcr(@RequestParam("url") String url) {
        if (ocrService == null) {
            return "ocrService is null";
        }
        return ocrService.recognizeUiPageParse(url);
    }

    /**
     * 获取文字中心坐标
     *
     * @param text 文字内容
     * @param picUrl 图片地址
     * @return 文字中心坐标
     */
    @GetMapping("/getTextCenterPosByOcr")
    public Resp getTextCenterPosByOcr(String text, String picUrl) {
        return ocrService.getTextCenterPosByOcr(text, picUrl);
    }

    /**
     * 全局图片查找
     *
     * @param baseUrl 全局图片
     * @param targetUrl 目标匹配图片地址
     * @return 匹配成功，返回匹配图像中心坐标；未匹配成功，返回null
     */
    @GetMapping("/getMatchPicLocation")
    public Resp getMatchPicLocation(@RequestParam("baseUrl") String baseUrl, @RequestParam("targetUrl") String targetUrl) throws IOException, InterruptedException {
        return ocrService.getMatchPicLocation(baseUrl, targetUrl);
    }

}
