package com.sankuai.mdp.compass.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.*;
import com.sankuai.mdp.compass.service.CompatilityService;
import com.sankuai.mdp.compass.service.DynamicCaseService;
import com.sankuai.mdp.compass.service.DynamicProcessService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.sankuai.security.sdk.SecSdk;
import java.util.*;

/**
 * Created by sunkangtong on 2021/3/9.
 */

@RestController
@RequestMapping("/compass/api/process")
public class DynamicProcessController extends BaseController{
    @Autowired
    private DynamicProcessService dynamicProcessService;


    @PostMapping("/updateProcess")
    public Map<String, Object> updateProcess(CompatilityJob compatilityJob) {
        System.out.print(compatilityJob);
        boolean updateProcess  = dynamicProcessService.updateProcess(compatilityJob);
        return null;
    }

    @GetMapping("/list/listAggregationSecond")
    public Map<String, Object> listAggregationSecond(QueryRequest request, DynamicProcess dynamicProcess)  {
        IPage<DynamicProcess> DynamicProcessIPage = this.dynamicProcessService.listAggregationSecond(request, dynamicProcess);
        if (DynamicProcessIPage != null) {
            return getDataTable(DynamicProcessIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/step")
    public ArrayList getStep(@RequestParam("processId") String processId)  {
        ArrayList stepList = this.dynamicProcessService.getStep(processId);
        if (stepList != null) {
            return stepList;
        } else {
            return null;
        }
    }

    @GetMapping("/updateStep")
    public ArrayList updateStep(@RequestParam("processId") String processId,@RequestParam("current") int current, @RequestParam("clickStatus") String clickStatus, @RequestParam("onesUrl") String onesUrl, @RequestParam("confirmUser") String confirmUser)  {
         ArrayList result = this.dynamicProcessService.updateStep(processId, current, clickStatus, onesUrl,confirmUser);
         return result;
    }

    @GetMapping("/getCurrent")
    public ArrayList getCurrent(@RequestParam("processId") String processId)  {
        ArrayList current = new ArrayList();
        try{
            current = this.dynamicProcessService.getCurrent(processId);
        }catch (Exception e){
            current.add(1);
            current.add(2);
        }
        return current;
    }
    @GetMapping("/stopProcess")
    public boolean updateStep(@RequestParam("processId") String processId )  {
        boolean result = this.dynamicProcessService.stopProcess(processId);
        return result;
    }


    @GetMapping("/getProcessId")
    public int getProcessId(@RequestParam("templateName") String templateName )  {
        int processId = this.dynamicProcessService.getProcessId(templateName);
        return processId;
    }

    @GetMapping("/jobId2ProcessId")
    public int getProcessId(@RequestParam("id") int id )  {
        int processId = this.dynamicProcessService.jobId2ProcessId(id);
        return processId;
    }

    @GetMapping("/setScore")
    public void setScore(@RequestParam("processId") String processId ,@RequestParam("score") String score )  {
        this.dynamicProcessService.setScore(processId,score);
    }

    @GetMapping("/retrySmoke")
    public String retrySmoke(@RequestParam("templateName") String templateName, @RequestParam("business") String business,  @RequestParam("user") String user)  {
        String result = this.dynamicProcessService.retrySmoke(templateName, business, user);
        return result;
    }

    @GetMapping("/getProcessStatus")
    public int getProcessStatus(@RequestParam("templateName") String templateName )  {
        int status = this.dynamicProcessService.getProcessStatus(templateName);
        //todo 模板状态判断有问题，暂时强制返回1，不卡控
        status = 1;
        return status;
    }

    @GetMapping("/allCancle")
    public int allCancle(@RequestParam("processId") int processId, @RequestParam("user") String user, @RequestParam("cancleReason") String cancleReason)  {
        this.dynamicProcessService.allCancle(processId, user, cancleReason);
        return 0;
    }


    @GetMapping("/diff")
    public ArrayList diff(@RequestParam("diffA") String diffA, @RequestParam("diffB") String diffB)  {
        if (!SecSdk.checkSSRF(diffA)||!SecSdk.checkSSRF(diffB)) {
            System.out.println("危险，可能是SSRF攻击请求");
        }
        StringUtil st = new StringUtil();
        if (st.isBlank(diffA) || st.isBlank(diffB)){
            return null;
        }
        ArrayList ans = this.dynamicProcessService.xmlDiff(diffA,diffB);

        return ans;
    }



    @GetMapping("/completeTemplate")
    public ArrayList completeTemplate(@RequestParam("startDate") Date startDate, @RequestParam("endDate") Date endDate)  {

        ArrayList<DynamicProcess> list =  this.dynamicProcessService.completeTemplate(startDate,endDate);

        return list;
    }

    @GetMapping("/onlineTemplate")
    public ArrayList onlineTemplate(@RequestParam("startDate") Date startDate, @RequestParam("endDate") Date endDate)  {

        ArrayList<ChangeComplianceTemplate> list =  this.dynamicProcessService.onlineTemplate(startDate,endDate);

        return list;
    }

    @PostMapping("/sendTemplateStatus")
    public void sendTemplateStatus(DynamicTemplatePublishDetail dynamicTemplatePublishDetail) {
        System.out.print(dynamicTemplatePublishDetail);
        dynamicProcessService.updatePublish(dynamicTemplatePublishDetail);
    }


}
