package com.sankuai.mdp.compass.controller;

import com.sankuai.mdp.compass.entity.AutotestTask;
import com.sankuai.mdp.compass.mapper.AutotestTaskMapper;
import com.sankuai.mdp.compass.robust.entity.CommonPage;
import com.sankuai.mdp.compass.service.AutotestTaskSevice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by xieyongrui on 2019/12/7.
 */

@RestController
@RequestMapping("compass/api/task")
public class AutotestTaskController {
    @Autowired
    AutotestTaskSevice autotestTaskSevice;

    @PutMapping("/add")
    public void create(AutotestTask autotestTask) {
        autotestTaskSevice.add(autotestTask);
    }

    @PutMapping("/updateTaskFinishTime")
    public void updateCaseFinishTime(AutotestTask autotestTask){
        autotestTaskSevice.updateTaskFinishTime(autotestTask);
    }

}
