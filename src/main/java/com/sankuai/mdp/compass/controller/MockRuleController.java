package com.sankuai.mdp.compass.controller;

import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.service.MockRuleService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by xieyongrui on 2019/11/29.
 */
public class MockRuleController extends BaseController{
    @Autowired
    private MockRuleService mockRuleService;

    public void setRule(Integer id, String key, String rule, String api) { mockRuleService.setMockRule(id,key,rule,api);}


}
