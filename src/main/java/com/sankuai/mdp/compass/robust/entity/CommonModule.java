package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/27 8:38 下午
 */
@Data
@TableName("common_module")
public class CommonModule {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String moduleDescription;
    private String pageId;
    private String moduleName;
}
