package com.sankuai.mdp.compass.robust.controller;

import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.robust.service.RobustResultService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description:
 * @author: xin<PERSON><PERSON><PERSON>
 * @date: 2022/4/28 下午8:29
 */
@RestController
@RequestMapping("/compass/api/RobustJob")
public class RobustResultController extends BaseController {
    @Autowired
    RobustResultService robustResultService;


    /**
     *case维度更新result
     */
    @PostMapping("/updateRobustResult")
    public Resp updateRobustResult(@RequestBody JSONObject body){
        return robustResultService.updateRobustResult(body);
    }

    @PostMapping("/preConanCallback")
    public Resp preConanCallback(@RequestBody JSONObject conanBody) {
        return robustResultService.preConanCallback(conanBody);
    }

    @PostMapping("/recurCaseMockdata")
    public Resp recurCaseMockdata(@RequestBody String data){
        return robustResultService.recurCaseMockdata(data);
    }


}
