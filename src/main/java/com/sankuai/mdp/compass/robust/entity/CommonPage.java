package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/27 8:26 下午
 */
@Data
@TableName("common_page")
public class CommonPage {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String pageDescription;
    private String schemaAndroid;
    private String schemaIos;
    private String whiteApi;
    private String blackApi;
    private String appName;
    private Integer androidComponentId;
    private Integer iosComponentId;
    private String creator;
    private String bu;
    private String bg;
    private String biz;
    private String pageType;
}
