package com.sankuai.mdp.compass.robust.config;

import com.sankuai.mdp.compass.common.enums.Significance;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by skt on 2022/9/14.
 */
public class RobustCaseConfig {

    public HashMap<Significance,ArrayList<String>> caseConfig = new HashMap();
    public RobustCaseConfig() {
        ArrayList intList =  new ArrayList<>();

        intList.add("null");
        intList.add("tooBig");
        intList.add("deleteKey");

        ArrayList boolList = new ArrayList();

        boolList.add("null");
        boolList.add("boolReverse");
        boolList.add("deleteKey");

        ArrayList FLOATofStringList = new ArrayList();

        FLOATofStringList.add("0");
        FLOATofStringList.add("空串");
        FLOATofStringList.add("超长");
        FLOATofStringList.add("deleteKey");

        ArrayList ContentList = new ArrayList();

        ContentList.add("null");
        ContentList.add("超长");
        ContentList.add("空串");
        ContentList.add("deleteKey");

        ArrayList arrayList = new ArrayList();

        arrayList.add("null");
        arrayList.add("[]");
        arrayList.add("deleteKey");

        ArrayList DictList = new ArrayList();
        DictList.add("null");
        DictList.add("deleteKey");

        ArrayList intOfStringList = new ArrayList();
        intOfStringList.add("0");
        intOfStringList.add("null");
        intOfStringList.add("超长");
        intOfStringList.add("空串");
        intOfStringList.add("deleteKey");

        ArrayList JsObjectList = new ArrayList();
        JsObjectList.add("deleteKey");

        ArrayList SchemeList = new ArrayList();

        SchemeList.add("ulrSuffix");
        SchemeList.add("urlEnCode");
        SchemeList.add("urlDeCode");
        SchemeList.add("deleteKey");

        ArrayList MillisecondList = new ArrayList();
        MillisecondList.add("deleteKey");

        ArrayList FLOATList = new ArrayList();
        FLOATList.add("deleteKey");

        ArrayList SecondList = new ArrayList();
        SecondList.add("deleteKey");

        ArrayList LinkList = new ArrayList();
        LinkList.add("ulrSuffix");
        LinkList.add("urlEnCode");
        LinkList.add("urlDeCode");
        LinkList.add("deleteKey");

        ArrayList ColorList = new ArrayList();
        ColorList.add("deleteKey");
        ColorList.add("wellNum");
        ColorList.add("超长");
        ColorList.add("空串");
        ArrayList JsonStringList = new ArrayList();
        JsonStringList.add("deleteKey");


        caseConfig.put(Significance.INT,intList);
        caseConfig.put(Significance.Bool,boolList);
        caseConfig.put(Significance.FLOATofString,FLOATofStringList);
        caseConfig.put(Significance.Content,ContentList);
        caseConfig.put(Significance.Array,arrayList);
        caseConfig.put(Significance.Dict,DictList);
        caseConfig.put(Significance.INTofString,intOfStringList);
        caseConfig.put(Significance.JsObject,JsObjectList);
        caseConfig.put(Significance.Scheme,SchemeList);
        caseConfig.put(Significance.FLOAT,FLOATList);
        caseConfig.put(Significance.Millisecond,MillisecondList);
        caseConfig.put(Significance.Second,SecondList);
        caseConfig.put(Significance.Link,LinkList);
        caseConfig.put(Significance.Color,ColorList);
        caseConfig.put(Significance.JsonString,JsonStringList);


    }
}
