package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by skt on 2022/4/26
 */
@Data
@TableName("robust_build")
public class RobustBuild {
    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private int status;
    private String buildUrl;
    private String totalBuildUrl;
    private String apkUrl;
    private String platform;
    private String pageId;
    private String schemaUrl;
    private String moudleId;
    private String abConfig;
    private String hornConfig;
    private String baseData;
    private String featureId;
    private int caseCount;
    private String conanJobId;
    private String finishedConanJobId;
    private String creator;
    private String cancelBy;
    private String checkBy;
    private int checkResult;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    private float totalTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueUpdateTime;

    private String caseDesc;

    @TableField(exist = false)
    private String mode;

    private int triggerId;

    private String apiCaseCount;

    private String appName;

}