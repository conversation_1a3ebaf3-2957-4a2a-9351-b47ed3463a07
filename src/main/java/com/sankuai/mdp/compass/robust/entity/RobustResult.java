package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/27 8:14 下午
 */
@Data
@TableName("robust_result")
public class RobustResult {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private int buildId;
    private int conanJobId;
    private int conanTaskId;
    private int conanCaseId;
    private String testConfig;
    private String modifyDetail;
    private int caseResult;
    private int crashResult;
    private int commentState;
    private String failReason;
    private String pic;

    private String issueKey;
    private String issueStatus;
    private String issueAssignee;

    private String crashInfo;
    private String crashHash;
    private String crashUuid;
    private String crashVersion;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;
    private float totalTime;
    private String modifyUrl;
    private String caseData;
    private String appName;
}
