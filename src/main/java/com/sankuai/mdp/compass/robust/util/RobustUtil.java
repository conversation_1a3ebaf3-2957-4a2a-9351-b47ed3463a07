package com.sankuai.mdp.compass.robust.util;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.*;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.enums.Significance;
import com.sankuai.mdp.compass.common.utils.MockUtil;
import com.sankuai.mdp.compass.robust.config.RobustCaseConfig;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by skt on 2022/9/14.
 */
@Slf4j
public class RobustUtil {
    public static Object getTargetValue(String caseMode, Object originValue) {
        try {
            originValue = originValue.toString();
        } catch (Exception e) {
            originValue = "mock";
        }
        String longCase = "\"\\n\\n\"" + originValue + "\"特殊字符9999!@#$￥%^&*~……·!,.！。，”“”\"\"''()_+|}{}>反括号)(][]颜文字⚖️【富文本待补充】超长超长超长【非法富文本待补充】超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长}\"";
        String urlCase = originValue +"|68478|68476|68483|68480";
        switch (caseMode) {
            case "null":
                return null;
            case "bool":
                return false;
            case "tooBig":
                return 2147483647;
            case "空串":
                return "";
            case "ulrSuffix":
                return urlCase;
            case "超长":
                return longCase;
            case "[]":
                return new ArrayList<>();
            case "wellNum":
                return originValue.toString().replace("#", "＃");
            case "urlEnCode":
                return codeChange(originValue.toString(),"urlEnCode");
            case "urlDeCode":
                return codeChange(originValue.toString(),"urlDeCode");
        }
        return caseMode;
    }


    public static JsonObject setKeyNew(String originResponse, List<List> arrayLists, ArrayList<String> mockCaseList, String url, ArrayList<RobustResult> robustResults, boolean needMock, boolean needSaveResult, JSONArray modifyDetailList) throws Exception {
        //每次从mockCaseList中取arrayList长度的case，如果arrayLists比mockCaseList长，就只取arrayLists的最长长度
        //// TODO: 2022/9/14
        Integer mockId = 0;
        JsonParser jsonParser = new JsonParser();
        DocumentContext tempDocumentContext = null;
        String keyPath = "";
        String keyPathNew = "";
        int count = arrayLists.size();
        int caseCount = mockCaseList.size();
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        Object targetValue = null;

        //// TODO: 2022/9/15 在这里获取对应的映射，不要直接在调用setKey就指定
        String configCase = "";
        //可以把这段代码抽象出去 只要执行一次就好了
        DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(originResponse);
        //把每一个tempItemDataJson的节点都专成一个DocumentContext
         Map<String,DocumentContext> templateMap = createDocumentMap(tempItemDataJson);
        int index = 0;
        String keyWordIndex = "";
        boolean judgmentFlag = false;
        for (String mockCase : mockCaseList) {
            try {
            List arrayList = arrayLists.get(index);
            index += 1;
              Object originValue = new Object();
              keyPath = arrayList.get(0).toString();
              try {
                tempItemDataJson.read(keyPath);
                judgmentFlag = false;
              } catch (PathNotFoundException e) {
                judgmentFlag = true;
              }
            if(judgmentFlag){
              int secondDotIndex = keyPath.indexOf(".", keyPath.indexOf(".") + 1);
              String newKeyPath = "$." + keyPath.substring(secondDotIndex + 1);
              for (Map.Entry<String, DocumentContext> entry : templateMap.entrySet()) {
                try {
                    DocumentContext tempNodeContext = entry.getValue();
                    DocumentContext newTempDocumentContext = null;
                    originValue = tempNodeContext.read(newKeyPath);
                    keyWordIndex = "$." + entry.getKey();
                    keyPathNew += "【" + keyPath + "】 ";
                    if (mockCase != "deleteKey") {
                      targetValue = getTargetValue(mockCase, originValue);
                      if (null == tempDocumentContext) {
                        newTempDocumentContext = tempNodeContext.set(newKeyPath, targetValue);
                        tempDocumentContext = tempItemDataJson.set(keyWordIndex,newTempDocumentContext.jsonString());
                      } else {
                        newTempDocumentContext = tempNodeContext.set(newKeyPath, targetValue);
                        tempDocumentContext = tempItemDataJson.set(keyWordIndex,newTempDocumentContext.jsonString());
                      }
                      break;
                    } else {
                      if (null == tempDocumentContext) {
                        newTempDocumentContext = tempNodeContext.delete(newKeyPath);
                        tempDocumentContext = tempItemDataJson.set(keyWordIndex,newTempDocumentContext.jsonString());
                      } else {
                        newTempDocumentContext = tempNodeContext.delete(newKeyPath);
                        tempDocumentContext = tempItemDataJson.set(keyWordIndex, newTempDocumentContext.jsonString());
                      }
                      break;
                  }
                } catch (PathNotFoundException e) {
                  continue;
                }
              }
            }else {
              originValue = tempItemDataJson.read(keyPath);
              keyPathNew += "【" + keyPath + "】 ";
              if (mockCase != "deleteKey") {
                targetValue = getTargetValue(mockCase, originValue);
                if (null == tempDocumentContext) {
                  tempDocumentContext = tempItemDataJson.set(keyPath, targetValue);
                } else {
                  tempDocumentContext = tempDocumentContext.set(keyPath, targetValue);
                }
              } else {
                if (null == tempDocumentContext) {
                  tempDocumentContext = tempItemDataJson.delete(keyPath);
                } else {
                  tempDocumentContext = tempDocumentContext.delete(keyPath);
                }
              }
            }
            }catch (Exception e){
                log.error("setKeyNew"+e.toString());
            }
        }
      JsonObject caseData = null;
      caseData = jsonParser.parse(tempDocumentContext.jsonString()).getAsJsonObject();

        if (needSaveResult && needMock && EnvUtil.isOnline()) {
            for (int i = 0; i < 2; i++) {
                //如果mock失败了，重试一次
                JsonObject mockResult = new MockUtil().create(url, caseData);
                if (null != mockResult) {
                    mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                    break;
                }
            }
        }
        JSONArray modifyDiffList = new JSONArray();

        Map diffMap = new HashMap<>();
        diffMap.put("key", keyPathNew);
        diffMap.put("value", mockCaseList.toString());
        diffMap.put("modify_desc", "共构造了" + caseCount + "个字段，" + keyPathNew + " 值为 " + mockCaseList.toString());

        modifyDiffList.put(diffMap);

        JSONObject modifyDetailMap = new JSONObject();
        modifyDetailMap.put("modify_url", url);
        if (needSaveResult) modifyDetailMap.put("mockId", mockId);
        modifyDetailMap.put("modify_diff", modifyDiffList);
//        JSONArray modifyDetailList = new JSONArray();
        modifyDetailList.put(modifyDetailMap);
        //// TODO: 2022/9/23  modifyDetailList装着一次构造的case，但是调用此setKeyNew方法的是一个遍历，需要想办法把modifyDetailList暴露出去

        if (needSaveResult) {
            RobustResult robustResult = new RobustResult();
            robustResult.setModifyDetail(modifyDetailList.toString());
            try{
                robustResult.setCaseData(caseData.toString());
            }catch (Exception e){
                log.info("robustResult.setcaseData:"+e.toString());
            }
            robustResults.add(robustResult);
        }
        return caseData;
    }

    // 广度优先
    public static ArrayList<List<Integer>> findCircleNum(int[][] F) {
        int[] visited = new int[F.length];
        Queue<Integer> queue = new LinkedList<Integer>();
        Set<Integer> afterFriendGroupSet = new HashSet<>();
        ArrayList<List<Integer>> resultList = new ArrayList<>();
        for (int i = 0; i < F.length; i++) {
            if (visited[i] == 0) {
                queue.add(i);
                afterFriendGroupSet = new HashSet<>();
                afterFriendGroupSet.add(i);
                while (!queue.isEmpty()) {
                    int k = queue.remove();
                    visited[k] = 1;
                    for (int j = 0; j < F.length; j++) {
                        if (F[k][j] == 1 && visited[j] == 0) {
                            queue.add(j);
                            afterFriendGroupSet.add(j);
                        }
                    }
                }
                List<Integer> list = new ArrayList<Integer>(afterFriendGroupSet);
                resultList.add(list);
            }
        }
        return resultList;
    }

    //编辑距离动态规划算法，应该改成以节点整体作为原子的路径计算
    public static int getDistance(String str1, String str2) {
//        char[] wd1 = str1.toCharArray();
//        char[] wd2 = str2.toCharArray();
        String[] wd1 = str1.split("\\.");
        String[] wd2 = str2.split("\\.");

        int len1 = wd1.length;
        int len2 = wd2.length;
        //定义一个矩阵
        int[][] dist = new int[len1 + 1][len2 + 1];
        //初始状态 F(i, 0) = i; F(0, j) = j
        for (int i = 0; i <= len1; ++i)
            dist[i][0] = i;
        for (int j = 0; j <= len2; ++j)
            dist[0][j] = j;
        for (int i = 1; i <= len1; ++i) {
            for (int j = 1; j <= len2; ++j) {
                //F(i,j) = min(F(i-1, j) + 1,
                // F(i, j-1) + 1, F(i-1, j-1) + (wd1[i] == wd2[j] ? 0 : 1))
                // 首先求出插入和删除的最小值
                dist[i][j] = Math.min(dist[i - 1][j], dist[i][j - 1]) + 1;
                //再和替换进行比较
                if (wd1[i - 1].equals(wd2[j - 1])) {
                    //不需要进行替换
                    dist[i][j] = Math.min(dist[i][j], dist[i - 1][j - 1]);
                } else {
                    dist[i][j] = Math.min(dist[i][j], dist[i - 1][j - 1] + 1);
                }
            }
        }
        return dist[len1][len2];
    }

    //余弦距离 基准算法，需定制权重计算规则
    public static int cos(String a, String b) {
        if (a == null || b == null) {
            return 0;
        }
        Set<Integer> aChar = a.chars().boxed().collect(Collectors.toSet());
        Set<Integer> bChar = b.chars().boxed().collect(Collectors.toSet());

        // 统计字频
        Map<Integer, Integer> aMap = new HashMap<>();
        Map<Integer, Integer> bMap = new HashMap<>();
        for (Integer a1 : aChar) {
            aMap.put(a1, aMap.getOrDefault(a1, 0) + 1);
        }
        for (Integer b1 : bChar) {
            bMap.put(b1, bMap.getOrDefault(b1, 0) + 1);
        }

        // 向量化
        Set<Integer> union = SetUtils.union(aChar, bChar);
        int[] aVec = new int[union.size()];
        int[] bVec = new int[union.size()];
        List<Integer> collect = new ArrayList<>(union);
        for (int i = 0; i < collect.size(); i++) {
//            aVec[i] = aMap.getOrDefault(collect.get(i), 0);
//            bVec[i] = bMap.getOrDefault(collect.get(i), 0);
            aVec[i] = aMap.getOrDefault(collect.get(i), 0) * (collect.size() - i);
            bVec[i] = bMap.getOrDefault(collect.get(i), 0) * (collect.size() - i);
        }

        // 分别计算三个参数
        int p1 = 0;
        for (int i = 0; i < aVec.length; i++) {
//            p1 += (aVec[i] * bVec[i]);
            p1 += (aVec[i] * bVec[i]);
        }

        float p2 = 0f;
        for (int i : aVec) {
            p2 += (i * i);
        }
        p2 = (float) Math.sqrt(p2);

        float p3 = 0f;
        for (int i : bVec) {
            p3 += (i * i);
        }
        p3 = (float) Math.sqrt(p3);


        return (int) ((p1 * 100) / (p2 * p3));
    }

    public JsonObject setKey(String originResponse, List<List> arrayLists, String mockCase, String url, ArrayList<RobustResult> robustResults, boolean needMock, JSONArray modifyDetailList) throws Exception {
      Integer mockId = 0;
        JsonParser jsonParser = new JsonParser();
        DocumentContext tempDocumentContext = null;
        String keyPath = "";
        String keyPathNew = "";
        int count = arrayLists.size();
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        Object targetValue = null;

        DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(originResponse);
      //把每一个tempItemDataJson的节点都专成一个DocumentContext
      Map<String,DocumentContext> templateMap = createDocumentMap(tempItemDataJson);
      String keyWordIndex = "";
      boolean judgmentFlag = false;
      for (int i = 0; i < arrayLists.size(); i++) {
          Object originValue = new Object();
            try {
                keyPath = arrayLists.get(i).get(0).toString();
              try {
                tempItemDataJson.read(keyPath);
                judgmentFlag = false;
              } catch (PathNotFoundException e) {
                judgmentFlag = true;
              }
        if(judgmentFlag) {
          int secondDotIndex = keyPath.indexOf(".", keyPath.indexOf(".") + 1);
          String newKeyPath = "$." + keyPath.substring(secondDotIndex + 1);
          for (Map.Entry<String, DocumentContext> entry : templateMap.entrySet()) {
            try {
              DocumentContext tempNodeContext = entry.getValue();
              DocumentContext newTempDocumentContext = null;
              originValue = tempNodeContext.read(newKeyPath);
              keyWordIndex = "$." + entry.getKey();
              keyPathNew += "【" + keyPath + "】 ";
              if (null == tempDocumentContext) {
                targetValue = getTargetValue(mockCase, originValue);
                newTempDocumentContext = tempNodeContext.set(newKeyPath, targetValue);
                tempDocumentContext = tempItemDataJson.set(keyWordIndex, newTempDocumentContext.jsonString());
              } else {
                newTempDocumentContext = tempNodeContext.set(newKeyPath, targetValue);
                tempDocumentContext = tempItemDataJson.set(keyWordIndex, newTempDocumentContext.jsonString());
              }
              break;
            } catch (PathNotFoundException e) {
              continue;
            }
          }
        }else {
          originValue = tempItemDataJson.read(keyPath);
          keyPathNew += "【" + keyPath + "】 ";
          if (mockCase != "deleteKey") {
            targetValue = getTargetValue(mockCase, originValue);
            if (null == tempDocumentContext) {
              tempDocumentContext = tempItemDataJson.set(keyPath, targetValue);
            } else {
              tempDocumentContext = tempDocumentContext.set(keyPath, targetValue);
            }
          } else {
            if (null == tempDocumentContext) {
              tempDocumentContext = tempItemDataJson.delete(keyPath);
            } else {
              tempDocumentContext = tempDocumentContext.delete(keyPath);
            }

          }
        }
            }catch (Exception e){
                log.info(e.toString());
            }
        }
        JsonObject caseData = null;
        caseData = jsonParser.parse(tempDocumentContext.jsonString()).getAsJsonObject();

        if (needMock && EnvUtil.isOnline()) {
            for (int i = 0; i < 2; i++) {
                //如果mock失败了，重试一次
                JsonObject mockResult = new MockUtil().create(url, caseData);
                if (null != mockResult) {
                    mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                    break;
                }
            }
        }
        JSONArray modifyDiffList = new JSONArray();

        Map diffMap = new HashMap<>();
        diffMap.put("key", keyPathNew);
        diffMap.put("value", mockCase);
        diffMap.put("modify_desc", "共构造了" + count + "个字段，" + keyPathNew + " 值为 " + mockCase);

        modifyDiffList.put(diffMap);

        JSONObject modifyDetailMap = new JSONObject();
        modifyDetailMap.put("modify_url", url);
        modifyDetailMap.put("mockId", mockId);
        modifyDetailMap.put("modify_diff", modifyDiffList);
//        JSONArray modifyDetailList = new JSONArray();
        modifyDetailList.put(modifyDetailMap);

        RobustResult robustResult = new RobustResult();
        robustResult.setModifyDetail(modifyDetailList.toString());
        try{
            robustResult.setCaseData(caseData.toString());
        }catch (Exception e){
            log.info("robustResult.setcaseData:"+e.toString());
        }
        robustResults.add(robustResult);

        return caseData;

    }


    //2期用例精简：朋友圈算法
    public static ArrayList<List<List>> makeFriendNodeList(Map<Integer, List<List>> duplicationNodeMap) {
        ArrayList<List<List>> duplicationNodeListNew = new ArrayList<>();
        //// TODO: 2022/9/22 进行二期用例合并算法的设计
        HashMap<String, ArrayList<List<List>>> nodeLayerHashMap = new HashMap<>();
        ArrayList<List<List>> list;
        for (Map.Entry<Integer, List<List>> entry : duplicationNodeMap.entrySet()) {
            List<List> nodeList = entry.getValue();
            String value = (String) nodeList.get(0).get(0);
            Significance sign = (Significance) nodeList.get(0).get(2);
            ArrayList<String> mockCaseList = new ArrayList<>();
            RobustCaseConfig robustCaseConfig = new RobustCaseConfig();
            if (robustCaseConfig.caseConfig.containsKey(sign)) {
                mockCaseList = robustCaseConfig.caseConfig.get(sign);
            }
            int mockCaseSize = mockCaseList.size();
            int listSize = nodeList.size();
            String layerKey = String.valueOf(value.split("\\.").length);
            Boolean childNode = (Boolean) nodeList.get(0).get(4);
            if (childNode) layerKey += "isChildNode";
            if (listSize < mockCaseSize) {
                ArrayList nodeList1 = new ArrayList<>();
                nodeList1.add(nodeList);
                duplicationNodeListNew.add(nodeList1);
                continue;
            }
            if (nodeLayerHashMap.containsKey(layerKey)) {
                list = nodeLayerHashMap.get(layerKey);
                list.add(nodeList);
            } else {
                list = new ArrayList<List<List>>();
                list.add(nodeList);
                nodeLayerHashMap.put(layerKey, list);
            }
        }
        //拿到了分好层的节点
        for (Map.Entry<String, ArrayList<List<List>>> entry : nodeLayerHashMap.entrySet()) {
            String layer = entry.getKey();
            ArrayList<List<List>> allFiledInLayer = entry.getValue();
            int len = allFiledInLayer.size();
            int[][] arr = new int[len][len];
            int maxLen = 0;
            for (int i = 0; i < allFiledInLayer.size(); i++) {
                for (int j = 0; j < allFiledInLayer.size(); j++) {
                    String first = (String) allFiledInLayer.get(i).get(0).get(0);
                    String second = (String) allFiledInLayer.get(j).get(0).get(0);
                    int dis = new RobustUtil().getDistance(first, second);
                    arr[i][j] = dis > 1 ? 0 : 1;
                }
            }
            ArrayList<List<Integer>> circleList = findCircleNum(arr);
            //拿最大长度
            for (List<Integer> hashSet : circleList) {
                maxLen = Math.max(maxLen, hashSet.size());
            }
            for (int i = 0; i < maxLen; i++) {
                ArrayList tempList = new ArrayList<>();
                for (List<Integer> integerSet : circleList) {
                    if (i < integerSet.size()) {
                        int index = integerSet.get(i);
                        List<List> lists = allFiledInLayer.get(index);
                        tempList.add(lists);
                    }
                }
                duplicationNodeListNew.add(tempList);
            }
        }
        return duplicationNodeListNew;

    }
    //1 将已经去重复的路径，俩俩计算相似度，评估俩俩路径间亲密关系，形成 N x N 二维矩阵(编辑距离算法：一层节点作为原子)

    /*   设定阈值，  M[0][1]  代表第0个索引的路径 & 第1个索引的路径 是否是相似节点
            int[][] M = new int[][] {
                {1, 1, 0, 0, 0, 0},
                {1, 1, 0, 0, 0, 0},
                {0, 0, 1, 1, 1, 0},
                {0, 0, 1, 1, 0, 0},
                {0, 0, 1, 0, 1, 0},
                {0, 0, 0, 0, 0, 1}
        };
    */
    //2 将所有的路径进行分组（广度优先遍历）
    /*
            第一组：[0, 1]
            第二组：[2, 3, 4]
            第三组：[5]
    * */

    //3 从每个组里抽调1个路径，合并成一个case

    /*
    * 亲密度规则
    * 1.如果路径A包含在路径B中，说明路径A是路径B的父节点，亲密度为100
    * 2.计算两个路径的编辑距离，
    *
    *
    * */
  /**
   *字符串encode、decode编码方法
   */
  public  static  String codeChange(String original,String CodeMethod){
    String processResult = "";
    if(CodeMethod.equals("urlEnCode")){
      try {
        processResult = URLEncoder.encode(original, StandardCharsets.UTF_8.toString());
      } catch (Exception e) {
        log.error("url encode fail",e);
      }
    }else {
      try {
        processResult = URLDecoder.decode(original, StandardCharsets.UTF_8.toString());
      } catch (Exception e) {
        log.error("url decode fail",e);
      }
    }
    return processResult;
  }

  /**
   * 生成含有DocumentContext的map
   */
  public static Map<String,DocumentContext> createDocumentMap(DocumentContext tempItemDataJson){
    Map<String,DocumentContext> templateMap = new HashMap<>();
    Map<String, Object> map = tempItemDataJson.read("$");
    for (Map.Entry<String, Object> entry : map.entrySet()) {
      if(entry.getValue() == null) continue;
      try {
        DocumentContext otherDocumentContext = JsonPath.parse((entry.getValue().toString()));
        templateMap.put(entry.getKey(),otherDocumentContext);
      }catch (Exception e){
        log.error("jsonpath 解析失败:",e);
      }
    }
    return templateMap;
  }
}

