package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <AUTHOR>
 * @date ：Created in 2022/8/23 3:11 下午
 */
@Data
@TableName("robust_auto_config")
public class RobustAutoConfig {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String appName;
    private String bu;
    private String page;
    private String triggerTime;
    private String manager;
}