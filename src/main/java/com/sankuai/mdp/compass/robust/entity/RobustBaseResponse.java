package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("robust_base_response")
public class RobustBaseResponse {
    private static final long serialVersionUID = 1L; //??干嘛的

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String api;
    private String pageId;
    private String platform;
    private String responseBody;
    private String responseHeader;
    private String createBy;
    private String updateBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String manager;
    private String switchStatus;


}
