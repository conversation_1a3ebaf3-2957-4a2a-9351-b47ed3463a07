package com.sankuai.mdp.compass.robust.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.AppIterationStatusEnum;
import com.sankuai.mdp.compass.common.enums.ErrorEnum;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.robust.config.ResponseData;
import com.sankuai.mdp.compass.robust.entity.CommonPage;
import com.sankuai.mdp.compass.robust.entity.PageInfo;
import com.sankuai.mdp.compass.robust.entity.RobustBaseResponse;
import com.sankuai.mdp.compass.robust.entity.RobustBuild;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import com.sankuai.mdp.compass.robust.mapper.CommonPageMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustBaseResponseMapper;
import com.sankuai.mdp.compass.getAPK.controller.GetApkDataController;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import com.sankuai.mdp.compass.robust.entity.*;
import com.sankuai.mdp.compass.robust.mapper.RobustAutoConfigMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustTriggerJobMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustBuildMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustResultMapper;
import com.sankuai.mdp.compass.robust.service.RobustBuildService;
import com.sankuai.mdp.compass.robust.service.RobustResultService;
import com.sankuai.mdp.compass.robust.util.RobustUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by skt on 2022/4/26.
 */
@Slf4j
@Service
public class RobustBuildServiceImpl extends ServiceImpl<RobustBuildMapper, RobustBuild> implements RobustBuildService {
    @Autowired
    RobustBuildMapper robustBuildMapper;
    @Autowired
    RobustResultMapper robustResultMapper;
    //@Autowired
    //RobustBuildService robustBuildService;
    @Autowired
    RobustBaseResponseMapper robustBaseResponseMapper;
    @Autowired
    CommonPageMapper commonPageMapper;
    @Autowired
    RobustResultService robustResultService;
    @Autowired
    GetApkDataController getApkDataController;
    @Autowired
    RobustAutoConfigMapper robustAutoConfigMapper;
    @Autowired
    RobustTriggerJobMapper robustTriggerJobMapper;


    private static final String rootPath = "$";
    private static final String abURL = "/abtest/v1/getAllStrategys";
    private static final String divideStrategies = "/abtest/v1/getDivideStrategies";
    private static final String newTest = "new";
    private static final String retrySkipCase = "retrySkipCase";
    private static final String retryCombinationCrashCase = "retryCombinationCrashCase";
    private static int combinationCrashCode = 301;
    private static final String BASEURL = "https://hpx.sankuai.com/api/open/getAppPackageInfo?appName=";
    private static final String FLAVOR = "meituanInternal";
    private static final String CHANNEL = "meituaninternaltest";
    private static final String CFBUNDLEDISPLAYNAME = "美团";
    public static final int CRASH_RESULT_DEFAULT = -2;//数据库中crash_result字段计算前的默认值
    public static final int NO_COMMENTS_YET = 0;//表示这条case尚未添加过评论
    boolean needMock = EnvUtil.isOnline();


    ValueUtil v = new ValueUtil();
    DxUtil dx = new DxUtil();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     *触发新任务
     */
    @Override
    public Resp createNewJob(String body) {
        System.out.println("开始处理 " + v.printNowTime());
//        dx.sendToPersionByCompass("任务开始"+v.printNowTime(),"sunkangtong");
        String pageDescription = "";
        String schema = "";
        Resp resp = new Resp();
        String ABConfig = "";
        String hornConfig = "";
        String platform = "";
        String creator = "";
        String pageId = "";
        String moudleId = "";
        String describe = "";
        String appName = "meituan";
        Date startTime = null;
        JSONArray baseData = new JSONArray();
        //createData和handleData都用的这个类型，为了避免后续改动，在这存一份备份数据
        ArrayList<Map> baseDataTemp = new ArrayList<>();

        JsonObject responseBody = new JsonParser().parse(body).getAsJsonObject();

        JsonObject payload = responseBody.get("payload").getAsJsonObject();
        JsonArray keyArray = payload.get("keys").getAsJsonArray();
        if (payload.has("creator")) {
            creator = payload.get("creator").getAsString();
        } else {
            creator = "unKnowUser";
        }
        if (payload.has("pageId")) {
            pageId = payload.get("pageId").getAsString();
        }
        if (payload.has("moudleId")) {
            moudleId = payload.get("moudleId").getAsString();
        }
        if (payload.has("caseDesc")) {
            describe = payload.get("caseDesc").getAsString();
        }
        if (payload.has("appName")) {
            appName = payload.get("appName").getAsString();
        }


        String apkUrl = payload.get("apkUrl").getAsString();
        JsonElement hornElement = payload.get("hornSetting");
        JsonElement ABElement = payload.get("ABSetting");
        if (!(null == ABElement)) {
            ABConfig = ABElement.getAsString();
        }
        if (!(null == hornElement)) {
            hornConfig = hornElement.getAsString();
        }

        platform = ValueUtil.whichPlatform(apkUrl);
        if (!"Android".equals(platform) && !"iOS".equals(platform)) {
            resp.setCode(-1);
            resp.setMsg("illegalUrl");
            return resp;
        }
        if (payload.has("pageId")) {
            pageDescription = payload.get("pageId").getAsString();
        }
        CommonPage commonPage = commonPageMapper.selectOne(new QueryWrapper<CommonPage>().eq("page_description", pageDescription));
        if (null == commonPage) {
            resp.setCode(-1);
            resp.setMsg("page表无数据");
            return resp;
        }

        //// FIXME: 2023/3/21 对url判断，需兼容优选
        if ("Android".equals(platform)) {
            schema = commonPage.getSchemaAndroid();
        }else {
            schema = commonPage.getSchemaIos();
        }
        /*
        if ("Android".equals(platform)) {
            schema = commonPage.getSchemaAndroid();
            JsonObject responseJson = androidGetResponse(apkUrl);
            //// FIXME: 2022/10/19 判断包是不是有效包这个方法有bug，在历史包上获取的状态不正确导致任务触发失败了
//            if (responseJson.get("status").getAsInt() != 1) {
//                resp.setCode(-1);
//                resp.setMsg("illegalUrl");
//                return resp;
//            }
            JsonObject data = responseJson.get("data").getAsJsonObject();
            if (androidCheckAPIMiss(data)) {
                resp.setCode(-1);
                resp.setMsg("非测试包");
                sendNotification(creator, apkUrl, data.get("taskUrl").getAsString());
                return resp;
            }
        } else {
            schema = commonPage.getSchemaIos();
            JsonObject responseJson = iOSGetResponse(apkUrl);
//            if (responseJson.get("status").getAsInt() != 1) {
//                resp.setCode(-1);
//                resp.setMsg("illegalUrl");
//                return resp;
//            }
            JsonObject data = responseJson.get("data").getAsJsonObject();
            if (iOSCheckAPIMiss(data)) {
                resp.setCode(-1);
                resp.setMsg("非测试包");
                sendNotification(creator, apkUrl, data.get("taskUrl").getAsString());
                return resp;
            }
        }
        */
        //将前端返回的多个url+response在这儿拼装
        if (keyArray.size() != 0) {
            JsonArray urlValueArray = payload.get("urlValue").getAsJsonArray();
            JsonArray responseValueArray = payload.get("responseValue").getAsJsonArray();
            JsonArray modifyValueArray = payload.get("modify").getAsJsonArray();
            String modifyValue = "";
            for (JsonElement jsonElement : keyArray) {
                int arrayIndex = jsonElement.getAsInt();
                String urlValue = urlValueArray.get(arrayIndex).getAsString();
                String responseValue = responseValueArray.get(arrayIndex).getAsString();
                JsonElement res = modifyValueArray.get(arrayIndex);
                modifyValue = res.isJsonNull() ? "false" : res.getAsString();
                Map<String, String> map = new HashMap<>();
                map.put("url", urlValue);
                map.put("isModify", modifyValue);
                map.put("base_data", responseValue);

                JSONObject baseDataMap = new JSONObject();
                baseDataMap.put("url", urlValue);
                baseDataMap.put("isModify", modifyValue);
                JsonObject responseObject = new JsonObject();
                try {
                    responseObject = new JsonParser().parse(responseValue).getAsJsonObject();
                } catch (Exception e) {
                    resp.setCode(-1);
                    resp.setMsg("responseBody解析非json格式");
                    return resp;
                }
                baseDataMap.put("base_data", responseObject);

                baseData.put(baseDataMap);
                baseDataTemp.add(map);
            }
        }

        try {
            startTime = sdf.parse(sdf.format(new Date()));
        } catch (ParseException e) {
            log.info(e.getMessage());
        }

        RobustBuild robustBuild = new RobustBuild();
        robustBuild.setApkUrl(apkUrl);
        robustBuild.setPlatform(platform);
        robustBuild.setAbConfig(ABConfig);
        robustBuild.setHornConfig(hornConfig);
        robustBuild.setCreator(creator);
        robustBuild.setBaseData(baseData.toString());
        robustBuild.setStartTime(startTime);
        robustBuild.setPageId(pageId);
        robustBuild.setMoudleId(moudleId);
        robustBuild.setSchemaUrl(schema);
        robustBuild.setCaseDesc(describe);
        robustBuild.setAppName(appName);
        robustBuildMapper.insert(robustBuild);

        //在这里进行数据的实际拼装
        try {
            //handleData如果成功就什么也不返回，任何返回值都代表出现错误了。
            String result = handleData(baseDataTemp, moudleId, true);
            if (!StringUtil.isBlank(result)) {
                resp.setCode(-1);
                resp.setMsg(result);
                return resp;
            }
        } catch (Exception e) {
            resp.setCode(-1);
            resp.setMsg("处理数据出错 " + e.toString());
            return resp;

        }
        ArrayList<RobustResult> resultList = createMock(baseDataTemp, needMock);
        int total = resultList.size();
        if (total > 1200) {
            resp.setCode(-1);
            resp.setMsg("case超过1200，联系sunkangtong排查");
            dx.sendToPersionByCompass("case超过1200，联系sunkangtong排查", "sunkangtong");
            return resp;
        }
        robustBuild.setCaseCount(total);
        robustBuildMapper.updateById(robustBuild);
        int jodId = robustBuild.getId();
//        dx.sendToPersionByCompass(jodId+" build表插入结束"+v.printNowTime(),"sunkangtong");

        robustResultService.insertRobustResult(jodId, baseDataTemp, resultList);

//        dx.sendToPersionByCompass(jodId+" 主线程结束"+v.printNowTime(),"sunkangtong");
        if (EnvUtil.isOnline()) {
            new JenkinsUtil().robustJob(robustBuild, newTest, appName);
        }
        resp.setCode(200);
        resp.setMsg("success 构造了 " + total + " 条用例，测试开始执行");
        System.out.println("处理结束 " + v.printNowTime());
        return resp;
    }

    public boolean iOSCheckAPIMiss(JsonObject data) {
        JsonObject paramInfo = data.get("paramInfo").getAsJsonObject();
        JsonObject infoValue = paramInfo.get("info_plist_value").getAsJsonObject();
        JsonElement cfBundleName = infoValue.get("CFBundleDisplayName");
        if (cfBundleName == null || CFBUNDLEDISPLAYNAME.equals(cfBundleName.getAsString())) return true;
        return false;
    }

    public boolean androidCheckAPIMiss(JsonObject data) {
        JsonObject paramInfo = data.get("paramInfo").getAsJsonObject();
        JsonElement channel = paramInfo.get("channel");
        JsonElement flavor = paramInfo.get("flavor");
        if (channel == null || flavor == null) {
            return true;
        } else {
            if (!CHANNEL.equals(channel.getAsString()) || !FLAVOR.equals(flavor.getAsString())) {
                return true;
            }
        }
        return false;
    }

    public void sendNotification(String creator, String apkUrl, String hpxUrl) {
        JsonArray users = new JsonArray();
        users.add(creator);
        users.add("guanxin04");
        for (JsonElement user : users) {
            String u = user.getAsString();
            if (!"unKnowUser".equals(u)) {
                dx.sendToPersionByCompass("※ 健壮性测试提醒 ※：非测试包\n" +
                        "【包链接】：" + apkUrl + "\n" +
                        "【hpx任务链接】：" + hpxUrl + "\n" +
                        "请联系guanxin04", u);
            }
        }
    }

    public JsonObject androidGetResponse(String apkUrl) {
        String buildNumber = "blank";
        String[] split1 = apkUrl.split("/");
        String[] split2 = split1[split1.length - 1].split("_");
        String[] split3 = split2[split2.length - 1].split("-");
        for (String temp : split3) {
            boolean flag = false;
            for (int i = 0; i < temp.length(); i++) {
                if (!(temp.charAt(i) >= '0' && temp.charAt(i) <= '9')) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                buildNumber = temp;
                break;
            }
        }
        String totalUrl = BASEURL + "com.sankuai.meituan&buildNumber=" + buildNumber;
        String response = HttpUtil.vGet(totalUrl, TokenUtil.getToken("hpx_token"));
        JsonObject responseJson = new JsonParser().parse(response).getAsJsonObject();
        return responseJson;
    }

    public JsonObject iOSGetResponse(String apkUrl) {
        String buildNumber = "blank";
        String[] split1 = apkUrl.split("-");
        String[] split2 = split1[split1.length - 1].split("\\.");
        if (split2.length >= 3) {
            buildNumber = split2[split2.length - 3];
        }
        String totalUrl = BASEURL + "imeituan&buildNumber=" + buildNumber;
        String response = HttpUtil.vGet(totalUrl, TokenUtil.getToken("hpx_token"));
        JsonObject responseJson = new JsonParser().parse(response).getAsJsonObject();
        return responseJson;
    }


    /**
     * 测试合并效果
     */
    public static void testMerge() {
        ImmutableMap<String, String> a = new ResponseData().pageApiData;
        RobustBuildServiceImpl s = new RobustBuildServiceImpl();
        for (Map.Entry<String, String> entry : a.entrySet()) {
            JsonObject resposeObject = new JsonParser().parse(entry.getValue()).getAsJsonObject();
            List<List> allField = new ArrayList<>();
            String responseData = entry.getValue();
            allField = new JsonUtil().readObject(resposeObject, rootPath, allField);
            List<List> duplicationNodeList = new JsonUtil().markDuplicationNode(allField);
            Map<Integer, List<List>> duplicationNodeMap = new HashMap<>();
            for (List duplicationNode : duplicationNodeList) {
                int index = (int) duplicationNode.get(3);
                String path = duplicationNode.get(0).toString();

                if (!duplicationNodeMap.containsKey(index)) {
                    List<List> tempList = new ArrayList<>();
                    tempList.add(duplicationNode);
                    duplicationNodeMap.put(index, tempList);
                } else {
                    List<List> tempList = duplicationNodeMap.get(index);
                    if (tempList.size() > 9) {
                        continue;
                    }
                    tempList.add(duplicationNode);
                    duplicationNodeMap.put(index, tempList);
                }

            }
            ArrayList<RobustResult> resultList = new ArrayList();
            ArrayList<List<List>> duplicationNodeListNew = RobustUtil.makeFriendNodeList(duplicationNodeMap);
            String url = "default";
            for (List<List> afterFriengDuplocationNodeList : duplicationNodeListNew) {
                JsonObject changeResponse = null;
                JSONArray modifyDetailList = new JSONArray();
                for (List duplicationList : afterFriengDuplocationNodeList) {
                    boolean needSave = false;
                    if (duplicationList.equals(afterFriengDuplocationNodeList.get(afterFriengDuplocationNodeList.size() - 1)))
                        needSave = true;
                    if (null == changeResponse) {
                        changeResponse = new JsonUtil().createCase(responseData, duplicationList, url, resultList, false, 1, needSave, modifyDetailList);
                    } else {
                        changeResponse = new JsonUtil().createCase(changeResponse.toString(), duplicationList, url, resultList, false, 1, needSave, modifyDetailList);
                    }
                }
            }
        }
    }

    @Override
    public Resp testCount(String body) {
        Resp resp = new Resp();
        String pageId = "";
        String moudleId = "";

        JSONArray baseData = new JSONArray();
        //createData和handleData都用的这个类型，为了避免后续改动，在这存一份备份数据
        ArrayList<Map> baseDataTemp = new ArrayList<>();

        JsonObject responseBody = new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload = responseBody.get("payload").getAsJsonObject();
        JsonArray keyArray = payload.get("keys").getAsJsonArray();

        if (payload.has("moudleId")) {
            moudleId = payload.get("moudleId").getAsString();
        }
        //将前端返回的多个url+response在这儿拼装
        if (keyArray.size() != 0) {
            JsonArray urlValueArray = payload.get("urlValue").getAsJsonArray();
            JsonArray responseValueArray = payload.get("responseValue").getAsJsonArray();
            JsonArray modifyValueArray = payload.get("modify").getAsJsonArray();
            String modifyValue = "";
            for (JsonElement jsonElement : keyArray) {
                int arrayIndex = jsonElement.getAsInt();
                String urlValue = urlValueArray.get(arrayIndex).getAsString();
                String responseValue = responseValueArray.get(arrayIndex).getAsString();
                JsonElement res = modifyValueArray.get(arrayIndex);
                modifyValue = res.isJsonNull() ? "false" : res.getAsString();
                Map<String, String> map = new HashMap<>();
                map.put("url", urlValue);
                map.put("isModify", modifyValue);
                map.put("base_data", responseValue);

                JSONObject baseDataMap = new JSONObject();
                baseDataMap.put("url", urlValue);
                baseDataMap.put("isModify", modifyValue);
                JsonObject responseObject = new JsonObject();
                try {
                    responseObject = new JsonParser().parse(responseValue).getAsJsonObject();
                } catch (Exception e) {
                    resp.setCode(-1);
                    resp.setMsg("responseBody解析非json格式");
                    return resp;
                }
                baseDataMap.put("base_data", responseObject);

                baseData.put(baseDataMap);
                baseDataTemp.add(map);
            }
        }


        //在这里进行数据的实际拼装
        try {
            //handleData如果成功就什么也不返回，任何返回值都代表出现错误了。
            boolean needMock = false;
            String result = handleData(baseDataTemp, moudleId, needMock);
            if (!StringUtil.isBlank(result)) {
                resp.setCode(-1);
                resp.setMsg(result);
                return resp;
            }
        } catch (Exception e) {
            resp.setCode(-1);
            resp.setMsg("处理数据出错 " + e.toString());
            return resp;

        }

        ArrayList<RobustResult> resultList = createMock(baseDataTemp, false);
        resp.setMsg("构造case数量为：" + resultList.size());
        return resp;
    }

    //根据 robust_build表id 查询用例
    @Override
    public Resp getRobustCase(String buildJobId, String mode) {
        if (buildJobId == null || buildJobId.equals("")) {
            return Resp.error(ErrorEnum.S_400, "入参错误");
        } else {
            RobustBuild robustBuild = robustBuildMapper.selectById(buildJobId);
            JsonObject respData = new JsonObject();
            // 设置测试项信息
            respData.addProperty("jobId", buildJobId);
//            respData.addProperty("caseCount", robustBuild.getCaseCount());
            respData.addProperty("apkUrl", robustBuild.getApkUrl());
            respData.addProperty("platform", robustBuild.getPlatform());
            // FIXME 确认这里的是pageId还是pageName
            respData.addProperty("pageId", robustBuild.getPageId());

            // 设置CaseArray信息
            JsonArray caseArray = new JsonArray();
            /*
            *   mockId
                caseId
                abConfig
                hornConfig
            * */
            QueryWrapper<RobustResult> robustResultQueryWrapper = new QueryWrapper<>();

            if (mode.equals(retrySkipCase)) {
                log.info("---robust：重新触发未执行的case，jobId=" + buildJobId);
                robustResultQueryWrapper.eq("build_id", buildJobId).eq("case_result", 0);
            } else if (mode.equals(newTest)) {
                log.info("---robust：触发新测试，jobId=" + buildJobId);
                robustResultQueryWrapper.eq("build_id", buildJobId);
            } else if (mode.equals(retryCombinationCrashCase)) {
                log.info("---robust: 重新执行发生Crash的聚合case，jobId=" + buildJobId);
                robustResultQueryWrapper.eq("build_id", buildJobId).eq("case_result", combinationCrashCode);
            } else {
                log.info("---robust: 输入模式不存在");
            }

            List<RobustResult> robustResultList = robustResultMapper.selectList(robustResultQueryWrapper);
            respData.addProperty("caseCount", robustResultList.size());

            if (robustResultList != null && robustResultList.size() != 0) {
                for (RobustResult robustResultItem : robustResultList) {
                    JsonObject caseInfo = new JsonObject();
                    // 写入case的基本build信息
                    caseInfo.addProperty("schema", robustBuild.getSchemaUrl());
                    caseInfo.addProperty("abConfig", robustBuild.getAbConfig());
                    caseInfo.addProperty("hornConfig", robustBuild.getHornConfig());

                    // 写入case的基本结果信息
                    caseInfo.addProperty("caseId", robustResultItem.getId());
                    // 拼接mockId
                    try {
                        JsonArray modifyDetailArray = (new JsonParser().parse(robustResultItem.getModifyDetail()).getAsJsonArray());
                        if (modifyDetailArray != null && modifyDetailArray.size() >= 1) {
                            StringBuilder mockIds = new StringBuilder();
                            boolean firstFlag = true;
                            for (int i = 0; i < modifyDetailArray.size(); ++i) {
                                JsonObject modifyDetailJsonItem = modifyDetailArray.get(i).getAsJsonObject();
                                if (modifyDetailJsonItem.has("mockId") && modifyDetailJsonItem.get("mockId") != null) {
                                    if (firstFlag) {
                                        mockIds.append(modifyDetailJsonItem.get("mockId").getAsString());
                                        firstFlag = false;
                                    } else mockIds.append("_").append(modifyDetailJsonItem.get("mockId").getAsString());
                                }
                            }
                            caseInfo.addProperty("mockId", mockIds.toString());
                        } else {
                            return Resp.error(ErrorEnum.S_400, "mock信息获取错误");
                        }
                    } catch (Exception e) {
                        log.error(e.toString());
                    }
                    caseArray.add(caseInfo);
                }
                respData.add("cases", caseArray);
            } else {
                return Resp.error(ErrorEnum.S_400, "未获取到case信息");
            }
            return Resp.success(respData);
        }
    }


    /**
     * @param baseData 原始数据
     * @param moduleId 前端传进来的模块ID
     * @param needCut  是否需要进行裁剪操作，这个入参目前没有用到
     * @return 返回值代表了是否有异常，实际产生的内容会放回baseData中
     */
    public String handleData(ArrayList<Map> baseData, String moduleId, boolean needCut) {
        moduleId = moduleId.replaceAll("\"", "");
        String modulePath = "";
        List<List> moudleField = new ArrayList();
        List<List> pageField = new ArrayList();
        ArrayList<String> repeatArray = new ArrayList();
        String root = "";
        //key是url，value是所有构造的字段信息
//        Map<String,List<List>> cutFieldMap = new HashMap<>();

        for (Map singleBaseDataMap : baseData) {
            String isModify = singleBaseDataMap.get("isModify").toString();
            if ("true".equals(isModify)) {
                String responseData = singleBaseDataMap.get("base_data").toString();
                JsonObject resposeObject = new JsonParser().parse(responseData).getAsJsonObject();
                String url = singleBaseDataMap.get("url").toString();
                List<List> allField = new ArrayList<>();
                //拍平的字段在这儿
                allField = new JsonUtil().readObject(resposeObject, rootPath, allField);
                List<List> duplicationNodeList = new JsonUtil().markDuplicationNode(allField);
                Map<Integer, List<List>> duplicationNodeMap = new HashMap<>();

//                oldRetrenchMethod();
                //如果带了moduleID,需要再筛一次，
                if (!StringUtil.isBlank(moduleId)) {
                    //拿到mockID对应的所有字段
                    boolean moudleIdCorrect = false;
                    for (List field : allField) {
                        String chlidValue = field.get(1).toString();
                        if (chlidValue.contains(moduleId)) {
                            modulePath = field.get(0).toString();
                            modulePath = modulePath.substring(0, modulePath.lastIndexOf("."));
                            moudleIdCorrect = true;
                            break;
                        }
                    }
                    if (!moudleIdCorrect) return "未找到moduleId";
                }
                for (List duplicationNode : duplicationNodeList) {
                    int index = (int) duplicationNode.get(3);
                    String path = duplicationNode.get(0).toString();
                    if (!modulePath.equals("") && !path.contains(modulePath)) {
                        //如果指定了moudleID且这个path不属于这个moudle，就不存了
                    } else {
                        if (!duplicationNodeMap.containsKey(index)) {
                            List<List> tempList = new ArrayList<>();
                            tempList.add(duplicationNode);
                            duplicationNodeMap.put(index, tempList);
                        } else {
                            List<List> tempList = duplicationNodeMap.get(index);
                            if (tempList.size() > 9) {
                                continue;
                            }
                            tempList.add(duplicationNode);
                            duplicationNodeMap.put(index, tempList);
                        }
                    }
                }
                int child = 0;
                HashMap<Integer, ArrayList<List<List>>> myHashMap = new HashMap<>();
                ArrayList<List<List>> list;
                //#测试代码
                for (Map.Entry<Integer, List<List>> entry : duplicationNodeMap.entrySet()) {
                    List<List> nodeList = entry.getValue();
                    System.out.println("==" + nodeList);
                    String value = (String) nodeList.get(0).get(0);
                    int key = value.split("\\.").length;
                    if (myHashMap.containsKey(key)) {
                        // if the key has already been used,
                        // we'll just grab the array list and add the value to it
                        list = myHashMap.get(key);
                        list.add(nodeList);
                    } else {
                        // if the key hasn't been used yet,
                        // we'll create a new ArrayList<String> object, add the value
                        // and put it in the array list with the new key
                        list = new ArrayList<List<List>>();
                        list.add(nodeList);
                        myHashMap.put(key, list);
                    }
                }


                singleBaseDataMap.put(url, duplicationNodeMap);
            }
        }
        return "";
    }

    //判断子节点是否越界（超过1），没越界返回false
    private boolean subArrayOutOfIndex(String saveList) {
        List l = extractMessage(saveList);
        if (l.size() <= 1) return false;
        for (int i = 1; i < l.size(); i++) {
            String num = (String) l.get(i);
            if (!num.equals("0")) {
                return true;
            }
        }
        return false;
    }

    public static List<String> extractMessage(String msg) {
        List<String> list = new ArrayList<String>();
        Pattern p = Pattern.compile("(\\[[^\\]]*\\])");
        Matcher m = p.matcher(msg);
        while (m.find()) {
            list.add(m.group().substring(1, m.group().length() - 1));
        }
        return list;
    }

    public ArrayList<RobustResult> createMock(ArrayList<Map> baseData, boolean needMock) {

        ArrayList<RobustResult> resultList = new ArrayList();
        for (Map map : baseData) {
            String url = map.get("url").toString();
            String responseData = map.get("base_data").toString();
            if (map.containsKey(url)) {
                //在这里多构造一条用例：根据URL生成code码为504的用例
                new JsonUtil().createCodeStatusCase(url, resultList, needMock);
                Map<Integer, List<List>> duplicationNodeMap = (Map<Integer, List<List>>) map.get(url);
                //这个新的list承接了之前的duplicationNodeMap，但是又包装了一层，把之前朋友圈算法拿到的list再次封装，这样在createCase的时候就可以直接拼接遍历
                ArrayList<List<List>> duplicationNodeListNew = RobustUtil.makeFriendNodeList(duplicationNodeMap);
                //拿到真实节点值
                for (List<List> afterFriengDuplocationNodeList : duplicationNodeListNew) {
                    JsonObject changeResponse = null;
                    JSONArray modifyDetailList = new JSONArray();
                    for (List duplicationList : afterFriengDuplocationNodeList) {
                        boolean needSave = false;
                        if (duplicationList.size() > 5) {
                            System.out.println("走到了V2方法");
                        }
                        //如果遍历到每个朋友圈聚合后节点的最后一个，则可以保存到result表中了
                        if (duplicationList.equals(afterFriengDuplocationNodeList.get(afterFriengDuplocationNodeList.size() - 1)))
                            needSave = true;
                        if (null == changeResponse) {
                            changeResponse = new JsonUtil().createCase(responseData, duplicationList, url, resultList, needMock, 1, needSave, modifyDetailList);
                        } else {
                            changeResponse = new JsonUtil().createCase(changeResponse.toString(), duplicationList, url, resultList, needMock, 1, needSave, modifyDetailList);
                        }
                    }
                }
            }
        }
        return resultList;
    }


    @Override
    public Resp retryJob(String body) {
        Resp resp = new Resp();
        JsonObject responseBody = new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload = responseBody.get("payload").getAsJsonObject();
        int buildId = payload.get("buildId").getAsInt();
        String creator = "";
        String apkUrl = "";
        if (payload.has("creator")) {
            creator = payload.get("creator").getAsString();
        } else {
            creator = "unKnowUser";
        }
        int retryBuildId = 0;
        apkUrl = payload.get("apkUrl").getAsString();
        String platform = "";
        platform = ValueUtil.whichPlatform(apkUrl);
        if (!"Android".equals(platform) && !"iOS".equals(platform)) {
            resp.setCode(-1);
            resp.setMsg("无效包链接，已取消测试");
            return resp;
        }

        //存新的build表
        RobustBuild robustBuild = robustBuildMapper.selectById(buildId);
        if (null == robustBuild) {
            resp.setCode(-1);
            resp.setMsg("无此buildId，请联系sunkangtong排查");
            return resp;
        }
        String appName = robustBuild.getAppName();
        RobustBuild retryRobustBuild = new RobustBuild();
        Date startTime = null;
        retryRobustBuild.setAbConfig(robustBuild.getAbConfig());
        retryRobustBuild.setCaseCount(robustBuild.getCaseCount());
        String describe = robustBuild.getCaseDesc();
        if (payload.has("caseDesc")) {
            String describe_new = payload.get("caseDesc").getAsString();
            if (!StringUtil.isBlank(describe_new)) {
                describe = describe_new;
            }
        }
        retryRobustBuild.setCaseDesc(describe);
        retryRobustBuild.setPageId(robustBuild.getPageId());
        retryRobustBuild.setHornConfig(robustBuild.getHornConfig());
        retryRobustBuild.setBaseData(robustBuild.getBaseData());
        retryRobustBuild.setMoudleId(robustBuild.getMoudleId());
        retryRobustBuild.setSchemaUrl(robustBuild.getSchemaUrl());
        retryRobustBuild.setApkUrl(apkUrl);
        retryRobustBuild.setCreator(creator);
        retryRobustBuild.setPlatform(platform);
        retryRobustBuild.setAppName(appName);
        try {
            startTime = sdf.parse(sdf.format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        retryRobustBuild.setStartTime(startTime);
        robustBuildMapper.insert(retryRobustBuild);

        //存新的result表
        retryBuildId = retryRobustBuild.getId();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("build_id", buildId);
        List<RobustResult> robustResultList = robustResultMapper.selectList(queryWrapper);
        int total = robustResultList.size();
        for (RobustResult robustResult : robustResultList) {
            RobustResult retryRobustResult = new RobustResult();
            retryRobustResult.setBuildId(retryBuildId);
            retryRobustResult.setModifyDetail(robustResult.getModifyDetail());
            retryRobustResult.setAppName(appName);
            retryRobustResult.setCommentState(NO_COMMENTS_YET);  //入库之前，初始化comment_state、crash_result字段
            retryRobustResult.setCrashResult(CRASH_RESULT_DEFAULT);
            robustResultMapper.insert(retryRobustResult);
        }
        //触发jenkins任务
        if (EnvUtil.isOnline()) {
            new JenkinsUtil().robustJob(retryRobustBuild, newTest, appName);
        }
        resp.setCode(200);
        resp.setMsg("success 构造了 " + total + " 条用例，测试开始执行");
        return resp;
    }

    @Override
    public IPage<RobustBuild> list(QueryRequest request, RobustBuild robustBuild) {
        try {
            LambdaQueryWrapper<RobustBuild> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.last("order by start_time DESC");
            if (null != robustBuild.getCreator()) {
                queryWrapper.eq(RobustBuild::getCreator, robustBuild.getCreator());
            }
            Page<RobustBuild> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<RobustBuild> iPage = this.page(page, queryWrapper);
            return iPage;

        } catch (Exception e) {
            return null;

        }
    }

    @Override
    public List<String> getAllCreator() {
        QueryWrapper<RobustBuild> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<RobustBuild> robustBuild = robustBuildMapper.selectList(queryWrapper
                .groupBy("creator"));
        for (RobustBuild Robust : robustBuild) {
            list.add(Robust.getCreator());
        }
        return list;
    }

    /**
     * 上传页面对应的api、response、ab、horn信息
     * 接口文档https://km.sankuai.com/page/1307363846
     */
    @Override
    public Resp uploadPageInfo(PageInfo pageInfo) {
        log.info("健壮性自动化 uploadPageInfo接口入参：" + pageInfo.toString());
        Resp resp = new Resp<>();
        String triggerId = pageInfo.getTriggerId();
        String platform = pageInfo.getPlatform();
        String schema = "";
        //在robust_trigger_job表查
        RobustTriggerJob robustTriggerJob = robustTriggerJobMapper.selectOne(new QueryWrapper<RobustTriggerJob>().eq("id", triggerId));
        if (null == robustTriggerJob) return Resp.error(ErrorEnum.S_100, "未找到robustTriggerJob");
        String apkUrl = robustTriggerJob.getApkUrl();
        int configId = robustTriggerJob.getAutoConfigId();
        String appName = robustTriggerJob.getAppName();

        RobustAutoConfig robustAutoConfig = robustAutoConfigMapper.selectOne(new QueryWrapper<RobustAutoConfig>().eq("id", configId));
        String creater = robustAutoConfig.getManager();
        Object a = pageInfo.getPageInfo();
        JsonArray allPageInfo = new JsonParser().parse(a.toString()).getAsJsonArray();
        //需兼容可能传多个页面数据过来的情况,不同的页面，可以分发到不同的build表中
        for (JsonElement pageInfos : allPageInfo) {
            String pageId = pageInfos.getAsJsonObject().get("pageId").getAsString();
            if (StringUtil.isBlank(pageId)) continue;
            JsonArray apiInfoArray = pageInfos.getAsJsonObject().get("apiInfo").getAsJsonArray();
            if (apiInfoArray.size() == 0) {
                log.info("apiInfoArray-size为0", pageId);
                continue;
            }
            synchronized (this) {

                RobustTriggerJob robustTriggerRW = robustTriggerJobMapper.selectOne(new QueryWrapper<RobustTriggerJob>().eq("id", triggerId));
                String alreadyCallBackPageId = robustTriggerRW.getCallbackPage();

                if (!new StringUtil().isEmpty(alreadyCallBackPageId)) {
                    String[] callBack = alreadyCallBackPageId.split("_");
                    if (Arrays.asList(callBack).contains(pageId)) continue;
                    alreadyCallBackPageId += "_" + pageId;
                } else {
                    alreadyCallBackPageId = pageId;
                }
                robustTriggerRW.setCallbackPage(alreadyCallBackPageId);
                robustTriggerJobMapper.updateById(robustTriggerRW);
            }
            //在common_page表查pageSchema，注意区分端
            CommonPage commonPage = commonPageMapper.selectOne(new QueryWrapper<CommonPage>().eq("id", pageId));
            if (null == commonPage) return Resp.error(ErrorEnum.S_100, "未找到录入的页面信息");
            schema = platform.equals("iOS") ? commonPage.getSchemaIos() : commonPage.getSchemaAndroid();
            String pageDesc = commonPage.getPageDescription();
            String version = robustTriggerJob.getVersionInfo();
            String caseDesc = platform + " " + pageDesc + "页面" + version + "版本自动测试";

            HashMap<String, String> customizedDataMap = getRespByPageId(pageId, platform);
            HashMap<String, String> resultMap = new HashMap<>();
            for (JsonElement apiInfo : apiInfoArray) {
                String responseBody = "";
                String api = "";
                try {
                    JsonObject apiObject = apiInfo.getAsJsonObject();
                    api = apiObject.get("path").getAsString();
                    responseBody = apiObject.get("responseBody").toString();
                    if (customizedDataMap.containsKey(api)) {
                        resultMap.put(api, customizedDataMap.get(api));
                    } else {
                        resultMap.put(api, responseBody);
                    }
                } catch (Exception e) {
                    log.error("uploadPageInfo出错",e.toString());
                    continue;
                }
            }
            //2.1  更新trigger表，并在trigger表拿对应的数据提供到build表
            RobustBuild robustBuild = new RobustBuild();
            robustBuild.setTriggerId(Integer.parseInt(triggerId));
            robustBuild.setStartTime(new Date());
            robustBuild.setCreator(creater);
            robustBuild.setSchemaUrl(schema);
            robustBuild.setApkUrl(apkUrl);
            robustBuild.setPlatform(platform);
            robustBuild.setCaseDesc(caseDesc);
            robustBuild.setPageId(pageDesc);
            robustBuild.setBaseData(apiInfoArray.toString());
            robustBuild.setAppName(appName);
            robustBuildMapper.insert(robustBuild);
            int roubustBuildId = robustBuild.getId();
            //1 做数据的预处理和去重 ：resultMap里存了url：duplicationNodeMap   ，singleNodeData里存了url：baseData
            // fuzzNodeDatas 保存了一个页面所有api和对应拍平的数据
            HashMap<String, Map<Integer, List<List>>> fuzzNodeDatas = fuzzDataAuto(resultMap);
            HashMap<String, Integer> apiCaseCount = structureDataAuto(fuzzNodeDatas, resultMap, roubustBuildId, pageInfo, appName);
            int caseCount = 0;
            for (Map.Entry entry : apiCaseCount.entrySet()) {
                caseCount = Math.max(caseCount, (Integer) entry.getValue());
            }
            robustBuild.setCaseCount(caseCount);
            robustBuild.setApiCaseCount(apiCaseCount.toString());
            robustBuildMapper.updateById(robustBuild);
            new JenkinsUtil().robustJob(robustBuild, newTest, appName);
            synchronized (this) {
                RobustTriggerJob robustTriggerRW = robustTriggerJobMapper.selectOne(new QueryWrapper<RobustTriggerJob>().eq("id", triggerId));
                int alreadyCaseNumber = robustTriggerRW.getCaseNumber();
                alreadyCaseNumber += caseCount;
                robustTriggerRW.setCaseNumber(alreadyCaseNumber);
                robustTriggerJobMapper.updateById(robustTriggerRW);
            }
        }
        resp.setCode(0);
        resp.setMsg("success");
        return resp;
    }

    /**
     * @return：HashMap，key为api，value为根据pageId对应的responseBody
     */
    public HashMap<String, String> getRespByPageId(String pageId, String platform) {
        try {
            HashMap<String, String> resp = new HashMap<>();
            QueryWrapper<RobustBaseResponse> queryWrapper = new QueryWrapper<>();
            if (pageId != null && platform != null) {
                queryWrapper.eq("page_id", pageId);
                queryWrapper.in("platform", platform, "ALL"); //or ALL
                queryWrapper.eq("switch_status", "on");
                List<RobustBaseResponse> list = robustBaseResponseMapper.selectList(queryWrapper);
                if (list != null && list.size() != 0) {
                    for (int i = 0; i < list.size(); i++) {
                        String api = list.get(i).getApi();
                        String responseBody = list.get(i).getResponseBody().toString();
                        Date updateTime = list.get(i).getUpdateTime();
                        Date now = new Date();
                        if (updateTime != null) {
                            int diffDays = (int) ((now.getTime() - updateTime.getTime()) / (1000 * 60 * 60 * 24));
                            if (diffDays <= 60) {
                                if (!resp.containsKey(api)) {
                                    resp.put(api, responseBody);
                                }
                            }
                        }
                    }
                }
            }
            return resp;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public HashMap<String, Integer> getAllPageDescription() {
        QueryWrapper<CommonPage> queryWrapper = new QueryWrapper();
        HashMap<String, Integer> map = new HashMap<>();
        List<CommonPage> pageList = commonPageMapper.selectList(queryWrapper
                .groupBy("page_description"));

        try {
            if ((pageList != null && pageList.size() != 0)) {
                for (CommonPage page : pageList) {
                    String description = page.getPageDescription();
                    int id = page.getId();
                    if (!map.containsKey(description)) {
                        map.put(description, id);
                    }
                }
            }
        } catch (Exception e) {
            map.put(e.toString(), 0);
            return map;
        }
        return map;
    }

    @Override
    public void writeBaseResponseIntoDB(RobustBaseResponse body) {
        robustBaseResponseMapper.insert(body);
    }

    @Override
    public void updateCommonPage(CommonPage commonPage) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("page_description",commonPage.getPageDescription());
        CommonPage commonPage1 = commonPageMapper.selectOne(queryWrapper);
        if (null == commonPage1){
            commonPageMapper.insert(commonPage);
        }else {
            commonPageMapper.update(commonPage,queryWrapper);
        }
    }

    @Override
    public void updateTriggerConfig(RobustAutoConfig robustAutoConfig) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("bu",robustAutoConfig.getBu());
        queryWrapper.eq("trigger_time",robustAutoConfig.getTriggerTime());
        RobustAutoConfig  robustAutoConfig1 = robustAutoConfigMapper.selectOne(queryWrapper);
        if (null == robustAutoConfig1){
            robustAutoConfigMapper.insert(robustAutoConfig);
        }else {
            robustAutoConfigMapper.update(robustAutoConfig,queryWrapper);
        }
    }


    public HashMap<String, Integer> structureDataAuto(HashMap<String, Map<Integer, List<List>>> fuzzNodeDatas, HashMap<String, String> resultMap, int robustBuildId, PageInfo pageInfo, String appName) {
//        singleNodeData里面存了一个页面下所有的接口
        //urlResultListInPage里存了一个页面维度所有api构造出来的数据，存于modifyDetail中
        HashMap<String, Integer> noModifyMap = new HashMap<>();
        HashMap<String, Integer> apiCaseCount = new HashMap<>();
        Object abObject = pageInfo.getAb();
        Object divide = pageInfo.getAbDivideStrategies();
        if (null == abObject) {
            dx.sendToPersionByCompass("没有收到ab接口的KV", "sunkangtong");
        } else {
            JsonObject abData = new JsonParser().parse(abObject.toString()).getAsJsonObject();
            JsonObject mockResult = new MockUtil().create(abURL, abData);
            int mockId = 0;
            try {
                if (null != mockResult) mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
            } catch (Exception e) {
                dx.sendToPersionByCompass(e.getMessage() + mockResult.getAsString(), "sunkangtong");
            }
            noModifyMap.put(abURL, mockId);
        }
        if (null == divide) {
            dx.sendToPersionByCompass("没有收到画像接口的KV", "sunkangtong");
        } else {
            JsonObject divideData = new JsonParser().parse(divide.toString()).getAsJsonObject();
            JsonObject mockResult = new MockUtil().create(divideStrategies, divideData);
            int mockId = 0;
            try {
                if (null != mockResult) mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
            } catch (Exception e) {
                dx.sendToPersionByCompass(e.getMessage() + mockResult.getAsString(), "sunkangtong");
            }
            noModifyMap.put(divideStrategies, mockId);
        }

        int maxIndex = 0;
        HashMap<String, ArrayList<RobustResult>> urlResultListInPage = new HashMap();
        for (Map.Entry entry : fuzzNodeDatas.entrySet()) {
            ArrayList<RobustResult> singleUrlResultList = new ArrayList();
            String api = (String) entry.getKey();
            String baseData = resultMap.get(api);
            Map<Integer, List<List>> fuzzNodes = (Map<Integer, List<List>>) entry.getValue();
            ArrayList<List<List>> duplicationNodeListNew = new RobustUtil().makeFriendNodeList(fuzzNodes);
            for (List<List> afterFriengDuplocationNodeList : duplicationNodeListNew) {
                JsonObject changeResponse = null;
                JSONArray modifyDetailList = new JSONArray();
                for (List duplicationList : afterFriengDuplocationNodeList) {
                    boolean needSave = false;
                    if (duplicationList.equals(afterFriengDuplocationNodeList.get(afterFriengDuplocationNodeList.size() - 1)))
                        needSave = true;
                    if (null == changeResponse) {
                        changeResponse = new JsonUtil().createCase(baseData, duplicationList, api, singleUrlResultList, needMock, 1, needSave, modifyDetailList);
                    } else {
                        changeResponse = new JsonUtil().createCase(changeResponse.toString(), duplicationList, api, singleUrlResultList, needMock, 1, needSave, modifyDetailList);
                    }
                }
            }
            maxIndex = Math.max(maxIndex, singleUrlResultList.size());
            Collections.shuffle(singleUrlResultList);
            urlResultListInPage.put(api, singleUrlResultList);
            apiCaseCount.put(api, singleUrlResultList.size());
        }
        maxIndex = Math.min(maxIndex, 1000);//做一个保护，超过1000就不构造了
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        for (int i = 0; i < maxIndex; i++) {
            List singleRobustResult = new ArrayList();
            List modifyUrl = new ArrayList();
            for (Map.Entry entry : urlResultListInPage.entrySet()) {
                ArrayList<RobustResult> singleUrlModifyDetail = (ArrayList<RobustResult>) entry.getValue();
                if (singleUrlModifyDetail.size() <= i) continue;
                String tempModifyDetail = singleUrlModifyDetail.get(i).getModifyDetail();
                DocumentContext tempTemplateDataJson = JsonPath.using(configuration).parse(tempModifyDetail);
                JsonObject apiModify = new JsonParser().parse(tempTemplateDataJson.jsonString())
                        .getAsJsonArray().get(0).getAsJsonObject();
                singleRobustResult.add(apiModify);
                modifyUrl.add(entry.getKey());
            }
            for (Map.Entry entry : noModifyMap.entrySet()) {
                JSONObject modifyDetailMap = new JSONObject();
                modifyDetailMap.put("modify_url", entry.getKey());
                modifyDetailMap.put("mockId", entry.getValue());
                singleRobustResult.add(modifyDetailMap);
            }
            RobustResult result = new RobustResult();
            result.setModifyDetail(singleRobustResult.toString());
            result.setModifyUrl(modifyUrl.toString());
            result.setBuildId(robustBuildId);
            result.setStartTime(new Date());
            result.setAppName(appName);
            result.setCommentState(NO_COMMENTS_YET);  //入库之前，初始化comment_state、crash_result字段
            result.setCrashResult(CRASH_RESULT_DEFAULT);
            robustResultMapper.insert(result);
        }
        return apiCaseCount;
    }


    /**
     * @param resultMap
     * @return
     */
    public HashMap<String, Map<Integer, List<List>>> fuzzDataAuto(HashMap<String, String> resultMap) {
        HashMap<String, Map<Integer, List<List>>> singleNodeData = new HashMap();
        for (Map.Entry entry : resultMap.entrySet()) {
            String url = (String) entry.getKey();
            String responseString = (String) entry.getValue();
            JsonObject responseBody = new JsonParser().parse(responseString).getAsJsonObject();
            Map<Integer, List<List>> duplicationNodeMap = new HashMap<>();
            List<List> allField = new ArrayList<>();
            allField = new JsonUtil().readObject(responseBody, rootPath, allField);
            List<List> duplicationNodeList = new JsonUtil().markDuplicationNode(allField);
            for (List duplicationNode : duplicationNodeList) {
                int index = (int) duplicationNode.get(3);
                List<List> tempList = new ArrayList<>();
                if (duplicationNodeMap.containsKey(index)) {
                    tempList = duplicationNodeMap.get(index);
                }
                tempList.add(duplicationNode);
                duplicationNodeMap.put(index, tempList);
            }
            singleNodeData.put(url, duplicationNodeMap);
        }
        return singleNodeData;
    }


    public Resp triggerRobustJob(String triggerTime, String apkUrl, String platform, String appName, String body) {
        if (StringUtil.isBlank(appName)){
            return Resp.error(ErrorEnum.S_400, "缺失appName参数，需填入正确的appName以触发测试");
        }
        //// TODO: 2022/12/27 如果 apkUrl 不为空，要判断下该链接是否属于该appName
        if (null == triggerTime || null == platform) {
            return Resp.error(ErrorEnum.S_400, "参数triggerTime或platform缺失！triggerTime:" + triggerTime + "; platform:" + platform);
        }
        String version = "";
        //step1:获取测试包
        if (null == apkUrl) {
            //没有测试包，默认获取回归测试包
            try {
                version = new JsonParser().parse(body).getAsJsonObject().get("content").getAsJsonObject().get("versionNumber").getAsString();
                ApkInfo apkInfo = new ApkInfo();
                apkInfo.setCurrentBranch(1);
                apkInfo.setOs(platform);
                apkInfo.setProcessType(AppIterationStatusEnum.REGRESSION_PROCESS.getCode());
                if (platform.equals("Android")) {
                    apkInfo.setArch64("1");
                    apkInfo.setBuildTypeName("Release 打包");
                    if (appName.equals("youxuan")){
                        apkInfo.setAppName("youxuan");
                    }else if (appName.equals("meituan")) {
                        apkInfo.setAppName("com.sankuai.meituan");
                    }else {
                        return Resp.error(ErrorEnum.S_400, "不支持该appName参数");
                    }
                } else {
                    if (appName.equals("youxuan")){
                        apkInfo.setAppName(appName);
                    }else if (appName.equals("meituan")){
                        apkInfo.setAppName("imeituan");
                    }else {
                        return Resp.error(ErrorEnum.S_400, "不支持该appName参数");
                    }
                    apkInfo.setBuildTypeName("组件集成打包");
                }
                String response = getApkDataController.getApkData(apkInfo);
                if (response.contains("appUrl")) {
                    apkUrl = new JsonParser().parse(response).getAsJsonObject().get("appUrl").getAsString();
                }
                log.info(apkUrl);
            } catch (Exception e) {
                return Resp.error(ErrorEnum.S_400, "没有os参数！");
            }
        }
        //step2:插入trigger表
        String triggerIds = "";
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("trigger_time", triggerTime);
        queryWrapper.like("app_name", appName);
        List<RobustAutoConfig> robustAutoConfigs = new ArrayList<>();
        try {
            robustAutoConfigs = robustAutoConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            return Resp.error(ErrorEnum.S_400, e.toString());
        }
        for (int i = 0; i < robustAutoConfigs.size(); i++) {
            RobustTriggerJob robustTriggerJob = new RobustTriggerJob();
            robustTriggerJob.setAutoConfigId(robustAutoConfigs.get(i).getId());
            robustTriggerJob.setPlatform(platform);
            robustTriggerJob.setApkUrl(apkUrl);
            robustTriggerJob.setVersionInfo(version);
            robustTriggerJob.setAppName(appName);
            String pageIds = robustAutoConfigs.get(i).getPage();
            if (null != pageIds) {
                robustTriggerJob.setPageNumber(pageIds.split("_").length);
            }
            robustTriggerJobMapper.insert(robustTriggerJob);
            if (triggerIds.isEmpty()) {
                triggerIds = robustTriggerJob.getId().toString();
            } else {
                triggerIds += "_" + robustTriggerJob.getId().toString();
            }
        }
        log.info(triggerIds);
        if (EnvUtil.isOnline()){
            new JenkinsUtil().robustPreJob(triggerIds, platform, apkUrl, newTest, EnvUtil.getEnv().toString(),appName);
        }
        return Resp.success();
    }

    @Override
    public Resp updateRobustTriggerJob(RobustTriggerJob robustTriggerJob) {
        String mode = robustTriggerJob.getMode();
        int triggerId = robustTriggerJob.getId();
        RobustTriggerJob robustTriggerJob1 = robustTriggerJobMapper.selectById(triggerId);
        if (mode.equals(newTest)) {
            robustTriggerJob1.setConanJobId(robustTriggerJob.getConanJobId());
            robustTriggerJob1.setJenkinsUrl(robustTriggerJob.getJenkinsUrl());
        } else if (mode.equals(retrySkipCase)) {
            robustTriggerJob1.setConanJobId(robustTriggerJob1.getConanJobId() + "_" + robustTriggerJob.getConanJobId());
            robustTriggerJob1.setJenkinsUrl(robustTriggerJob1.getJenkinsUrl() + "," + robustTriggerJob.getJenkinsUrl());
        } else {
            return Resp.error(ErrorEnum.S_400, "mode入参错误，实际传入的mode是：" + mode);
        }
        robustTriggerJobMapper.updateById(robustTriggerJob1);
        return Resp.success();
    }

    @Override
    public Resp getPageInfo(String triggerId, String mode) {
        StringUtil st = new StringUtil();
        if (st.isEmpty(triggerId)) return Resp.error(ErrorEnum.S_400, "参数异常");
        //1-》先用triggerId在robust_trigger_job表拿数据，得到auto_config_id
        RobustTriggerJob robustTriggerJob = robustTriggerJobMapper.selectOne(new QueryWrapper<RobustTriggerJob>().eq("id", triggerId));
        if (null == robustTriggerJob) return Resp.error(ErrorEnum.S_400, "robustTriggerJob 为null");
        int autoConfigId = robustTriggerJob.getAutoConfigId();
        //2-》用auto_config_id 在robust_auto_config里 得到pageId的集合
        RobustAutoConfig robustAutoConfig = robustAutoConfigMapper.selectOne(new QueryWrapper<RobustAutoConfig>().eq("id", autoConfigId));
        String allConfigPage = robustAutoConfig.getPage();
        if (st.isEmpty(allConfigPage)) return Resp.error(ErrorEnum.S_400, "page信息为空");
        String callBackPageString = robustTriggerJob.getCallbackPage();
        String[] callBackPage = new String[0];
        if (!st.isEmpty(callBackPageString)) {
            callBackPage = callBackPageString.split("_");
        }
        String[] pageList = allConfigPage.split("_");
        ArrayList<HashMap> resultArray = new ArrayList();
        for (String pageId : pageList) {
            if ((Arrays.asList(callBackPage).contains(pageId)) && mode.equals(retrySkipCase)) continue;
            HashMap tempMap = new HashMap();
            CommonPage commonPage = commonPageMapper.selectOne(new QueryWrapper<CommonPage>().eq("id", pageId));
            tempMap.put("pageId", pageId);
            tempMap.put("schemaIOS", commonPage.getSchemaIos());
            tempMap.put("schemaAndroid", commonPage.getSchemaAndroid());
            tempMap.put("whiteApi", commonPage.getWhiteApi());
            tempMap.put("blackApi", commonPage.getBlackApi());
            resultArray.add(tempMap);
        }
        Resp r = new Resp<>();
        r.setCode("0000");
        r.setMsg("ok");
        r.setData(resultArray);
        return r;
    }

    public Resp triggerOnePlatformYouXuanRobust(String body, String platform) {
        Resp resp = new Resp();
        log.info("this is body:\n" + body);
        JsonParser jp = new JsonParser();
        JsonObject bodyJson = (JsonObject) jp.parse(body);
        String eventType = "";
        String version = "";
        try {
            eventType = bodyJson.get("eventType").getAsString();
            version = bodyJson.get("content").getAsJsonObject().get("versionNumber").getAsString();
        }catch (Exception e){
            log.info("Error"+e);
        }
        if (Objects.equals(eventType, "APP_REGRESS")) {
            final int oneDay = 1000 * 60 * 60 * 24;
            log.info("一天后触发" + platform + "优选健壮性测试\n");
            String finalVersion = version;
            setTimeout(() -> actuallyTriggerYouXuan(platform, finalVersion), oneDay);
            log.info("此条日志证明进程未阻塞");
            resp.setCode(200);
            resp.setMsg("一天后将触发优选健壮性测试，平台：" + platform);
        } else {
            resp.setCode(-1);
            resp.setMsg("此事件不是回归，不执行任何操作。");
        }
        return resp;
    }

    public void actuallyTriggerYouXuan(String platform, String version) {
        log.info("进入了actuallyTriggerYouXuan方法");
        if (new LionUtil().getBooleanValue("EnableYouXuanRobust")) {
            log.info("开始真正触发优选" + version + "健壮性测试!");
            JsonObject versionJson = new JsonObject();
            JsonObject contentJson = new JsonObject();
            contentJson.addProperty("versionNumber", version);
            versionJson.add("content", contentJson);
            Resp triggerRes = triggerRobustJob(
                    "yxRegression",
                    null,
                    platform,
                    "youxuan",
                    versionJson.toString()
            );
            log.info("triggerResult is: " + triggerRes);
            log.info(platform + "优选健壮性测试触发完毕！");
            if (EnvUtil.isOnline()) {
                new DxUtil().sendToPersionByCompass(platform + "优选健壮性测试已触发！", "sunkangtong", "zhaojunming02");
            }
        }
    }

    //类似javaScript中的setTimeout，用法：setTimeout(()->f(arg1,arg2,...),1000)
    public static void setTimeout(Runnable runnable, int delay) {
        new Thread(() -> {
            try {
                Thread.sleep(delay);
                runnable.run();
            } catch (Exception e) {
                System.err.println(e);
            }
        }).start();
    }
}
