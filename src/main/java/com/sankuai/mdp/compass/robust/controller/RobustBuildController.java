package com.sankuai.mdp.compass.robust.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.ValueUtil;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.robust.entity.*;

import com.sankuai.mdp.compass.robust.service.RobustBuildService;
import com.sankuai.mdp.compass.robust.service.RobustResultService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import javax.annotation.PostConstruct;
import java.util.*;


/**
 * Created by skt on 2022/04/26.
 */
@Slf4j
@RestController
@RequestMapping("/compass/api/RobustJob")
public class RobustBuildController extends BaseController {
    @Autowired
    RobustBuildService robustBuildService;
    @Autowired
    RobustResultService robustResultService;

    DxUtil dxUtil = new DxUtil();

    /**
     *触发新任务
     */
    @PostMapping("newJob")
    public Resp add(@RequestBody String body) {
        Resp result = robustBuildService.createNewJob(body);
        JsonObject responseBody =  new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload =  responseBody.get("payload").getAsJsonObject();
        String creator = payload.get("creator").getAsString();
        String code = result.getCode();
        dxUtil.sendToPersionByCompass("健壮性测试触发结果："+result.getMsg(), creator);
        System.out.println("任务执行结束 "+ new ValueUtil().printNowTime());
        return  result;
    }

    @PostMapping("triggerAndroidYouXuanRobust")
    public Resp triggerAndroidYouXuanRobust(@RequestBody String body){
        return robustBuildService.triggerOnePlatformYouXuanRobust(body,"Android");
    }

    @PostMapping("triggeriOSYouXuanRobust")
    public Resp triggeriOSYouXuanRobust(@RequestBody String body){
        return robustBuildService.triggerOnePlatformYouXuanRobust(body,"iOS");
    }

    @PostMapping("test")
    public Resp add(){
        HashMap<String,String> re = robustBuildService.getRespByPageId("123", "iOS");
        System.out.println(re);
        return null;
    }


    /**
     *触发新任务
     */
    @PostMapping("testCount")
    public Resp testCount(@RequestBody String body) {
        Resp result = robustBuildService.testCount(body);
        JsonObject responseBody =  new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload = responseBody.get("payload").getAsJsonObject();
        String creator = payload.get("creator").getAsString();
        String code = result.getCode();
//        dxUtil.sendToPersionByCompass("健壮性测试触发结果："+result.getMsg(), creator);
        System.out.println("任务执行结束 "+ new ValueUtil().printNowTime());
        return  result;
    }


    /**
     *重试任务
     */
    @SneakyThrows
    @PostMapping("retryJob")
    public Resp retryJob(@RequestBody String body) {
        Resp result = robustBuildService.retryJob(body);
        JsonObject responseBody =  new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload =  responseBody.get("payload").getAsJsonObject();
        String creator = payload.get("creator").getAsString();
        String code = result.getCode();
        dxUtil.sendToPersionByCompass("健壮性测试触发结果："+result.getMsg(), creator);
        return  result;
    }

    /**
     *提交任务回写接口--回写云测job id等信息
     * 接口文档https://km.sankuai.com/page/1307363846
     */
    @PostMapping("/updateRobustJob")
    public Resp updateRobustJob(RobustBuild robustBuild){
        return robustResultService.updateRobustJob(robustBuild);
    }


    //根据 robust_build表id 查询用例
    @GetMapping("/getRobustCase")
    public Resp getRobustCase(@RequestParam("jobId") String buildJobId, String mode){
        return robustBuildService.getRobustCase(buildJobId,mode);
    }

    /*
     * compass/api/robust/resultReport
     * */
    @GetMapping("/resultReport")
    public Resp getResultReport(@RequestParam("jobId") String jobId){
        return robustResultService.getResultReport(jobId);
    }

    /**
     * 云测回调
     * @param conanBody
     * @return
     */
    @PostMapping("/conanUpdate")
    public Resp result(@RequestBody JSONObject conanBody) {
        return robustResultService.conanUpdate(conanBody);
    }

    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, RobustBuild robustBuild)  {
        IPage<RobustBuild> robustBuildIPage = this.robustBuildService.list(request, robustBuild);
        if (robustBuildIPage != null) {
            return getDataTable(robustBuildIPage);
        } else {
            return null;
        }
    }
    @GetMapping("/getAllCreator")
    public List<String> getAllCreator() {
        return robustBuildService.getAllCreator();
    }


    /**

     * 上传页面对应的api、response、ab、horn信息
     * 接口文档https://km.sankuai.com/page/1307363846
     */
    @PostMapping("/uploadPageInfo")
    public Resp uploadPageInfo(@RequestBody PageInfo pageInfo){
        return robustBuildService.uploadPageInfo(pageInfo);
    }



    public static void main(String[] args) {
        String result = "";
        String s = "covid2019";
        StringBuilder st =  new StringBuilder();
        char[] str=s.toCharArray();
        System.out.println(str);
        int len = s.length();
        Arrays.sort(str);
        System.out.println(str);
        for(int i=0,j=len-1;i<j;i++,j--){
            System.out.println(i+" "+str[i]+" "+j+" "+str[j]);
            if (str[i]>'9' || str[i]<'a')
                result = "";
            st.append(str[i]);
            st.append(str[j]);
        }
        System.out.println(st.toString());
        if (len%2==1){

        }

    }



    /**
     * 触发指定时机的任务
     * @param triggerTime
     * @param apkUrl
     * @param platform  Android/iOS
     * @return
     */
    @PostMapping("/triggerRobust")
    public Resp triggerRobust(String triggerTime, String apkUrl, String platform,String appName, @RequestBody String body){
        return robustBuildService.triggerRobustJob(triggerTime,apkUrl,platform,appName,body);
    }


    @PostMapping("/updateRobustTriggerJob")
    public Resp updateRobustTriggerJob(RobustTriggerJob robustTriggerJob){
        return robustBuildService.updateRobustTriggerJob(robustTriggerJob);
    }

    @GetMapping("/getPageInfo")
    public Resp getPageInfo(String triggerId, String mode) {
        return robustBuildService.getPageInfo(triggerId, mode);
    }

    @GetMapping("/getAllPageDescription")
    public HashMap<String, Integer> getAllPageDescription() {
        return robustBuildService.getAllPageDescription();
    }


    @PutMapping("/updateCommonPage")
    public void updateCommonPage(CommonPage commonPage) {
        robustBuildService.updateCommonPage(commonPage);
    }


    @PutMapping("/updaterobustAutoConfig")
    public void updateTriggerConfig(RobustAutoConfig robustAutoConfig) {
        robustBuildService.updateTriggerConfig(robustAutoConfig);
    }


}
