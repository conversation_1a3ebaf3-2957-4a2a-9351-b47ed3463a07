package com.sankuai.mdp.compass.robust.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.enums.ErrorEnum;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.robust.entity.RobustAutoConfig;
import com.sankuai.mdp.compass.robust.entity.RobustBuild;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import com.sankuai.mdp.compass.robust.entity.RobustTriggerJob;
import com.sankuai.mdp.compass.robust.mapper.RobustAutoConfigMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustBuildMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustResultMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustTriggerJobMapper;
import com.sankuai.mdp.compass.robust.service.RobustResultService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
/**
 * <AUTHOR>
 * @date ：Created in 2022/4/27 3:52 下午
 */
@Service
@Slf4j
public class RobustResultServiceImpl extends ServiceImpl<RobustBuildMapper, RobustBuild> implements RobustResultService {
    @Autowired
    RobustBuildMapper robustBuildMapper;
    @Autowired
    RobustResultMapper robustResultMapper;
    @Autowired
    RobustTriggerJobMapper robustTriggerJobMapper;

    @Autowired
    RobustAutoConfigMapper robustAutoConfigMapper;
    @Autowired
    ConanJobService conanJobService;




    private static int openMockStatus = 1;

    ConanUtil conanUtil = new ConanUtil();
    DxUtil dxUtil = new DxUtil();
    ValueUtil v = new ValueUtil();
    public static final int CONAN_CASE_FAIL = 0;
    public static final int CONAN_CASE_PASS = 1;
    public static final int CONAN_CASE_CRASH = 2;
    public static final int CONAN_CASE_SKIP = 3;
    public static final int CONAN_CASE_WAIT = 4;
    public static final int ROBUST_CASE_SKIP = 0;
    public static final int ROBUST_CASE_FAIL_BY_UI = 100;
    public static final int ROBUST_CASE_FAIL_BY_COMPASS = 101;
    public static final int ROBUST_CASE_SUCCESS_BY_UI = 200;
    public static final int ROBUST_CASE_SUCCESS_BY_COMPASS = 201;
    public static final int ROBUST_CASE_CRASH = 3;
    public static final int ROBUST_COMBINATION_CASE_CRASH = 301;
    public static final int ROBUST_CASE_JUDGE_FAIL = 1000;
    public static final int ROBUST_CASE_JUDGE_SUCCESS = 2000;
    public static final int CRASH_RESULT_DEFAULT = -2;//数据库中crash_result字段计算前的默认值
    public static final int NO_COMMENTS_YET = 0;//表示这条case尚未添加过评论
    public static String newTest = "new";
    public static String retrySkipCase = "retrySkipCase";
    public static String retryCombinationCrashCase = "retryCombinationCrashCase";
    @Override
    public Resp updateRobustJob(RobustBuild robustBuild) {
        log.info("开始测试");
        Integer id = robustBuild.getId();
        RobustBuild robustBuild1 = robustBuildMapper.selectById(id);
        if (null != robustBuild1) {
            robustBuild1.setBuildUrl(robustBuild.getBuildUrl());
            robustBuild1.setStatus(robustBuild.getStatus());
            if (robustBuild.getMode().equals(newTest)) {
                log.info("---robust:新测试，直接更新云测jobid");
                robustBuild1.setConanJobId(robustBuild.getConanJobId());
                robustBuild1.setTotalBuildUrl(robustBuild.getTotalBuildUrl());
            } else if (robustBuild.getMode().equals(retrySkipCase)) {
                log.info("----robust，重新触发未执行case，追加云测jobid");
                String conanJobId = robustBuild1.getConanJobId() + "_" + robustBuild.getConanJobId();
                robustBuild1.setConanJobId(conanJobId);
                String totalBuildUrl = robustBuild1.getTotalBuildUrl() + "_" + robustBuild.getTotalBuildUrl();
                robustBuild1.setTotalBuildUrl(totalBuildUrl);
            } else if (robustBuild.getMode().equals(retryCombinationCrashCase)) {
                log.info("----robust，重新触发聚合崩溃case，追加云测jobid");
                String conanJobId = robustBuild1.getConanJobId() + "_" + robustBuild.getConanJobId();
                robustBuild1.setConanJobId(conanJobId);
                String totalBuildUrl = robustBuild1.getTotalBuildUrl() + "_" + robustBuild.getTotalBuildUrl();
                robustBuild1.setTotalBuildUrl(totalBuildUrl);
            } else {
                log.info("----robust, 模式不存在");
            }
            robustBuildMapper.updateById(robustBuild1);
        } else {
            return Resp.error(ErrorEnum.S_400, "查找不到该任务，请检查id是否正确");
        }
        return Resp.success();
    }

    @Override
    public Resp updateRobustResult(JSONObject body1) {
        log.info("动态上传用例结果接口-参数：" + body1.toString());
        JsonObject body = new JsonParser().parse(body1.toString()).getAsJsonObject();
        if (body.has("id") && body.get("id").getAsString() != null) {
            RobustResult robustResult = robustResultMapper.selectById(body.get("id").getAsString());
            if (null != robustResult) {
                if (body.has("caseResult")) {
                    // 兼容从前端确认的caseResult
                    int result = body.get("caseResult").getAsInt();
                    if (result == ROBUST_CASE_JUDGE_FAIL || result == ROBUST_CASE_JUDGE_SUCCESS) {
                        result += robustResult.getCaseResult();
                        String user = "";
                        if (body.has("user")) {
                            user = body.get("user").getAsString();
                        }
                        log.info("user:" + user + "人工干预了测试id为" + robustResult.getId() + "的结果" + "干预值" + result);
                        robustResult.setCaseResult(result);
                        if (body.has("failReason")) {
                            robustResult.setFailReason(body.get("failReason").getAsString());
                        }
                    }
                    //如果本次结果是未执行或xray异常（状态码都是0），并且原先已经保存了有结果的状态，则不覆盖原先结果，否则覆盖原先结果
                    if (!(ROBUST_CASE_SKIP==result && ROBUST_CASE_SKIP!=robustResult.getCaseResult())){
                        //如果上一次是崩溃，下一次不是成功，也没必要更新case
                        if (!(ROBUST_CASE_CRASH == robustResult.getCaseResult() && ROBUST_CASE_SUCCESS_BY_UI != result)){
                            robustResult.setCaseResult(result);
                            if (body.has("failReason")) {
                                robustResult.setFailReason(body.get("failReason").getAsString());
                            }
                        }
                    }
                }
                if (body.has("pic")) {
                    if (robustResult.getPic() != null && !robustResult.getPic().equals("")) {
                        String pic = robustResult.getPic() + "|" + body.get("pic").getAsString();
                        robustResult.setPic(pic);
                    } else {
                        robustResult.setPic(body.get("pic").getAsString());
                    }
                }
                robustResultMapper.updateById(robustResult);
                return Resp.success();
            }
            return Resp.error(ErrorEnum.S_400, "查找不到该任务，请检查id是否正确");
        } else {
            return Resp.error(ErrorEnum.S_400, "参数缺失");
        }
    }

    @Override
    public Resp getResultReport(String buildJobId) {
        if (buildJobId == null || buildJobId.equals("")) {
            return Resp.error(ErrorEnum.S_400, "入参错误");
        } else {
            updateCaseIssueKeyBybuildJobId(buildJobId);
            RobustBuild robustBuild = robustBuildMapper.selectById(buildJobId);
            JsonObject respData = new JsonObject();
            // 设置测试项信息
            respData.addProperty("jobId", buildJobId);
            respData.addProperty("caseCount", robustBuild.getCaseCount());
            respData.addProperty("apkUrl", robustBuild.getApkUrl());
            respData.addProperty("platform", robustBuild.getPlatform());
            // FIXME 确认这里的是pageId还是pageName
            respData.addProperty("pageId", robustBuild.getPageId());

            respData.addProperty("apiCaseCount", robustBuild.getApiCaseCount());
            respData.addProperty("schemaUrl", robustBuild.getSchemaUrl());
            respData.addProperty("appName", robustBuild.getAppName());
            // 设置result信息
            JsonArray resultArray = new JsonArray();
            /*  result  failReason  modifyKey modifyValue moduleName crashUrl pic* */
            QueryWrapper<RobustResult> robustResultQueryWrapper = new QueryWrapper<>();
            robustResultQueryWrapper.eq("build_id", buildJobId);
            List<RobustResult> robustResultList = robustResultMapper.selectList(robustResultQueryWrapper);
            if (robustResultList != null && robustResultList.size() != 0) {
                for (RobustResult robustResultItem : robustResultList) {
                    // 更新当前case的result信息
//                    robustResultItem = updateCaseIssueKeyByCaseId(robustResultItem);
                    JsonObject resInfo = new JsonObject();
                    // 写入case的基本build信息
                    // FIXME(确认是module Id，还是name)
                    resInfo.addProperty("moduleName", robustBuild.getMoudleId());
                    // 写入case的基本结果信息
                    int result = robustResultItem.getCaseResult();
                    if (result == 3) {
                        result = getCrashResult(robustResultItem);
                    }
                    resInfo.addProperty("result", result);
                    resInfo.addProperty("failReason", robustResultItem.getFailReason());
                    resInfo.addProperty("onesUrl", robustResultItem.getIssueKey());
                    resInfo.addProperty("pic", robustResultItem.getPic());
                    resInfo.addProperty("caseId", robustResultItem.getId());
                    resInfo.addProperty("conanTaskId", robustResultItem.getConanTaskId());
                    // 拼接modifyKey modifyValue 是个JSONArray
                    JsonArray modifyInfo = new JsonArray();
                    JsonArray modifyDetail = (new JsonParser().parse(robustResultItem.getModifyDetail()).getAsJsonArray());
                    if (modifyDetail != null && modifyDetail.size() != 0) {
                        for (int i = 0; i < modifyDetail.size(); ++i) {
                            try {
                                JsonObject modifyDetailItemJson = modifyDetail.get(i).getAsJsonObject();
                                if (modifyDetailItemJson.has("modify_diff")) {
                                    try {
                                        JsonArray modifyDiffArray = modifyDetailItemJson.get("modify_diff").getAsJsonArray();
                                        for (int j = 0; j < modifyDiffArray.size(); ++j) {
                                            JsonObject modifyDiffItem = modifyDiffArray.get(j).getAsJsonObject();
                                            //  modifyKey:"a", modifyValue:"b"
                                            JsonObject modifyItemJson = new JsonObject();
                                            if (modifyDiffItem.has("key")) {
                                                modifyItemJson.addProperty("modifyKey", modifyDiffItem.get("key").getAsString());
                                            }
                                            if (modifyDiffItem.has("value")) {
                                                modifyItemJson.addProperty("modifyValue", modifyDiffItem.get("value").getAsString());
                                            }
                                            if (modifyDiffItem.has("modify_desc")) {
                                                modifyItemJson.addProperty("modifyDesc", modifyDiffItem.get("modify_desc").getAsString());
                                            }
                                            if (modifyDetailItemJson.has("mockId")) {
                                                modifyItemJson.addProperty("mockId", modifyDetailItemJson.get("mockId").getAsInt());
                                            }
                                            modifyInfo.add(modifyItemJson);
                                        }
                                    } catch (Exception e) {
                                        log.error(e.toString());
                                        log.error("解析modify_diff字段异常");
                                    }
                                }
                            } catch (Exception e) {
                                log.error("modifyDetail格式非标准JsonArray");
                            }
                        }
                        resInfo.add("modifyInfo", modifyInfo);
                    } else {
                        return Resp.error(ErrorEnum.S_400, "未获取到构造数据信息");
                    }
                    resultArray.add(resInfo);
                }
                respData.add("results", resultArray);
            } else {
                return Resp.error(ErrorEnum.S_400, "未获取到case信息");
            }
            return Resp.success(respData);
        }
    }

    public int getCrashResult(RobustResult robustResult) {
        if (robustResult.getIssueKey() == null) return 302;
        String conanJobId = String.valueOf(robustResult.getConanJobId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startTime = sdf.format(robustResult.getStartTime());
        String endTime = sdf.format(robustResult.getFinishTime());
        ;
        String issueKey1 = robustResult.getIssueKey();
        try {
            JsonArray jobCrashDetailList = conanUtil.getJobCrashInfo(conanJobId);
            if (jobCrashDetailList != null) {
                for (int i = 0; i < jobCrashDetailList.size(); i++) {
                    JsonObject jsonObject = jobCrashDetailList.get(i).getAsJsonObject();
                    String issueKey2 = jsonObject.get("issue").getAsJsonObject().get("issueKey").getAsString();
                    if (!issueKey1.equals(issueKey2)) continue;
                    String crashUUID = jsonObject.get("uuid").getAsString();
                    String project = jsonObject.get("issue").getAsJsonObject().get("project").getAsString();
                    Map<String, Object> crashDetail = conanUtil.getCrashDetail(crashUUID, project, startTime, endTime);
                    String crashHash = crashDetail.get("crash_hash").toString();
                    String type = crashDetail.get("type").toString();
                    String crashVersion = conanUtil.getMaxMinCrashVersion(crashHash, type, project);
                    String[] version = crashVersion.split("-");
                    if (!version[0].equals(version[1])) return 303;
                    break;
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return 3;
    }

    public void updateCaseIssueKeyBybuildJobId(String buildJobId) {
        RobustBuild robustBuild = robustBuildMapper.selectById(buildJobId);
        double sevenDay = 7.0 * 24 * 60 * 60 * 1000;
        double thirtyM = 30.0 * 1000;
        Date finishTime = robustBuild.getFinishTime();
        Date updateIssueTime = robustBuild.getIssueUpdateTime();
        Date currTime = new Date();
        if (updateIssueTime != null) {
            // 上次更新时间是执行结束时间的2天后，后续不再更新
            if (finishTime != null && updateIssueTime.getTime() - finishTime.getTime() > sevenDay) {
                return;
            }
            // 当前时间距离上次更新还不足30分钟，不更新
            if (currTime.getTime() - updateIssueTime.getTime() < thirtyM) {
                return;
            }
        }
        robustBuild.setIssueUpdateTime(currTime);
        robustBuildMapper.updateById(robustBuild);
        log.info("开始执行更新job-issue" + buildJobId + currTime.toString());
        QueryWrapper<RobustResult> robustResultQueryWrapper = new QueryWrapper<>();
        robustResultQueryWrapper.eq("build_id", buildJobId);
        List<RobustResult> robustResultList = robustResultMapper.selectList(robustResultQueryWrapper);
        // 更新此次健壮性测试下所有的云测job对应的case结果
        Set<String> conanJobSet = new HashSet<>();
        if (robustResultList != null && robustResultList.size() != 0) {
            for (RobustResult robustResultItem : robustResultList) {
                conanJobSet.add(String.valueOf(robustResultItem.getConanJobId()));
            }
        }
        for (String jobId : conanJobSet) {
            JsonArray crashArray = conanUtil.getJobCrashInfo(jobId);
            // caseId - issueKey 的键值对
            HashMap<String, String> caseToIssue = new HashMap<>();
            for (int k = 0; k < crashArray.size(); ++k) {
                try {
                    caseToIssue.put(
                            crashArray.get(k).getAsJsonObject().get("caseId").getAsString(),
                            crashArray.get(k).getAsJsonObject().get("issue").getAsJsonObject().get("issueKey").getAsString());
                } catch (Exception e) {
                    log.info("解析云测返回的issue数组元失败");
                }
            }
            // 获取job的case崩溃信息
            // 把符合要更新的jobId 查询出来。
            QueryWrapper<RobustResult> robustResultQueryWrapperTemp = new QueryWrapper<>();
            robustResultQueryWrapperTemp.eq("conan_job_id", jobId);
            List<RobustResult> robustResultListTemp = robustResultMapper.selectList(robustResultQueryWrapperTemp);
            // 按case去更新jobId
            for (RobustResult robustResultTemp : robustResultListTemp) {
                String caseId = String.valueOf(robustResultTemp.getConanCaseId());
                if (caseToIssue.containsKey(caseId)) {
                    robustResultTemp.setIssueKey(caseToIssue.get(caseId));
                    robustResultMapper.updateById(robustResultTemp);
                    log.info("更新caseId->issueKey" + caseId + caseToIssue.get(caseId));
                }
                //// TODO: 2022/12/2 判断是否是新增，是的话添加到新字段里：issueClass   新增/历史
//                    if (caseToIssue.containsValue(caseId)){
//                        List<String> keyList = new ArrayList();
//                        for(String getKey: caseToIssue.keySet()){
//                            if(caseToIssue.get(getKey).equals(caseId)){
//                                keyList.add(getKey);
//                            }
//                        }
//                        if (keyList.size()>1){
//                            log.error("云测同一个Case有多个Issue,只更新第一个"+caseId);
//                            robustResultTemp.setIssueKey(keyList.get(0));
//                        }
//                        else if (keyList.size()<1){log.info("当前case没有issue"+caseId);}
//                        else {
//                            robustResultTemp.setIssueKey(keyList.get(0));
//                        }
//                    }
            }
        }
        log.info("更新结束job-issue" + buildJobId + new Date().toString());
    }

    public RobustResult updateCaseIssueKeyByCaseId(RobustResult robustResult) {
        int conJobId = robustResult.getConanJobId();
        ConanUtil conanUtil = new ConanUtil();
        // http://conan.sankuai.com/ci/data/jira/list/job/7032813
        JsonArray crashArray = conanUtil.getJobCrashInfo(String.valueOf(robustResult.getConanJobId()));
        for (int i = 0; i < crashArray.size(); ++i) {
            JsonObject crashObject = crashArray.get(i).getAsJsonObject();
            if (crashObject.has("caseId") && crashObject.has("issueKey") &&
                    crashObject.get("caseId").getAsString().equals(String.valueOf(robustResult.getConanCaseId()))) {
                robustResult.setIssueKey(crashObject.get("issueKey").getAsString());
                robustResultMapper.updateById(robustResult);
                break;
            }
        }
        return robustResult;
    }

    public Resp preConanCallback(JSONObject conanBody) {
        Resp resp = new Resp();
        try {
            try {
                Thread.sleep(3000);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            JsonParser jsonParser = new JsonParser();
            //解析云测回调参数
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            int conanJobId = jobData.get("id").getAsInt();
            RobustTriggerJob robustTriggerJob = robustTriggerJobMapper.selectOne(new QueryWrapper<RobustTriggerJob>().like("conan_job_id", conanJobId));
            String[] callBackPage = robustTriggerJob.getCallbackPage().split("_");
            String appName = robustTriggerJob.getAppName();
            if (StringUtil.isBlank(appName))appName="meituan";
            if (callBackPage.length != robustTriggerJob.getPageNumber()) {
                Integer retryCount = robustTriggerJob.getRetryCount();
                if (retryCount == null) retryCount = 0;
                if (retryCount < 3) {
                    robustTriggerJob.setRetryCount(retryCount + 1);
                    robustTriggerJobMapper.updateById(robustTriggerJob);
                    new JenkinsUtil().robustPreJob(Integer.toString(robustTriggerJob.getId()), robustTriggerJob.getPlatform(), robustTriggerJob.getApkUrl(), retrySkipCase, EnvUtil.getEnv().toString(), appName);
                } else {
                    RobustAutoConfig robustAutoConfig = robustAutoConfigMapper.selectOne(new QueryWrapper<RobustAutoConfig>().eq("id", robustTriggerJob.getAutoConfigId()));
                    String manager = robustAutoConfig.getManager();
                    String[] managers = manager.split("_");
                    JsonArray users = new JsonArray();
                    for (int i = 0; i < managers.length; i++) users.add(managers[i]);
                    for (JsonElement user : users) {
                        String u = user.getAsString();
                        dxUtil.sendToPersionByCompass("※ 健壮性测试提醒 ※：\n 【任务ID】：" + robustTriggerJob + "\n" +
                                        "预处理超过最大重试次数"
                                , u);
                    }
                    resp.setCode(-1);
                    resp.setMsg("任务预处理超过最大重试次数");
                    return resp;
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            resp.setCode(-1);
            resp.setMsg("error");
            return resp;
        }
        return Resp.success();
    }

    @Override
    public Resp recurCaseMockdata(String data) {
        Resp resp = new Resp<>();
        JsonObject request = new JsonParser().parse(data).getAsJsonObject();
        int caseId = request.get("caseId").getAsInt();
        String misId = request.get("misId").getAsString();
        LambdaQueryWrapper<RobustResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RobustResult::getId, caseId);
        RobustResult robustResult = robustResultMapper.selectOne(queryWrapper);
        String modifyDetail = robustResult.getModifyDetail();
        try {
            if (!StringUtil.isBlank(modifyDetail)) {
                JsonArray modify = new JsonParser().parse(modifyDetail).getAsJsonArray();
                for (JsonElement jsonElement : modify) {
                    Integer mockId = jsonElement.getAsJsonObject().get("mockId").getAsInt();
                    resp = new MockUtil().updateMockStatus(mockId, misId, openMockStatus);
                }
            }
        } catch (Exception e) {
            resp.setMsg("modifyDetail数据为空");
            resp.setCode("404");
            return resp;
        }
        resp.setMsg("mock已生效，请用任意手机链接appmock");
        return resp;


    }

    public synchronized Resp conanUpdate(JSONObject conanBody) {
        Resp resp = new Resp();
        try {
            JsonParser jsonParser = new JsonParser();
            //解析云测回调参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject timeData = jsonParser.parse(conanBody.get("timedata").toString()).getAsJsonObject();
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            int conanJobId = jobData.get("id").getAsInt();
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.like("conan_job_id", conanJobId);
            try {
                Thread.sleep(3000);
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            RobustBuild robustBuild = robustBuildMapper.selectOne(queryWrapper);
            if (null == robustBuild) {
                resp.setMsg("未找到对应云测任务！");
                resp.setCode(-1);
                return resp;
            }
            //通过conanJobService更新job，task及case表
            conanJobService.collectResult(conanBody);
            try {
                //更新result表
                JsonArray caseList = conanUtil.getAllCaseInfo(Integer.toString(conanJobId));
                if (null != caseList) {
                    for (int i = 0; i < caseList.size(); i++) {
                        JsonObject conanCase = caseList.get(i).getAsJsonObject();
                        String methodName = conanCase.get("methodName").getAsString();
                        try {
                            String[] methodSplit = methodName.split("_");
                            if (methodSplit.length <= 1) {
                                resp.setMsg("获取方法名异常！");
                                resp.setCode(-1);
                                return resp;
                            }
                            for (int j = 1; j < methodSplit.length; j++) {
                                int resultId = Integer.parseInt(methodSplit[j]);
                                RobustResult robustResult = robustResultMapper.selectById(resultId);
                                robustResult.setConanJobId(conanJobId);
                                robustResult.setConanTaskId(conanCase.get("taskId").getAsInt());
                                robustResult.setConanCaseId(conanCase.get("id").getAsInt());
                                int conanCaseStatus = conanCase.get("status").getAsInt();
                                if (conanCaseStatus < 3) {
                                    //case已执行。conanCaseStatus：0执行失败，1执行通过，2崩溃，3未执行，4等待执行
                                    robustResult.setStartTime(sdf.parse(conanCase.get("startTime").getAsString()));
                                    robustResult.setFinishTime(sdf.parse(conanCase.get("endTime").getAsString()));
                                    robustResult.setTotalTime((conanCase.get("duration").getAsFloat()) / (methodSplit.length - 1));
                                }
                                int robuseCaseResult = robustResult.getCaseResult();
                                //通过云测case状态判断用例结果
                                if (CONAN_CASE_CRASH == conanCaseStatus) {
                                    if (methodSplit.length > 2) {
                                        //如果methodSplit的长度大于2，则存在多个Case，把状态更新为聚合Crash
                                        robuseCaseResult = ROBUST_COMBINATION_CASE_CRASH;
                                    } else {
                                        robuseCaseResult = ROBUST_CASE_CRASH;
                                    }
                                } else {
                                    if (ROBUST_CASE_SKIP == robuseCaseResult) {  //结果主要以UI自动化中上传的结果为准，如果自动化没上传结果，再用云测崩溃结果
//                                if(CONAN_CASE_CRASH == conanCaseStatus){
//                                    robuseCaseResult = ROBUST_CASE_CRASH;  //如果自动化没上传结果，再用云测的崩溃结果，过滤掉无效崩溃。2022-05-16注释掉改代码，因为发生有效crash之后case还是会继续执行，导致crash漏判
//                                }
                                    }
                                    if (CONAN_CASE_PASS == conanCaseStatus) {
                                        //云测结果成功，则判断为成功
                                        robuseCaseResult = ROBUST_CASE_SUCCESS_BY_UI;
                                        if (robustResult.getCaseResult() == ROBUST_COMBINATION_CASE_CRASH) {
                                            robustResult.setIssueKey("");
                                            robustResult.setIssueStatus("");
                                            robustResult.setIssueAssignee("");
                                        }
                                    } else if (CONAN_CASE_FAIL == conanCaseStatus) {
                                        //先保留不读云测失败结果，云测有失败误报
                                    }
                                }
                                robustResult.setCaseResult(robuseCaseResult);
                                robustResultMapper.updateById(robustResult);
                            }
                        } catch (Exception e) {
                            log.error("========【robust】获取case id失败: " + methodName + "========");
                            resp.setMsg("error：获取case id失败！caseName: " + methodName);
                            resp.setCode(-1);
                            return resp;
                        }
                    }
                    //如果job下的case列表不为空，则获取该job的crash信息
                    JsonArray jobCrashList = conanUtil.getAllCrashInfo(Integer.toString(conanJobId));
                    if (null != jobCrashList) {
                        log.info("---robust job ---collectUICrash---size of jobCrashList(c) is: " + jobCrashList.size());
                        for (int c = 0; c < jobCrashList.size(); c++) {
                            log.info("collectUICrash" + ",c=" + c);
                            try {
                                JsonObject jsonObject = jobCrashList.get(c).getAsJsonObject();
                                String methodName = jsonObject.get("methodName").getAsString();
                                String[] methodSplit = methodName.split("_");
                                if (methodSplit.length <= 1) {
                                    resp.setMsg("获取方法名异常！");
                                    resp.setCode(-1);
                                    return resp;
                                }
                                for (int j = 1; j < methodSplit.length; j++) {
                                    //int caseId = jsonObject.get("caseId").getAsInt();
                                    int caseId = Integer.parseInt(methodSplit[j]);
                                    String issueKey = jsonObject.get("issueKey").getAsString();
                                    String issueStatus = jsonObject.get("status").getAsString();
                                    String issueAssignee = jsonObject.get("assignee").getAsString();
                                    //QueryWrapper queryWrapper2 = new QueryWrapper();
                                    //queryWrapper2.eq("conan_case_id", caseId);
                                    //log.info("---robust job ---collectUICrash---conan_case_id: " + caseId);
                                    //RobustResult robustResult = robustResultMapper.selectOne(queryWrapper2);
                                    RobustResult robustResult = robustResultMapper.selectById(caseId);
                                    robustResult.setIssueKey(issueKey);
                                    robustResult.setIssueStatus(issueStatus);
                                    robustResult.setIssueAssignee(issueAssignee);
                                    robustResultMapper.updateById(robustResult);
                                }
                            } catch (Exception e) {
                                log.info("---robust job ---collectUICrash---e.getMessage()：" + e.getMessage());
                            }
                        }
                    }
                }
            } catch (Exception e) {
            } finally {
                Integer jobId = robustBuild.getId();
                String conanJobIds = robustBuild.getConanJobId();
                String creator = robustBuild.getCreator();
                String startTime = sdf.format(robustBuild.getStartTime());
                //todo：更新build表finished_conan_job_id，判断测试是否结束，通知发起人测试进度
                String finishedConanJobId = robustBuild.getFinishedConanJobId();
                String finishFlag = "0";
                if (finishedConanJobId == null) {
                    finishedConanJobId = "";
                }
                if (finishedConanJobId == "") {
                    finishedConanJobId = Integer.toString(conanJobId);
                } else if (!finishedConanJobId.contains(Integer.toString(conanJobId))) {
                    finishedConanJobId = finishedConanJobId + "_" + conanJobId;
                }
                if (finishedConanJobId.length() == conanJobIds.length()) {
                    finishFlag = "1";
                    robustBuild.setStatus(1);
                    robustBuild.setFinishTime(new Date());
                    robustBuild.setTotalTime((robustBuild.getFinishTime().getTime() - robustBuild.getStartTime().getTime()) / 1000.0f);

                    //2022-09-06新增：判断本次自动触发的任务是否结束
                    int triggerId = robustBuild.getTriggerId();
                    if (triggerId > 0) {
                        QueryWrapper queryWrapper1 = new QueryWrapper();
                        queryWrapper1.eq("trigger_id", triggerId);
                        List<RobustBuild> allBuilds = robustBuildMapper.selectList(queryWrapper1);
                        if (allBuilds.size() > 0) {
                            boolean flag = true;
                            for (int buildIndex = 0; buildIndex < allBuilds.size(); buildIndex++) {
                                if (1 != allBuilds.get(buildIndex).getStatus() && !allBuilds.get(buildIndex).getId().equals(robustBuild.getId())) {
                                    flag = false;
                                }
                            }
                            if (flag) {
                                //所有任务均为结束状态，更新trigger表的结束时间
                                RobustTriggerJob robustTriggerJob = robustTriggerJobMapper.selectById(triggerId);
                                robustTriggerJob.setFinishTime(new Date());
                                robustTriggerJob.setTotalTime((robustBuild.getFinishTime().getTime() - robustBuild.getStartTime().getTime()) / 1000.0f);
                                robustTriggerJobMapper.updateById(robustTriggerJob);
                            }
                        }
                    }
                }
                robustBuild.setFinishedConanJobId(finishedConanJobId);
                robustBuildMapper.updateById(robustBuild);
                String domain = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/robustAutoTest/jobReport?";
                String reportUrl = domain + "id=" + jobId;
                JsonArray users = new JsonArray();
                users.add(creator);
                users.add("guanxin04");
                if (EnvUtil.isOnline()) {
                    if (finishFlag.equals("0")) {
                        int finishedCount = finishedConanJobId.split("_").length;
                        int leftCount = conanJobIds.split("_").length - finishedConanJobId.split("_").length;
                        for (JsonElement user : users) {
                            String u = user.getAsString();
                            dxUtil.sendToPersionByCompass("※ 健壮性测试提醒 ※：\n 【任务ID】：" + jobId + "\n" +
                                    "【模块id】：" + robustBuild.getMoudleId() + "\n" +
                                    "【任务名称】：" + robustBuild.getCaseDesc() + "\n" +
                                    "【测试进度】：⏳ " + "第" + finishedCount + "个子任务完成，剩余" + leftCount + "个子任务 \n" +
                                    "【开始时间】：" + startTime + "\n" +
                                    "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                    "如有问题请联系guanxin04", u);
                        }
                    } else if (finishFlag.equals("1")) {
                        float skipPercent = (float) skipCaseCount(jobId) / robustBuild.getCaseCount();
                        int retryBuildCount = robustBuild.getTotalBuildUrl().split("_").length;
                        if (combinationCrashCaseCount(jobId) > 1) {
                            log.info("------rubost job----has combination crash case,retry combination crash case...------");
                            finishFlag = "0";
                            retryCombinationCrashCase(robustBuild);
                            for (JsonElement user : users) {
                                String u = user.getAsString();
                                dxUtil.sendToPersionByCompass("※ 健壮性测试提醒 ※：\n 【任务ID】：" + jobId + "\n" +
                                        "【模块id】：" + robustBuild.getMoudleId() + "\n" +
                                        "【任务名称】：" + robustBuild.getCaseDesc() + "\n" +
                                        "【测试进度】：第一轮任务触发已完成，重试执行聚合CrashCase" +
                                        "【开始时间】：" + startTime + "\n" +
                                        "【完成时间】：" + sdf.format(new Date()) + "\n" +
                                        "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                        "如有问题请联系guanxin04", u);
                            }
                        }
                        if (skipPercent > 0 && skipPercent < 0.8 && retryBuildCount < 3) {
                            log.info("------rubost job----has skip case,retry skip case...------");
                            finishFlag = "0";
                            retrySkipCase(robustBuild);
                            for (JsonElement user : users) {
                                String u = user.getAsString();
                                dxUtil.sendToPersionByCompass("※ 健壮性测试提醒 ※：\n 【任务ID】：" + jobId + "\n" +
                                        "【模块id】：" + robustBuild.getMoudleId() + "\n" +
                                        "【任务名称】：" + robustBuild.getCaseDesc() + "\n" +
                                        "【测试进度】：第一轮任务触发已完成，重试未执行case" +
                                        "【开始时间】：" + startTime + "\n" +
                                        "【完成时间】：" + sdf.format(new Date()) + "\n" +
                                        "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                        "如有问题请联系guanxin04", u);
                            }
                        } else if (skipPercent >= 0.8 || retryBuildCount >= 3) {
                            for (JsonElement user : users) {
                                String u = user.getAsString();
                                dxUtil.sendToPersionByCompass("※ 健壮性测试提醒 ※：\n 【任务ID】：" + jobId + "\n" +
                                        "【模块id】：" + robustBuild.getMoudleId() + "\n" +
                                        "【任务名称】：" + robustBuild.getCaseDesc() + "\n" +
                                        "【测试进度】：任务异常终止，未执行case占比：" + skipPercent + "，重试次数：" + retryBuildCount + "\n" +
                                        "【开始时间】：" + startTime + "\n" +
                                        "【完成时间】：" + sdf.format(new Date()) + "\n" +
                                        "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                        "如有问题请联系guanxin04", u);
                            }
                        } else {
                            //全部case执行完成
                            for (JsonElement user : users) {
                                String u = user.getAsString();
                                dxUtil.sendToPersionByCompass("※ 健壮性测试提醒 ※：\n 【任务ID】：" + jobId + "\n" +
                                        "【模块id】：" + robustBuild.getMoudleId() + "\n" +
                                        "【任务名称】：" + robustBuild.getCaseDesc() + "\n" +
                                        "【测试进度】：✅ 已完成\n" +
                                        "【开始时间】：" + startTime + "\n" +
                                        "【完成时间】：" + sdf.format(new Date()) + "\n" +
                                        "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                        "如有问题请联系guanxin04", u);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
        return Resp.success();
    }

    private void retrySkipCase(RobustBuild robustBuild) {
        if (EnvUtil.isOnline()) {

            new JenkinsUtil().robustJob(robustBuild, retrySkipCase,robustBuild.getAppName());
        }
    }

    private void retryCombinationCrashCase(RobustBuild robustBuild) {
        if (EnvUtil.isOnline()) {

            new JenkinsUtil().robustJob(robustBuild, retryCombinationCrashCase,robustBuild.getAppName());
        }
    }

    private int skipCaseCount(int jobId) {
        QueryWrapper<RobustResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("build_id", jobId).eq("case_result", ROBUST_CASE_SKIP);
        List<RobustResult> robustResultList = robustResultMapper.selectList(queryWrapper);
        return robustResultList.size();
    }

    private int combinationCrashCaseCount(int jobId) {
        QueryWrapper<RobustResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("build_id", jobId).eq("case_result", ROBUST_COMBINATION_CASE_CRASH);
        List<RobustResult> robustResultList = robustResultMapper.selectList(queryWrapper);
        return robustResultList.size();
    }


    public void checkConanOnes(Date startTime, Date endTime){
        QueryWrapper<RobustBuild> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("start_time", startTime, endTime);
        List<RobustBuild> robustBuildList = robustBuildMapper.selectList(queryWrapper);

        for(RobustBuild robustBuild : robustBuildList){
            String buildId = robustBuild.getId().toString();
            updateCaseIssueKeyBybuildJobId(buildId);
        }
        //// TODO: 2022/12/2 工单追加信息
        QueryWrapper resultWrapper =  new QueryWrapper<>();
        resultWrapper.between("start_time", startTime, endTime);
        resultWrapper.ne("issue_key",null);
        List<RobustResult> robustResultList =  robustResultMapper.selectList(resultWrapper);
        //2 Ω
        //3 新增崩溃追加评论，调用onesUtil.addComments
        //4 标记该工单已经被评论过，可能需要一张新的表，刚好记录所有新增问题







    }

    @Async
    @Override
    public Resp insertRobustResult(int jodId, ArrayList<Map> baseDataTemp, ArrayList<RobustResult> resultList) {
        dxUtil.sendToPersionByCompass(jodId + " 子线程开始" + v.printNowTime(), "sunkangtong");
        org.json.JSONObject modifyDetailMap = new org.json.JSONObject();
        JSONArray modifyDetailList = new JSONArray();
        Map diffMap = new HashMap<>();
        //存之前还要干一件事：把所有不需要modify=false的数据都生成mock，加到modify_detail中
        for (Map map : baseDataTemp) {
            int mockId = 0;
            String isModify = map.get("isModify").toString();
            if (isModify != "true") {
                String api = map.get("url").toString();
                String responseData = map.get("base_data").toString();
                JsonObject responseObject = new JsonParser().parse(responseData).getAsJsonObject();
                JsonObject mockResult = new MockUtil().create(api, responseObject);
                if (null != mockResult) {
                    mockId = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                }
                modifyDetailMap.put("modify_url", api);
                modifyDetailMap.put("mockId", mockId);
                diffMap.put("modify_url", api);
                diffMap.put("mockId", mockId);
//                modifyDetailList.put(modifyDetailMap);
                modifyDetailList.put(diffMap);
            }
        }
        System.out.println("插入开始 " + v.printNowTime());
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        for (RobustResult robustResult : resultList) {
            robustResult.setBuildId(jodId);
            String modifyDetail = robustResult.getModifyDetail();
            DocumentContext tempTemplateDataJson = JsonPath.using(configuration).parse(modifyDetail);
            JsonArray tempTemplateData = new JsonParser().parse(tempTemplateDataJson.jsonString()).getAsJsonArray();
            for (Object noModifyList : modifyDetailList) {
                JsonObject re = new JsonParser().parse(noModifyList.toString()).getAsJsonObject();
                tempTemplateData.add(re);
            }
            robustResult.setModifyDetail(tempTemplateData.toString());
            robustResult.setCommentState(NO_COMMENTS_YET);  //入库之前，初始化comment_state、crash_result字段
            robustResult.setCrashResult(CRASH_RESULT_DEFAULT);
            robustResultMapper.insert(robustResult);
        }
        System.out.println("插入结束 " + v.printNowTime());
//        dxUtil.sendToPersionByCompass(jodId+" 子线程结束"+v.printNowTime(),"sunkangtong");
        return null;
    }

    @Override
    public ArrayList robustWbrReport(Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startDateFormat = sdf.format(startTime);
        String endDateFormat = sdf.format(endTime);
        LambdaQueryWrapper<RobustBuild> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(RobustBuild::getStartTime, startDateFormat).le(RobustBuild::getFinishTime, endDateFormat);
        ArrayList<RobustBuild> buildArrayList = (ArrayList<RobustBuild>) robustBuildMapper.selectList(queryWrapper);
        HashMap resp = new HashMap<>();
        ArrayList<Long> buildTimeList = new ArrayList<>();
        ValueUtil valueUtil = new ValueUtil();
        for (RobustBuild robustBuild : buildArrayList) {
            Date start = robustBuild.getStartTime();
            Date end = robustBuild.getFinishTime();
            long diff = valueUtil.getDatePoor(start, end);
            buildTimeList.add(diff);
        }
        LambdaQueryWrapper<RobustResult> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.ge(RobustResult::getStartTime, startDateFormat).le(RobustResult::getFinishTime, endDateFormat);
        ArrayList<RobustResult> buildArrayList2 = (ArrayList<RobustResult>) robustResultMapper.selectList(queryWrapper2);
        ArrayList<Long> buildTimeList2 = new ArrayList<>();
        for (RobustResult robustResult : buildArrayList2) {
            Date start = robustResult.getStartTime();
            Date end = robustResult.getFinishTime();
            long diff = valueUtil.getDatePoor(start, end);
            buildTimeList2.add(diff);
        }
        long build_avg90 = new ValueUtil().getTpData(buildTimeList, 0.9);
        long build_avg50 = new ValueUtil().getTpData(buildTimeList, 0.5);
        long result_avg90 = new ValueUtil().getTpData(buildTimeList2, 0.9);
        long result_avg50 = new ValueUtil().getTpData(buildTimeList2, 0.5);
        ArrayList result = new ArrayList<>();
        resp.put("任务执行时间T90", build_avg90);
        resp.put("任务执行时间T50", build_avg50);
        resp.put("用例执行时间T90", result_avg90);
        resp.put("用例执行时间T50", result_avg50);
        result.add(resp);
        return result;
    }
}