package com.sankuai.mdp.compass.robust.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.robust.entity.PageInfo;
import com.sankuai.mdp.compass.robust.entity.RobustBuild;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import net.minidev.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/4/27 3:52 下午
 */
public interface RobustResultService extends IService<RobustBuild> {
    Resp updateRobustJob(RobustBuild robustBuild);

    Resp updateRobustResult(JSONObject body);

    Resp getResultReport(String jobId);

    Resp conanUpdate(JSONObject conanBody);

    public Resp insertRobustResult(int jodId, ArrayList<Map> baseDataTemp, ArrayList<RobustResult> resultList);

    public ArrayList robustWbrReport(Date startTime, Date endTime);

    void checkConanOnes(Date startTime, Date endTime);

    int getCrashResult(RobustResult robustResult);

    Resp preConanCallback(JSONObject conanBody);

    Resp recurCaseMockdata(String data);
}




