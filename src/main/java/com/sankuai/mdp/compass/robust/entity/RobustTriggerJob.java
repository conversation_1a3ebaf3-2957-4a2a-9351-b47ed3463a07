package com.sankuai.mdp.compass.robust.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2022/8/23 3:15 下午
 */
@Data
@TableName("robust_trigger_job")
public class RobustTriggerJob {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private int autoConfigId;
    private String platform;
    private String apkUrl;
    private String versionInfo;
    private int pageNumber;
    private int apiNumber;
    private int caseNumber;
    private String callbackPage;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;
    private float totalTime;
    private String conanJobId;
    @TableField(exist = false)
    private String mode;

    private Integer retryCount;
    private String jenkinsUrl;
    private String appName;

}
