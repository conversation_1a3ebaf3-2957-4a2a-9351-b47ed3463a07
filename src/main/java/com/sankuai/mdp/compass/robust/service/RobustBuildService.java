package com.sankuai.mdp.compass.robust.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.robust.entity.*;

import java.util.HashMap;
import com.sankuai.mdp.compass.robust.entity.RobustBuild;
import net.minidev.json.JSONObject;

import java.util.List;

/**
 * Created by skt on 2022/4/26.
 */
public interface RobustBuildService extends IService<RobustBuild> {
    Resp createNewJob(String body);

    Resp testCount(String body);


    Resp getRobustCase(String buildJobId,String mode);

    Resp retryJob(String body);

    IPage<RobustBuild> list(QueryRequest request, RobustBuild robustBuild);

    List<String> getAllCreator();

    Resp uploadPageInfo(PageInfo pageInfo);

    HashMap<String, String> getRespByPageId(String pageId, String platform);

    Resp triggerRobustJob(String triggerTime, String apkUrl, String platform,String appName, String body);

    Resp updateRobustTriggerJob(RobustTriggerJob robustTriggerJob);

    Resp getPageInfo(String triggerId, String mode);

    HashMap<String, Integer> getAllPageDescription();

    void writeBaseResponseIntoDB(RobustBaseResponse body);

    void updateCommonPage(CommonPage commonPage);

    void updateTriggerConfig(RobustAutoConfig robustAutoConfig);

    Resp triggerOnePlatformYouXuanRobust(String body,String platform);
}
