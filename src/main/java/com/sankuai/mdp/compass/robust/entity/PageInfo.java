package com.sankuai.mdp.compass.robust.entity;

import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by skt on 2022/8/22.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageInfo {
    String triggerId;
    Object ab;
    Object abDivideStrategies;
    Object pageInfo;
    String platform;
}
