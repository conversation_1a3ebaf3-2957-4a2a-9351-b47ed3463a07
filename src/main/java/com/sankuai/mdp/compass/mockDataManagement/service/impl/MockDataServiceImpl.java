package com.sankuai.mdp.compass.mockDataManagement.service.impl;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.MockUtil;
import com.sankuai.mdp.compass.mockDataManagement.entity.MockData;
import com.sankuai.mdp.compass.mockDataManagement.mapper.MockDataMapper;
import com.sankuai.mdp.compass.mockDataManagement.service.MockDataService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by xieyongrui on 2020/1/20.
 */
public class MockDataServiceImpl implements MockDataService{
    @Autowired
    MockDataMapper mockDataMapper;

    MockUtil mockUtil = new MockUtil();

    @Override
    public JsonObject insert(MockData mockData) {
        mockDataMapper.insert(mockData);
        JsonObject result = mockUtil.create(mockData.getApiRule(), new JsonParser().parse(mockData.getResponse()).getAsJsonObject());
        if (null != result) {
            Integer id = result.getAsJsonObject("data").get("mockId").getAsInt();

        }
        return result;
    }
}
