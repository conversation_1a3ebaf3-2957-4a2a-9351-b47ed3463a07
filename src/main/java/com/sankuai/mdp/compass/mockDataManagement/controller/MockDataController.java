package com.sankuai.mdp.compass.mockDataManagement.controller;

import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.mockDataManagement.entity.MockData;
import com.sankuai.mdp.compass.mockDataManagement.service.MockDataService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by xieyongrui on 2020/1/20.
 */
public class MockDataController extends BaseController{
    @Autowired
    MockDataService mockDataService;

    public JsonObject insert(MockData mockData) {
        return mockDataService.insert(mockData);
    }
}
