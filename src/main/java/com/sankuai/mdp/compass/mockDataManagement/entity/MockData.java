package com.sankuai.mdp.compass.mockDataManagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by xieyongrui on 2020/1/20.
 */

@Data
@TableName("autotest_mock_data")
public class MockData implements Serializable{
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer mockId;

    private Integer groupId;

    private String groupName;

    private String description;

    private String apiRule;

    private String response;
}
