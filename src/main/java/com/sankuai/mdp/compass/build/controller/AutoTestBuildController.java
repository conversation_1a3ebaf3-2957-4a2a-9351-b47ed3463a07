package com.sankuai.mdp.compass.build.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.build.entity.AutoTestBuild;
import com.sankuai.mdp.compass.build.service.AutoTestBuildService;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by xieyongrui on 2020/3/31.
 */
@RestController
@RequestMapping("/compass/api/autotest/build")
public class AutoTestBuildController extends BaseController {
    @Autowired
    AutoTestBuildService autoTestBuildService;

    @PostMapping("/add")
    public Integer create(AutoTestBuild autoTestBuild) {
        return autoTestBuildService.add(autoTestBuild);
    }

    @PostMapping("/update/startUploadApk")
    public void startUploadApk(AutoTestBuild autoTestBuild) {
        autoTestBuildService.uploadApkStartTime(autoTestBuild);
    }

    @PostMapping("/update/endUploadApk")
    public void endUploadApk(AutoTestBuild autoTestBuild) {
        autoTestBuildService.uploadApkEndTime(autoTestBuild);
    }

    @PostMapping("/update/startUploadCase")
    public void startUploadCase(AutoTestBuild autoTestBuild) {
        autoTestBuildService.uploadCaseStartTime(autoTestBuild);
    }
    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, AutoTestBuild autoTestBuild) {
        IPage<AutoTestBuild> CompatilityJobIPage = this.autoTestBuildService.list(request, autoTestBuild);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }
    @PostMapping("/update/endUploadCase")
    public void endUploadCase(AutoTestBuild autoTestBuild) {
        autoTestBuildService.uploadCaseEndTime(autoTestBuild);
    }

    @PostMapping("/update/startSubmit")
    public void startSubmit(AutoTestBuild autoTestBuild) {
        autoTestBuildService.updateSubmitStartTime(autoTestBuild);
    }

    @PostMapping("/update/endSubmit")
    public void endSubmit(AutoTestBuild autoTestBuild) {
        autoTestBuildService.updateSubmitEndTime(autoTestBuild);
    }

    @PostMapping("/update/end")
    public void end(AutoTestBuild autoTestBuild) {
        autoTestBuildService.updateEndTime(autoTestBuild);
    }

    @PostMapping("/update/status")
    public void updateStatus(AutoTestBuild autoTestBuild) {
        autoTestBuildService.updateStatus(autoTestBuild);
    }

}
