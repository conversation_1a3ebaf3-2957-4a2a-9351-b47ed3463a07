package com.sankuai.mdp.compass.build.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.build.entity.AutoTestBuild;
import com.sankuai.mdp.compass.build.mapper.AutoTestBuildMapper;
import com.sankuai.mdp.compass.build.service.AutoTestBuildService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.ConanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Created by xieyongrui on 2020/3/31.
 */
@Slf4j
@Service
public class AutoTestBuildServiceImpl extends ServiceImpl<AutoTestBuildMapper, AutoTestBuild> implements AutoTestBuildService {
    @Autowired
    AutoTestBuildMapper autoTestBuildMapper;

    public Integer add(AutoTestBuild autoTestBuild) {
        Date date = new Date();
        autoTestBuild.setStartTime(date);
        autoTestBuild.setStatus(-1);
        autoTestBuild.setStatusStr("排队中");
        autoTestBuildMapper.insert(autoTestBuild);
        return autoTestBuild.getId();
    }

    @Override
    public void uploadApkStartTime(AutoTestBuild autoTestBuild) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", autoTestBuild.getId());
        AutoTestBuild autoTestBuild1 = autoTestBuildMapper.selectOne(queryWrapper);
        if (null != autoTestBuild1) {
            Date date = new Date();
            if (null != autoTestBuild.getStartUploadApkTime()) {
                date = autoTestBuild.getStartUploadApkTime();
            }
            autoTestBuild1.setStartUploadApkTime(date);
            autoTestBuildMapper.update(autoTestBuild1, queryWrapper);
        }
    }

    @Override
    public void uploadApkEndTime(AutoTestBuild autoTestBuild) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", autoTestBuild.getId());
        AutoTestBuild autoTestBuild1 = autoTestBuildMapper.selectOne(queryWrapper);
        if (null != autoTestBuild1) {
            Date date = new Date();
            if (null != autoTestBuild.getEndUploadApkTime()) {
                date = autoTestBuild.getEndUploadApkTime();
            }
            autoTestBuild1.setEndUploadApkTime(date);
            autoTestBuildMapper.update(autoTestBuild1, queryWrapper);
        }
    }

    @Override
    public void uploadCaseStartTime(AutoTestBuild autoTestBuild) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", autoTestBuild.getId());
        AutoTestBuild autoTestBuild1 = autoTestBuildMapper.selectOne(queryWrapper);
        if (null != autoTestBuild1) {
            Date date = new Date();
            if (null != autoTestBuild.getStartUploadCaseTime()) {
                date = autoTestBuild.getStartUploadCaseTime();
            }
            autoTestBuild1.setStartUploadCaseTime(date);
            autoTestBuildMapper.update(autoTestBuild1, queryWrapper);
        }
    }

    @Override
    public void uploadCaseEndTime(AutoTestBuild autoTestBuild) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", autoTestBuild.getId());
        AutoTestBuild autoTestBuild1 = autoTestBuildMapper.selectOne(queryWrapper);
        if (null != autoTestBuild1) {
            Date date = new Date();
            if (null != autoTestBuild.getEndUploadCaseTime()) {
                date = autoTestBuild.getEndUploadCaseTime();
            }
            autoTestBuild1.setEndUploadCaseTime(date);
            autoTestBuildMapper.update(autoTestBuild1, queryWrapper);
        }
    }

    @Override
    public void updateSubmitStartTime(AutoTestBuild autoTestBuild) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", autoTestBuild.getId());
        AutoTestBuild autoTestBuild1 = autoTestBuildMapper.selectOne(queryWrapper);
        if (null != autoTestBuild1) {
            Date date = new Date();
            if (null != autoTestBuild.getStartSubmitTime()) {
                date = autoTestBuild.getStartSubmitTime();
            }
            autoTestBuild1.setStartSubmitTime(date);
            autoTestBuildMapper.update(autoTestBuild1, queryWrapper);
        }
    }

    @Override
    public void updateSubmitEndTime(AutoTestBuild autoTestBuild) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", autoTestBuild.getId());
        AutoTestBuild autoTestBuild1 = autoTestBuildMapper.selectOne(queryWrapper);
        if (null != autoTestBuild1) {
            Date date = new Date();
            if (null != autoTestBuild.getEndSubmitTime()) {
                date = autoTestBuild.getEndSubmitTime();
            }
            autoTestBuild1.setEndSubmitTime(date);
            autoTestBuildMapper.update(autoTestBuild1, queryWrapper);
        }
    }

    @Override
    public void updateEndTime(AutoTestBuild autoTestBuild) {
        if (null == autoTestBuild.getId() && null == autoTestBuild.getBuildUrl()) {
            return;
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        if (null != autoTestBuild.getId()) {
            queryWrapper.eq("id", autoTestBuild.getId());
        }
        if (null != autoTestBuild.getBuildUrl()) {
            queryWrapper.eq("build_url", autoTestBuild.getBuildUrl());
        }
        List<AutoTestBuild> autoTestBuildList = autoTestBuildMapper.selectList(queryWrapper);
        Date date = new Date();
        if (null != autoTestBuild.getEndTime()) {
            date = autoTestBuild.getEndTime();
        }

        Iterator<AutoTestBuild> iterator = autoTestBuildList.iterator();
        while (iterator.hasNext()) {
            AutoTestBuild build = iterator.next();
            build.setEndTime(date);
            autoTestBuildMapper.update(build, queryWrapper);
        }

    }

    @Override
    public void updateStatus(AutoTestBuild autoTestBuild) {
        if (null == autoTestBuild.getId() && null == autoTestBuild.getBuildUrl()) {
            return;
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        if (null != autoTestBuild.getId()) {
            queryWrapper.eq("id", autoTestBuild.getId());
        }
        if (null != autoTestBuild.getBuildUrl()) {
            queryWrapper.eq("build_url", autoTestBuild.getBuildUrl());
        }
        List<AutoTestBuild> autoTestBuildList = autoTestBuildMapper.selectList(queryWrapper);
        String statusStr = "";
        Integer status = autoTestBuild.getStatus();
        if (0 == status) {
            statusStr = "运行中";
        } else if (1 == status) {
            statusStr = "已完成";
        } else if (2 == status) {
            statusStr = "已取消";
        }
        Iterator<AutoTestBuild> iterator = autoTestBuildList.iterator();
        while (iterator.hasNext()) {
            AutoTestBuild build = iterator.next();
            build.setStatus(status);
            build.setStatusStr(statusStr);
            autoTestBuildMapper.update(build, queryWrapper);
        }
    }

    @Override
    public IPage<AutoTestBuild> list(QueryRequest request, AutoTestBuild autoTestBuild) {
        try {
            LambdaQueryWrapper<AutoTestBuild> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AutoTestBuild::getBuildType, "interface").orderByDesc(AutoTestBuild::getStartTime);

            if (null != autoTestBuild.getJenkinsId()) {
                queryWrapper.like(AutoTestBuild::getJenkinsId, autoTestBuild.getJenkinsId().toString());
            }
            if (null != autoTestBuild.getPlatform() && !"All".equals(autoTestBuild.getPlatform())) {
                queryWrapper.eq(AutoTestBuild::getPlatform, autoTestBuild.getPlatform());
            }
            if (request.getSwitchStatus()) {
                queryWrapper.ne(AutoTestBuild::getStatus, "1");
            }
            Page<AutoTestBuild> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public AutoTestBuild selectOneBuild(String buildId){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", buildId);
        List<AutoTestBuild> buildlist = (ArrayList) autoTestBuildMapper.selectList(queryWrapper);
        if(!buildlist.isEmpty()){
            return buildlist.get(0);
        }
        return null;
    }

}
