package com.sankuai.mdp.compass.build.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.build.entity.AutoTestBuild;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.ConanCase;

/**
 * Created by xieyongrui on 2020/3/31.
 */

public interface AutoTestBuildService {
    Integer add(AutoTestBuild autoTestBuild);

    void uploadApkStartTime(AutoTestBuild autoTestBuild);

    void uploadApkEndTime(AutoTestBuild autoTestBuild);

    void uploadCaseStartTime(AutoTestBuild autoTestBuild);

    void uploadCaseEndTime(AutoTestBuild autoTestBuild);

    void updateSubmitStartTime(AutoTestBuild autoTestBuild);

    void updateSubmitEndTime(AutoTestBuild autoTestBuild);

    void updateEndTime(AutoTestBuild autoTestBuild);

    void updateStatus(AutoTestBuild autoTestBuild);

    IPage<AutoTestBuild> list(QueryRequest request, AutoTestBuild autoTestBuild);

    AutoTestBuild selectOneBuild(String buildId);

}