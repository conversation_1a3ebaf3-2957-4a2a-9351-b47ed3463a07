package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.jayway.jsonpath.*;
import com.sankuai.mdp.compass.adapter.GsonAdapter;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicData;
import com.sankuai.mdp.compass.mapper.DynamicDataMapper;
import com.sankuai.mdp.compass.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.sankuai.it.sso.sdk.utils.EnvUtil;

import static com.jayway.jsonpath.JsonPath.using;


/**
 * Created by xieyongrui on 2019/11/21.
 */

@Slf4j
@Service
public class DynamicDataServiceImpl extends ServiceImpl<DynamicDataMapper, DynamicData> implements DynamicDataService {
    @Autowired
    DynamicDataMapper dynamicDataMapper;

//    MockRuleController mockRuleController = new MockRuleController();

    @Autowired
    MockRuleService mockRuleService;

    @Autowired
    CustomService customService;

    @Autowired
    DynamicTemplateService dynamicTemplateService;

    @Autowired
    private DynamicTemplateDataService dynamicTemplateDataService;

    EvaUtil evaUtil = new EvaUtil();
    MbcUtil mbcUtil = new MbcUtil();

    private static String NORMAL = "normal";
    private static String ABNORMAL = "abnormal";
    private static String ROOT_JSON_PATH = "$";
    private static String LONG_CONTEXT = "<font color='#f47920'><b>yg撒火</b></font>\uD83D\uDE2DW68可爱多<米线可可乐乐乐超长超长！¥超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长超长";
    private static String BLANK_CONTEXT = "";
    private static String BACK_GROUND_COLOR_KEY = ".backgroundColor";
    private static String BORDER_COLOR_KEY = ".borderColor";
    private static String KEYWORD_COLOR_KEY = ".keywordColor";
    private static String AGGREGATE_TEXT_BACK_GROUND_COLOR = "#FFC125";
    private static String AGGREGATE_TEXT_BORDER_COLOR = "#2db7f5";
    private static String AGGREGATE_TEXT_KEYWORD_COLOR = "#87d068";
    private static String RICH_TEXT_FIRST = "<font>富文本<font color='#ff6200'>";
    private static String RICH_TEXT_SECOND = "</font></font>";
    private static String GENERAL_TEXT = "普通文本";
    private static String INVALID_URL = "https://p0.meituan.net/travelcube/8e3601c28c2efd05c4310a4d24cdab9f1677null.png";
    Configuration configuration = Configuration.builder()
            .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();

    JsonParser jsonParser = new JsonParser();

    Gson gson = new GsonAdapter().getGson();

    @Override
    public DynamicData find(String template) {
//        LambdaQueryWrapper<DynamicData> queryWrapper = new LambdaQueryWrapper<>();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("template_name", template);
        DynamicData result = dynamicDataMapper.selectOne(queryWrapper);
        return result;
    }

    @Override
    public IPage<DynamicData> list(QueryRequest request, DynamicData dynamicData) {
        try {
            LambdaQueryWrapper<DynamicData> queryWrapper = new LambdaQueryWrapper<>();

            queryWrapper.last("order by id desc");

            Page<DynamicData> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public Boolean add(JsonObject body) {
        System.out.print(body);
        try {

            JsonObject map = body.get("payload").getAsJsonObject();
            DynamicData dynamicData1 = new DynamicData();
            Integer type = map.get("type").getAsInt();
            dynamicData1.setChineseName(map.get("chineseName").getAsString());
            String address = map.get("address").getAsString();
            dynamicData1.setAddress(address);
            String businessEnName = customService.getBusinessEnNameByCnName(map.get("chineseName").getAsString(), address);
            JsonArray el_config = null;
            if (null != map.get("el_config")) {
                if (!(map.get("el_config") instanceof JsonNull)) {
                    el_config = map.getAsJsonArray("el_config");
                    log.info("\n" + "------el_config在这里-----" + el_config);
                }
            }
//

            dynamicData1.setBusiness(businessEnName);

            if (1 == type) {
                String object = map.get("templateName").getAsString();
                String name = object.split(":")[0];
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("template_name", name);
                DynamicData result = dynamicDataMapper.selectOne(queryWrapper);

                dynamicData1.setTemplateName(name);
                if (null != map.get("templateData")) {
                    Pattern p = Pattern.compile("\\s{3,}|\t|\r|\n|\r\n");
                    Matcher m = p.matcher(map.get("templateData").getAsString());
                    dynamicData1.setTemplateData(m.replaceAll(""));
                }
                if (null != map.get("templatePath")) {
                    dynamicData1.setTemplatePath(map.get("templatePath").getAsString());
                }
                if (null != map.get("zipPath")) {
                    dynamicData1.setZipPath(map.get("zipPath").getAsString());
                }
                if (null != map.get("mockRule")) {
//                dynamicData1.setMockRule((String) map.get("mockRule"));
                    log.info("-----mockRule在这里------" + map.get("mockRule"));
                    JsonObject mockRuleMap = map.get("mockRule").getAsJsonObject();
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.add("long", mockRuleMap.get("long"));
                    jsonObject.add("blank", mockRuleMap.get("blank"));
                    jsonObject.add("richText", mockRuleMap.get("richText"));
                    jsonObject.add("invalidUrl", mockRuleMap.get("invalidUrl"));
                    jsonObject.add("el_config", el_config);

                    System.out.println("------得到结果在这里---------" + jsonObject);
                    dynamicData1.setMockRule(jsonObject.toString());
                }
                if (null != result) {
                    dynamicData1.setApi(result.getApi());
                    dynamicData1.setApiData(result.getApiData());
                    dynamicData1.setListPath(result.getListPath());
                }

                if (null == result) {
                    dynamicDataMapper.insert(dynamicData1);
                } else {
                    dynamicData1.setId(result.getId());
                    dynamicDataMapper.updateById(dynamicData1);
                }
            }
            if (0 == type) {
                JsonArray list = map.getAsJsonArray("templateName");

                for (int i = 0; i < list.size(); i++) {
                    String name = list.get(i).toString().split(":")[0].replaceAll("\"", "");
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("template_name", name);
                    DynamicData result = dynamicDataMapper.selectOne(queryWrapper);

                    dynamicData1.setApi(map.get("api").getAsString());
                    dynamicData1.setTemplateName(name);

                    if (null != map.get("api")) {
                        dynamicData1.setApi(map.get("api").getAsString());
                    }
                    if (null != map.get("apiData")) {
                        Pattern p = Pattern.compile("\\s*|\t|\r|\n|\r\n");
                        Matcher m = p.matcher(map.get("apiData").getAsString());
                        dynamicData1.setApiData(m.replaceAll(""));
                    }
                    if (null != map.get("listPath")) {
                        dynamicData1.setListPath(map.get("listPath").getAsString());
                    }
                    if (null != result) {
                        dynamicData1.setTemplateData(result.getTemplateData());
                        dynamicData1.setTemplatePath(result.getTemplatePath());
                        dynamicData1.setZipPath(result.getZipPath());
                    }

                    if (null == result) {
                        dynamicDataMapper.insert(dynamicData1);
                    } else {
                        dynamicData1.setId(result.getId());
                        dynamicDataMapper.updateById(dynamicData1);
                    }
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public JsonObject mockSmoke(CompatilityJob compatilityJob, Integer type, String scenes) {
        return mockByData(compatilityJob, type, scenes);
    }

    @Override
    public JsonArray mockByCase(String templateData, String listPath, String apiData, String apiPath, JSONObject mockRule, Integer type) {
        return mockByData(templateData, listPath, apiData, apiPath, mockRule, type);
    }

    @Override
    public int updateMbcProperty(int id, JsonObject body, String mode) {
        if (!(mode.equals("apiData") || mode.equals("templateData"))) {
            return -5;
        }
        if (body.toString().length() == 0) {
            return -100;
        }
        JsonParser jsonParser = new JsonParser();
        String jsonBody = (body.get(mode).toString());
        jsonBody = StringEscapeUtils.unescapeJava(jsonBody.substring(1, jsonBody.length() - 1));
        try {
            jsonParser.parse(jsonBody);
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
//        JsonObject jsonBodyEle=jsonParser.parse(jsonBody).getAsJsonObject();
        //带参回新增页
//        try {
//            Configuration conf = Configuration.builder()
//                    .options(Option.AS_PATH_LIST).build();
//            List<String> list = JsonPath.using(conf).parse(jsonBody).read("$..*");
//            List<String> result = new ArrayList<>();
//            for (String path : list) {
//                String[] subPath = path.split("\'");
//                String resultPath = "";
//                for (String s : subPath) {
//                    String keyStr = s.replaceAll("\\[", "").replaceAll("\\]", "");
//                    Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
//                    Matcher isNum = pattern.matcher(keyStr);
//                    if ("".equals(keyStr) || "$".equals(keyStr)) {
//                        continue;
//                    }
//                    if (isNum.matches()) {
//                        resultPath += "[" + keyStr + "]";
//                    } else {
//                        resultPath += "." + keyStr;
//                    }
//                }
//                result.add(resultPath);
//            }
//            String listPath;
//            String templatePath;
//            String zipPath;
//            int flag = 0;
//            QueryWrapper<DynamicData> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("id", id);
//            DynamicData data = dynamicDataMapper.selectOne(queryWrapper);
//            if (mode.equals("apiData")) {
//                listPath = (data.getListPath());
//                for (String path : result) {
//                    if (path.equals(listPath)) {
//                        flag = 1;
//                        break;
//                    }
//                }
//            } else {
//                templatePath = (data.getTemplatePath());
//                zipPath = (data.getZipPath());
//                for (String path : result) {
//                    if (path.equals(templatePath)) {
//                        flag = 2;
//                        break;
//                    }
//                }
//                if (flag == 2) {
//                    for (String path : result) {
//                        if (path.equals(zipPath)) {
//                            flag = 1;
//                            break;
//                        }
//                    }
//                }
//            }
//            if (flag != 1) {
//                return 3;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return -7;
//        }

        QueryWrapper<DynamicData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        DynamicData data = dynamicDataMapper.selectOne(queryWrapper);
        try {
            if (mode.equals("apiData")) {
                data.setApiData(jsonBody);
                dynamicDataMapper.updateById(data);
            } else {
                data.setTemplateData(jsonBody);
                dynamicDataMapper.updateById(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return -3;
        }
        return 1;
    }

    /**
     * 从mbc获取配置的json数据进行数据构造（gson）
     *
     * @param compatilityJob
     * @param type
     * @return
     */
    public JsonObject mockByData(CompatilityJob compatilityJob, Integer type, String scenes) {
        String mbcBusiness = compatilityJob.getBusiness();
        String templateName = compatilityJob.getTemplateName();
        JsonObject result = new JsonObject();
        JsonArray jsonArray = new JsonArray();
        String business = "";
        DxUtil dxUtil = new DxUtil();

        try {
            //获取templateName对应的api数据
            String[] templateList = templateName.split(",");

            for (int i = 0; i < templateList.length; i++) {
                String name = templateList[i];

                log.info("默认在我的页面进行测试");
                String prefix = "";
                String templateData = "";
                String api = "";
                String listPath = "";
                String apiData = "";

                if (mbcBusiness != null && ((mbcBusiness.contains("search")) || mbcBusiness.contains(CompatilityServiceImpl.COMPONENT_MODE))) {
                    //适配搜索
                    prefix = ".";
                    api = "/group/v4/poi/search/1";
                    business = "search";
                    listPath = ".gathers[0].items";
                    apiData = PageUtil.pageApiData.get(business);
                    templateData = mbcUtil.getCardData(name, business, scenes);
                } else if (mbcBusiness != null && mbcBusiness.contains("shoppingcart")) {
                    //适配购物车
                    prefix = ".biz.";
                    api = "/mbc/shoppingCart";
                    business = "shoppingcart";
                    listPath = ".data.groups[0].items";
                    apiData = PageUtil.pageApiData.get(business);
                    templateData = mbcUtil.getCardData(name, null, scenes);
                } else if (mbcBusiness != null && mbcBusiness.contains("feed")) {
                    //适配猜喜
                    prefix = ".";
                    api = "/group/v2/recommend/homepage/city/1";
                    business = "staggered_feed";
                    listPath = ".data";
                    apiData = PageUtil.pageApiData.get(business);
                    templateData = mbcUtil.getCardData(name, business, scenes);
                    // 07.15Android渲染依赖"_style": "dynamicV2"，保证模板数据有该字段
                    JsonObject templateDataJson = new JsonParser().parse(templateData).getAsJsonObject();
                    templateDataJson.addProperty("_style", "dynamicV2");
                    templateData = templateDataJson.toString();
                } else if (mbcBusiness != null && mbcBusiness.contains("groupbuy")) {
                    //适配团购业务
                    if (new LionUtil().getListValue("dynamicTopcardList").contains(templateName)) {
                        listPath = ".topCards";
                    } else {
                        listPath = ".gathers[0].items";
                    }
                    prefix = ".";
                    api = "/v4/poi/tuan/list/1";
                    business = "groupbuy";
                    apiData = PageUtil.pageApiData.get(business);
                    templateData = mbcUtil.getCardData(name, business, scenes);
                } else {
                    log.info("默认在我的页面进行测试");
                    prefix = ".biz.";
                    templateData = "";
                    business = "mine";
                    api = "/mbc/homemine";
                    listPath = ".data.groups[1].items";
                    //获取固定参数:模板数据结构除了data外的部分
                    apiData = PageUtil.pageApiData.get(business);
                    //从mbc拿模板数据:卡片数据
                    templateData = mbcUtil.getCardData(name, null, scenes);
                }
                // 11.20在此处输出从MBC获取到的模版数据或者接口传入的模版数据，并大象通知
                if (new LionUtil().getBooleanValue("mockDataManager")) {
                    dxUtil.sendToPersionByCompass("模板名称：" + compatilityJob.getTemplateName() +
                            "\nid：" + compatilityJob.getId() +
                            "\n【从MBC上获取到的mock数据】\n" + templateData +
                            "\n【接口传入的mock数据】\n" + compatilityJob.getMockData(), "liujiao11");
                }
                // 12.12增加if条件判断：如果是it_mbc提交的任务，则使用mbc获取的数据，否则判断接口是否有传入mock数据，有传入则优先使用传入的数据
                if (!compatilityJob.getCreatedBy().equals("it_mbc")) {
                    //08.24修改templateData数据使用逻辑：如果传入了mockdata就用传入的，没有传入则使用mbc平台拉取的
                    if (!StringUtil.isBlank(compatilityJob.getMockData()) || CompatilityServiceImpl.COMPONENT_MODE.equals(compatilityJob.getTestMode()) || ((scenes != "regression") && (!compatilityJob.getCreatedBy().equals("it_mbc")) && (null != compatilityJob.getCaseName()))) {
                        String tempMockData = compatilityJob.getMockData();
                        if (null == compatilityJob.getTemplateUrl()) {
                            compatilityJob.setTemplateUrl("");
                        }
                        String handleTempMockData = mbcUtil.replaceCardData(name, business, scenes, compatilityJob.getTemplateUrl(), tempMockData);
                        //08.24:下方为原有逻辑没有变动，如果后期需要改回强兜底，删除上述判断条件中的!("".equals(compatilityJob.getMockData())) 即可恢复
                        new StringUtil();
                        if (!StringUtils.isBlank(tempMockData)) {
                            templateData = handleTempMockData;
                        }
                    }
                }
                DocumentContext apiDataJson = JsonPath.using(configuration).parse(apiData);
                JsonObject templateItem = new JsonObject();
                JsonArray mockIds = new JsonArray();
                templateItem.addProperty("templateName", name);
                templateItem.add("mock", new JsonArray());

                //mock正常数据获得mockId
                //组装数据
                JsonObject tempApiData = jsonParseHelper(apiDataJson, listPath, templateData);
                //提交appmock
                JsonObject mockResult = MockUtil.create(api, tempApiData);
                if (null != mockResult) {
                    Integer id = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                    //插入mock规则
                    mockRuleService.setMockRule(id, "", "normal", api);
                    JsonObject mockIdItem = new JsonObject();
                    mockIdItem.addProperty("type", "normal");
                    mockIdItem.addProperty("mockId", id);
                    mockIdItem.addProperty("caseName", "MBC的API数据");
                    //构造mockIds列表
                    mockIds.add(mockIdItem);
                }
                String typeStr = type.toString();
                if (0 == type || "0".equals(typeStr)) {
                    //查询当前模版的xml
                    String xml = "";
                    if (CompatilityServiceImpl.COMPONENT_MODE.equals(compatilityJob.getTestMode())) {
                        xml = dynamicTemplateService.getXmlContentByZipUrl(name, compatilityJob.getDownloadUrl());
                    } else {
                        xml = dynamicTemplateService.getXmlContentByTemplateName(mbcBusiness, name, "MBC", scenes);
                    }
                    //根据xml获取冒烟case
                    JSONObject mockRule = getSmokeCase(xml, prefix);

                    JSONArray longRuleArray = mockRule.getJSONArray("long");
                    JSONArray blankRuleArray = mockRule.getJSONArray("blank");
//                  JSONArray richTextArray = mockRule.getJSONArray("richText");
                    JSONArray invalidUrlArray = mockRule.getJSONArray("invalidUrl");

                    //// TODO: 2021/7/29 构造 三元表达式的数据， 比如true\false; 0\1


                    //遍历需要构造不下发的字段
                    createMock(blankRuleArray, templateData, apiData, mockIds, listPath, api, null, "del", "abnormal");

                    //遍历需要构造超长的字段
                    createMock(longRuleArray, templateData, apiData, mockIds, listPath, api, LONG_CONTEXT, "long", "abnormal");

                    //遍历需要构造为空的字段
                    createMock(blankRuleArray, templateData, apiData, mockIds, listPath, api, BLANK_CONTEXT, "blank", "abnormal");

                    //遍历需要构造下发为null的字段
                    createMock(blankRuleArray, templateData, apiData, mockIds, listPath, api, null, "null", ABNORMAL);

                    //遍历需要构造富文本的字段
//                    createMock(richTextArray, templateData, apiData, mockIds, listPath, api, null, "richText", ABNORMAL);

                    //遍历需要构造无效链接的字段
                    createMock(invalidUrlArray, templateData, apiData, mockIds, listPath, api, INVALID_URL, "invalidUrl", ABNORMAL);

                    //遍历需要构造兜底颜色的字段
                    createMock(null, templateData, apiData, mockIds, listPath, api, null, "noColor", ABNORMAL);

                    //遍历需要构造自定义颜色的字段
                    createMock(null, templateData, apiData, mockIds, listPath, api, null, "customColor", ABNORMAL);

                    //所有text标签字段构造超长
                    createMock(longRuleArray, templateData, apiData, mockIds, listPath, api, LONG_CONTEXT, "allLong", "abnormal");

                }
                templateItem.get("mock").getAsJsonArray().addAll(mockIds);
                result.add(business, jsonArray);
                log.info("业务是", business, jsonArray);
                result.getAsJsonArray(business).add(templateItem);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 根据模板名构造多套mock数据
     */
    public JsonObject mockByTemplateData(CompatilityJob compatilityJob, String type, JsonObject jsonObject, String scenes) {
        String mbcBusiness = compatilityJob.getBusiness();
        String templateName = compatilityJob.getTemplateName();
        String business = "";
        try {
            // 获取多套数据
            String getTemplateUrl = "http://qaassist.sankuai.com/compass/api/job/getTemplateData?templateName=";
            if (!EnvUtil.isOnline()) {
                getTemplateUrl = "http://qaassist.fe.test.sankuai.com/compass/api/job/getTemplateData?templateName=";
            }
            getTemplateUrl = getTemplateUrl + templateName;
            String templateMoreData = HttpUtil.vGet(getTemplateUrl);
            JsonObject templateMoreDataJson = new JsonParser().parse(templateMoreData).getAsJsonObject();
            JsonArray templateMoreDataJsonArray = templateMoreDataJson.getAsJsonArray("rows");
            if (templateMoreDataJsonArray.size() > 0) {
                log.info("默认在我的页面进行测试");
                String api = "";
                String listPath = "";
                String apiData = "";

                if (mbcBusiness != null && ((mbcBusiness.contains("search")) || mbcBusiness.contains(CompatilityServiceImpl.COMPONENT_MODE))) {
                    //适配搜索
                    api = "/group/v4/poi/search/1";
                    business = "search";
                    listPath = ".gathers[0].items";
                    apiData = PageUtil.pageApiData.get(business);
                } else if (mbcBusiness != null && mbcBusiness.contains("shoppingcart")) {
                    //适配购物车
                    api = "/mbc/shoppingCart";
                    business = "shoppingcart";
                    listPath = ".data.groups[0].items";
                    apiData = PageUtil.pageApiData.get(business);
                } else if (mbcBusiness != null && mbcBusiness.contains("feed")) {
                    //适配猜喜
                    api = "/group/v2/recommend/homepage/city/1";
                    business = "staggered_feed";
                    listPath = ".data";
                    apiData = PageUtil.pageApiData.get(business);
                } else if (mbcBusiness != null && mbcBusiness.contains("groupbuy")) {
                    //适配团购业务
                    if (new LionUtil().getListValue("dynamicTopcardList").contains(templateName)) {
                        listPath = ".topCards";
                    } else {
                        listPath = ".gathers[0].items";
                    }
                    api = "/v4/poi/tuan/list/1";
                    business = "groupbuy";
                    apiData = PageUtil.pageApiData.get(business);
                } else {
                    log.info("默认在我的页面进行测试");
                    business = "mine";
                    api = "/mbc/homemine";
                    listPath = ".data.groups[1].items";
                    //获取固定参数:模板数据结构除了data外的部分
                    apiData = PageUtil.pageApiData.get(business);
                }

                for (int i = 0; i < templateMoreDataJsonArray.size(); i++) {
                    String templateData = templateMoreDataJsonArray.get(i).getAsJsonObject().get("templateData").toString();
                    templateData = templateData.replace("\\\"", "\"");
                    if (templateData.startsWith("\"")) {
                        templateData = templateData.substring(1);
                    }
                    if (templateData.endsWith("\"")) {
                        templateData = templateData.substring(0, templateData.length() - 1);
                    }
                    JsonObject templateDataJson = jsonParser.parse(templateData).getAsJsonObject();
                    // 将模板数据的链接替换为MBC最新的
                    String templateUrl = mbcUtil.getTemplateZipApi(templateName, scenes);
                    if (null != templateUrl) {
                        log.info("多套数据：将模板数据替换为MBC上的。");
                        templateDataJson.remove("templateUrl");
                        templateDataJson.addProperty("templateUrl", templateUrl);
                        templateData = templateDataJson.toString();
                    }
                    String dataDesc = templateMoreDataJsonArray.get(i).getAsJsonObject().get("dataDesc").toString();
                    //组装数据
                    DocumentContext apiDataJson = JsonPath.using(configuration).parse(apiData);
                    JsonObject tempApiData = jsonParseHelper(apiDataJson, listPath, templateData);
                    JsonObject mockResult = MockUtil.create(api, tempApiData);
                    if (null != mockResult) {
                        Integer id = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                        //插入mock规则
                        mockRuleService.setMockRule(id, dataDesc, "normal", api);
                        JsonObject mockIdItem = new JsonObject();
                        mockIdItem.addProperty("type", "normal");
                        mockIdItem.addProperty("mockId", id);
                        mockIdItem.addProperty("caseName", dataDesc);
                        jsonObject.getAsJsonArray(business).get(0).getAsJsonObject().getAsJsonArray("mock").add(mockIdItem);
                    }
                }
            } else {
                log.info("没有获取到多套数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject;
    }

    /**
     * 根据指定参数构造mock数据
     *
     * @param templateData
     * @param listPath
     * @param apiData
     * @param apiRule
     * @param mockRule
     * @param type
     * @return 返回mockIds [{"type":"normal","mockId":123456}]
     */
    public JsonArray mockByData(String templateData, String listPath, String apiData, String apiRule, JSONObject mockRule, Integer type) {
        JsonArray result = new JsonArray();
        try {

            DocumentContext apiDataContext = JsonPath.using(configuration).parse(apiData);

            JsonObject tempApiData;

            //mock正常数据获得mockId
            tempApiData = jsonParseHelper(apiDataContext, listPath, templateData);

            JsonObject oneMockResp = MockUtil.create(apiRule, tempApiData);
            if (null != oneMockResp) {
                Integer id = oneMockResp.getAsJsonObject("data").get("mockId").getAsInt();
                JsonObject mockIdItem = new JsonObject();
                mockIdItem.addProperty("type", "normal");
                mockIdItem.addProperty("mockId", id);
                result.add(mockIdItem);
            }

            if (0 == type || type.toString().equals("0")) {
                JSONArray longRuleArray = null;
                Object longRule = mockRule.get("long");
                if (longRule != null && !longRule.toString().equals("null")) {
                    longRuleArray = mockRule.getJSONArray("long");
                }
                JSONArray blankRuleArray = null;
                Object blank = mockRule.get("blank");
                if (blank != null && !blank.toString().equals("null")) {
                    blankRuleArray = mockRule.getJSONArray("blank");
                }
                JSONArray richTextArray = null;
                Object richText = mockRule.get("richText");
                if (richText != null && !richText.toString().equals("null")) {
                    richTextArray = mockRule.getJSONArray("richText");
                }
                JSONArray invalidUrlArray = null;
                Object invalidUrl = mockRule.get("invalidUrl");
                if (invalidUrl != null && !invalidUrl.toString().equals("null")) {
                    invalidUrlArray = mockRule.getJSONArray("invalidUrl");
                }

                //遍历需要构造不下发的字段
                createMock(blankRuleArray, templateData, apiData, result, listPath, apiRule, LONG_CONTEXT, "del", "abnormal");

                //遍历需要构造超长的字段
                createMock(longRuleArray, templateData, apiData, result, listPath, apiRule, LONG_CONTEXT, "long", "abnormal");

                //遍历需要构造为空的字段
                createMock(blankRuleArray, templateData, apiData, result, listPath, apiRule, BLANK_CONTEXT, "blank", "abnormal");

                //遍历需要构造为缺失的字段
                createMock(blankRuleArray, templateData, apiData, result, listPath, apiRule, null, "null", ABNORMAL);

                //遍历需要构造富文本的字段
//                    createMock(richTextArray, templateData, apiData, result, listPath, api, null, "richText", ABNORMAL);

                //遍历需要构造无效链接的字段
                createMock(invalidUrlArray, templateData, apiData, result, listPath, apiRule, INVALID_URL, "invalidUrl", ABNORMAL);

                //遍历需要构造兜底颜色的字段
                createMock(null, templateData, apiData, result, listPath, apiRule, null, "noColor", ABNORMAL);

                //遍历需要构造自定义颜色的字段
                createMock(null, templateData, apiData, result, listPath, apiRule, null, "customColor", ABNORMAL);
            }

        } catch (Exception e) {
            log.info(e.getMessage());
        } finally {
            return result;
        }
    }

    public JsonObject jsonParseHelper(DocumentContext apiDataContext, String listPath, String templateData) {
        if (listPath.endsWith("]")) {
            Integer len = listPath.length();
            String first = listPath.substring(0, len - 3);
            String second = listPath.substring(len - 3, len);
            int index = Integer.parseInt(second.replace("[", "").replace("]", ""));
            if (first.endsWith("[")) {
                first = first.substring(first.length() - 1);
            }
            JsonArray jsonArray = jsonParser.parse(apiDataContext.read(ROOT_JSON_PATH + first).toString()).getAsJsonArray();
            JsonArray tempArray = jsonArray.deepCopy();
            JsonArray object = new JsonArray();
            for (int i = 0; i < tempArray.size(); i++) {
                if (i == 0) {
                    if (index == i) {
                        object.add(jsonParser.parse(templateData).getAsJsonObject());
                    }
                    object.add(tempArray.get(i));
                } else {
                    if (index == i) {
                        object.add(jsonParser.parse(templateData).getAsJsonObject());
                    }
                    object.add(tempArray.get(i));
                }
            }
            if (null != object) {
                return jsonParser.parse(apiDataContext.set(ROOT_JSON_PATH + first, gson.fromJson(object.toString(), Object.class)).jsonString()).getAsJsonObject();
            } else {
                return null;
            }
        } else {
            return jsonParser.parse(apiDataContext.add(ROOT_JSON_PATH + listPath, gson.fromJson(templateData, Object.class)).jsonString()).getAsJsonObject();
        }
    }

    /**
     * 根据规则创建mock（gson）
     *
     * @param jsonArray    需要mock的字段
     * @param templateData 模版数据
     * @param apiData      api框架数据
     * @param mockIds      mock结果
     * @param listPath     数组字段
     * @param api
     * @param text
     * @param rule
     * @param type
     */
    public void createMock(JSONArray jsonArray, String templateData, String apiData, JsonArray
            mockIds, String listPath, String api, String text, String rule, String type) {
        try {
            if (null == jsonArray || "noColor".equals(rule) || "customColor".equals(rule)) {
                JsonObject tempItemData = jsonParser.parse(templateData).getAsJsonObject();
                JsonObject tempApiData = jsonParser.parse(apiData).getAsJsonObject();
                DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(tempItemData.toString());
                DocumentContext tempMockApiJson = JsonPath.using(configuration).parse(tempApiData.toString());
                JsonElement jsonElement = null;

                if ("noColor".equals(rule)) {
                    DocumentContext tempDocumentContext = tempItemDataJson.set(ROOT_JSON_PATH + "." + BACK_GROUND_COLOR_KEY, "");
                    tempDocumentContext = tempDocumentContext.set(ROOT_JSON_PATH + "." + BACK_GROUND_COLOR_KEY, "");
                    tempDocumentContext = tempDocumentContext.set(ROOT_JSON_PATH + "." + KEYWORD_COLOR_KEY, "");
                    jsonElement = jsonParser.parse(tempDocumentContext.jsonString());
                } else if ("customColor".equals(rule)) {
                    DocumentContext tempDocumentContext = tempItemDataJson.set(ROOT_JSON_PATH + "." + BACK_GROUND_COLOR_KEY, AGGREGATE_TEXT_BACK_GROUND_COLOR);
                    tempDocumentContext = tempDocumentContext.set(ROOT_JSON_PATH + "." + BORDER_COLOR_KEY, AGGREGATE_TEXT_BORDER_COLOR);
                    tempDocumentContext = tempDocumentContext.set(ROOT_JSON_PATH + "." + KEYWORD_COLOR_KEY, AGGREGATE_TEXT_KEYWORD_COLOR);
                    jsonElement = jsonParser.parse(tempDocumentContext.jsonString());
                }

                //创建mock
                if (null != jsonElement) {
                    tempApiData = jsonParseHelper(tempMockApiJson, listPath, jsonElement.toString());

                    JsonObject mockResult = MockUtil.create(api, tempApiData);

                    if (null != mockResult) {
                        Integer id = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                        mockRuleService.setMockRule(id, "allColor", rule, api);
                        JsonObject mockIdItem = new JsonObject();
                        mockIdItem.addProperty("type", type);
                        mockIdItem.addProperty("mockId", id);
                        mockIds.add(mockIdItem);
                    }
                }
            } else {
                if ("allLong".equals(rule)) {
                    JsonElement jsonElement = null;
                    JsonObject tempItemData = jsonParser.parse(templateData).getAsJsonObject();
                    JsonObject tempApiData = jsonParser.parse(apiData).getAsJsonObject();
                    DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(tempItemData.toString());
                    DocumentContext tempMockApiJson = JsonPath.using(configuration).parse(tempApiData.toString());
                    for (int index = 0; index < jsonArray.length(); index++) {
                        String path = jsonArray.getString(index);
                        //构造数据
                        String[] subPath = path.split("\\.");
                        String targetContext = null;
                        targetContext = subPath[subPath.length - 1] + text;
                        tempItemDataJson = tempItemDataJson.set(ROOT_JSON_PATH + path, targetContext);
                    }
                    jsonElement = jsonParser.parse(tempItemDataJson.jsonString());
                    if (null != jsonElement) {
                        tempApiData = jsonParseHelper(tempMockApiJson, listPath, jsonElement.toString());
                        JsonObject mockResult = MockUtil.create(api, tempApiData);
                        if (null != mockResult) {
                            Integer id = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                            mockRuleService.setMockRule(id, "所有text类型字段", rule, api);
                            JsonObject mockIdItem = new JsonObject();
                            mockIdItem.addProperty("type", type);
                            mockIdItem.addProperty("mockId", id);
                            mockIds.add(mockIdItem);
                        }
                    }
                }
                if ("el_config".equals(rule)) {
                    JsonObject tempItemData = jsonParser.parse(templateData).getAsJsonObject();
                    JsonObject tempApiData = jsonParser.parse(apiData).getAsJsonObject();
                    JsonElement jsonElement = null;
                    if (jsonArray.length() != 0) {


                        for (int i = 0; i < jsonArray.length(); i++) {
                            JsonObject tempItemDataEl = tempItemData.deepCopy();
                            DocumentContext tempItemDataElJson = JsonPath.using(configuration).parse(tempItemDataEl.toString());
                            JsonObject tempMockDataEl = tempApiData.deepCopy();
                            DocumentContext tempMockApiJson = JsonPath.using(configuration).parse(tempMockDataEl.toString());


                            JSONObject el_config2 = jsonArray.getJSONObject(i);
                            String path = "." + el_config2.getString("name");
                            if ("" == path) {
                                continue;
                            }
                            int el_num = el_config2.getInt("num");
                            Object object = tempItemDataElJson.read(ROOT_JSON_PATH + path);
                            if (object instanceof JSONArray) {
                                JsonArray value = jsonParser.parse(tempItemDataElJson.read(ROOT_JSON_PATH + path).toString()).getAsJsonArray();
                                if (value.size() == 0) {
                                    continue;
                                }
                                JsonElement element = value.get(0);
                                int have_el = value.size();

                                if (null != value) {
                                    if (have_el < el_num) {
                                        for (int j = 0; j < el_num - have_el; j++) {
                                            jsonElement = jsonParser.parse(tempItemDataElJson.add(ROOT_JSON_PATH + path, gson.fromJson(element.toString(), Object.class)).jsonString());
                                        }
                                    } else {
                                        //如果配置数量小于原有数量
                                        jsonElement = jsonParser.parse(tempItemDataElJson.set(ROOT_JSON_PATH + path, gson.fromJson(element.toString(), Object.class)).jsonString());
                                        for (int j = 0; j < el_num - 1; j++) {
                                            jsonElement = jsonParser.parse(tempItemDataElJson.add(ROOT_JSON_PATH + path, gson.fromJson(element.toString(), Object.class)).jsonString());
                                        }
                                    }
                                    if (null != jsonElement) {
                                        tempApiData = jsonParseHelper(tempMockApiJson, listPath, jsonElement.toString());

                                        JsonObject mockResult = MockUtil.create(api, tempApiData);

                                        if (null != mockResult) {
                                            Integer id = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                                            mockRuleService.setMockRule(id, path, "num:" + el_num, api);
                                            JsonObject mockIdItem = new JsonObject();
                                            mockIdItem.addProperty("type", type);
                                            mockIdItem.addProperty("mockId", id);
                                            mockIds.add(mockIdItem);
                                        }
                                    } else {
                                        log.info("=============aaa==============" + rule);
                                        log.info(jsonElement.toString());
                                    }
                                }

                            }


                        }
                    }
                } else {
                    for (int index = 0; index < jsonArray.length(); index++) {
                        String path = jsonArray.getString(index);
                        JsonObject tempItemData = jsonParser.parse(templateData).getAsJsonObject();
                        JsonObject tempApiData = jsonParser.parse(apiData).getAsJsonObject();
                        DocumentContext tempItemDataJson = JsonPath.using(configuration).parse(tempItemData.toString());
                        DocumentContext tempMockApiJson = JsonPath.using(configuration).parse(tempApiData.toString());
                        JsonElement jsonElement = null;
                        //构造数据
                        String[] subPath = path.split("\\.");
                        String targetContext = null;
                        if ("invalidUrl".equals(rule)) {
                            jsonElement = jsonParser.parse(tempItemDataJson.set(ROOT_JSON_PATH + path, text).jsonString());
                        } else if ("blank".equals(rule)) {
                            Object object = tempItemDataJson.read(ROOT_JSON_PATH + path);
                            System.out.println(object.getClass());
                            System.out.print(object);
                            if (object instanceof JsonArray || object instanceof JSONArray) {
                                for (int i = 0; i < ((JSONArray) object).length(); i++) {
                                    jsonElement = jsonParser.parse(tempItemDataJson.delete(ROOT_JSON_PATH + path + "[" + i + "]").jsonString());
                                }
                            } else if (object instanceof String) {
                                jsonElement = jsonParser.parse(tempItemDataJson.set(ROOT_JSON_PATH + path, text).jsonString());
                            } else if (object instanceof JsonObject || object instanceof JSONObject) {
                                Iterator<String> iterator = ((JSONObject) object).keySet().iterator();
                                while (iterator.hasNext()) {
                                    String key = (String) iterator.next();
                                    jsonElement = jsonParser.parse(tempItemDataJson.delete(ROOT_JSON_PATH + path + "." + key).jsonString());
                                }
                            }
                        } else if ("long".equals(rule)) {
                            targetContext = subPath[subPath.length - 1] + text;
                            jsonElement = jsonParser.parse(tempItemDataJson.set(ROOT_JSON_PATH + path, targetContext).jsonString());
                        } else if ("richText".equals(rule)) {
                            String originalText = tempItemDataJson.read(ROOT_JSON_PATH + path);
                            if (originalText.contains("font")) {
                                targetContext = subPath[subPath.length - 1] + GENERAL_TEXT;
                            } else {
                                targetContext = RICH_TEXT_FIRST + subPath[subPath.length - 1] + RICH_TEXT_SECOND;
                            }
                            jsonElement = jsonParser.parse(tempItemDataJson.set(ROOT_JSON_PATH + jsonArray.getString(index), targetContext).jsonString());
                        } else if ("null".equals(rule)) {
//                            jsonElement = jsonParser.parse(tempItemDataJson.delete(ROOT_JSON_PATH + path).jsonString());
                            jsonElement = jsonParser.parse(tempItemDataJson.set(ROOT_JSON_PATH + path, null).jsonString());
                        } else if ("del".equals(rule)) {
                            jsonElement = jsonParser.parse(tempItemDataJson.delete(ROOT_JSON_PATH + path).jsonString());
                        }

                        //创建mock
                        if (null != jsonElement) {
                            tempApiData = jsonParseHelper(tempMockApiJson, listPath, jsonElement.toString());

                            JsonObject mockResult = MockUtil.create(api, tempApiData);

                            if (null != mockResult) {
                                Integer id = mockResult.getAsJsonObject("data").get("mockId").getAsInt();
                                mockRuleService.setMockRule(id, path, rule, api);
                                JsonObject mockIdItem = new JsonObject();
                                mockIdItem.addProperty("type", type);
                                mockIdItem.addProperty("mockId", id);
                                mockIds.add(mockIdItem);
                            }
                        } else {
                            System.out.print("=============aaa==============" + rule);
                            System.out.print(jsonElement);
                        }

                    }

                }

            }
        } catch (Exception e) {
            log.info("create mock err>>>>>" + e.getMessage());
        } finally {

        }

    }

    /**
     * 从xml文件中提取出文本字段和图片字段做为默认测试case，包括超长、空字符串、字段不存在、无效图片链接
     *
     * @param xml
     * @return
     */
    @Override
    public JSONObject getSmokeCase(String xml, String prefix) {
        JSONObject xmlJSONObj = XML.toJSONObject(xml);
        Configuration configuration = Configuration.builder()
                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        DocumentContext documentContext = using(configuration).parse(xmlJSONObj.toString());
        List<Object> text = documentContext.read("$..text");
        List<Object> img = documentContext.read("$..src");

        JSONObject rule = new JSONObject();
        Set<String> set = new HashSet<>();
        Set<String> invalidUrlSet = new HashSet<>();

        for (int i = 0; i < text.size(); i++) {
            Object object = text.get(i);
            if (null == object) {
                continue;
            }
            String path = object.toString();
            //倒计时
            if (path.contains("${fn,time_span_format")) {
                continue;
            }
            //
            if (path.contains("fn,string_format")) {
                continue;
            }
            //{i}+1，下标类
            if (path.contains("{i}") && !path.contains("[") && path.contains("`")) {
                continue;
            }
            //固定内容
            if (!path.contains("{") || !path.contains("}")) {
                continue;
            }
            //带for循环：Text="{tags[{For2}].text}"
            String pattern = "(\\{For\\d+})";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(path);

            while (m.find()) {
                String s = m.group();
                log.info(s);
                path = path.replace(s, "0");
            }
            //不是三元表达式
            if (!path.contains("?")) {
                String indexPattern = "(\\{[a-z]})";
                Pattern p = Pattern.compile(indexPattern);
                Matcher m1 = p.matcher(path);

                while (m1.find()) {
                    String s = m1.group();
                    log.info(s);
                    path = path.replace(s, "0");
                }
                path = path.split("\\{")[1].split("}")[0];
                set.add(prefix + path);
            } else {
                //三元表达式只取核心字段，例如："@{{items[{i}].subMessageStyle}==1?<strike>{items[{i}].subMessage}</strike>:{items[{i}].subMessage}}"
                path = path.replaceAll("\\(", "").replaceAll("\\)", "").replace("@", "");
                //08.23/猜喜处理优化:直接在<strike>里的元素
                if (!path.startsWith("<strike>")) {
                    path = path.substring(1, path.length() - 1);
                } else {
                    path = path.replaceFirst("^<strike>", "").replaceFirst("</strike>$", "");
                }
                //08.23end
                String firstPath = path.split("\\?")[1].split(":")[0].replace("{i}", "0");
                String secondPath = "";
                try {
                    secondPath = path.split(":")[1].replace("{i}", "0");
                } catch (Exception e) {
                    secondPath = "";
                }
                if (firstPath.contains("<") || firstPath.contains(">")) {
                    firstPath = firstPath.split(">")[1].split("<")[0];
                }
                if (secondPath.contains("<") || secondPath.contains(">")) {
                    if (secondPath.split(">").length < 2) {

                    } else {
                        secondPath = secondPath.split(">")[1].split("<")[0];
                    }
                }
                if (firstPath.contains("{") || firstPath.contains("}")) {
                    firstPath = firstPath.replace("{", "").replace("}", "");
                    //去除尾部的"."
                    if (firstPath.endsWith(".")) {
                        firstPath = firstPath.substring(0, firstPath.length() - 1);

                    }
                    if (!"".equals(firstPath)) {
                        set.add(prefix + firstPath);
                    }
                }
                if (secondPath.contains("{") || secondPath.contains("}")) {
                    secondPath = secondPath.replace("{", "").replace("}", "");
                    if (secondPath.endsWith(".")) {
                        secondPath = secondPath.substring(0, secondPath.length() - 1);
                    }
                    if (!"".equals(secondPath)) {
                        set.add(prefix + firstPath);
                    }
                }
            }
        }
        rule.put("long", set);
        for (int i = 0; i < img.size(); i++) {
            Object object = img.get(i);
            if (null == object) {
                continue;
            }
            String path = object.toString();
            //固定内容
            if (!path.contains("{") || !path.contains("}") || path.equals("{}")) {
                continue;
            }
            //带for循环：Text="{tags[{For2}].text}"
            String pattern = "(\\{For\\d+})";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(path);

            while (m.find()) {
                String s = m.group();
                log.info(s);
                path = path.replace(s, "0");
            }
            //不是三元表达式
            if (!path.contains("?")) {
                String indexPattern = "(\\{[a-z]})";
                Pattern p = Pattern.compile(indexPattern);
                Matcher m1 = p.matcher(path);

                while (m1.find()) {
                    String s = m1.group();
                    log.info(s);
                    path = path.replace(s, "0");
                }
                path = path.split("\\{")[1].split("}")[0];
                set.add(prefix + path);
                invalidUrlSet.add(prefix + path);
            } else {
                //三元表达式只取核心字段，例如："@{{items[{i}].subMessageStyle}==1?<strike>{items[{i}].subMessage}</strike>:{items[{i}].subMessage}}"
                path = path.replaceAll("\\(", "").replaceAll("\\)", "").replace("@", "");
                path = path.substring(1, path.length() - 1);
                String firstPath = path.split("\\?")[1].split(":")[0].replace("{i}", "0").replace("{", "").replace("}", "");
                String secondPath = "";
                try {
                    secondPath = path.split(":")[1].replace("{i}", "0").replace("{", "").replace("}", "");
                } catch (Exception e) {
                    secondPath = "";
                }

                if (firstPath.contains("<") || firstPath.contains(">")) {
                    firstPath = firstPath.split(">")[1].split("<")[0];
                }
                if (secondPath.contains("<") || secondPath.contains(">")) {
                    secondPath = secondPath.split(">")[1].split("<")[0];
                }
                set.add(prefix + firstPath);
                set.add(prefix + secondPath);
                invalidUrlSet.add(prefix + firstPath);
                invalidUrlSet.add(prefix + secondPath);
            }
        }
        rule.put("blank", set);
        rule.put("null", set);
        rule.put("invalidUrl", invalidUrlSet);
        log.info(rule.toString());
        return rule;
    }


    /**
     * 将冒烟case分组
     *
     * @param body data 测试用例 count 组数
     */
    @Override
    public String groupingSmokeCase(String body) {
        JSONObject bodyJson = new JSONObject(body);
        JSONArray result = new JSONArray();
        JSONObject jsonObject = new JSONObject(bodyJson.get("data").toString());
        int groupCount = bodyJson.getInt("count");
        Iterator<String> iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String business = iterator.next();
            JSONObject templateCase = new JSONObject(jsonObject.getJSONArray(business).get(0).toString());
            String templateName = templateCase.getString("templateName");
            JSONArray jsonArray = templateCase.getJSONArray("mock");
            // 将normal类型数据暂存
            JSONArray normalArray = new JSONArray();
            int caseCount = jsonArray.length();
            //如果原本case数就很少 || case数少于组数，不用分组
            if (caseCount <= 6 || caseCount <= groupCount) {
                result.put(jsonObject);
                return result.toString();
            }
            int size = jsonArray.length() / groupCount;
            if (size > 0) {
                for (int i = 0; i < caseCount; i++) {
                    JSONObject caseItem = jsonArray.getJSONObject(i);
                    // 兼容多个normal数据场景，将normal数据查找出来，只将非normal数据进行分组
                    if (caseItem.get("type").equals("normal")) {
                        normalArray.put(caseItem);
                        continue;
                    }

                    //先计算当前case应该属于第几个分组
                    int iPosition = i / size;

                    //边界值
                    if (i % size == 0) {
                        if (iPosition == 0) {
                            result.getJSONObject(0).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                        } else {
                            result.getJSONObject(iPosition - 1).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                        }
                    } else if (result.length() > iPosition) {    //该分组已创建
                        result.getJSONObject(iPosition).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                    } else {
                        if (result.length() == groupCount) {
                            //分组数已达到目标，剩余case插入最后一个分组即可
                            result.getJSONObject(iPosition - 1).getJSONArray(business).getJSONObject(0).getJSONArray("mock").put(caseItem);
                        } else {
                            //先创建分组
                            result.put(createGroupItem(business, templateName, caseItem));
                        }
                    }
                }
                for (int j = 0; j < result.length(); j++) {
                    JSONArray tempArray = new JSONArray();
                    JSONArray oldMockArray = result.getJSONObject(j).getJSONArray(business).getJSONObject(0).getJSONArray("mock");
                    for (int i = 0; i < normalArray.length(); i++) {
                        tempArray.put(normalArray.getJSONObject(i));
                    }
                    for (int i = 0; i < oldMockArray.length(); i++) {
                        tempArray.put(oldMockArray.getJSONObject(i));
                    }
                    result.getJSONObject(j).getJSONArray(business).getJSONObject(0).put("mock", tempArray);
                }
                return result.toString();
            }

        }
        result.put(jsonObject);
        return result.toString();
    }

    public JSONObject createGroupItem(String business, String templateName, JSONObject caseItem) {
        JSONObject groupItem = new JSONObject();
        JSONArray businessItem = new JSONArray();
        JSONArray mockItem = new JSONArray();
        mockItem.put(caseItem);
        JSONObject templateItem = new JSONObject();
        templateItem.put("templateName", templateName);
        templateItem.put("mock", mockItem);
        businessItem.put(templateItem);
        groupItem.put(business, businessItem);
        return groupItem;
    }

}
