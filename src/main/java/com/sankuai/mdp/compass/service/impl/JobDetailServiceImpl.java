package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.MockUtil;
import com.sankuai.mdp.compass.common.utils.StringUtil;
import com.sankuai.mdp.compass.entity.*;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import com.sankuai.mdp.compass.mapper.DynamicProcessMapper;
import com.sankuai.mdp.compass.mapper.JobDetailMapper;
import com.sankuai.mdp.compass.mapper.LayoutMgeDetailMapper;
import com.sankuai.mdp.compass.service.JobDetailService;
import jdk.nashorn.internal.parser.JSONParser;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import com.sankuai.mdp.compass.common.utils.OnesUtil;

/**
 * Created by xieyongrui on 2019/11/17.
 */
@Slf4j

@Service
public class JobDetailServiceImpl extends ServiceImpl<JobDetailMapper, JobDetail> implements JobDetailService {
    @Autowired
    JobDetailMapper jobDetailMapper;
    @Autowired
    CompatilityMapper compatilityMapper;
    @Autowired
    LayoutMgeDetailMapper layoutMgeDetailMapper;
    @Autowired
    DynamicProcessMapper dynamicProcessMapper;
    MockUtil mockUtil = new MockUtil();


    @Override
    public IPage<LayoutMgeDetail> getMgeList(String jobId) {
        QueryRequest request = new QueryRequest();
        Page<LayoutMgeDetail> page = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<LayoutMgeDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LayoutMgeDetail::getJobId, jobId);
        List<LayoutMgeDetail> layoutMgeDetailList = layoutMgeDetailMapper.selectList(queryWrapper);
        return page.setRecords(layoutMgeDetailList);
    }

    @Override
    public IPage<JobDetail> list(QueryRequest request, JobDetail jobDetail) {
        JsonParser jsonParser = new JsonParser();
        try {
            LambdaQueryWrapper<JobDetail> queryWrapper = new LambdaQueryWrapper<>();
            Integer jobId = jobDetail.getJobId();
            if (jobDetail.getTemplateName() != null) {
                queryWrapper.eq(JobDetail::getTemplateName, jobDetail.getTemplateName());
            }
            if (jobDetail.getType() != null) {
                queryWrapper.eq(JobDetail::getType, jobDetail.getType());
            }
            if (jobId == -1) {
                jobId = lastestJobId(request, jobDetail);
            }
            queryWrapper.eq(JobDetail::getJobId, jobId);
            Page<JobDetail> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<JobDetail> iPage = this.page(page, queryWrapper);
            String baseLx = null;
            Integer chechStatus = null;
            for (int i = 0; i < iPage.getRecords().size(); i++) {
                if (0 == i) {
                    CompatilityJob compatilityJob = compatilityMapper.selectById(iPage.getRecords().get(i).getJobId());
                    baseLx = compatilityJob.getBaseLx();
                    chechStatus = compatilityJob.getCheckStatus();
                }
//                iPage.getRecords().get(i).setJsonMockData(jsonParser.parse(iPage.getRecords().get(i).getMockData().toString()));
//                Object object = jsonParser.parse(iPage.getRecords().get(i).getCompareMge().toString());
//                if (object instanceof JSONObject) {
//                    iPage.getRecords().get(i).setJsonCompareMge(object);
//                } else if (object instanceof JSONArray) {
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("reportData",object);
//                    iPage.getRecords().get(i).setJsonCompareMge(jsonObject);
//                }
                if (null != baseLx) {
                    iPage.getRecords().get(i).setJsonCfgMge(baseLx);
                }
                iPage.getRecords().get(i).setCheckStatus(chechStatus);
            }
            return iPage;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    public Integer lastestJobId(QueryRequest request, JobDetail jobDetail) {
        Integer jobId = -1;
        try {
            LambdaQueryWrapper<JobDetail> queryWrapper = new LambdaQueryWrapper<>();

            queryWrapper.eq(JobDetail::getTemplateName, jobDetail.getTemplateName());
            queryWrapper.eq(JobDetail::getType, jobDetail.getType());
            queryWrapper.last("order by job_id desc");
            Page<JobDetail> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<JobDetail> iPage = this.page(page, queryWrapper);
            if (iPage.getRecords().size() > 0) {
                jobId = iPage.getRecords().get(0).getJobId();
            }
        } catch (Exception e) {
            log.error("获取列表失败", e);
            return -1;
        } finally {
            return jobId;
        }
    }

    public IPage<JobDetail> list(QueryRequest request, JobDetail jobDetail, String type) {
        try {
            LambdaQueryWrapper<JobDetail> queryWrapper = new LambdaQueryWrapper<>();

            Integer jobId = jobDetail.getJobId();
            if (jobDetail.getTemplateName() != null) {
                queryWrapper.eq(JobDetail::getTemplateName, jobDetail.getTemplateName());
            }
            if (jobDetail.getType() != null) {
                queryWrapper.eq(JobDetail::getType, jobDetail.getType());
            }
            if (jobId == -1) {
                jobId = lastestJobId(request, jobDetail);
            }
            queryWrapper.eq(JobDetail::getJobId, jobId);
            if ("operation".equals(type)) {
                queryWrapper.isNull(JobDetail::getMockType);
            } else {
                queryWrapper.eq(JobDetail::getMockType, type);
            }

            Page<JobDetail> page = new Page<>(request.getPageNum(), 500);

            IPage<JobDetail> iPage = this.page(page, queryWrapper);
            IPage<JobDetail> customPage = null;
            long total = iPage.getTotal();
            if ("abnormal".equals(type)) {
//                customPage = abnormalUIList(iPage,total);
            } else if ("operation".equals(type)) {
                customPage = operationList(iPage, total);
            } else {
                customPage = normalUIList(iPage, total);
            }
            return customPage;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    public IPage<AbnormalUi> abnormalList(QueryRequest request, JobDetail jobDetail, String type) {
        try {
            LambdaQueryWrapper<JobDetail> queryWrapper = new LambdaQueryWrapper<>();
            Integer jobId = jobDetail.getJobId();
            String searchKey = (String) jobDetail.getJsonMockData();
            String[] keySet = searchKey.split(",");

            if (jobDetail.getTemplateName() != null) {
                queryWrapper.eq(JobDetail::getTemplateName, jobDetail.getTemplateName());
            }
            if (jobDetail.getType() != null) {
                queryWrapper.eq(JobDetail::getType, jobDetail.getType());
            }
            queryWrapper.eq(JobDetail::getMockType, "abnormal");
            if (jobId == -1) {
                jobId = lastestJobId(request, jobDetail);
            }
            queryWrapper.eq(JobDetail::getJobId, jobId);

            queryWrapper.eq(JobDetail::getMockType, type);

            Page<JobDetail> page = new Page<>(request.getPageNum(), 500);

            IPage<JobDetail> iPage = this.page(page, queryWrapper);
            IPage<AbnormalUi> customPage = null;
            long total = iPage.getTotal();
            customPage = abnormalUIList(iPage, total);

            try {
                if (!searchKey.isEmpty()) {
                    Page<AbnormalUi> abnormalUiPageNew = new Page<>();
                    List<AbnormalUi> list4Search = new ArrayList<>();
                    List<AbnormalUi> list = customPage.getRecords();
                    for (AbnormalUi abnormalUi : list) {
                        String key = abnormalUi.getMockKey();
                        for (String s : keySet) {
                            if (key.equals(s)) {
                                list4Search.add(abnormalUi);
                            }
                        }
                    }
                    abnormalUiPageNew.setRecords(list4Search);
                    abnormalUiPageNew.setTotal((long) list4Search.size());
                    return abnormalUiPageNew;
                }
            } catch (Exception e) {
            }

            return customPage;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    private Page<JobDetail> normalUIList(IPage<JobDetail> page, long total) {
        Page<JobDetail> normalUIPage = new Page<>();
        List<JobDetail> list = new ArrayList<>();
        JSONObject flagObject = new JSONObject();
        for (int i = 0; i < page.getRecords().size(); i++) {

            JobDetail itemDetail = new JobDetail();

            String mockType = page.getRecords().get(i).getMockType();

            if ("abnormal".equals(mockType) || ("" == mockType || null == mockType)) {
                continue;
            }

            String platform = page.getRecords().get(i).getPlatform();
            Integer mockId = page.getRecords().get(i).getMockId();
            // normal需要根据mockId查找对应的数据名称
            Integer jobId = page.getRecords().get(i).getJobId();
            String caseName = getMockDataName(jobId, mockId);
            try {
                itemDetail.setTemplateName(page.getRecords().get(i).getTemplateName());
                itemDetail.setMockKey(page.getRecords().get(i).getMockKey());
                itemDetail.setMockRule(page.getRecords().get(i).getMockRule());
                itemDetail.setMockId(page.getRecords().get(i).getMockId());
                itemDetail.setPlatform(platform);
                itemDetail.setDeviceModel(page.getRecords().get(i).getDeviceModel());
                itemDetail.setDeviceVersion(page.getRecords().get(i).getDeviceVersion());
                itemDetail.setComparePic(page.getRecords().get(i).getComparePic());
                itemDetail.setMockType(page.getRecords().get(i).getMockType());
                itemDetail.setBasePic(page.getRecords().get(i).getBasePic());
                itemDetail.setResultPic(page.getRecords().get(i).getResultPic());
                //2023/08/03 视图层增加传递DeviceResolution字段
                itemDetail.setDeviceResolution(page.getRecords().get(i).getDeviceResolution());
                itemDetail.setExra(caseName);
                flagObject.put(mockId.toString(), itemDetail);
                list.add(itemDetail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        normalUIPage.setRecords(list);
        normalUIPage.setTotal(total);
        return normalUIPage;
    }

    // 根据mockId查询对应的数据名称
    public String getMockDataName(Integer jobId, Integer mockId) {
        String caseName = "MBC的API数据";
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", jobId);
        CompatilityJob compatilityJob = compatilityMapper.selectOne(queryWrapper);
        String mockRule = compatilityJob.getMockRule();
        JsonObject jsonObject = new JsonParser().parse(mockRule).getAsJsonObject();
        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            JsonArray mockArray = entry.getValue().getAsJsonArray().get(0).getAsJsonObject().getAsJsonArray("mock");
            for (JsonElement jsonElement : mockArray) {
                if (jsonElement.getAsJsonObject().get("mockId").getAsInt() == mockId) {
                    if (jsonElement.getAsJsonObject().has("caseName")) {
                        caseName = jsonElement.getAsJsonObject().get("caseName").getAsString();
                        if (caseName.startsWith("\"")) {
                            caseName = caseName.substring(1);
                        }
                        if (caseName.endsWith("\"")){
                            caseName = caseName.substring(0, caseName.length() - 1);
                        }
                    } else {
                        log.info("该mockId没有caseName");
                    }
                    break;
                }
            }
        }
        return caseName;
    }

    private Page<AbnormalUi> abnormalUIList(IPage<JobDetail> page, long total) {

        List<AbnormalUi> uiList = new ArrayList<>();
        Set mockkeySet = new HashSet();
        Page<AbnormalUi> abnormalUiPageNew = new Page<>();
        List<JobDetail> listRecord = page.getRecords();

        for (int i = 0; i < listRecord.size(); i++) {
            String mockKey = listRecord.get(i).getMockKey();
            if (null == mockKey) {
                continue;
            }
            String platform = listRecord.get(i).getPlatform();//Android iOS
            String deviceModel = listRecord.get(i).getDeviceModel();
            String comparePic = listRecord.get(i).getComparePic();
            String deviceVersion = listRecord.get(i).getDeviceVersion();
            String extra = listRecord.get(i).getExra();
            String mockRule = listRecord.get(i).getMockRule();
            String mockRule2Chi = mockRuleChinese(mockRule);
            String mockData = listRecord.get(i).getMockData();
            int deviceVersionDiff = Integer.parseInt(deviceVersion.split("[.]")[0]);
            JSONObject item = new JSONObject();
            String primaryKey = mockKey + " " + mockRule2Chi;
            if (!mockkeySet.contains(primaryKey)) {

                mockkeySet.add(primaryKey);
                AbnormalUi abnormalUi = new AbnormalUi();
                abnormalUi.setMockKey(primaryKey);
                abnormalUi.setMockData(mockData);
                uiList.add(abnormalUi);
                item.put("deviceModel", deviceVersion + "系统 - " + deviceModel);
                item.put("ComparePic", comparePic);

                if (StringUtil.isBlank(extra)) {
                    item.put("extra", "通过");
                } else {
                    item.put("extra", extra);
                }

                setVersion(abnormalUi, item, platform, deviceVersionDiff);
            } else {
                for (AbnormalUi u : uiList) {
                    if (u.getMockKey().equals(primaryKey)) {
                        item.put("deviceModel", deviceVersion + "系统 - " + deviceModel);
                        item.put("ComparePic", comparePic);
                        if (StringUtil.isBlank(extra)) {
                            item.put("extra", "通过");
                        } else {
                            item.put("extra", extra);
                        }
                        setVersion(u, item, platform, deviceVersionDiff);
                    }
                }
            }
        }
        abnormalUiPageNew.setRecords(uiList);
        abnormalUiPageNew.setTotal((long) mockkeySet.size());
        return abnormalUiPageNew;

        //以下代码用来回滚旧样式
//        Page<JobDetail> abnormalUIPage = new Page<>();
//        List<JobDetail> list = new ArrayList<>();
//        JSONObject flagObject = new JSONObject();
//        for(int i =0 ;i < page.getRecords().size();i++){
//
//            JobDetail itemDetail = new JobDetail();
//
//            String mockType = page.getRecords().get(i).getMockType();
//
//            if ("normal".equals(mockType) || ("" == mockType || null == mockType)) {
//                continue;
//            }
//
//            JSONObject item = new JSONObject();
//            item.put("deviceModel", page.getRecords().get(i).getDeviceModel());
//            item.put("deviceVersion", page.getRecords().get(i).getDeviceVersion());
//            item.put("comparePic", page.getRecords().get(i).getComparePic());
//
////            item.put("isVaild",page.getRecords().get(i).getIsValid());
////            item.put("exra",page.getRecords().get(i).getExra());
////            item.put("overlapPic",page.getRecords().get(i).getOverlapPic());
//
//            itemDetail.setMockType(page.getRecords().get(i).getMockType());
//
//            String platform = page.getRecords().get(i).getPlatform();
//            Integer mockId = page.getRecords().get(i).getMockId();
//            JsonObject mockData = new JsonParser().parse(mockUtil.get(mockId)).getAsJsonObject();
//            try {
//                itemDetail.setTemplateName(page.getRecords().get(i).getTemplateName());
//                itemDetail.setMockKey(page.getRecords().get(i).getMockKey());
//                itemDetail.setMockRule(page.getRecords().get(i).getMockRule());
//                itemDetail.setMockId(page.getRecords().get(i).getMockId());
//                itemDetail.setPlatform(platform);
//                itemDetail.setJsonMockData(mockData.toString());
//                itemDetail.setExra(page.getRecords().get(i).getExra());
//
//                if ("Android".equals(platform)) {
//                    itemDetail.setJsonAndr(item);
//                } else if ("iOS".equals(platform)) {
//                    itemDetail.setJsonIos(item);
//                }
//
//                flagObject.put(mockId.toString(), itemDetail);
//                list.add(itemDetail);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        abnormalUIPage.setRecords(list);
//        abnormalUIPage.setTotal(total);
//        return abnormalUIPage;
    }

    public String mockRuleChinese(String mockRule) {
        if (mockRule.equals("invalidUrl")) {
            return "无效链接";
        } else if (mockRule.equals("noColor")) {
            return "没有颜色";
        } else if (mockRule.equals("customColor")) {
            return "自定义颜色";
        } else if (mockRule.equals("null")) {
            return "字段值为null";
        } else if (mockRule.equals("blank")) {
            return "字段值为空字符串";
        } else if (mockRule.equals("del")) {
            return "字段未下发";
        } else if (mockRule.contains("rich")) {
            return "富文本";
        } else if (mockRule.equals("long")) {
            return "字段超长";
        } else {
            return mockRule;
        }
    }

    public void setVersion(AbnormalUi abnormalUi, JSONObject item, String platform, int deviceVersionDiff) {
        ArrayList<JSONObject> arrayList = new ArrayList<>();
        if ("Android".equals(platform)) {
            if (5 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getAndroid5();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setAndroid5(arrayList);
            } else if (6 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getAndroid6();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setAndroid6(arrayList);
            } else if (7 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getAndroid7();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setAndroid7(arrayList);
            } else if (8 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getAndroid8();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setAndroid8(arrayList);
            } else if (9 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getAndroid9();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setAndroid9(arrayList);
            } else if (10 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getAndroid10();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setAndroid10(arrayList);
            }
        } else if ("iOS".equals(platform)) {
            if (10 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getIOS10();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setIOS10(arrayList);
            } else if (11 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getIOS11();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setIOS11(arrayList);
            } else if (12 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getIOS12();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setIOS12(arrayList);
            } else if (13 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getIOS13();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setIOS13(arrayList);
            } else if (14 == deviceVersionDiff) {
                ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUi.getIOS14();
                if (!(null == items)) {
                    for (JSONObject temp : items) {
                        arrayList.add(temp);
                    }
                }
                arrayList.add(item);
                abnormalUi.setIOS14(arrayList);
            }
        } else {
            return;
        }
    }


    private Page<JobDetail> operationList(IPage<JobDetail> page, long total) {
        Page<JobDetail> operationPage = new Page<>();
        List<JobDetail> list = new ArrayList<>();
        JSONObject flagObject = new JSONObject();

        for (int i = 0; i < page.getRecords().size(); i++) {

            String mockType = page.getRecords().get(i).getMockType();
            if ("abnormal".equals(mockType) || "normal".equals(mockType)) {
                continue;
            }

            String templateName = page.getRecords().get(i).getTemplateName();
            if (!flagObject.containsKey(templateName)) {
                flagObject.put(templateName, new JSONArray());
            }

            String operationComponent = page.getRecords().get(i).getOperationComponent();
//            if (flagObject.getJSONArray(templateName).contains(operationComponent)) {
//                continue;
//            } else {
//                flagObject.getJSONArray(templateName).add(operationComponent);
//            }

            JSONObject item = new JSONObject();
            item.put("beforeJumpPic", page.getRecords().get(i).getBeforeJumpPic());
            item.put("clickPic", page.getRecords().get(i).getClickPic());
            item.put("afterJumpPic", page.getRecords().get(i).getAfterJumpPic());
            item.put("deviceModel", page.getRecords().get(i).getDeviceModel());
            item.put("deviceVersion", page.getRecords().get(i).getDeviceVersion());

            JobDetail itemDetail = new JobDetail();

            String platform = page.getRecords().get(i).getPlatform();
            itemDetail.setTemplateName(templateName);
            itemDetail.setOperationComponent(operationComponent);
            itemDetail.setMockRule(page.getRecords().get(i).getMockRule());
            itemDetail.setPlatform(platform);

            if ("Android".equals(platform)) {
                itemDetail.setJsonAndr(item);
            } else if ("iOS".equals(platform)) {
                itemDetail.setJsonIos(item);
            }

            flagObject.put(templateName + "-" + operationComponent, itemDetail);
            list.add(itemDetail);

        }
        operationPage.setRecords(list);
        operationPage.setTotal(total);
        return operationPage;
    }

    @Override
    public void add(JobDetail jobDetail) {
        jobDetailMapper.insert(jobDetail);
    }

    @Override
    public List<JobDetail> selectAllByJobId(Integer jobId) {
        LambdaQueryWrapper<JobDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JobDetail::getJobId, jobId);
        return jobDetailMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean reportBug(JsonObject body) {
        JsonObject map = body.get("payload").getAsJsonObject();
        JsonArray array = map.get("platform").getAsJsonArray();
        int processId = map.get("processId").getAsInt();
        ArrayList list = new ArrayList();
        for (int i = array.size() - 1; i >= 0; i--) {
            if (i % 2 == 1) {
                if (!(array.get(i).toString().equals("0"))) {
                    list.add(array.get(i).getAsJsonObject().get("ComparePic").getAsString());
                } else {
                    i--;
                }
            } else {
                list.add(array.get(i).getAsString());
            }
        }

        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getId, processId);
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        String template = dynamicProcess.getTemplateName();
//        String rd = dynamicProcess.getSmokeUser();
        String assigned = map.get("assignee").getAsString();
        String onesUrl = dynamicProcess.getOnesUrl();
        String[] temp = onesUrl.split("/");
        String projectId = onesUrl.split("/")[temp.length - 1];
        String title = " #自动化#【" + template + "】模板UI缺陷";
        StringBuilder detail = new StringBuilder();
        detail.append("<h3>报告地址：http://qaassist.sankuai.com/compass/dashboard#/dlautotest/job/JobDetail?processId=" + processId + "</h3>");
        detail.append("<hr/>");
        for (int i = list.size() - 1; i >= 0; i--) {
            if (i % 2 == 1) {
                detail.append("<h5>【问题字段】" + list.get(i).toString().split("_")[0] + "</h5>");
                detail.append("<h5>【问题机型】" + list.get(i).toString().split("_")[1] + "</h5>");
            } else {
                detail.append("<br/><b>测试图：</b><br/>");
                detail.append("<img width=\"200\" src=" + list.get(i) + ">");
                detail.append("<hr/>");
            }

        }
        JsonArray cc = new JsonArray();
        JsonArray label = new JsonArray();
        label.add(7533);
        String createOnesIssues = "https://ones.sankuai.com/api/1.0/ones/projects/7991/issue";

        String response = new OnesUtil().createBug(detail.toString(), cc, title, assigned, 7991, 13786, label, createOnesIssues, projectId);
        String bugId = new JsonParser().parse(response).getAsJsonObject().get("data").getAsJsonObject().get("id").getAsString();

        String bugIds = dynamicProcess.getBugId();
        if (!(null == bugIds)) {
            bugId = bugId + "_" + bugIds;
        }
        dynamicProcess.setBugId(bugId);
        dynamicProcessMapper.updateById(dynamicProcess);
        return true;
    }

    @Override
    public IPage<AbnormalUIList> abnormalListv2(QueryRequest request, JobDetail jobDetail, String type) {
        try {
            LambdaQueryWrapper<JobDetail> queryWrapper = new LambdaQueryWrapper<>();
            Integer jobId = jobDetail.getJobId();
            String searchKey = (String) jobDetail.getJsonMockData();
            String[] keySet = searchKey.split(",");

            if (jobDetail.getTemplateName() != null) {
                queryWrapper.eq(JobDetail::getTemplateName, jobDetail.getTemplateName());
            }
            if (jobDetail.getType() != null) {
                queryWrapper.eq(JobDetail::getType, jobDetail.getType());
            }
            queryWrapper.eq(JobDetail::getMockType, "abnormal");
            if (jobId == -1) {
                jobId = lastestJobId(request, jobDetail);
            }
            queryWrapper.eq(JobDetail::getJobId, jobId);

            queryWrapper.eq(JobDetail::getMockType, type);

            Page<JobDetail> page = new Page<>(request.getPageNum(), 500);

            IPage<JobDetail> iPage = this.page(page, queryWrapper);
            IPage<AbnormalUIList> customPage = null;
            long total = iPage.getTotal();
            customPage = abnormalUIListv2(iPage, total);

            try {
                if (!searchKey.isEmpty()) {
                    Page<AbnormalUIList> abnormalUiPageNew = new Page<>();
                    List<AbnormalUIList> list4Search = new ArrayList<>();
                    List<AbnormalUIList> list = customPage.getRecords();
                    for (AbnormalUIList abnormalUi : list) {
                        String key = abnormalUi.getMockKey();
                        for (String s : keySet) {
                            if (key.equals(s)) {
                                list4Search.add(abnormalUi);
                            }
                        }
                    }
                    abnormalUiPageNew.setRecords(list4Search);
                    abnormalUiPageNew.setTotal((long) list4Search.size());
                    return abnormalUiPageNew;
                }
            } catch (Exception e) {
            }

            return customPage;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    private Page<AbnormalUIList> abnormalUIListv2(IPage<JobDetail> page, long total) {

        List<AbnormalUIList> uiList = new ArrayList<>();
        Set mockkeySet = new HashSet();
        Page<AbnormalUIList> abnormalUiPageNew = new Page<>();
        List<JobDetail> listRecord = page.getRecords();

        for (int i = 0; i < listRecord.size(); i++) {
            String mockKey = listRecord.get(i).getMockKey();
            if (null == mockKey) {
                continue;
            }
            String platform = listRecord.get(i).getPlatform();//Android iOS
            String deviceModel = listRecord.get(i).getDeviceModel();
            String comparePic = listRecord.get(i).getComparePic();
            String deviceVersion = listRecord.get(i).getDeviceVersion();
            String extra = listRecord.get(i).getExra();
            String mockRule = listRecord.get(i).getMockRule();
            String mockRule2Chi = mockRuleChinese(mockRule);
            String mockData = listRecord.get(i).getMockData();
            String mergedUrl = listRecord.get(i).getMergedUrl();
            int deviceVersionDiff = Integer.parseInt(deviceVersion.split("[.]")[0]);
            JSONObject item = new JSONObject();
            String primaryKey = mockKey + " " + mockRule2Chi;
            if (!mockkeySet.contains(primaryKey)) {

                mockkeySet.add(primaryKey);
                AbnormalUIList abnormalUi = new AbnormalUIList();
                abnormalUi.setMockKey(primaryKey);
                abnormalUi.setMockData(mockData);
                uiList.add(abnormalUi);
                item.put("deviceModel", deviceVersion + "系统 - " + deviceModel);
                item.put("ComparePic", comparePic);
                // 返回合并后带有坐标标识的问题图片
                item.put("mergedUrl", mergedUrl);

                if (StringUtil.isBlank(extra)) {
                    item.put("extra", "通过");
                } else {
                    item.put("extra", extra);
                }

                setVersionforlist(abnormalUi, item, platform, deviceVersionDiff);
            } else {
                for (AbnormalUIList u : uiList) {
                    if (u.getMockKey().equals(primaryKey)) {
                        item.put("deviceModel", deviceVersion + "系统 - " + deviceModel);
                        item.put("ComparePic", comparePic);
                        // 返回合并后带有坐标标识的问题图片
                        item.put("mergedUrl", mergedUrl);
                        if (StringUtil.isBlank(extra)) {
                            item.put("extra", "通过");
                        } else {
                            item.put("extra", extra);
                        }
                        setVersionforlist(u, item, platform, deviceVersionDiff);
                    }
                }
            }
        }
        abnormalUiPageNew.setRecords(uiList);
        abnormalUiPageNew.setTotal((long) mockkeySet.size());
        return abnormalUiPageNew;

        //以下代码用来回滚旧样式
//        Page<JobDetail> abnormalUIPage = new Page<>();
//        List<JobDetail> list = new ArrayList<>();
//        JSONObject flagObject = new JSONObject();
//        for(int i =0 ;i < page.getRecords().size();i++){
//
//            JobDetail itemDetail = new JobDetail();
//
//            String mockType = page.getRecords().get(i).getMockType();
//
//            if ("normal".equals(mockType) || ("" == mockType || null == mockType)) {
//                continue;
//            }
//
//            JSONObject item = new JSONObject();
//            item.put("deviceModel", page.getRecords().get(i).getDeviceModel());
//            item.put("deviceVersion", page.getRecords().get(i).getDeviceVersion());
//            item.put("comparePic", page.getRecords().get(i).getComparePic());
//
////            item.put("isVaild",page.getRecords().get(i).getIsValid());
////            item.put("exra",page.getRecords().get(i).getExra());
////            item.put("overlapPic",page.getRecords().get(i).getOverlapPic());
//
//            itemDetail.setMockType(page.getRecords().get(i).getMockType());
//
//            String platform = page.getRecords().get(i).getPlatform();
//            Integer mockId = page.getRecords().get(i).getMockId();
//            JsonObject mockData = new JsonParser().parse(mockUtil.get(mockId)).getAsJsonObject();
//            try {
//                itemDetail.setTemplateName(page.getRecords().get(i).getTemplateName());
//                itemDetail.setMockKey(page.getRecords().get(i).getMockKey());
//                itemDetail.setMockRule(page.getRecords().get(i).getMockRule());
//                itemDetail.setMockId(page.getRecords().get(i).getMockId());
//                itemDetail.setPlatform(platform);
//                itemDetail.setJsonMockData(mockData.toString());
//                itemDetail.setExra(page.getRecords().get(i).getExra());
//
//                if ("Android".equals(platform)) {
//                    itemDetail.setJsonAndr(item);
//                } else if ("iOS".equals(platform)) {
//                    itemDetail.setJsonIos(item);
//                }
//
//                flagObject.put(mockId.toString(), itemDetail);
//                list.add(itemDetail);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        abnormalUIPage.setRecords(list);
//        abnormalUIPage.setTotal(total);
//        return abnormalUIPage;
    }

    public void setVersionforlist(AbnormalUIList abnormalUis, JSONObject item, String platform, int deviceVersionDiff) {
        ArrayList<JSONObject> arrayList = new ArrayList<>();
        ArrayList<JSONObject> items = (ArrayList<JSONObject>) abnormalUis.getProblems();
        if (!(null == items)) {
            for (JSONObject temp : items) {
                arrayList.add(temp);
            }
        }
        arrayList.add(item);
        abnormalUis.setProblems(arrayList);

    }
}