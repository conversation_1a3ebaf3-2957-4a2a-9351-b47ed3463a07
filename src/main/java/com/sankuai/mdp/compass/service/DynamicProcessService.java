package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.*;

import java.util.ArrayList;
import java.util.Date;

/**
 * Created by sunkangtong on 2021/3/9.
 */
public interface DynamicProcessService extends IService<DynamicProcess> {



    boolean updateProcess(CompatilityJob compatilityJob);


    IPage<DynamicProcess> listAggregationSecond(QueryRequest request, DynamicProcess dynamicProcess);

    ArrayList getStep(String processId);

    ArrayList updateStep(String processId, int current, String clickStatus, String onesUrl, String confirmUser);

    ArrayList getCurrent(String processId);

    boolean stopProcess(String processId);

    void inform(String templateName);

    int getProcessId(String templateName);

    int jobId2ProcessId(int id);

    void setScore(String processId, String score);

    DynamicProcess getProcessByTemplateName(String templateName);

    void handleProcessNotice();

    String retrySmoke(String templateName, String business, String user);

    int getProcessStatus(String templateName);

    boolean allCancle(int processId, String user, String cancleReason);

    ArrayList xmlDiff(String diffA, String diffB);


    ArrayList completeTemplate(Date startDate,Date endDate);

    ArrayList<ChangeComplianceTemplate> onlineTemplate(Date startDate, Date endDate);

    void updatePublish(DynamicTemplatePublishDetail dynamicTemplatePublishDetail);
}
