package com.sankuai.mdp.compass.service.crane;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.utils.HttpUtil;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import javax.crypto.Mac;

import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.TreeMap;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import org.apache.commons.codec.binary.Base64;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;

@CraneConfiguration
public class PTQATOR {

    //APP KEY
    //platform_qa
    private final static String APP_KEY = "platform_qa";
    // APP密钥
    //cr4mpuJkPqN3cfUmxciJtxFQGL3ZQgbH
    private final static String APP_SECRET = "cr4mpuJkPqN3cfUmxciJtxFQGL3ZQgbH";
    //API域名
    private final static String HOST = "uem.sankuai.com";
    //自定义参与签名Header前缀（可选,默认只有"S-Ca-"开头的参与到Header签名）
    private final static List<String> CUSTOM_HEADERS_TO_SIGN_PREFIX = new ArrayList<String>();

    private static final String HMAC_ALGORITHM = "HmacSHA256";
//    private static final Base64.Encoder BASE64_ENCODER = Base64.getEncoder();

    public static String sendGetRequest(String urlStr,
                                        String appName,
                                        long timestamp,
                                        String secret,
                                        Map<String, String> queryParams) throws Exception {
        // 1. 构建完整URL
        String fullUrl = buildUrlWithQuery(urlStr, queryParams);

        String path = "/ueo/openapi/rcf/dashboard/getBgAggregationData";

        // 2. 生成签名
        String signature = generateSignature(appName, timestamp, secret, path, queryParams);
        System.out.println(signature);

        // 3. 创建HTTP连接
        URL url = new URL(fullUrl);
        System.out.println("url is " + url);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");

        // 设置必要请求头
        conn.setRequestProperty("S-Ca-App", appName);
        conn.setRequestProperty("S-Ca-Timestamp", String.valueOf(timestamp));
        conn.setRequestProperty("S-Ca-Signature", signature);
        conn.setRequestProperty("Content-type", "application/json");

        // 4. 读取响应
        int status = conn.getResponseCode();
        BufferedReader in = new BufferedReader(
                new InputStreamReader(conn.getInputStream()));
        String inputLine;
        StringBuilder content = new StringBuilder();
        while ((inputLine = in.readLine()) != null) {
            content.append(inputLine);
        }
        in.close();
        conn.disconnect();

        return content.toString();
    }

    private static String generateSignature(String appName,
                                            long timestamp,
                                            String secret,
                                            String path,
                                            Map<String, String> queryParams) throws Exception {
        String stringToSign = buildStringToSign(appName, timestamp, path, queryParams);

        Mac hmacSha256 = Mac.getInstance(HMAC_ALGORITHM);
        byte[] keyBytes = secret.getBytes("UTF-8");
        hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
        String sign = new String(Base64.encodeBase64(hmacSha256.doFinal(stringToSign.getBytes("UTF-8"))));

        return sign;
    }

    private static String buildStringToSign(String appName,
                                            long timestamp,
                                            String path,
                                            Map<String, String> queryParams) {
        // HTTP方法
        String httpMethod = "GET";

        // Content-MD5（空值）
        String contentMD5 = "";

        // Headers部分（按字典序排序）
        TreeMap<String, String> headers = new TreeMap<>();
        headers.put("S-Ca-App", appName);
        headers.put("S-Ca-Timestamp", String.valueOf(timestamp));

        StringBuilder headersBuilder = new StringBuilder();
        headers.forEach((k, v) -> headersBuilder.append(k).append(":").append(v).append("\n"));

        if (headersBuilder.length() > 0) {
            headersBuilder.setLength(headersBuilder.length() - 1); // 删除最后一个'\n'
        }

        // URL部分

        String urlPart = buildUrlPart(path, queryParams);

        // 组合签名字符串（调试输出建议）
        String stringToSign = String.join("\n",
                httpMethod,
                contentMD5,       // 修正后：空值需保留换行符
//                headersBuilder.toString(),
                urlPart);

        return stringToSign;


    }

    private static String buildUrlWithQuery(String baseUrl, Map<String, String> params) {
        if (params == null || params.isEmpty()) return baseUrl;

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");
        appendSortedParams(urlBuilder, params);
        return urlBuilder.toString();
    }

    private static String buildUrlPart(String path, Map<String, String> params) {
        StringBuilder urlPart = new StringBuilder(path);
        if (params != null && !params.isEmpty()) {
            urlPart.append("?");
            appendSortedParams(urlPart, params);
        }
        return urlPart.toString();
    }

    private static void appendSortedParams(StringBuilder builder, Map<String, String> params) {
        new TreeMap<>(params).forEach((k, v) -> {
            builder.append(k);
            if (v != null && !v.trim().isEmpty()) {
                builder.append("=").append(v);
            }
            builder.append("&");
        });
        if (params != null && !params.isEmpty()) {
            builder.deleteCharAt(builder.length() - 1); // 移除末尾的&
        }
    }

    // 使用示例

    @Crane("com.sankuai.compass.uploadRcfData")
    public void uploadRcfData() throws Exception {
        String apiUrl = "https://uem.sankuai.com/ueo/openapi/rcf/dashboard/getBgAggregationData";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        long timestamp = System.currentTimeMillis();

        // 设备类型配置
        List<Map<String, String>> deviceConfigs = Arrays.asList(
                createDeviceConfig("Android", "ALL", "mainRCF_android", "Android主流程RCF分"),
                createDeviceConfig("iOS", "ALL", "mainRCF_ios", "iOS主流程RCF分"),
                createDeviceConfig("Android", "LOW", "mainRCF_android_low", "Android主流程低端机RCF分"),
                createDeviceConfig("iOS", "LOW", "mainRCF_ios_low", "iOS主流程低端机RCF分")
        );

        // 获取周数据
        LocalDate endDate = today.minusDays(1);
        LocalDate startDate = endDate.minusDays(6);
        WeekFields weekFields = WeekFields.ISO;
        int year = endDate.get(weekFields.weekBasedYear());
        int week = endDate.get(weekFields.weekOfWeekBasedYear());
        String weekCode = String.format("%d-%02d", year, week);

        // 遍历设备配置获取并上传数据
        for (Map<String, String> config : deviceConfigs) {
            Map<String, String> params = getBaseParams(startDate, endDate, config.get("deviceType"), config.get("deviceLevel"));
            String response = sendGetRequest(apiUrl, APP_KEY, timestamp, APP_SECRET, params);
            String score = extractScore(response);

            // 上传周数据
            uploadWeekData(today, weekCode, score, config.get("sub_metrictype"), config.get("sub_name"));

            // 如果是周一，处理MTD和月度数据
            if (today.getDayOfWeek() == DayOfWeek.MONDAY) {
                handleMtdData(today, config, apiUrl, timestamp);

                // 如果是本月第一个周一，处理上月数据
                if (today.equals(today.with(TemporalAdjusters.firstInMonth(DayOfWeek.MONDAY)))) {
                    handleMonthData(today, config, apiUrl, timestamp);
                }
            }
        }
    }


    private static Map<String, String> getBaseParams(LocalDate startDate, LocalDate endDate, String deviceType, String deviceLevel) {
        Map<String, String> params = new TreeMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        params.put("startDate", formatter.format(startDate));
        params.put("endDate", formatter.format(endDate));
        params.put("platform", "MT");
        params.put("deviceType", deviceType);
        params.put("deviceLevel", deviceLevel);
        params.put("indicatorName", "RCF");
        params.put("tab", "1");
        params.put("techStack", "ALL");
        params.put("region", "ALL");
        params.put("cityTier", "ALL");
        return params;
    }

    private static String extractScore(String response) {
        JSONObject jsonObject = JSON.parseObject(response);
        JSONArray data = jsonObject.getJSONArray("data");
        return data.getJSONObject(2).getString("score");
    }

    // 周汇总表：使用当周最后一天（即endDate）
    private static void uploadWeekData(LocalDate today, String weekCode, String value, String subMetrictype, String subName) {
        LocalDate endDate = today.minusDays(1); // 获取前一天作为当周最后一天
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("date", endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        jsonObject.addProperty("weekcode", weekCode);
        jsonObject.addProperty("metrictype", "RCF");
        jsonObject.addProperty("sub_metrictype", subMetrictype);
        jsonObject.addProperty("name", "RCF综合得分");
        jsonObject.addProperty("sub_name", subName);
        jsonObject.addProperty("value", value);
        HttpUtil.jsonPost("https://tor.sankuai.com/tor/open/InsertWeekData", jsonObject);
    }

    // MTD数据表：使用MTD截止日期（即mtdEnd）
    private static void uploadMtdData(LocalDate today, String value, String subMetrictype, String subName) {
        LocalDate mtdEnd = today.minusDays(1); // MTD截止日期为前一天
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("date", mtdEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        jsonObject.addProperty("month", mtdEnd.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        jsonObject.addProperty("metrictype", "RCF");
        jsonObject.addProperty("sub_metrictype", subMetrictype);
        jsonObject.addProperty("name", "RCF综合得分");
        jsonObject.addProperty("sub_name", subName);
        jsonObject.addProperty("value", value);
        HttpUtil.jsonPost("https://tor.sankuai.com/tor/open/InsertMtdData", jsonObject);
    }

    // 月汇总表：使用当月最后一天
    private static void uploadMonthData(LocalDate monthEnd, String value, String subMetrictype, String subName) {
        // monthEnd已经是上月最后一天，直接使用
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("date", monthEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        jsonObject.addProperty("month", monthEnd.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        jsonObject.addProperty("metrictype", "RCF");
        jsonObject.addProperty("sub_metrictype", subMetrictype);
        jsonObject.addProperty("name", "RCF综合得分");
        jsonObject.addProperty("sub_name", subName);
        jsonObject.addProperty("value", value);
        HttpUtil.jsonPost("https://tor.sankuai.com/tor/open/InsertMonthData", jsonObject);
    }
    private static void handleMtdData(LocalDate today, Map<String, String> config, String apiUrl, long timestamp) throws Exception {
        LocalDate mtdStart = today.withDayOfMonth(1);
        LocalDate mtdEnd = today.minusDays(1);
        if (!mtdStart.isAfter(mtdEnd)) {
            Map<String, String> params = getBaseParams(mtdStart, mtdEnd, config.get("deviceType"), config.get("deviceLevel"));
            String response = sendGetRequest(apiUrl, APP_KEY, timestamp, APP_SECRET, params);
            String score = extractScore(response);
            uploadMtdData(today, score, config.get("sub_metrictype"), config.get("sub_name"));
        }
    }

    private static void handleMonthData(LocalDate today, Map<String, String> config, String apiUrl, long timestamp) throws Exception {
        LocalDate lastMonth = today.minusMonths(1);
        LocalDate monthStart = lastMonth.withDayOfMonth(1);
        LocalDate monthEnd = lastMonth.withDayOfMonth(lastMonth.lengthOfMonth());
        Map<String, String> params = getBaseParams(monthStart, monthEnd, config.get("deviceType"), config.get("deviceLevel"));
        String response = sendGetRequest(apiUrl, APP_KEY, timestamp, APP_SECRET, params);
        String score = extractScore(response);
        uploadMonthData(monthEnd, score, config.get("sub_metrictype"), config.get("sub_name"));
    }

    // 辅助方法创建不可变Map
    private static Map<String, String> createDeviceConfig(String deviceType, String deviceLevel, String subMetrictype, String subName) {
        Map<String, String> config = new HashMap<>();
        config.put("deviceType", deviceType);
        config.put("deviceLevel", deviceLevel);
        config.put("sub_metrictype", subMetrictype);
        config.put("sub_name", subName);
        return Collections.unmodifiableMap(config);
    }


}
