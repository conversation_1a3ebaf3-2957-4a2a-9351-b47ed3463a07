package com.sankuai.mdp.compass.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;

import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.MbcUtil;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicCase;
import com.sankuai.mdp.compass.entity.DynamicPage;
import com.sankuai.mdp.compass.mapper.DynamicCaseMapper;
import com.sankuai.mdp.compass.mapper.DynamicDataMapper;
import com.sankuai.mdp.compass.mapper.DynamicPageMapper;
import com.sankuai.mdp.compass.service.*;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * Created by sunkangtong on 2021/3/9.
 */

@Slf4j
@Service
public class DynamicCaseServiceImpl extends ServiceImpl<DynamicCaseMapper, DynamicCase> implements DynamicCaseService {
    @Autowired
    DynamicCaseMapper dynamicCaseMapper;

    @Autowired
    DynamicPageMapper dynamicPageMapper;

    JsonParser jsonParser = new JsonParser();

    public static String listPath = ".data.groups[1].items";
    public static String api = "/mbc/homemine";
    public static String apiData = "{\"code\":0,\"data\":{\"actionBar\":{\"biz\":{\"account\":{\"footstep\":{\"gameName\":\"走路赚钱\",\"iconUrl\":\"https://p0.meituan.net/linglong/dd83de89907e30b86483be08f3d99593134193.gif\",\"targetUrl\":\"imeituan://www.meituan.com/web?notitlebar=1&url=https://ux.meituan.com/footstep/index.html\",\"content\":\"走路赚钱\",\"isShow\":false},\"avatarUrl\":\"https://img.meituan.net/avatar/7f1eddcb4d1d7fcf855bb13529c9c37e81540.jpg\",\"nickname\":\"不要改名字哈\",\"iconDestUrl\":\"imeituan://www.meituan.com/userinfo\",\"id\":**********,\"growthDestUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=group&mrn_entry=member-center&mrn_component=member-center\",\"growthValue\":35,\"verifyInfo\":{\"verifyStatus\":0,\"more\":{\"activity_id\":\"-999\",\"biz_id\":\"65\",\"display_name\":\"去实名\",\"config_id\":\"5615\",\"activity_from\":\"-999\"},\"link\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fnpay.meituan.com%2Ffup%2Fverification%2Findex.html\",\"content\":\"去实名\"}}},\"titleColor\":\"#222222\",\"type\":\"simple_slide_gradient\"},\"extra\":{\"moduleVersion\":2},\"groups\":[{\"id\":\"minepage_account_group\",\"items\":[{\"biz\":{\"account\":{\"footstep\":{\"gameName\":\"走路赚钱\",\"iconUrl\":\"https://p0.meituan.net/linglong/dd83de89907e30b86483be08f3d99593134193.gif\",\"targetUrl\":\"imeituan://www.meituan.com/web?notitlebar=1&url=https://ux.meituan.com/footstep/index.html\",\"content\":\"走路赚钱\",\"isShow\":false},\"avatarUrl\":\"https://img.meituan.net/avatar/7f1eddcb4d1d7fcf855bb13529c9c37e81540.jpg\",\"nickname\":\"不要改名字哈\",\"iconDestUrl\":\"imeituan://www.meituan.com/userinfo\",\"id\":**********,\"growthDestUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=group&mrn_entry=member-center&mrn_component=member-center\",\"growthValue\":35,\"verifyInfo\":{\"verifyStatus\":0,\"more\":{\"activity_id\":\"-999\",\"biz_id\":\"65\",\"display_name\":\"去实名\",\"config_id\":\"5615\",\"activity_from\":\"-999\"},\"link\":\"imeituan://www.meituan.com/web?url=https%3A%2F%2Fnpay.meituan.com%2Ffup%2Fverification%2Findex.html\",\"content\":\"去实名\"}}},\"id\":\"mine_account\",\"position\":0,\"style\":{\"ratio\":[360,45],\"zIndex\":1},\"type\":\"minepage_account\"}],\"showCount\":-1,\"style\":{\"background\":{},\"borderWidth\":\"0\",\"margin\":[\"0\",\"0\",\"10\",\"0\"],\"zIndex\":0},\"type\":\"type_linear\"},{\"items\":[],\"showCount\":-1,\"style\":{\"background\":{},\"borderWidth\":\"0\",\"zIndex\":0},\"type\":\"type_linear\"}],\"id\":\"minepage\",\"itemCount\":17,\"overlap\":true,\"refreshTop\":{\"enable\":true,\"type\":\"default\"},\"style\":{\"background\":{\"color\":\"#F4f4f4\"},\"zIndex\":0}}}";



    @Override
    public JsonObject add(JsonObject body,DynamicCase dynamicCase) {
        JsonObject result = new JsonObject();
        String msg = "触发成功";
        Boolean success = true;
        try{
            JsonObject map = body.get("payload").getAsJsonObject();

            String dataSource = map.get("dataSource").getAsString();
            String createdBy = map.get("createdBy").getAsString();
            String description = map.get("description").getAsString();
            String business = map.get("business").getAsString();
            String templateName = map.get("templateName").getAsString();
            String templateData = map.get("templateData").getAsString();
            String moduleCn = map.get("moduleCn").getAsString();
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("module_cn",moduleCn);
            DynamicPage dynamicPage = dynamicPageMapper.selectOne(queryWrapper);
            if (dynamicPage != null) {
                dynamicCase.setModule(dynamicPage.getModule());
            }

            try {
                JsonObject js = new JsonParser().parse(templateData).getAsJsonObject();
            } catch (Exception e) {
                success = false;
                msg = "请检查填入数据是否为json格式";
                result.addProperty("success", success);
                result.addProperty("msg", msg);
                return result;
            }
            dynamicCase.setDataSource(dataSource);
            dynamicCase.setCreatedAt(new Date());
            dynamicCase.setCreatedBy(createdBy);
            dynamicCase.setDescription(description);
            dynamicCase.setBusiness(business);
            dynamicCase.setTemplateName(templateName);
            dynamicCase.setTemplateData(templateData);

//            dynamicCase.setListPath(".data.groups[0].page.groups[0].items");
//            dynamicCase.setApi("/mbc/goodsgroup");
//            String apiData = "{\"code\":0,\"data\":{\"actionBar\":{\"biz\":{\"shareTitle\":\"来美团 团好货\",\"shareImgUrl\":\"https://p0.meituan.net/travelcube/3e39ea4015a94500f41213684656315c1134.png\",\"titleImgUrl\":\"https://p1.meituan.net/travelcube/ac71477794d09ac11688f47bde64d1925063.png\",\"shareSubTitle\":\"爆款商品低至4折，数量有限，再不抢就亏咯!\",\"shareContentImgUrl\":\"https://p0.meituan.net/travelcube/1aac1659a4d709ddb3ccdfc1871f0c115417.png\"},\"type\":\"simple_title_goodsgroup\"},\"dataType\":\"replace\",\"extra\":{\"globalId\":\"5e4cc11d5b07a4f146710ad45175e631\",\"sessionId\":\"8d2fc42e01783d89cd65f9e71eb064e2\"},\"groups\":[{\"dataType\":\"replace\",\"id\":\"goods_feed_tabs\",\"page\":{\"dataType\":\"replace\",\"extra\":{\"globalId\":\"5e4cc11d5b07a4f146710ad45175e631\",\"sessionId\":\"8d2fc42e01783d89cd65f9e71eb064e2\"},\"groups\":[{\"dataType\":\"replace\",\"id\":\"tab_items_group\",\"items\":[],\"showCount\":-1,\"style\":{\"columnCount\":1,\"zIndex\":0},\"type\":\"type_staggered\"}],\"id\":\"goods_group_tab\",\"itemCount\":16,\"overlap\":false,\"refreshBottom\":{\"enable\":false,\"preloadNum\":0,\"type\":\"default\"},\"refreshTop\":{\"enable\":false,\"type\":\"default\"},\"style\":{\"background\":{\"color\":\"#FFFFFF\"},\"zIndex\":0}},\"showCount\":-1,\"style\":{\"columnCount\":1,\"zIndex\":0},\"tab\":{\"biz\":{\"divider\":{\"color\":\"#FFFFFF\",\"margin\":[\"0dp\",\"10dp\",\"0dp\",\"10dp\"],\"visible\":false,\"width\":\"0dp\"},\"indicator\":{\"endColor\":\"#FFD100\",\"startColor\":\"#FFD100\",\"widthEqualTitle\":false},\"select\":{\"subTitleBold\":false,\"subTitleTextColor\":\"#543214\",\"subTitleTextSize\":\"12.5dp\",\"titleBold\":true,\"titleTextColor\":\"#222222\",\"titleTextSize\":\"17dp\"},\"selectPosition\":0,\"tabMode\":\"fixed\",\"tabs\":[{\"extra\":{\"mge\":{\"ext\":{},\"tab_id\":\"group_Mall_Tab\",\"tab_index\":0}},\"id\":\"group_Mall_Tab\",\"index\":0,\"margin\":[\"0dp\",\"0dp\",\"0dp\",\"15dp\"],\"param\":{\"tab\":\"group_Mall_Tab\",\"sessionId\":\"8d2fc42e01783d89cd65f9e71eb064e2\"},\"scheme\":\"imeituan://www.meituan.com/mbc?pageId=goods_group_tab_inner&path=mbc%2fgoodsgroup&httpMethod=get\",\"subTitle\":\"全国包邮\",\"subTitleStyle\":\"text\",\"title\":\"好货热卖\",\"titleStyle\":\"text\"},{\"extra\":{\"mge\":{\"ext\":{},\"tab_id\":\"group_MeiShi_Tab\",\"tab_index\":1}},\"id\":\"group_MeiShi_Tab\",\"index\":1,\"param\":{\"tab\":\"group_MeiShi_Tab\",\"sessionId\":\"8d2fc42e01783d89cd65f9e71eb064e2\"},\"scheme\":\"imeituan://www.meituan.com/mbc?pageId=goods_group_tab_inner&path=mbc%2fgoodsgroup&httpMethod=get\",\"subTitle\":\"<font color=\\\"#FF6600\\\">¥6.8</font><font color=\\\"#80000000\\\">起</font>\",\"subTitleStyle\":\"html\",\"title\":\"美食\",\"titleStyle\":\"text\"},{\"extra\":{\"mge\":{\"ext\":{},\"tab_id\":\"group_WanLe_Tab\",\"tab_index\":2}},\"id\":\"group_WanLe_Tab\",\"index\":2,\"param\":{\"tab\":\"group_WanLe_Tab\",\"sessionId\":\"8d2fc42e01783d89cd65f9e71eb064e2\"},\"scheme\":\"imeituan://www.meituan.com/mbc?pageId=goods_group_tab_inner&path=mbc%2fgoodsgroup&httpMethod=get\",\"subTitle\":\"<font color=\\\"#FF6600\\\">¥18.8</font><font color=\\\"#80000000\\\">起</font>\",\"subTitleStyle\":\"html\",\"title\":\"玩乐\",\"titleStyle\":\"text\"},{\"extra\":{\"mge\":{\"ext\":{},\"tab_id\":\"group_BianMei_Tab\",\"tab_index\":3}},\"id\":\"group_BianMei_Tab\",\"index\":3,\"margin\":[\"0dp\",\"15dp\",\"0dp\",\"0dp\"],\"param\":{\"tab\":\"group_BianMei_Tab\",\"sessionId\":\"8d2fc42e01783d89cd65f9e71eb064e2\"},\"scheme\":\"imeituan://www.meituan.com/mbc?pageId=goods_group_tab_inner&path=mbc%2fgoodsgroup&httpMethod=get\",\"subTitle\":\"<font color=\\\"#FF6600\\\">¥9.9</font><font color=\\\"#80000000\\\">起</font>\",\"subTitleStyle\":\"html\",\"title\":\"变美\",\"titleStyle\":\"text\"}],\"unselect\":{\"subTitleBold\":false,\"subTitleTextColor\":\"#FF6600\",\"subTitleTextSize\":\"12.5dp\",\"titleBold\":true,\"titleTextColor\":\"#222222\",\"titleTextSize\":\"14.5dp\"},\"visible\":true},\"config\":{\"exposeDelay\":500,\"exposePart\":0.7},\"id\":\"goods_tab\",\"position\":0,\"style\":{\"background\":{\"color\":\"#F8F9FA\",\"gradientColor\":{\"colors\":[\"#F8F9FA\",\"#FFFFFF\"],\"direction\":\"top-bottom\",\"type\":\"linear\"}},\"height\":\"55dp\",\"margin\":[\"0dp\",\"0dp\",\"0dp\",\"0dp\"],\"padding\":[\"5dp\",\"0dp\",\"5dp\",\"0dp\"],\"zIndex\":0},\"type\":\"tab_common2\"},\"type\":\"type_tab\"}],\"id\":\"goods_group\",\"itemCount\":-1,\"metricsInfo\":{\"totalTime\":1},\"overlap\":false,\"refreshBottom\":{\"enable\":false,\"preloadNum\":0,\"type\":\"default\"},\"refreshTop\":{\"enable\":true,\"type\":\"default\"},\"statusBar\":{\"darkMode\":true,\"type\":\"default\"},\"style\":{\"background\":{\"color\":\"#F8F9FA\"},\"zIndex\":0}}}";

//            dynamicCase.setListPath(listPath);
//            dynamicCase.setApi(api);
//            dynamicCase.setApiData(apiData);


            if (null != map.get("mockRule")) {
                JsonObject mockRuleMap = map.get("mockRule").getAsJsonObject();
                JsonObject jsonObject = new JsonObject();
                jsonObject.add("long", mockRuleMap.get("long"));
                jsonObject.add("blank", mockRuleMap.get("blank"));
                jsonObject.add("richText", mockRuleMap.get("richText"));
                jsonObject.add("invalidUrl", mockRuleMap.get("invalidUrl"));
                dynamicCase.setMockRule(jsonObject.toString());
            }

            dynamicCaseMapper.insert(dynamicCase);
        }catch (Exception e){
            success = false;
        }
        finally {
            result.addProperty("success", success);
            result.addProperty("msg", msg);
            return result;
        }

    }

    @Override
    public IPage<DynamicCase> caseList(QueryRequest request, DynamicCase dynamicCase){
        {
            try {

                Page<DynamicCase> result = new Page<>();
                List<DynamicCase> list = new ArrayList<>();
                Map<String, DynamicCase> map = new HashMap<>();

                MbcUtil mbcUtil = new MbcUtil();
                ArrayList arrayList = mbcUtil.getAllTemplate();
                List templateList = (List) arrayList.get(0);
                List businessList = (List) arrayList.get(1);
                String templateLast="";
                String businessLast="";
                for (int i = 0; i < templateList.size(); i++) {
                    String template = (String)templateList.get(i);
                    String business = (String)businessList.get(i);
                    if(template.equals(templateLast) && business.equals(businessLast)){
                        continue;
                    }
                    templateLast = template;
                    businessLast = business;

                    DynamicCase c = new DynamicCase();
                    c.setBusiness(business);
                    c.setTemplateName(template);
                    list.add(c);
                }
                result.setRecords(list);

                return result;

            } catch (Exception e) {
                log.error("获取列表失败", e);
                return null;
            }
        }
    }

    @Override
    public IPage<DynamicCase> childList(String templateName) {
        QueryRequest request = new QueryRequest();
        LambdaQueryWrapper<DynamicCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicCase::getTemplateName,templateName);
        queryWrapper.last("order by modifty_at DESC");
        Page<DynamicCase> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<String> pageNameList() {
        List<String> result = new ArrayList<>();
        QueryWrapper queryWrapper = new QueryWrapper();
        List<DynamicPage> list = dynamicPageMapper.selectList(queryWrapper);
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                String chineseName = list.get(i).getModuleCn();
                if (chineseName != null) {
                    result.add(chineseName);
                }
            }
        }
        return result;
    }


}
