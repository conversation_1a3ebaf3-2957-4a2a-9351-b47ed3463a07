package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicSdkReport;

public interface DynamicSdkReportService extends IService<DynamicSdkReport> {

    Integer add(Integer jenkinsId, String platform);

    IPage<DynamicSdkReport> list(QueryRequest request);

}
