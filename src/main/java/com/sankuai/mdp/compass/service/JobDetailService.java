package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.AbnormalUIList;
import com.sankuai.mdp.compass.entity.AbnormalUi;
import com.sankuai.mdp.compass.entity.JobDetail;
import com.sankuai.mdp.compass.entity.LayoutMgeDetail;

import java.util.List;

/**
 * Created by xieyongrui on 2019/11/17.
 */
public interface JobDetailService {
    IPage<JobDetail> list(QueryRequest queryRequest, JobDetail jobDetail);
    IPage<JobDetail> list(QueryRequest queryRequest, JobDetail jobDetail, String type);
    IPage<AbnormalUi> abnormalList(QueryRequest queryRequest, JobDetail jobDetail, String type);
    //列表版
    IPage<AbnormalUIList> abnormalListv2(QueryRequest queryRequest, JobDetail jobDetail, String type);
    void add(JobDetail jobDetail);
    List<JobDetail> selectAllByJobId(Integer jobId);
    Boolean reportBug(JsonObject body);
    IPage<LayoutMgeDetail> getMgeList(String jobId);
}
