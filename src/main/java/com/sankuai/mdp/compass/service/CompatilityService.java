package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import jnr.ffi.annotations.In;
import org.json.*;

import java.io.IOException;
import java.util.HashMap;

/**
 * Created by xieyongrui on 2019/11/9.
 */
public interface CompatilityService extends IService<CompatilityJob> {
    CompatilityJob find(Integer id);

    IPage<CompatilityJob> list(QueryRequest request, CompatilityJob compatilityJob);

    IPage<CompatilityJob> aggList(QueryRequest request, CompatilityJob compatilityJob);

    IPage<CompatilityJob> listAggregation(QueryRequest request, CompatilityJob compatilityJob);

    IPage<CompatilityJob> listAggregationSecond(QueryRequest request, CompatilityJob compatilityJob);

    JsonObject createV2(CompatilityJob dynamicJob);

    JSONObject createSmokeJob(CompatilityJob compatilityJob);

    void updateCheckStatus(CompatilityJob compatilityJob);

    void updateJobId(CompatilityJob compatilityJob);

    void updateReport(CompatilityJob compatilityJob);

    void updateEventConanId(CompatilityJob compatilityJob);

    void updateStatusById(CompatilityJob compatilityJob);

    void updateStatusByJenkinsId(CompatilityJob compatilityJob);

    void updateFinishTime(CompatilityJob compatilityJob);

    void updateEventFinishTime(CompatilityJob compatilityJob);

    void updateCheckTime(CompatilityJob compatilityJob);

    void updateAutoResult(int jobId, String result);

    String getEventId(Integer jobId, String templateName);

    CompatilityJob getJobInfo(CompatilityJob compatilityJob);

    int changeFailedReason(int id, String failedReason);

    IPage<CompatilityJob> listProcess(String processId, String currentPage, Integer testType);

    Resp cancel(Integer jobId);

    HashMap weekReport();
}
