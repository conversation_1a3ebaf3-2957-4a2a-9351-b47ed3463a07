package com.sankuai.mdp.compass.service;

import com.google.gson.JsonArray;
import com.sankuai.mdp.compass.common.enums.Resp;
import org.apache.thrift.TException;

import java.io.IOException;

/**
 * Created by xieyongrui on 2020/12/2.
 */
public interface OcrService {
    JsonArray getTextFromAI(String PicUrl);

    JsonArray getTextAndLocationFromAI(String PicUrl);

    String textShowJudement(String mockId, String id, String template, int jobId);

    String recognizeUIBug(String PicUrl);

    String recognizeUIBugByAI(String PicUrl);

    String blurDetect(String picUrl);

    String blurDetectByAI(String picUrl, int imageType, String type, float lineThreshold) throws TException;

    String diff(String basePic, String testPic, String compareModel, String cutModel, String lineConfidence,
            String ignoreArea) throws TException;

    String recognizeUiPageParse(String picUrl);

    String[] recognizeTemplateUIBug(String mockId, String id, String template, int jobId);

    String[] recognizeUIBugByOnePic(String picUrl);

    String[] recognizeP0UIBugByOnePic(String picUrl);

    Resp getTextCenterPosByOcr(String text, String picUrl);

    String UiPageParseRequest(String PicUrl);

    Resp getMatchPicLocation(String baseUrl, String targetUrl) throws IOException, InterruptedException;
}
