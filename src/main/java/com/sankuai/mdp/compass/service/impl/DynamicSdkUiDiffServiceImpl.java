package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dianping.zebra.util.StringUtils;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.entity.DynamicSdkBasicPic;
import com.sankuai.mdp.compass.entity.DynamicSdkReport;
import com.sankuai.mdp.compass.entity.DynamicSdkUiDiff;
import com.sankuai.mdp.compass.mapper.DynamicSdkBasicPicMapper;
import com.sankuai.mdp.compass.mapper.DynamicSdkReportMapper;
import com.sankuai.mdp.compass.mapper.DynamicSdkUiDiffMapper;
import com.sankuai.mdp.compass.service.DynamicSdkBasicPicService;
import com.sankuai.mdp.compass.service.DynamicSdkUiDiffService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class DynamicSdkUiDiffServiceImpl extends ServiceImpl<DynamicSdkUiDiffMapper, DynamicSdkUiDiff> implements DynamicSdkUiDiffService {

    @Autowired
    DynamicTemplateServiceImpl dynamicTemplateService;

    @Autowired
    DynamicSdkBasicPicService dynamicSdkBasicPicService;

    @Autowired
    DynamicSdkReportServiceImpl dynamicSdkReport;

    @Autowired
    DynamicSdkBasicPicMapper dynamicSdkBasicPicMapper;

    @Autowired
    DynamicSdkReportMapper dynamicSdkReportMapper;

    @Autowired
    DynamicSdkUiDiffMapper dynamicSdkUiDiffMapper;

    @Autowired
    private DynamicSdkUiDiffService dynamicSdkPicService;

    private Boolean result = true;
    private static Gson GSON = new Gson();

    //    private static String UIDIFF = "/Users/<USER>/FLexbox_auto_test/platform_compass/diff/ssim.py";
    private static String UIDIFF = "/opt/meituan/script/diff/ssim.py";


    @Override
    public IPage<DynamicSdkUiDiff> list(QueryRequest request, Integer reportId) {
        try {
            LambdaQueryWrapper<DynamicSdkUiDiff> queryWrapper = new LambdaQueryWrapper<>();

            queryWrapper.eq(DynamicSdkUiDiff::getReportId, reportId);

            queryWrapper.last("order by id desc");

            Page<DynamicSdkUiDiff> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }

    }

    @Override
    public IPage<DynamicSdkUiDiff> errorList(QueryRequest request, Integer reportId) {
        try {
            LambdaQueryWrapper<DynamicSdkUiDiff> queryWrapper = new LambdaQueryWrapper<>();

            queryWrapper.eq(DynamicSdkUiDiff::getReportId, reportId);

//            queryWrapper.ne(DynamicSdkUiDiff::getSimilarity, "1");
            queryWrapper.lt(DynamicSdkUiDiff::getSimilarity, "0.997");


            if (request.getSortOrder()==null){
                queryWrapper.last("order by id desc");//以id降序排序
            }else {
                if (request.getSortOrder().equals("descend")) {
                    queryWrapper.last("order by similarity desc");
                }else {
                    queryWrapper.last("order by similarity asc");
                }
            }
            Page<DynamicSdkUiDiff> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }

    }

    @Override
    public String startPicUrlDiff(Integer jenkinsId, String platform, String testPicMapStr, boolean isUpdate) {
//        JsonParser jsonParser = new JsonParser();
        //插入一条job记录
        Integer report_id = dynamicSdkReport.add(jenkinsId, platform);
        log.info("isUpdate parameter is: " + isUpdate);
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.execute(() -> {
            Map<String, Object> testPicMap = new HashMap<>();
            testPicMap = GSON.fromJson(testPicMapStr, testPicMap.getClass());
            //        Map<String, String> testPicMap = (Map<String, String>) jsonParser.parse(testPicMapStr);

            for (Map.Entry entry : testPicMap.entrySet()) {
                String xmlName = platform + "_" + entry.getKey().toString();
                String testPic = entry.getValue().toString();
                if (null != testPic) {
//              查找是否存在基准图
                    DynamicSdkBasicPic dynamicBasicPicByXmlName = dynamicSdkBasicPicService.find(xmlName);
                    if (null == dynamicBasicPicByXmlName) {
                        DynamicSdkBasicPic dynamicSdkBasicPicEntity = new DynamicSdkBasicPic();
                        dynamicSdkBasicPicEntity.setBasePic(testPic);
                        dynamicSdkBasicPicEntity.setXmlName(xmlName);
                        dynamicSdkBasicPicMapper.insert(dynamicSdkBasicPicEntity);
                    } else if(isUpdate){
                        DynamicSdkBasicPic dynamicSdkBasicPicEntity = new DynamicSdkBasicPic();
                        dynamicSdkBasicPicEntity.setBasePic(testPic);
                        UpdateWrapper updateWrapper = new UpdateWrapper();
                        updateWrapper.eq("xml_name", xmlName);
                        dynamicSdkBasicPicMapper.update(dynamicSdkBasicPicEntity, updateWrapper);
                    }
                    else{
                        // 图像对比，并插入一条记录
                        String basePic = dynamicBasicPicByXmlName.getBasePic();
                        setDiffData(basePic, testPic, xmlName, report_id, platform);
                    }
                }
            }
//        更新测试结果
            Integer selectCount = dynamicSdkUiDiffMapper.selectCount(new QueryWrapper<DynamicSdkUiDiff>().eq("report_id", report_id).ne("Similarity", 1));
            if (selectCount > 0) {
                DynamicSdkReport dynamicSdkReportEntity = new DynamicSdkReport();
                dynamicSdkReportEntity.setId(report_id);
                dynamicSdkReportEntity.setStatus("fail");
                dynamicSdkReportMapper.updateById(dynamicSdkReportEntity);
                //发送大象通知
                sendDxfinishMessage(platform);
            } else {
                DynamicSdkReport dynamicSdkReportEntity = new DynamicSdkReport();
                dynamicSdkReportEntity.setId(report_id);
                dynamicSdkReportEntity.setStatus("pass");
                dynamicSdkReportMapper.updateById(dynamicSdkReportEntity);
                //发送大象通知
                sendDxfinishMessage(platform);
            }
            try {
                URIBuilder uriBuilder = new URIBuilder("http://172.18.76.220:8080/job/killSlimeDebug/build");
                uriBuilder.addParameter("token", "qatest");
                URI uri = uriBuilder.build();
                log.info("kill slime debug request: " + uri.toString());
                HttpClient client = new HttpClient();
                GetMethod getMethod = new GetMethod(uri.toString());
                client.executeMethod(getMethod);
            }catch (Exception e){
                log.error("end slime debug fail");
            }
        });

        JsonObject result = new JsonObject();
        result.addProperty("status", "success");
        result.addProperty("report_id", report_id);
        return result.toString();
    }


    @Override
    public String startLocalPicDiff(Integer jenkinsId, String platform) {
        //      插入一条job记录
        Integer report_id = dynamicSdkReport.add(jenkinsId, platform);
        JSONObject result = new JSONObject();

        ArrayList<String> fileList = new ArrayList<>();
        String picPath = ComConstant.UI_DIFF_TEST_DIR;
//      获取1个文件夹下的所有文件名称
        FileUtil.getFileList(picPath, "", fileList);
        for (int i = 0; i < fileList.size(); i++) {
            String basePic = "";
            String fileName = fileList.get(i);

            String xmlName = StringUtils.substringBefore(fileName, ".png");
            String testPic = dynamicTemplateService.uploadPicture(picPath + fileName);

            if (null != testPic) {
                DynamicSdkBasicPic dynamicBasicPicByXmlName = dynamicSdkBasicPicService.find(xmlName);
                if (null == dynamicBasicPicByXmlName) {

                    DynamicSdkBasicPic dynamicSdkBasicPicEntity = new DynamicSdkBasicPic();
                    dynamicSdkBasicPicEntity.setBasePic(testPic);
                    dynamicSdkBasicPicEntity.setXmlName(xmlName);
                    dynamicSdkBasicPicMapper.insert(dynamicSdkBasicPicEntity);

                } else {
                    basePic = dynamicBasicPicByXmlName.getBasePic();
//                    图像对比，并插入一条记录
                    setDiffData(basePic, testPic, xmlName, report_id, platform);
                }
            }
        }
        result.put("status", "success");
        return result.toString();
    }


    public void setDiffData(String basePic, String testPic, String xmlName, Integer report_id, String platform) {

        String projectPath = System.getProperty("user.dir");
        String resultPicPath = projectPath + "/" + xmlName + "_diff.png";

        DynamicSdkUiDiff uiDiffEntiry = new DynamicSdkUiDiff();
        uiDiffEntiry.setXmlName(xmlName);
        uiDiffEntiry.setPlatform(platform);
        uiDiffEntiry.setReportId(report_id);

        try {
            if ("" != basePic && "" != testPic) {
                uiDiffEntiry.setBasePic(basePic);
                uiDiffEntiry.setTestPic(testPic);

                String score = ComUtil.doPython(UIDIFF, testPic + " " + basePic + " " + resultPicPath);
                System.out.print("score:" + score);
                String resultPic = dynamicTemplateService.uploadPicture(resultPicPath);
                uiDiffEntiry.setSimilarity(Double.parseDouble(score));
                uiDiffEntiry.setDiffUrl(resultPic);

                FileUtil.delete(resultPicPath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        dynamicSdkUiDiffMapper.insert(uiDiffEntiry);

    }

    @Override
    public String createBug(JSONArray buglist) {
        String createOnesIssues = "https://ones.sankuai.com/api/1.0/ones/projects/5700/issue";
        OnesUtil onesUtil = new OnesUtil();
        List<DynamicSdkUiDiff> dynamicSdkUiDiffs = new ArrayList<>();
        for (int i = 0; i < buglist.size(); i++) {
            int id = Integer.parseInt(buglist.get(i).toString());
            QueryWrapper<DynamicSdkUiDiff> dynamicSdkUiDiffWrapper= new QueryWrapper<>();
            dynamicSdkUiDiffWrapper.eq("id",id);
            DynamicSdkUiDiff dynamicSdkUiDiff = dynamicSdkUiDiffMapper.selectOne(dynamicSdkUiDiffWrapper);
            dynamicSdkUiDiffs.add(dynamicSdkUiDiff);
        }
        String title = dynamicSdkUiDiffs.get(0).getPlatform()+"动态布局SDK模板第"+dynamicSdkUiDiffs.get(0).getReportId()+"次UI对比问题汇总";
        StringBuilder detail = new StringBuilder();
        detail.append("<h3>本次报告地址：http://qaassist.sankuai.com/compass/dashboard#/sdkautotest/ErrorReportDetail?reportId="+dynamicSdkUiDiffs.get(0).getReportId()+"</h3>");
        detail.append("<hr/>");
        for (int i = 0; i < dynamicSdkUiDiffs.size(); i++) {
            detail.append("<h5>【问题】"+(i+1)+"</h5>");
            detail.append("<h5>【模板名称】："+dynamicSdkUiDiffs.get(i).getXmlName()+"</h5>");
            detail.append("<h5>【模板URL】："+dynamicSDKTemplateURL(dynamicSdkUiDiffs.get(i).getXmlName())+"</h5>");
            detail.append("<h5>【问题id】："+dynamicSdkUiDiffs.get(i).getId()+"</h5>");
            detail.append("<h5>【对比图如下】：</h5>");
            detail.append("<b>基准图：</b><br/>");
            detail.append("<img width=\"200\" src="+dynamicSdkUiDiffs.get(i).getBasePic()+">");
            detail.append("<br/><b>测试图：</b><br/>");
            detail.append("<img width=\"200\" src="+dynamicSdkUiDiffs.get(i).getTestPic()+">");
            detail.append("<br/><b>对比图：</b><br/>");
            detail.append("<img width=\"200\" src="+dynamicSdkUiDiffs.get(i).getDiffUrl()+">");
            detail.append("<hr/>");
        }
        JsonArray cc = new JsonArray();
        cc.add("dongheng");
        cc.add("yangjing81");
        cc.add("huohekai");
        JsonArray label = new JsonArray();
        label.add(7533);
        StringBuilder assigned = new StringBuilder();
        if ("Android".equals(dynamicSdkUiDiffs.get(0).getPlatform())){
            assigned.append("zhangchao84");
//            assigned.append("dongheng");
        }else if ("iOS".equals(dynamicSdkUiDiffs.get(0).getPlatform())){
            assigned.append("oubaiquan");
//            assigned.append("dongheng");
        }
        return onesUtil.createBug(detail.toString(),cc,title,assigned.toString(),5700,13786,label,createOnesIssues,"");
    }

    //返回动态布局SDK模板地址
    private String dynamicSDKTemplateURL(String xmlName){
        String mbcAddress="http://mbc.sankuai.com/home/<USER>/template-business?instVersionId=0&bizID=0&moduleID=0&status=0&name=";
        String evaAddress="http://appupdate.sankuai.com/Android/group/layout?businessId=&moduleId=&styleType=&platform=&styleTags=&styleStatus=&query=";
        if (xmlName.startsWith("iOS_mbc")){
            return mbcAddress+xmlName.substring(8);
        }else if (xmlName.startsWith("Android_mbc")){
            return mbcAddress+xmlName.substring(12);
        }else if (xmlName.startsWith("iOS_eva")){
            return evaAddress+xmlName.substring(8);
        }else if (xmlName.startsWith("Android_eva")){
            return evaAddress+xmlName.substring(12);
        }else {
            return "";
        }
    }

    private void sendDxfinishMessage(String platform){
        //发送大象通知
        try {
            DxUtil dxUtil = new DxUtil();
            String finishMessage=platform+"端，动态布局SDK UI自动化图像对比完成，请及时查看结果";
            String[] receiver = {"wanghaojie07"};
            dxUtil.sendToPersionByCompass(finishMessage,receiver);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
