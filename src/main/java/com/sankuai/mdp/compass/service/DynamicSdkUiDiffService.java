package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicSdkUiDiff;
import net.sf.json.JSONArray;

public interface DynamicSdkUiDiffService extends IService<DynamicSdkUiDiff> {

    IPage<DynamicSdkUiDiff> list(QueryRequest request, Integer reportId);

    IPage<DynamicSdkUiDiff> errorList(QueryRequest request, Integer reportId);

    String startLocalPicDiff(Integer jenkinsId, String platform);

    String startPicUrlDiff(Integer jenkinsId, String platform, String testPicMapStr, boolean isUpdate);

    String createBug(JSONArray buglist);
}


