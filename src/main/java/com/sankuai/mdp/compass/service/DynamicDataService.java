package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dianping.lion.client.util.Json;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicData;
import org.json.*;

/**
 * Created by xieyongrui on 2019/11/21.
 */
public interface DynamicDataService extends IService<DynamicData> {
    DynamicData find(String template);
    IPage<DynamicData> list(QueryRequest request,DynamicData dynamicData);
    Boolean add(JsonObject body);
    JsonObject mockSmoke(CompatilityJob compatilityJob, Integer type, String scenes);
    JsonObject mockByTemplateData(CompatilityJob compatilityJob, String type, JsonObject jsonObject,String scenes);
    JsonArray mockByCase(String templateData, String listPath, String apiData, String apiPath, JSONObject rule, Integer type);
    int updateMbcProperty(int id,JsonObject apiData, String mode);
    String groupingSmokeCase(String body);
    JSONObject getSmokeCase(String xml, String prefix);
}
