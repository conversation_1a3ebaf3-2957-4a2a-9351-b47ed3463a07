package com.sankuai.mdp.compass.service;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by xieyongrui on 2019/11/16.
 */
public interface CustomService {

    Set<String> getModule(String address);

    List<String> getTemplate(String module,String address);

    String getBusinessEnNameByCnName(String name,String address);

    Map<String,String> getBusinessInfo(String address);

    String getMockData(Integer mockId);

    JsonArray getJsonKey(JsonObject jsonObject);

    JsonObject getOriginalKey(String templateName);

    Boolean submitSql(JsonObject jsonObject);

    JsonObject getMockRule(JsonObject body);

    JsonObject getLingLongData(Integer moduleId,String platform,String version);

    String getCategoryData(int platform,String version);

    String getCategoryList(int platform, String version, String cities);

}
