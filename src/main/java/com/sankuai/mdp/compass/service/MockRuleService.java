package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.entity.MockRule;

/**
 * Created by xieyongrui on 2019/11/29.
 */
public interface MockRuleService extends IService<MockRule> {
    void setMockRule(MockRule mockRule);
    void setMockRule(Integer mockId, String key, String value, String api);
    MockRule getMockRule(Integer mockId);
}
