package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicCase;
import com.sankuai.mdp.compass.entity.DynamicData;
import com.sankuai.mdp.compass.entity.DynamicPage;

import java.util.List;

/**
 * Created by sunkangtong on 2021/3/9.
 */
public interface DynamicCaseService extends IService<DynamicCase> {

    JsonObject add(JsonObject body,DynamicCase dynamicCase);

    IPage<DynamicCase> caseList(QueryRequest request,DynamicCase dynamicCase);

    IPage<DynamicCase> childList(String processId);

    List<String> pageNameList();
}
