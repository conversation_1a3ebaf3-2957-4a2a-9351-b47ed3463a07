package com.sankuai.mdp.compass.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Profession;
import com.sankuai.mdp.compass.common.utils.ConanUtil;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.FileUtil;
import com.sankuai.mdp.compass.common.utils.MbcUtil;
import com.sankuai.mdp.compass.entity.ChangeComplianceTemplate;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicProcess;
import com.sankuai.mdp.compass.entity.DynamicTemplatePublishDetail;
import com.sankuai.mdp.compass.mapper.ChangeComplianceTemplateMapper;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import com.sankuai.mdp.compass.mapper.DynamicProcessMapper;
import com.sankuai.mdp.compass.mapper.DynamicTemplatePublishDetailMapper;
import com.sankuai.mdp.compass.service.CompatilityService;
import com.sankuai.mdp.compass.service.DynamicProcessService;
import com.sankuai.security.sdk.SecSdk;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;


/**
 * Created by sunkangtong on 2021/3/9.
 */

@Slf4j
@Service
public class DynamicProcessServiceImpl extends ServiceImpl<DynamicProcessMapper, DynamicProcess> implements DynamicProcessService {
    @Autowired
    DynamicProcessMapper dynamicProcessMapper;

    @Autowired
    CompatilityMapper compatilityMapper;

    @Autowired
    DynamicTemplatePublishDetailMapper dynamicTemplatePublishDetailMapper;

    @Autowired
    ChangeComplianceTemplateMapper changeComplianceTemplateMapper;

    @Autowired
    CompatilityService compatilityService;

    JsonParser jsonParser = new JsonParser();

    public static final int buttonApper = 0;
    public static final int buttonDisapper = 1;

    public static final int smokePage = 0;
    public static final int UIPage = 1;
    public static final int compatilityPage = 2;
    public static final int confirmPage = 3;

    public static final int 运行中 = 0;
    public static final int 已完成 = 1;
    public static final int 已打回 = -1;
    public static final int 已跳过 = 2;

    public static final String success = "success";
    public static final String process = "process";
    public static final String waiting = "waiting";
    public static final String stop = "stop";
    public static final String abort = "abort";
    public static final String finetune = "finetune";
    public static final String skip = "skip";

    public static final String adminUser = "liujiao11";

    String wordUrl = "https://km.sankuai.com/page/634769563";


    @Override
    public boolean updateProcess(CompatilityJob compatilityJob) {
        try {
            int jobId = compatilityJob.getId();
            String jobidTeam = "";
            String createdBy = compatilityJob.getCreatedBy();
            String templateName = compatilityJob.getTemplateName();
            String business = compatilityJob.getBusiness();
            String scenes = compatilityJob.getScenes();
            String curMockData = compatilityJob.getMockData();
            int testType = compatilityJob.getType();

            LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DynamicProcess::getTemplateName, templateName);
            queryWrapper.eq(DynamicProcess::getProcessCondition, 运行中);

            DynamicProcess dynamicProcess;
            dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
            //如果list为0，代表目前该模板没有正在流程中的了，
            if (null != dynamicProcess) {
                String jobidTeamRes = dynamicProcess.getJobidTeam();
                jobidTeam = jobidTeamRes + "_" + jobId;
                dynamicProcess.setJobidTeam(jobidTeam);
                //重置流水线状态代码，初版设计，于2021年11月24日注释
//                dynamicProcess.setThirdStatus(waiting);
//                dynamicProcess.setReplenishUiStatus(waiting);
//                dynamicProcess.setUiAcceptStatus(waiting);
//                dynamicProcess.setSmokeStatus(process);
                dynamicProcessMapper.updateById(dynamicProcess);
                try {
                    //1、判断其他job是不是未完成，如果是，直接取消掉
                    ConanUtil conanUtil = new ConanUtil();
                    String[] jobIds = jobidTeamRes.split("_");
                    for (String jobIdTemp : jobIds) {
                        LambdaQueryWrapper<CompatilityJob> queryWrapper1 = new LambdaQueryWrapper<>();
                        queryWrapper1.eq(CompatilityJob::getId, Integer.valueOf(jobIdTemp));
                        compatilityJob = compatilityMapper.selectOne(queryWrapper1);
                        int status = compatilityJob.getStatus();
                        String data = compatilityJob.getMockData();
                        int type = compatilityJob.getType();
                        // 如果是测试中0或者排队中-1，且测试类型与新任务一致，则取消原任务的测试
                        if (status < 1 && testType == type) {
                            if (curMockData != null && curMockData.equals(data)) {
                                // 2、把取消的job status置为已取消
                                compatilityJob.setStatus(2);
                                compatilityMapper.updateById(compatilityJob);
                                String[] uiReports = compatilityJob.getReport().split("_");
                                String[] eventReports = compatilityJob.getEventConanId().split("_");
                                if (null != uiReports){
                                    for (String s : uiReports) {
                                        conanUtil.cancelJob(s);
                                    }
                                }
                                if (null != eventReports){
                                    for (String s : eventReports) {
                                        conanUtil.cancelJob(s);
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("updateProcess error1>> " + e.getMessage());
                }
            } else {
                DynamicProcess dynamicProcessNew = new DynamicProcess();
                dynamicProcessNew.setTemplateName(templateName);
                dynamicProcessNew.setBusiness(business);
                dynamicProcessNew.setJobidTeam(jobidTeam + jobId);
                dynamicProcessNew.setCreatedBy(createdBy);
                dynamicProcessNew.setCreatedAt(new Date());
                if (scenes.equals("smoke") || scenes.equals("regression")) {
                    dynamicProcessNew.setSmokeStatus(process);
                } else {
                    dynamicProcessNew.setReplenishUiStatus(process);
                }
                dynamicProcessMapper.insert(dynamicProcessNew);
            }
            return true;
        } catch (Exception e) {
            log.error("updateProcess error2>> " + e.getMessage());
            return false;
        }
    }

    @Override
    public IPage<DynamicProcess> listAggregationSecond(QueryRequest request, DynamicProcess dynamicProcess) {
        try {
            LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(DynamicProcess::getTemplateName);
            String template_name = dynamicProcess.getTemplateName();
            queryWrapper.eq(DynamicProcess::getTemplateName, template_name);
            queryWrapper.last("order by id DESC");
            Page<DynamicProcess> result = new Page<>();
            List list1 = dynamicProcessMapper.selectList(queryWrapper);
            result.setRecords(list1);
            return result;
        } catch (Exception e) {
            log.error("listAggregationSecond获取列表失败" + e, e);
            return null;
        }
    }

    @Override
    public ArrayList getStep(String processId) {
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getId, processId);
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        if (dynamicProcess != null) {
            String smoke_status = dynamicProcess.getSmokeStatus();
            String replenish_ui_status = dynamicProcess.getReplenishUiStatus();
            String thirdStatus = dynamicProcess.getThirdStatus();
            String ui_accept_status = dynamicProcess.getUiAcceptStatus();

            Date smoke_handle = dynamicProcess.getSmokeTime();
            Date ui_handle = dynamicProcess.getUiTime();
            Date replenish_handle = dynamicProcess.getReplenishTime();
            Date last_handle = dynamicProcess.getLastTime();

            String op = "操作人：";
            String smokeOperator = dynamicProcess.getSmokeUser() != null ? op + dynamicProcess.getSmokeUser() : "";
            String uiOperator = dynamicProcess.getUiUser() != null ? op + dynamicProcess.getUiUser() : "";
            String thirdOperator = dynamicProcess.getReplenishUser() != null ? op + dynamicProcess.getReplenishUser() : "";
            String lastOperator = dynamicProcess.getLastUser() != null ? op + dynamicProcess.getLastUser() : "";


            ArrayList stepList = new ArrayList();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            Map smokeMap = new HashMap();
            smokeMap.put("name", "冒烟测试");
            smokeMap.put("status", smoke_status);
            smokeMap.put("description", Eng2Chi(smoke_status));
//        smokeMap.put("time",sdf.format(new Date()));
            smokeMap.put("op", smokeOperator);
            if (null != smoke_handle) {
                smokeMap.put("time", sdf.format(smoke_handle));
            } else {
                smokeMap.put("time", "");
            }
            stepList.add(smokeMap);

            Map uiMap = new HashMap();
            uiMap.put("name", "UI验收");
            uiMap.put("status", ui_accept_status);
            uiMap.put("description", Eng2Chi(ui_accept_status));
            uiMap.put("op", uiOperator);
            if (null != ui_handle) {
                uiMap.put("time", sdf.format(ui_handle));
            } else {
                uiMap.put("time", "");
            }
            stepList.add(uiMap);

            Map compatilityMap = new HashMap();
            compatilityMap.put("name", "PM验收");
            compatilityMap.put("status", replenish_ui_status);
            compatilityMap.put("description", Eng2Chi(replenish_ui_status));
            compatilityMap.put("op", thirdOperator);
            if (null != replenish_handle) {
                compatilityMap.put("time", sdf.format(replenish_handle));
            } else {
                compatilityMap.put("time", "");

            }
            stepList.add(compatilityMap);

            Map confirmMap = new HashMap();
            confirmMap.put("name", "确认阶段");
            confirmMap.put("status", thirdStatus);
            confirmMap.put("description", Eng2Chi(thirdStatus));
            confirmMap.put("op", lastOperator);
            if (null != last_handle) {
                confirmMap.put("time", sdf.format(last_handle));
            } else {
                confirmMap.put("time", "");
            }
            stepList.add(confirmMap);

            return stepList;
        }
        return null;
    }

    @Override
    public ArrayList updateStep(String processId, int current, String clickStatus, String onesUrl, String confirmUser) {
        ArrayList result = new ArrayList();
        Map resultMap = new HashMap();
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getId, processId);
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        String templateName = dynamicProcess.getTemplateName();
        DxUtil dxUtil = new DxUtil();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean skipFlag = false;
        if (clickStatus.equals("skip")) {
            clickStatus = "success";
            skipFlag = true;
        }
        if (!onesUrl.equals("")) {
            dynamicProcess.setOnesUrl(onesUrl);
        }
        // 2024-04-25改成新域名
        String url = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/dlautotest/job/jobDetail?processId=" + processId;

        if (onesUrl != null && onesUrl != "") {
            onesUrl = "[查看需求|" + onesUrl + "]";
        }
        if (current == smokePage) {
            JsonArray rdList = MbcUtil.getPermission(templateName, Profession.rdList);
            JsonArray dataList = MbcUtil.getPermission(templateName, Profession.dataList);
            //鉴权失败，这是为啥是与，应该改成或？
            if (!rdList.contains(new JsonParser().parse(confirmUser)) && !dataList.contains(new JsonParser().parse(confirmUser))) {
                resultMap.put("result", "false");
                resultMap.put("message", "无权限，此环节仅开发者有权限");
                result.add(resultMap);
                return result;
            }
            dynamicProcess.setSmokeStatus(clickStatus);
            JsonArray uiList = new MbcUtil().getPermission(templateName, Profession.uiList);
            JsonArray pmList = new MbcUtil().getPermission(templateName, Profession.pmList);

            if (skipFlag) {
                dynamicProcess.setUiAcceptStatus(skip);
                dynamicProcess.setReplenishUiStatus(process);
                for (JsonElement p : pmList) {
                    String user = p.getAsString();
                    dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                            "【模版名称】：" + templateName + "\n" +
                            "【需求链接】：" + onesUrl + "\n" +
                            "【提测类型】：首次提测\n" +
                            "【当前进度】：冒烟通过，待PM验收 \n" +
                            "【验收入口】：[查看报告|" + url + "]\n" +
                            "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                            "如有问题请联系" + adminUser, user);
                }

            } else {
                dynamicProcess.setUiAcceptStatus(process);
                for (JsonElement p : uiList) {
                    String user = p.getAsString();
                    try {
                        dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                                "【模版名称】：" + templateName + "\n" +
                                "【需求链接】：" + onesUrl + "\n" +
                                "【提测类型】：首次提测\n" +
                                "【当前进度】：冒烟通过，待UI验收 \n" +
                                "【验收入口】：[查看报告|" + url + "]\n" +
                                "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                                "如有问题请联系" + adminUser, user);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            dynamicProcess.setSmokeTime(new Date());
            dynamicProcess.setSmokeUser(confirmUser);
        } else if (current == UIPage) {
            JsonArray uiList = MbcUtil.getPermission(templateName, Profession.uiList);
            //鉴权失败
            if (!uiList.contains(new JsonParser().parse(confirmUser))) {
                resultMap.put("result", "false");
                resultMap.put("message", "无权限，请在MBC平台申请改模板权限");
                result.add(resultMap);
                return result;
            }

            dynamicProcess.setUiAcceptStatus(clickStatus);
            dynamicProcess.setUiTime(new Date());
            dynamicProcess.setUiUser(confirmUser);
            if (clickStatus.equals(success)) {
                dynamicProcess.setReplenishUiStatus(process);
                //这里是已经点击了验收通过，应该通知PM确认测试结果
                JsonArray qaList = new MbcUtil().getPermission(templateName, Profession.qaList);
                for (JsonElement p : qaList) {
                    String user = p.getAsString();
                    try {
                        dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                                "【模版名称】：" + templateName + "\n" +
                                "【需求链接】：" + onesUrl + "\n" +
                                "【提测类型】：首次提测\n" +
                                "【当前进度】：验收通过，待PM验收结果 \n" +
                                "【确认入口】：[查看报告|" + url + "]\n" +
                                "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                                "如有问题请联系" + adminUser, user);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                if (clickStatus.equals(finetune)) {
                    JsonArray rdList = new MbcUtil().getPermission(templateName, Profession.rdList);
                    for (JsonElement p : rdList) {
                        String user = p.getAsString();
                        try {
                            dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                                    "【模版名称】：" + templateName + "\n" +
                                    "【需求链接】：" + onesUrl + "\n" +
                                    "【提测类型】：首次提测\n" +
                                    "【当前进度】：验收不通过，待调整UI \n" +
                                    "【确认入口】：[查看报告|" + url + "]\n" +
                                    "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                                    "如有问题请联系" + adminUser, user);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        } else if (current == compatilityPage) {

            JsonArray pmList = MbcUtil.getPermission(templateName, Profession.pmList);
            //鉴权失败
            if (!pmList.contains(new JsonParser().parse(confirmUser))) {
                resultMap.put("result", "false");
                resultMap.put("message", "无权限，请在MBC平台申请改模板权限");
                result.add(resultMap);
                return result;
            }

            dynamicProcess.setReplenishUiStatus(clickStatus);
            dynamicProcess.setThirdStatus(process);
            dynamicProcess.setReplenishTime(new Date());
            dynamicProcess.setReplenishUser(confirmUser);
        } else if (current == confirmPage) {
            //TODO:需要考虑测试模版是否为最新模版，把状态传给MBC,MBC开启上线操作按钮
            JsonArray qaList = MbcUtil.getPermission(templateName, Profession.qaList);
            //鉴权失败
            if (!qaList.contains(new JsonParser().parse(confirmUser))) {
                resultMap.put("result", "false");
                resultMap.put("message", "无权限，请在MBC平台申请改模板权限");
                result.add(resultMap);
                return result;
            }
            dynamicProcess.setThirdStatus(success);
            dynamicProcess.setLastTime(new Date());
            dynamicProcess.setProcessCondition(1);
            dynamicProcess.setFinishAt(new Date());
            dynamicProcess.setLastUser(confirmUser);
        }
        try {
            dynamicProcessMapper.updateById(dynamicProcess);
        } catch (Exception e) {
            resultMap.put("result", "false");
            resultMap.put("message", "未知原因,请联系" + adminUser + "排查");
            result.add(resultMap);
            return result;
        }
        resultMap.put("result", "true");
        resultMap.put("message", "");
        result.add(resultMap);
        return result;
    }

    @Override
    public ArrayList getCurrent(String processId) {
        ArrayList arrayList = new ArrayList();
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getId, processId);
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        arrayList.add(dynamicProcess.getBusiness());
        String templateName = dynamicProcess.getTemplateName();
        arrayList.add(templateName);
        //用来控制前端按钮要不要再展示了
        if (dynamicProcess.getProcessCondition() == 运行中) {
            arrayList.add(buttonApper);
        } else {
            arrayList.add(buttonDisapper);
        }
        //如果任务异常终止，直接展示最后一页去看打回原因
        if (dynamicProcess.getProcessCondition() == 已打回) {
            arrayList.add(confirmPage);
            return arrayList;
        }
        String uiStatus = dynamicProcess.getUiAcceptStatus();
        if (!dynamicProcess.getSmokeStatus().equals(success)) {
            arrayList.add(smokePage);
        } else if (!skip.equals(uiStatus) && !success.equals(uiStatus)) {
            arrayList.add(UIPage);
        } else if (!success.equals(dynamicProcess.getReplenishUiStatus())) {
            arrayList.add(compatilityPage);
        } else {
            arrayList.add(confirmPage);
        }
        try {
            MbcUtil mbcUtil = new MbcUtil();
            //// TODO: 2021/12/20 这种处理方式，在回归场景下，模板链接取到的是test状态那个
            String zipUrl = mbcUtil.getTemplateZipApi(templateName, "");
            arrayList.add(zipUrl);
            String value = dynamicProcess.getStep();
            if (null == value) {
                arrayList.add("4");
            } else {
                arrayList.add(value);
            }
            //前端用来判断是否展示【跳过】按钮
            String onesURL = dynamicProcess.getOnesUrl();
            arrayList.add(onesURL);
        } catch (Exception e) {

        }
        return arrayList;
    }

    @Override
    public boolean stopProcess(String processId) {
        try {
            LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DynamicProcess::getId, processId);
            DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
            dynamicProcess.setProcessCondition(-1);
            dynamicProcess.setFinishAt(new Date());
            dynamicProcess.setReplenishUiStatus("stop");
            dynamicProcessMapper.updateById(dynamicProcess);
            DxUtil dxUtil = new DxUtil();
            String templateName = dynamicProcess.getTemplateName();
            JsonArray rdList = new MbcUtil().getPermission(templateName, Profession.rdList);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (JsonElement p : rdList) {
                try {
                    dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                            "【模版名称】：" + templateName + "\n" +
                            "【测试进度】：\uD83D\uDE22 提测打回 \n" +
                            "【完成时间】：" + sdf.format(new Date()) + "\n" +
                            "如有问题请联系" + adminUser, p.getAsString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }

    }


    public String Eng2Chi(String status) {
        if (success.equals(status)) {
            return "成功";
        } else if (process.equals(status)) {
            return "进行中";
        } else if (waiting.equals(status)) {
            return "等待中";
        } else if (stop.equals(status)) {
            return "已终止";
        } else if (abort.equals(status)) {
            return "已跳过";
        } else if (finetune.equals(status)) {
            return "UI调整";
        } else if (skip.equals(status)) {
            return "跳过";
        } else {
            return "等待中";
        }
    }

    @Override
    public void inform(String templateName) {
        DynamicProcess dynamicProcess = getProcessByTemplateName(templateName);
        String onesUrl = dynamicProcess.getOnesUrl();
        String smokeStatus = dynamicProcess.getSmokeStatus();
        String uiStatus = dynamicProcess.getUiAcceptStatus();
        String qaStatus = dynamicProcess.getReplenishUiStatus();
        String lastStatus = dynamicProcess.getThirdStatus();
        DxUtil dxUtil = new DxUtil();
        Integer id = dynamicProcess.getId();
        String url = "http://qaassist.sankuai.com/compass/dashboard#/dlautotest/job/jobDetail?processId=" + id;
        if (onesUrl != null && onesUrl != "") {
            onesUrl = "[查看需求|" + onesUrl + "]";
        }

        if ((qaStatus != null && qaStatus.equals("success")) || (uiStatus != null && uiStatus.equals("success"))) {
            JsonArray qaList = new MbcUtil().getPermission(templateName, Profession.qaList);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (JsonElement p : qaList) {
                String user = p.getAsString();
                try {
                    dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                            "【模版名称】：" + templateName + "\n" +
                            "【需求链接】：" + onesUrl + "\n" +
                            "【提测类型】：提测变更\n" +
                            "【当前进度】：待QA确认测试结果 \n" +
                            "【确认入口】：[查看报告|" + url + "]\n" +
                            "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                            "如有问题请联系" + adminUser, user);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        } else if (smokeStatus != null && smokeStatus.equals("success")) {
            JsonArray uiList = new MbcUtil().getPermission(templateName, Profession.uiList);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (JsonElement p : uiList) {
                String user = p.getAsString();
                try {
                    dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n" +
                            "【模版名称】：" + templateName + "\n" +
                            "【需求链接】：" + onesUrl + "\n" +
                            "【提测类型】：提测变更\n" +
                            "【当前进度】：待UI验收 \n" +
                            "【验收入口】：[查看报告|" + url + "]\n" +
                            "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                            "如有问题请联系" + adminUser, user);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
    }

    @Override
    public int getProcessId(String templateName) {
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getTemplateName, templateName);
        queryWrapper.last(" order by created_at DESC limit 1");
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        if (dynamicProcess != null) {
            int processId = dynamicProcess.getId();
            return processId;
        }
        return -1;
    }

    @Override
    public int jobId2ProcessId(int id) {
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(DynamicProcess::getJobidTeam, id);
//        queryWrapper.last(" order by created_at DESC limit 1");
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        if (dynamicProcess != null) {
            int processId = dynamicProcess.getId();
            return processId;
        }
        return -1;
    }

    @Override
    public void setScore(String processId, String score) {
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getId, processId);
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        dynamicProcess.setStep(score);
        dynamicProcessMapper.updateById(dynamicProcess);
    }

    @Override
    public DynamicProcess getProcessByTemplateName(String templateName) {
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getTemplateName, templateName);
        queryWrapper.last(" order by created_at DESC limit 1");
        DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
        return dynamicProcess;
    }

    @SneakyThrows
    @Override
    public void handleProcessNotice() {
        MbcUtil mbcUtil = new MbcUtil();
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getProcessCondition, 运行中);
        List<DynamicProcess> processesList = dynamicProcessMapper.selectList(queryWrapper);
        DxUtil dxUtil = new DxUtil();
        int id = 0;
        for (DynamicProcess dynamicProcess : processesList) {
            String noticeDes = "验收";
            String noticePerson = "";
            String templateName = dynamicProcess.getTemplateName();
            try {
                id = dynamicProcess.getId();
                long createTime = dynamicProcess.getCreatedAt().getTime();
                long noticeTime = new Date().getTime();
                //防止刚触发的任务就命中了定时通知
                if ((noticeTime - createTime) < 1000 * 60 * 60 * 24) continue;
                String wordUrl = "https://km.sankuai.com/page/634769563";
                if ("process".equals(dynamicProcess.getSmokeStatus())) {
                    //通知rd
                    JsonArray jsonArray = mbcUtil.getPermission(templateName, Profession.rdList);
                    noticePerson = jsonArray.get(2).getAsString();
                    noticeDes = "RD冒烟提测";
                }
                if ("process".equals(dynamicProcess.getUiAcceptStatus())) {
                    //通知UI
                    JsonArray jsonArray = mbcUtil.getPermission(templateName, Profession.uiList);
                    noticePerson = jsonArray.get(2).getAsString();
                    noticeDes = "UI验收";
                }
                if (("process".equals(dynamicProcess.getThirdStatus())) || ("process".equals(dynamicProcess.getReplenishUiStatus()))) {
                    //通知QA
                    JsonArray jsonArray = mbcUtil.getPermission(templateName, Profession.qaList);
                    noticePerson = jsonArray.get(2).getAsString();
                    noticeDes = "QA验收 & 上线前确认";
                }
                dxUtil.sendToPersionByCompass("※ 待 " + noticeDes + " ※：\n 【任务ID】：" + id + "\n" +
                        "【当前进度】：待" + noticeDes + "\n" +
                        "【模板名称】：" + templateName + "\n" + noticePerson + "\n" +
                        "【备注信息】：提测单已长时间未处理，请及时处理\n" +
                        "【说明文档】：[使用教程|" + wordUrl + "]\n" +
                        "【报告入口】：[去验收|http://qaassist.sankuai.com/compass/dashboard#/dlautotest/job/JobDetail?processId=" + id + "]\n", adminUser);
            } catch (Exception e) {
                dxUtil.sendToPersionByCompass("id为" + id + "的任务通知出错了" + e.getMessage(), adminUser);
            }
        }

    }

    @Override
    public String retrySmoke(String templateName, String business, String user) {
        CompatilityJob comjob = new CompatilityJob();
        comjob.setCreatedBy(user);
        comjob.setBusiness(business);
        comjob.setTemplateName(templateName);
        comjob.setAddress("MBC");
        comjob.setType(2);
        comjob.setMockData("{\"test\":1}");
        JSONObject json = compatilityService.createSmokeJob(comjob);
        return json.get("success").toString();
    }

    @Override
    public int getProcessStatus(String templateName) {
        if (!EnvUtil.isOnline()) {
            return 1;
        }
        int condition = 0;
        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DynamicProcess::getTemplateName, templateName);
        queryWrapper.last(" order by id DESC limit 1");
        DynamicProcess process = dynamicProcessMapper.selectOne(queryWrapper);
        if (null != process) {
            condition = process.getProcessCondition();
            if (condition < 1) {
                condition = 0;
            } else {
                log.info("getProcessStatus未获取到process");
                condition = 1;
            }
        } else {
            condition = 2;
        }

        if (condition != 0) {
            //存一下数据库
            new Thread(() -> {
                try {
                    String processUrl = "http://qaassist.sankuai.com/compass/dashboard#/dlautotest/job/jobDetail?processId=";
                    Date date = new Date();
                    String onesUrl = "";
                    String lastUser = "";
                    String templateId = new MbcUtil().getId(templateName, "");
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    date = sdf.parse(sdf.format(new Date()));
                    ChangeComplianceTemplate changeComplianceTemplate = new ChangeComplianceTemplate();
                    if (null != process) {
                        lastUser = process.getLastUser();
                        onesUrl = process.getOnesUrl();
                        int processId = process.getId();
                        processUrl += processId;
                        changeComplianceTemplate.setProcessUrl(processUrl);
                        if (null == onesUrl) onesUrl = "";
                    }
                    changeComplianceTemplate.setTemplateId(templateId);
                    changeComplianceTemplate.setTemplateName(templateName);
//                    changeComplianceTemplate.setOnesUrl(onesUrl);
//                    changeComplianceTemplate.setTester(lastUser);
                    changeComplianceTemplate.setOnlineTime(date);
                    changeComplianceTemplateMapper.insert(changeComplianceTemplate);
                } catch (Exception e) {
                    log.error(e.getMessage());

                }
            }, "变更合规存储线程").start();

        }
        log.info("getProcessStatus返回值" + condition);
        String re = Integer.toString(condition);
        return condition;
    }

    @Override
    public boolean allCancle(int processId, String user, String cancleReason) {
        try {
            LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DynamicProcess::getId, processId);
            DynamicProcess dynamicProcess = dynamicProcessMapper.selectOne(queryWrapper);
            dynamicProcess.setReplenishUiStatus(skip);
            dynamicProcess.setSmokeStatus(skip);
            dynamicProcess.setUiAcceptStatus(skip);
            dynamicProcess.setThirdStatus(skip);
            dynamicProcess.setProcessCondition(已跳过);
            dynamicProcess.setSmokeUser(user);
            dynamicProcess.setLastUser(user);
            dynamicProcess.setFinishAt(new Date());
            dynamicProcess.setCancleReason(cancleReason);

            dynamicProcessMapper.updateById(dynamicProcess);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public ArrayList xmlDiff(String diffA, String diffB) {
        ArrayList ans = new ArrayList<>();
        if (!SecSdk.checkSSRF(diffA) || !SecSdk.checkSSRF(diffB)) {
            System.out.println("危险，可能是SSRF攻击请求");
        } else {
            synchronized (this) {
                StringUtil st = new StringUtil();
                String zipA = diffA;
                String nameA = zipA.split("/")[zipA.split("/").length - 1].replaceAll(".zip", "");
                if (st.isBlank(nameA)) {
                    return null;
                }
                FileUtil.downloadAndReadFile(zipA, "./xmlDiff/", nameA);
                List<String> a = FileUtil.readFileByLines("./xmlDiff/" + nameA + "/" + nameA + ".xml");
                String stA = "";
                for (String s : a) {
                    stA += s + "\n";
                }
                FileUtil.delete("./xmlDiff/" + nameA);

                String zipB = diffB;
                String nameB = zipB.split("/")[zipB.split("/").length - 1].replaceAll(".zip", "");
                if (st.isBlank(nameB)) {
                    return null;
                }
                FileUtil.downloadAndReadFile(zipB, "./xmlDiff/", nameB);
                List<String> b = FileUtil.readFileByLines("./xmlDiff/" + nameB + "/" + nameB + ".xml");
                String stB = "";
                for (String s : b) {
                    stB += s + "\n";
                }
                FileUtil.delete("./xmlDiff/" + nameB);

                ans.add(stA);
                ans.add(stB);
            }
        }
        return ans;
    }

    @Override
    public ArrayList completeTemplate(Date startDate, Date endDate) {
        System.out.println(startDate);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startDateFormat = sdf.format(startDate);
        String endDateFormat = sdf.format(endDate);

        LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(DynamicProcess::getFinishAt, endDateFormat).ge(DynamicProcess::getFinishAt, startDateFormat);
        ArrayList<DynamicProcess> list = (ArrayList<DynamicProcess>) dynamicProcessMapper.selectList(queryWrapper);

        return list;
    }

    @Override
    public ArrayList onlineTemplate(Date startDate, Date endDate) {
        System.out.println(startDate);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startDateFormat = sdf.format(startDate);
        String endDateFormat = sdf.format(endDate);

        LambdaQueryWrapper<ChangeComplianceTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(ChangeComplianceTemplate::getOnlineTime, endDateFormat).ge(ChangeComplianceTemplate::getOnlineTime, startDateFormat);
        ArrayList<ChangeComplianceTemplate> list = (ArrayList<ChangeComplianceTemplate>) changeComplianceTemplateMapper.selectList(queryWrapper);

        return list;
    }

    @Override
    public void updatePublish(DynamicTemplatePublishDetail dynamicTemplatePublishDetail) {
        log.info(dynamicTemplatePublishDetail.toString());
        dynamicTemplatePublishDetailMapper.insert(dynamicTemplatePublishDetail);
    }

}

