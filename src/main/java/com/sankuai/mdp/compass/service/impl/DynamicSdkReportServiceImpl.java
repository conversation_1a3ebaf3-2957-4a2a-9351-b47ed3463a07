package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicSdkReport;
import com.sankuai.mdp.compass.mapper.DynamicSdkReportMapper;
import com.sankuai.mdp.compass.service.DynamicSdkReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DynamicSdkReportServiceImpl extends ServiceImpl<DynamicSdkReportMapper, DynamicSdkReport> implements DynamicSdkReportService {

    @Autowired
    DynamicSdkReportMapper dynamicSdkReportMapper;

    @Override
    public Integer add(Integer jenkinsId, String platform) {

        DynamicSdkReport dynamicSdkReportEntity = new DynamicSdkReport();
        dynamicSdkReportEntity.setJenkinsId(jenkinsId);
        dynamicSdkReportEntity.setPlatform(platform);
        dynamicSdkReportMapper.insert(dynamicSdkReportEntity);

        return dynamicSdkReportEntity.getId();
    }

    @Override
    public IPage<DynamicSdkReport> list(QueryRequest request) {
        try {
            LambdaQueryWrapper<DynamicSdkReport> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.last("order by id desc");
            Page<DynamicSdkReport> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }

    }
}
