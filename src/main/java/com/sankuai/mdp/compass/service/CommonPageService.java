package com.sankuai.mdp.compass.service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.robust.entity.CommonPage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface CommonPageService extends IService<CommonPage>{
    List<String> getAllAppName();
    IPage<CommonPage> list(QueryRequest request, CommonPage commonPage);
    Resp update(CommonPage commonPage);
    Resp delete(CommonPage commonPage);
    Resp add(CommonPage commonPage);
    Map<String,String> getSchema(String bg, String bu, String biz, String pageDescription, String pageType);
    HashMap<String, Integer> getAllBg();
    HashMap<String, Integer> getAllPageDescription();
    HashMap<String, Integer> getAllBu();
    HashMap<String, Integer> getAllBiz();
    HashMap<String, Integer> getAllPageType();
}
