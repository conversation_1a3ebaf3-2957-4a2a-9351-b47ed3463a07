package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.entity.MockRule;
import com.sankuai.mdp.compass.mapper.MockRuleMapper;
import com.sankuai.mdp.compass.service.MockRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by xieyongrui on 2019/11/29.
 */

@Slf4j
@Service
public class MockRuleServiceImpl extends ServiceImpl<MockRuleMapper,MockRule > implements MockRuleService{
    @Autowired
    MockRuleMapper mockRuleMapper;

    @Override
    public void setMockRule(MockRule mockRule) {
        mockRuleMapper.insert(mockRule);
    }

    @Override
    public void setMockRule(Integer mockId, String key, String value, String api) {
        MockRule mockRule = new MockRule();
        mockRule.setMockId(mockId);
        mockRule.setRuleKey(key);
        mockRule.setRuleValue(value);
        mockRule.setApi(api);
        mockRuleMapper.insert(mockRule);
    }

    @Override
    public MockRule getMockRule(Integer mockId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mockId",mockId);
        MockRule mockRule = mockRuleMapper.selectOne(queryWrapper);
        return mockRule;
    }

}
