package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.Option;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Profession;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.enums.TestType;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.controller.DynamicDataController;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicData;
import com.sankuai.mdp.compass.entity.DynamicProcess;
import com.sankuai.mdp.compass.entity.JobDetail;
import com.sankuai.mdp.compass.mapper.*;
import com.sankuai.mdp.compass.service.*;
import com.sankuai.mdp.compass.entity.*;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import com.sankuai.mdp.compass.service.CompatilityService;
import com.sankuai.mdp.compass.service.DynamicDataService;
import com.sankuai.mdp.compass.service.DynamicTemplateService;
import com.sankuai.mdp.compass.service.JobDetailService;
import com.sankuai.it.iam.common_base.utils.StringUtil;
import jnr.ffi.annotations.In;
import lombok.extern.slf4j.Slf4j;
import org.json.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;

import static com.jayway.jsonpath.JsonPath.isPathDefinite;
import static com.jayway.jsonpath.JsonPath.using;

/**
 * Created by xieyongrui on 2019/11/9.
 */
@Slf4j
@Service
public class CompatilityServiceImpl extends ServiceImpl<CompatilityMapper, CompatilityJob> implements CompatilityService {

    @Autowired
    CompatilityMapper compatilityMapper;

    @Autowired
    DynamicProcessMapper dynamicProcessMapper;

    static MbcUtil mbcUtil = new MbcUtil();

    DynamicDataController dynamicDataController = new DynamicDataController();

    JenkinsUtil jenkinsUtil = new JenkinsUtil();

    DxUtil dxUtil = new DxUtil();

    @Autowired
    DynamicDataService dynamicDataService;

    @Autowired
    JobDetailService jobDetailService;

    @Autowired
    DynamicCaseMapper dynamicCaseMapper;

    @Autowired
    DynamicTemplateService dynamicTemplateService;

    @Autowired
    DynamicProcessService dynamicProcessService;

    @Autowired
    DynamicPageMapper dynamicPageMapper;

    @Autowired
    DynamicIgnoreMapper dynamicIgnoreMapper;

    public static final String adminUser = "liujiao11";

    public static final String COMPONENT_MODE = "component";
    private static final String REPORT = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/dlautotest/job/jobDetail?templateName=";

    @Override
    public CompatilityJob find(Integer id) {

        CompatilityJob compatilityJob = compatilityMapper.selectById(id);
        compatilityJob.setJsonBasePic(new JsonParser().parse(compatilityJob.getBasePic()));
        return compatilityJob;
    }

    @Override
    public IPage<CompatilityJob> list(QueryRequest request, CompatilityJob compatilityJob) {
        try {
            LambdaQueryWrapper<CompatilityJob> queryWrapper = new LambdaQueryWrapper<>();
            Boolean cancelStatus = false;
            Boolean hasCondition = false;
            if (null != compatilityJob.getType()) {
                queryWrapper.eq(CompatilityJob::getType, compatilityJob.getType());
                hasCondition = true;
            }
            if (StringUtil.isNotBlank(compatilityJob.getTemplateName())) {
                queryWrapper.eq(CompatilityJob::getTemplateName, compatilityJob.getTemplateName());
                hasCondition = true;
            }
            // 07.26增加提交人模糊匹配筛选
            if (null != compatilityJob.getMisId()) {
                queryWrapper.like(CompatilityJob::getMisId, compatilityJob.getMisId());
                hasCondition = true;
            }
            if (null != request.getSwitchStatus() && request.getSwitchStatus()) {
                queryWrapper.ne(CompatilityJob::getStatus, 2);
                cancelStatus = true;
            }
            if (null != request.getVirtualStatus() && request.getVirtualStatus()) {
                if (cancelStatus) {
                    queryWrapper.last("and (created_by is null or created_by not in ('it_compass')) order by created_at DESC");
                } else if (hasCondition) {
                    queryWrapper.last("(created_by is null or created_by not in ('it_compass')) order by created_at DESC");
                } else {
                    queryWrapper.last("where (created_by is null or created_by not in ('it_compass')) order by created_at DESC");
                }
            } else {
                queryWrapper.last("order by created_at DESC");
            }

            Page<CompatilityJob> page = new Page<>(request.getPageNum(), request.getPageSize());
//            SortUtil.handlePageSort(request, page, "createdAt", ComConstant.ORDER_DESC, true);
            return this.page(page, queryWrapper);

//            for(int i= 0;i<result.getRecords().size();i++){
//                result.getRecords().get(i).setJsonBasePic(JSONObject.parseObject(result.getRecords().get(i).getBasePic()));
//            }


        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public IPage<CompatilityJob> aggList(QueryRequest request, CompatilityJob compatilityJob) {
        try {
            LambdaQueryWrapper<CompatilityJob> queryWrapper = new LambdaQueryWrapper<>();

            if (null != compatilityJob.getType()) {
                queryWrapper.eq(CompatilityJob::getType, compatilityJob.getType());
            }
            if (StringUtil.isNotBlank(compatilityJob.getTemplateName())) {
                queryWrapper.eq(CompatilityJob::getTemplateName, compatilityJob.getTemplateName());
            }
            queryWrapper.isNotNull(CompatilityJob::getTemplateName);
            queryWrapper.last("and template_name != '' order by created_at DESC");

            Page<CompatilityJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<CompatilityJob> iPage = this.page(page, queryWrapper);
            Page<CompatilityJob> result = new Page<>();
            List<CompatilityJob> list = new ArrayList<>();
            Map<String, Integer> map = new HashMap<>();
            for (int i = 0; i < iPage.getRecords().size(); i++) {

                String templateName = iPage.getRecords().get(i).getTemplateName();
                Integer status = iPage.getRecords().get(i).getStatus();
                if (!map.containsKey(templateName)) {
                    map.put(templateName, status);
                } else {
                    if (null != status && status == 0) {
                        map.replace(templateName, status);
                    }
                }
            }
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                CompatilityJob jobDetail = new CompatilityJob();
                try {
                    jobDetail.setTemplateName(entry.getKey());
                    jobDetail.setStatus(entry.getValue());
                    list.add(jobDetail);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            result.setRecords(list);

            return result;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public IPage<CompatilityJob> listAggregation(QueryRequest request, CompatilityJob compatilityJob) {
        try {
            LambdaQueryWrapper<CompatilityJob> queryWrapper = new LambdaQueryWrapper<>();

            if (null != compatilityJob.getType()) {
                queryWrapper.eq(CompatilityJob::getType, compatilityJob.getType());
            }
            if (StringUtil.isNotBlank(compatilityJob.getTemplateName())) {
                queryWrapper.eq(CompatilityJob::getTemplateName, compatilityJob.getTemplateName());
            }
            queryWrapper.isNotNull(CompatilityJob::getTemplateName);
            queryWrapper.ge(CompatilityJob::getId, 1712);//这之前都是老逻辑的，可以屏蔽掉
            queryWrapper.last("and template_name != '' order by id DESC");

            Page<CompatilityJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<CompatilityJob> iPage = this.page(page, queryWrapper);
            Page<CompatilityJob> result = new Page<>();
            List<CompatilityJob> list = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            for (int i = 0; i < iPage.getRecords().size(); i++) {
                String templateName = iPage.getRecords().get(i).getTemplateName();
                String business = iPage.getRecords().get(i).getBusiness();
                if (!map.containsKey(templateName)) {
                    map.put(templateName, business);
                    CompatilityJob jobDetail = new CompatilityJob();
                    try {
                        jobDetail.setTemplateName(templateName);
                        jobDetail.setBusiness(business);
                        list.add(jobDetail);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            result.setRecords(list);

            return result;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public IPage<CompatilityJob> listAggregationSecond(QueryRequest request, CompatilityJob compatilityJob) {
        try {
            LambdaQueryWrapper<CompatilityJob> queryWrapper = new LambdaQueryWrapper<>();

            if (null != compatilityJob.getType()) {
                queryWrapper.eq(CompatilityJob::getType, compatilityJob.getType());
            }

            queryWrapper.isNotNull(CompatilityJob::getTemplateName);
            String template_name = compatilityJob.getTemplateName();
            queryWrapper.last("and template_name = '" + template_name + "' order by id DESC");
            Page<CompatilityJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<CompatilityJob> iPage = this.page(page, queryWrapper);
            Page<CompatilityJob> result = new Page<>();
            List<CompatilityJob> list = new ArrayList<>();
            Map<String, CompatilityJob> map = new HashMap<>();
            for (int i = 0; i < iPage.getRecords().size(); i++) {
                Integer status = iPage.getRecords().get(i).getStatus();
                String downloadUrl = iPage.getRecords().get(i).getDownloadUrl();
                CompatilityJob temp = iPage.getRecords().get(i);

                if (downloadUrl == null || downloadUrl.equals("")) continue;

                if (!map.containsKey(downloadUrl)) {
                    map.put(downloadUrl, temp);
                } else {
                    if (null != status && status == 0) {
                        map.replace(downloadUrl, temp);
                    }
                }
            }
            for (Map.Entry<String, CompatilityJob> entry : map.entrySet()) {
                list.add(entry.getValue());
            }
            result.setRecords(list);

            return result;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public JsonObject createV2(CompatilityJob dynamicJob) {
        JsonObject resp = new JsonObject();
        JsonObject businessMockResult = new JsonObject();
        Boolean success = true;
        String msg = "触发成功";
        try {
            int type = dynamicJob.getType();
            String business = dynamicJob.getBusiness();
            String templateName = dynamicJob.getTemplateName();
            String caseIds = dynamicJob.getCaseIds();
            String[] caseIdList = caseIds.split(",");

            LambdaQueryWrapper<DynamicCase> queryWrapper = new LambdaQueryWrapper<>();

            for (String s : caseIdList) {
                queryWrapper.or().eq(DynamicCase::getId, s);
            }

            List<DynamicCase> caseList = dynamicCaseMapper.selectList(queryWrapper);

            businessMockResult.add(business, new JsonArray());
            JsonObject templateMockResult = new JsonObject();
            templateMockResult.addProperty("templateName", templateName);
            templateMockResult.add("mock", new JsonArray());

            //检查xml是否包含埋点代码
            if (!dynamicTemplateService.isContaintMge(business, templateName, dynamicJob.getAddress(), "")) {
                String desc = "XML中无埋点代码";
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("eventDesc", desc);
                dynamicJob.setDescription(jsonObject.toString());
                dynamicJob.setAutoResult(false);
                compatilityMapper.updateById(dynamicJob);

                if (type == 1) {
                    dynamicJob.setStatus(1);
                    compatilityMapper.updateById(dynamicJob);
                    resp.addProperty("success", false);
                    resp.addProperty("msg", desc);
                    return jsonObject;
                }
            }

            JsonArray mockIds = new JsonArray();
            for (int i = 0; i < caseList.size(); i++) {
                DynamicCase thisCase = caseList.get(i);
                String templateJsonData = thisCase.getTemplateData();
                JSONObject mockRule = new JSONObject(thisCase.getMockRule());

                String module = thisCase.getModule();
                QueryWrapper pageQuery = new QueryWrapper();
                pageQuery.eq("module", module);
                DynamicPage dynamicPage = dynamicPageMapper.selectOne(pageQuery);
                String apiJsonData = dynamicPage.getApiData();
                String apiPath = dynamicPage.getApiPath();
                String listPath = dynamicPage.getListPath();

                if (null == templateJsonData || "" == templateJsonData) {
                    //模版数据为空，跳过
                    continue;
                }
                if (null == apiJsonData || "" == apiJsonData) {
                    //api数据为空，跳过
                    continue;
                }
                if (null == apiPath || "" == apiPath) {
                    //apiPath数据为空，跳过
                    continue;
                }
                if (null == listPath || "" == listPath) {
                    continue;
                }

                //创建mockIds
                JsonArray curMockIds = dynamicDataService.mockByCase(templateJsonData, listPath, apiJsonData, apiPath, mockRule, type);
                log.info("创建任务mock结果", mockIds);

                mockIds.addAll(curMockIds);
            }

            templateMockResult.getAsJsonArray("mock").addAll(mockIds);
            businessMockResult.getAsJsonArray(business).add(templateMockResult);
            if (businessMockResult.has(business)) {
                if (businessMockResult.get(business).getAsJsonArray().size() == 0) {
                    success = false;
                    msg = "mock生成失败，请检查您的数据配置是否正确";
                    resp.addProperty("success", success);
                    resp.addProperty("msg", msg);
                    return resp;
                }
            }

            //// TODO: 2021/3/23 处理传进来的机型系统
            ArrayList androidList = new ArrayList();
            ArrayList iosList = new ArrayList();
            String[] versions = dynamicJob.getDevicesVersion().split(";");
            String[] androidVerison = versions[0].split(",");
            String[] iOSVerison = versions[1].split(",");
            for (String android : androidVerison) {
                if (!android.isEmpty()) {
                    androidList.add(android);
                }
            }
            for (String ios : iOSVerison) {
                if (!ios.isEmpty()) {
                    iosList.add(ios);
                }
            }
            if (androidList.isEmpty() && iosList.isEmpty()) {
                success = false;
                msg = "请至少选择一个设备系统进行测试";
                resp.addProperty("success", success);
                resp.addProperty("msg", msg);
                return resp;
            } else if (androidList.isEmpty()) {
                dynamicJob.setPlatform("iOS");
            } else if (iosList.isEmpty()) {
                dynamicJob.setPlatform("Android");
            }


            dynamicJob.setScenes("兼容");
            MbcUtil mbcUtil = new MbcUtil();
            String zipUrl = mbcUtil.getTemplateZipApi(templateName, "");
            dynamicJob.setDownloadUrl(zipUrl);
            dynamicJob.setAddress("MBC");

            dynamicJob.setMockIds(businessMockResult.toString());
            dynamicJob.setStatus(-1);
            compatilityMapper.insert(dynamicJob);
            if (EnvUtil.isOnline()) {
                jenkinsUtil.newDynamicTestJob(dynamicJob);
            }
            msg = "提交成功";

        } catch (Exception e) {
            log.error("创建任务失败", e);
            success = false;
            msg = e.getMessage();
        } finally {
            resp.addProperty("success", success);
            resp.addProperty("msg", msg);
            return resp;
        }

    }

    @Override
    public JSONObject createSmokeJob(CompatilityJob compatilityJob) {
        JSONObject result = new JSONObject();
        StringUtil st = new StringUtil();
        Boolean success = true;
        String testMode = compatilityJob.getTestMode();
        String msg = "触发成功";
        String business = compatilityJob.getBusiness();
        String name = compatilityJob.getTemplateName();
        int testType = compatilityJob.getType();
        String reportUrl = REPORT + name + "&type=" + testType;
        String createdBy = compatilityJob.getCreatedBy();
        // 如果是MBC手动触发测试，需要处理misId字段
        String misId = compatilityJob.getMisId();
        //如果是组件测试，则business也置为component
        if (COMPONENT_MODE.equals(testMode)) {
            business = COMPONENT_MODE;
            compatilityJob.setBusiness(business);
        }
        List<String> dynamicJobWhiteList = new LionUtil().getListValue("dynamicJobWhiteList");
        if (!dynamicJobWhiteList.contains(business)) {
            result.put("success", success);
            msg = String.format("%s已屏蔽，不触发", business);
            result.put("msg", msg);
            return result;
        }

        String apkUrl = compatilityJob.getApkUrl();
        String ipaUrl = compatilityJob.getImeituanUrl();
        String scenes = compatilityJob.getScenes();
        if (st.isBlank(scenes)) {
            scenes = "smoke";
            compatilityJob.setScenes(scenes);
        }
        if (st.isBlank(createdBy)) {
            compatilityJob.setCreatedBy("it_mbc");
        }
        // 手动触发测试设置misId
        if (null == misId) {
            // 若未传misId字段，表示自动触发，设置""兼容该场景
            misId = "";
            compatilityJob.setMisId(misId);
        } else if (misId.isEmpty()) {
            // 若传了misId，但为""，说明是手动触发，但接口传的不对，直接结束测试
            result.put("msg", "当前任务缺少任务触发人misId信息");
            result.put("success", false);
            return result;
        }

        String address = compatilityJob.getAddress();
        //组件测试的address置为component
        if (COMPONENT_MODE.equals(testMode)) {
            address = COMPONENT_MODE;
            compatilityJob.setAddress(address);
        }
        if (address != null && address != "") {
        } else {
            compatilityJob.setAddress("MBC");
        }

        // 非搜索业务，跳过引导浮层
        if (business != null && !business.contains("search")) {
            if (name != null && (name.contains("guide") || name.contains("bubble"))) {
                result.put("msg", "当前模版暂不支持自动化测试");
                result.put("success", false);
                return result;
            }
        }
        QueryWrapper ignoreQueryWrapper = new QueryWrapper();
        ignoreQueryWrapper.eq("name", name);
        int ignoreCount = dynamicIgnoreMapper.selectCount(ignoreQueryWrapper);
        if (ignoreCount > 0) {
            result.put("msg", "当前模版暂不支持自动化测试");
            result.put("success", false);
            return result;
        }

        /**
         * 存在相同测试类型的任务 & 模板链接没变化，则不触发新的测试
         */
        String zipUrlFull = compatilityJob.getDownloadUrl();
        if (st.isBlank(zipUrlFull)) {
            MbcUtil mbcUtil = new MbcUtil();
            String templateName = compatilityJob.getTemplateName();
            zipUrlFull = mbcUtil.getTemplateZipApi(templateName, scenes);
            compatilityJob.setDownloadUrl(zipUrlFull);
        }
        try {
            // 查询数据库中是否存在一条模板名相同&模板链接相同&测试类型相同&测试状态非跳过和取消的记录
            LambdaQueryWrapper<CompatilityJob> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CompatilityJob::getTemplateName, name);
            queryWrapper.eq(CompatilityJob::getDownloadUrl, zipUrlFull);
            queryWrapper.eq(CompatilityJob::getType, testType);
            queryWrapper.le(CompatilityJob::getStatus, 1);
            queryWrapper.orderByDesc(CompatilityJob::getId);
            queryWrapper.last("limit 1");

            CompatilityJob compatilityJobInfo;
            compatilityJobInfo = compatilityMapper.selectOne(queryWrapper);
            if (null != compatilityJobInfo && createdBy.equals("it_mbc")) {
                int jobId = compatilityJobInfo.getId();
                msg = "已有相同测试类型且模板链接一致的测试任务，可[查看报告|" + reportUrl + "]中的JobID:" + jobId;
                if (!misId.isEmpty()) {
                    dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 【" + name + "】的" + "冒烟测试任务提交失败。\n失败原因：" + msg + "。如有问题请联系liujiao11", misId);
                } else {
                    dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 【" + name + "】的" + "冒烟测试任务提交失败。\n失败原因：" + msg + "。如有问题请联系liujiao11", createdBy);
                }
                msg = "已有相同测试类型且模板链接一致的测试任务，JobID:" + jobId;
                result.put("success", success);
                result.put("msg", msg);
                result.put("reportUrl", reportUrl);
                return result;
            }
        } catch (Exception e) {
            log.error("存储模板链接失败", e);
        }

        String type = "";
        //默认UI类型
        if (st.isBlank(compatilityJob.getType())) {
            type = "UI";
            compatilityJob.setType(0);
        } else if (compatilityJob.getType() == 1) {
            type = "埋点";
        } else if (compatilityJob.getType() == 0) {
            type = "UI";
        } else if (compatilityJob.getType() == 2) {
            type = "UI&埋点";
        }

        try {
            Integer id = compatilityMapper.insert(compatilityJob);
            if (st.isBlank(business)) {
                business = new MbcUtil().getBusiness(name);
                if (st.isBlank(business)) {
                    result.put("msg", "没有查到business");
                    return result;
                } else {
                    compatilityJob.setBusiness(business);
                }
            }

            JsonObject jsonObject = null;
            if (type.equals("UI") || type.equals("UI&埋点")) {
                jsonObject = dynamicDataService.mockSmoke(compatilityJob, 0, scenes);
            } else if (type.equals("埋点")) {
                jsonObject = dynamicDataService.mockSmoke(compatilityJob, 1, scenes);
            }
            log.info("创建任务mock结果");
            log.info(jsonObject.toString());

            if ((jsonObject.has(business) && jsonObject.getAsJsonArray(business).size() > 0) || (jsonObject.has("search") && jsonObject.getAsJsonArray("search").size() > 0) || (jsonObject.has("mine") && jsonObject.getAsJsonArray("mine").size() > 0)) {
                // 如果jsonObject存在mock数据，则继续生成多套数据的mock
                log.info("生成多套数据的mock");
                jsonObject = dynamicDataService.mockByTemplateData(compatilityJob, type, jsonObject, scenes);
            } else {
                success = false;
                msg = "mock生成失败，请检查mock数据是否正确";
                result.put("success", success);
                result.put("msg", msg);
                if (!misId.isEmpty()) {
                    dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 【" + name + "】的" + "冒烟测试任务提交失败。\n失败原因：" + msg + "。如有问题请联系liujiao11", misId);
                } else {
                    dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 【" + name + "】的" + "冒烟测试任务提交失败。\n失败原因：" + msg + "。如有问题请联系liujiao11", createdBy);
                }
                return result;
            }

            compatilityJob.setMockRule(jsonObject.toString());
            compatilityJob.setStatus(-1);
            boolean isCommitEventJob = true;
            // 本地调试将环境取反
            if (EnvUtil.isOnline()) {
                if (type.contains("UI")) {
                    if (!dynamicJobWhiteList.contains(business)) {
                        result.put("success", success);
                        msg = String.format("%s已屏蔽，不触发", business);
                        result.put("msg", msg);
                    } else {
                        jenkinsUtil.UISmokeJob(compatilityJob);
                    }
                }

                // 06.21 XML中有埋点且MBC有配置，才触发埋点测试
                if (type.contains("埋点")) {
                    Boolean hasMgeCode = dynamicTemplateService.isContaintMge(business, name, compatilityJob.getAddress(), scenes);
                    if (hasMgeCode) {
                        // 增加XML有埋点，但非MBC配置场景
                        if (mbcUtil.isMbcConfigured(name, scenes)) {
                            jenkinsUtil.eventSmokeJob(compatilityJob);
                        } else {
                            compatilityJob.setDescription("有埋点，但非MBC配置，需人工校验");
                            isCommitEventJob = false;
                            msg = "有埋点，但非MBC配置，需人工校验";
                        }
                    } else {
                        compatilityJob.setDescription("XML未配置埋点");
                        isCommitEventJob = false;
                        msg = "XML未配置埋点";
                    }
                    if (!isCommitEventJob) {
                        if (type.equals("埋点")) {
                            compatilityJob.setStatus(2);
                        }
                        result.put("msg", msg);
                        result.put("success", success);
                        if (!misId.isEmpty()) {
                            dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 【" + name + "】的" + "埋点测试任务提交失败。\n失败原因：" + msg + "。如有问题请联系liujiao11", misId);
                        } else {
                            dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 【" + name + "】的" + "埋点测试任务提交失败。\n失败原因：" + msg + "。如有问题请联系liujiao11", createdBy);
                        }
                    }
                }
            }

            compatilityMapper.updateById(compatilityJob);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (type.contains("UI")) {
                isCommitEventJob = true;
                msg = "触发成功";
            }
            // 本地调试将环境取反
            if (EnvUtil.isOnline()) {
                if (isCommitEventJob) {
                    // 若misId有值，表示手动触发，则优先周知misId
                    if (!misId.isEmpty()) {
                        dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n 【任务ID】：" + compatilityJob.getId() + "\n" +
                                "【模版名称】：" + name + "\n" +
                                "【测试类型】：" + type + "\n" +
                                "【测试场景】：冒烟测试\n" +
                                "【测试进度】：⏳ 开始执行 \n" +
                                "【提交时间】：" + sdf.format(new Date()) + "\n" +
                                "如有问题请联系" + adminUser, misId);
                    } else if (null != createdBy && !createdBy.isEmpty()) {
                        dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n 【任务ID】：" + compatilityJob.getId() + "\n" +
                                "【模版名称】：" + name + "\n" +
                                "【测试类型】：" + type + "\n" +
                                "【测试场景】：冒烟测试\n" +
                                "【测试进度】：⏳ 开始执行 \n" +
                                "【提交时间】：" + sdf.format(new Date()) + "\n" +
                                "如有问题请联系" + adminUser, createdBy);
                    } else {
                        JsonArray rdList = new MbcUtil().getPermission(name, Profession.rdList);
                        for (JsonElement user : rdList) {
                            dxUtil.sendToPersionByCompass("※ MBC模版自动化测试提醒 ※：\n 【任务ID】：" + compatilityJob.getId() + "\n" +
                                    "【模版名称】：" + name + "\n" +
                                    "【测试类型】：" + type + "\n" +
                                    "【测试场景】：冒烟测试\n" +
                                    "【测试进度】：⏳ 开始执行 \n" +
                                    "【提交时间】：" + sdf.format(new Date()) + "\n" +
                                    "如有问题请联系" + adminUser, user.getAsString());
                        }
                    }
                }
            }

            dynamicProcessService.updateProcess(compatilityJob);

        } catch (Exception e) {
            log.error("创建任务失败", e);
            success = false;
            msg = e.getMessage();
        } finally {
            result.put("success", success);
            result.put("msg", msg);
            result.put("reportUrl", reportUrl);
            return result;
        }
    }


    public Resp cancel(CompatilityJob compatilityJob) {
        ConanUtil conanUtil = new ConanUtil();

        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        compatilityJob1.setStatus(2);
        String uiConanId = compatilityJob1.getReport();
        if (uiConanId != null) {
            String[] ids = uiConanId.split("_");
            for (int i = 0; i < ids.length; i++) {
                log.info("取消云测job：" + ids[i]);
                conanUtil.cancelJob(ids[i]);
            }
        }
        String eventConanId = compatilityJob1.getEventConanId();
        if (eventConanId != null) {
            String[] ids = eventConanId.split("_");
            for (int i = 0; i < ids.length; i++) {
                log.info("取消云测job：" + ids[i]);
                conanUtil.cancelJob(ids[i]);
            }
        }

        compatilityMapper.updateById(compatilityJob1);
        return Resp.success();
    }

    @Override
    public void updateCheckStatus(CompatilityJob compatilityJob) {
        Integer status = compatilityJob.getCheckStatus();
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        compatilityJob1.setCheckStatus(status);
        System.out.print(compatilityJob1);
        compatilityMapper.updateById(compatilityJob1);

    }

    @Override
    public void updateJobId(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        compatilityJob1.setJenkinsId(compatilityJob.getJenkinsId());
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateReport(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        compatilityJob1.setReport(compatilityJob.getReport());
        compatilityJob1.setReportIos(compatilityJob.getReportIos());
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateEventConanId(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        compatilityJob1.setEventConanId(compatilityJob.getEventConanId());
        compatilityJob1.setEventConanidIos(compatilityJob.getEventConanidIos());
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateStatusById(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        if (2 == compatilityJob1.getStatus()) return;
        compatilityJob1.setStatus(compatilityJob.getStatus());
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateStatusByJenkinsId(CompatilityJob compatilityJob) {
        Integer jenkinsId = compatilityJob.getJenkinsId();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("jenkins_id", jenkinsId);
        CompatilityJob compatilityJob1 = compatilityMapper.selectOne(queryWrapper);
        compatilityJob1.setStatus(compatilityJob.getStatus());
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateFinishTime(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        Date date = new Date();
        compatilityJob1.setFinishAt(date);
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateEventFinishTime(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        Date date = new Date();
        compatilityJob1.setEventFinishAt(date);
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateCheckTime(CompatilityJob compatilityJob) {
        Integer id = compatilityJob.getId();
        CompatilityJob compatilityJob1 = compatilityMapper.selectById(id);
        Date date = new Date();
        compatilityJob1.setCheckAt(date);
        compatilityMapper.updateById(compatilityJob1);
    }

    @Override
    public void updateAutoResult(int jobId, String result) {
        CompatilityJob compatilityJob = compatilityMapper.selectById(jobId);
        boolean autoResult;
        if (result.equals("0")) {
            autoResult = false;
        } else {
            autoResult = true;
        }
        compatilityJob.setAutoResult(autoResult);
        compatilityMapper.updateById(compatilityJob);
    }

    @Override
    public String getEventId(Integer jobId, String templateName) {
        CompatilityJob compatilityJob = compatilityMapper.selectById(jobId);
        if (null != compatilityJob && null != compatilityJob.getBaseLx()) {
            try {
                Configuration confForList = Configuration.builder()
                        .options(Option.REQUIRE_PROPERTIES).build();
                Configuration confForDefault = Configuration.builder()
                        .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();

                JsonElement jsonElement = new JsonParser().parse(compatilityJob.getBaseLx());
                JsonObject jsonObject = jsonElement.getAsJsonObject();
                String baseLxDate = jsonObject.get(templateName).toString();

                List<String> bidList = using(confForList).parse(baseLxDate).read("$..bid");
                List<String> cidList = using(confForList).parse(baseLxDate).read("$..cid");

                String templateData = compatilityJob.getMockData();
                if (null == templateData || "" == templateData) {
                    DynamicData dynamicData = dynamicDataService.find(templateName);
                    templateData = dynamicData.getTemplateData();
                }
                LinkedHashSet<String> set = new LinkedHashSet<String>(bidList.size());
                set.addAll(bidList);
                bidList.clear();
                bidList.addAll(set);
                String bidStr = "(";
                for (int i = 0; i < bidList.size(); i++) {
                    String bid = bidList.get(i).replaceAll("\"", "").replace("{", "").replace("}", "");
                    if (bid.contains(".")) {
                        bid = using(confForDefault).parse(templateData).read("$." + bid);
                    }
                    String tempBid = "'" + bid + "'";
                    if (0 == i) {
                        bidStr += tempBid;
                    } else {
                        bidStr += "," + tempBid;
                    }

                }
                String cid = cidList.get(0).replaceAll("\"", "").replace("{", "").replace("}", "");
                if (cid.contains(".")) {
                    cid = using(confForDefault).parse(templateData).read("$." + cid);
                }
                bidStr += ")";
                JsonObject resultJson = new JsonObject();
                resultJson.addProperty("bid", bidStr);
                resultJson.addProperty("cid", cid);
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date date = new Date();
                String testDate = format.format(new Date(date.getTime() - (long) 1 * 24 * 60 * 60 * 1000));
                if (null != compatilityJob.getFinishAt()) {
                    testDate = format.format(compatilityJob.getFinishAt());
                }
                resultJson.addProperty("testDate", testDate);
                List<JobDetail> jobDetailList = jobDetailService.selectAllByJobId(jobId);
                Set<String> uuidSet = new HashSet<>();
                if (null != jobDetailList) {
                    Iterator<JobDetail> iterator = jobDetailList.iterator();
                    while (iterator.hasNext()) {
                        JobDetail jobDetail = iterator.next();
                        String mge = jobDetail.getCompareMge();
                        if (null != mge && "" != mge) {
                            List<String> uuidList = (ArrayList) using(confForDefault).parse(mge).read("$..uuid");
                            for (int i = 0; i < uuidList.size(); i++) {
                                System.out.print(uuidList.get(i));
                                if (null != uuidList.get(i) && "" != uuidList.get(i)) {
                                    uuidSet.add(uuidList.get(0));
                                }
                            }
                        }

                    }
                }
                if (!uuidSet.isEmpty()) {
                    String uuidStr = "(";
                    Iterator<String> iterator = uuidSet.iterator();
                    int i = 0;
                    while (iterator.hasNext()) {
                        String uuid = "'" + iterator.next() + "'";
                        if (0 == i) {
                            uuidStr += uuid;
                        } else {
                            uuidStr += "," + uuid;
                        }
                        i++;
                    }
                    uuidStr += ")";
                    resultJson.addProperty("uuid", uuidStr);
                }
                return resultJson.toString();
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    @Override
    public CompatilityJob getJobInfo(CompatilityJob compatilityJob) {
        if (compatilityJob != null) {
            compatilityJob = compatilityMapper.selectById(compatilityJob.getId());
        }
        return compatilityJob;
    }

    @Override
    public int changeFailedReason(int id, String failedReason) {
        try {
            CompatilityJob compatilityJob = compatilityMapper.selectOne(new QueryWrapper<CompatilityJob>().eq("id", id));
            System.out.println(compatilityJob.getId());
            System.out.println(compatilityJob.getFailedReason());
            compatilityJob.setFailedReason(failedReason);
            System.out.println(compatilityJob.getFailedReason());
            compatilityMapper.updateById(compatilityJob);
            return 1;
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public IPage<CompatilityJob> listProcess(String processId, String currentPage, Integer testType) {
        if (!processId.equals("-1")) {
            LambdaQueryWrapper<DynamicProcess> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DynamicProcess::getId, processId);
            DynamicProcess dynamicProcess1 = dynamicProcessMapper.selectOne(queryWrapper);
            String jobid_team = dynamicProcess1.getJobidTeam();

            LambdaQueryWrapper<CompatilityJob> compatilityJobWrapper = new LambdaQueryWrapper<>();
            ArrayList list = new ArrayList();

            String[] jobs = jobid_team.split("_");
            // 若测试类型是0 UI或者1埋点，则只查询该模板的0或者1任务，为其他值则查询所有任务
            if (0 == testType || 1 == testType) {
                for (String jobid : jobs) {
                    compatilityJobWrapper.or().eq(CompatilityJob::getId, jobid).eq(CompatilityJob::getType, testType);
                }
            } else {
                for (String jobid : jobs) {
                    compatilityJobWrapper.or().eq(CompatilityJob::getId, jobid);
                }
            }
            Page<CompatilityJob> result = new Page<>();
            compatilityJobWrapper.orderByDesc(CompatilityJob::getId);

            Page<CompatilityJob> page = new Page<>(1, 100);
            IPage<CompatilityJob> iPage = this.page(page, compatilityJobWrapper);
            // 记录任务数量
            result.setTotal((long) iPage.getRecords().size());
            if (currentPage.equals("0") || currentPage.equals("1")) {
                for (int i = 0; i < iPage.getRecords().size(); i++) {
                    list.add(iPage.getRecords().get(i));
                }
                //最开始的设计是为了适应支持回退，如果不允许回退的话，其实四个step都可以拿到所有id的条目
            } else if (currentPage.equals("3") || currentPage.equals("2")) {
                List<CompatilityJob> list1 = compatilityMapper.selectList(compatilityJobWrapper);
                result.setRecords(list1);
                return result;
            }
            result.setRecords(list);
            return result;
        }
        return null;
    }

    @Override
    public Resp cancel(Integer jobId) {
        Resp resp = new Resp();
        LambdaQueryWrapper<CompatilityJob> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CompatilityJob::getId, jobId);
        CompatilityJob compatilityJob = compatilityMapper.selectOne(queryWrapper);
        if (compatilityJob != null) {
            compatilityJob.setStatus(2);
            compatilityMapper.update(compatilityJob, queryWrapper);
            resp.setCode(200);
            return resp;
        } else {
            return null;
        }
    }

    @Override
    public HashMap weekReport() {
        //完整UI+tp90
        HashMap ans = new HashMap();
        LambdaQueryWrapper<CompatilityJob> compatilityJobWrapper = new LambdaQueryWrapper<>();
        HashMap UI = new ValueUtil().getWeekReport(compatilityJobWrapper, compatilityMapper, TestType.UI);
        HashMap Mge = new ValueUtil().getWeekReport(compatilityJobWrapper, compatilityMapper, TestType.Mge);
        ans.put("UI", UI);
        ans.put("Mge", Mge);

        return ans;
    }

}
