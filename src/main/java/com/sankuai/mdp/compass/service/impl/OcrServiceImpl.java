package com.sankuai.mdp.compass.service.impl;

import com.google.gson.*;
import com.meituan.horus.service.*;
import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.inf.patriot.org.json.JSONObject;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.service.OcrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.apache.thrift.TException;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Point;
import org.bytedeco.opencv.opencv_core.Rect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.awt.image.BufferedImage;
import javax.imageio.ImageIO;

import org.bytedeco.opencv.opencv_core.*;

import static org.bytedeco.opencv.global.opencv_imgproc.*;
import static org.bytedeco.opencv.global.opencv_imgcodecs.*;
import static org.bytedeco.opencv.global.opencv_core.*;

/**
 * Created by xieyongrui on 2020/12/2.
 */
@Service
@Slf4j
public class OcrServiceImpl implements OcrService {

    @Autowired
    OCRServices.Iface ocr;

    static ComConstant comConstant = new ComConstant();

    static String url = " http://localhost:9092/vision/text";
    private static final Logger logger = LoggerFactory.getLogger(OcrUtil.class);

    public static String unicodeDecode(String string) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(string);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            string = string.replace(matcher.group(1), ch + "");
        }
        return string;
    }

    /**
     * 部署在compass服务器的ocr模型
     *
     * @param PicUrl
     * @return 拿到的文字数组
     */
    public JsonArray getText(String PicUrl) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("image", PicUrl);
        String result = "";
        try {
            result = unicodeDecode(HttpUtil.jsonPost(url, jsonObject));
        } catch (Exception e) {
            return null;
        }

        JsonObject allText = new JsonParser().parse(result).getAsJsonObject();
        JsonArray jsonArrayAll = allText.get("data").getAsJsonObject().getAsJsonArray("roi_text");
        JsonArray jsonArray = new JsonArray();
        for (int i = 0; i < jsonArrayAll.size(); i++) {
            String text = jsonArrayAll.get(i).getAsJsonObject().get("text").getAsString();
            jsonArray.add(text);
        }
        return jsonArray;
    }

    /**
     * AI团队提供的ocr模型
     *
     * @param PicUrl
     * @return 拿到的文字数组
     */
    @Override
    public JsonArray getTextFromAI(String PicUrl) {
        synchronized (this) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("url", PicUrl);
                jsonObject.put("source", "ocrtest");
                String req = jsonObject.toString();
                System.out.println("请求AI" + System.currentTimeMillis());
                String Result = ocr.recognizeSceneText(req);
                JsonObject ocrObject = new JsonParser().parse(Result).getAsJsonObject();
                JsonArray TextArray = ocrObject.get("results_str").getAsJsonArray();
                return TextArray;
            } catch (Exception e) {
                return null;
            }
        }
    }

    @Override
    public JsonArray getTextAndLocationFromAI(String PicUrl) {
        synchronized (this) {
            try {
                JsonArray resultArray = new JsonArray();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("url", PicUrl);
                jsonObject.put("source", "ocrtest");
                String req = jsonObject.toString();
                System.out.println("请求AI" + System.currentTimeMillis());
                String Result = ocr.recognizeSceneText(req);
                JsonObject ocrObject = new JsonParser().parse(Result).getAsJsonObject();
                JsonArray TextArray = ocrObject.get("results_str").getAsJsonArray();
                JsonArray locations = ocrObject.get("location").getAsJsonArray();
                for (int i = 0; i < TextArray.size(); i++) {
                    JsonArray pair = new JsonArray();
                    pair.add(TextArray.get(i).getAsString());
                    pair.add(locations.get(i).getAsString());
                    resultArray.add(pair);
                }
                logger.info("resultArray:" + resultArray);
                return resultArray;
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * @param mockText  testSDK想要校验的
     * @param textArray ocr拿到的
     * @return
     */
    public boolean ExistTest(String mockText, JsonArray textArray) {
        // JsonArray textArray = getText(url);
        for (int i = 0; i < textArray.size(); i++) {
//            String text = textArray.get(i).getAsJsonObject().get("text").getAsString();
            String text = textArray.get(i).getAsString();
            if (jaccard(mockText, text) > 0.4 || text.contains(mockText)) {
//                logger.info("mockText:"+mockText+" text:"+text+"匹配IOU\n");
                return true;
            }
            if (mockText.equals("￥")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 用来计算文字之间的相似度
     *
     * @param a
     * @param b
     * @return
     */
    public static float jaccard(String a, String b) {
        if (a == null && b == null) {
            return 1f;
        }
        // 都为空相似度为 1
        if (a == null || b == null) {
            return 0f;
        }
        Set<Integer> aChar = a.chars().boxed().collect(Collectors.toSet());
        Set<Integer> bChar = b.chars().boxed().collect(Collectors.toSet());
        // 交集数量
        int intersection = SetUtils.intersection(aChar, bChar).size();
        if (intersection == 0)
            return 0;
        // 并集数量
        int union = SetUtils.union(aChar, bChar).size();
        return ((float) intersection) / (float) union;
    }

    public String textShowJudement(String mockId, String id, String template, int jobId) {
        String disShowText = "";

        FileUtil r = new FileUtil();
        String textContentPath = comConstant.OUT_PUT + jobId + "/" + id + "/textContent/";

        File file = new File(textContentPath + mockId + ".txt");
        OcrUtil ocrUtil = new OcrUtil();

        if (file.exists()) {
            // 文字识别拿到的
            String picPath = comConstant.OUT_PUT + jobId + "/" + id + "/link/" + template + "/abnormal/" + mockId
                    + ".txt";
//            String url = uploadPicture(picPath);
            String url = new FileUtil().read(picPath);
            JsonArray textArray2 = getTextFromAI(url);
            if (null == textArray2) {
                return "";
            }
            // testSDK拿到的
            String textContent = r.read(textContentPath + mockId + ".txt");
            JsonArray jsonArray = new JsonParser().parse(textContent).getAsJsonArray();
            try {
                for (JsonElement jsonElement : jsonArray) {
                    JsonObject jsonObject = jsonElement.getAsJsonObject();
                    String text1 = jsonObject.get("text").getAsString();
                    if (!ocrUtil.ExistTest(text1, textArray2, mockId)) {
                        if (text1.replace(" ", "").isEmpty()) {
                            continue;
                        }
                        disShowText += "【" + text1 + "】未展示" + "\n";
                    }
                }
            } catch (Exception e) {
                return disShowText;
            }
        }
        return disShowText;
    }

    public String uploadPicture(String path) {
        try {
            String bucket = comConstant.BUCKET;
            String client_id = comConstant.CLIENT_ID;
            String client_secret = comConstant.CLIENT_SECRET;
            ImageUploadClient client = new ImageUploadClientImpl(bucket, client_id, client_secret);

            File file = new File(path);
            ImageRequest request = new ImageRequest(1, file, false);
            ImageResult res = client.postImage(request);
            return res.getOriginalLink();
        } catch (Exception e) {
//            e.printStackTrace();
            System.out.print("图片上传失败");
            return "";
        }
    }

    /**
     * AI团队提供的ocr模型：https://docs.sankuai.com/mt/vision/infra-horus-docs-web/master/ocr/ocr_ui_compatibility_except_det/
     *
     * @return 识别是否有白屏、纯色屏等异常
     */
    @Override
    public String recognizeUIBug(String PicUrl) {
        try {
            UiCompatExceptRequest requestUI = new UiCompatExceptRequest(PicUrl, "com.sankuai.sigma.compass", "");
            UiCompatExceptResponse result = ocr.recognizeUICompatibilityExcept(requestUI);
            Gson gson = new Gson();
            String jsonStr = gson.toJson(result);
            return jsonStr;
        } catch (Exception e) {
//            log.info(e.getMessage());
            return null;
        }
    }

    /**
     * @param PicUrl
     * @return
     */
    @Override
    public String recognizeUIBugByAI(String PicUrl) {
        try {
            UiCompatExceptRequest requestUI = new UiCompatExceptRequest(PicUrl, "com.sankuai.sigma.compass", "");
            UiCompatExceptResponse result = ocr.recognizeUICompatibilityExcept(requestUI);

            Gson gson = new Gson();
            JsonObject resultJson = gson.fromJson(gson.toJson(result), JsonObject.class);

            // 获取result_info_list数组
            JsonArray resultInfoList = resultJson.getAsJsonObject("result_str").getAsJsonArray("result_info_list");
            if (resultInfoList.size() == 0) {
                // 如果没有异常区域，返回特定信息
                return "没有检测到异常区域";
            }

            BufferedImage image = null;
            try {
                URL imageUrl = new URL(PicUrl); // 将字符串URL转换为URL对象
                image = ImageIO.read(imageUrl); // 使用ImageIO的read方法从URL读取图片
            } catch (IOException e) {
                log.error("图片读取失败{}", e.getMessage());
            }

            JsonArray exceptionsArray = new JsonArray(); // 用于存储异常区域信息的Json数组

            for (JsonElement elem : resultInfoList) {
                JsonObject resultInfo = elem.getAsJsonObject();
                int uiTestType = resultInfo.get("ui_test_type").getAsInt();
                double probability = resultInfo.get("probability").getAsDouble();
                int[] uiTestRegion = gson.fromJson(resultInfo.get("ui_test_region"), int[].class);

                // 标记每个异常区域
                if (image != null) {
                    markRegion2(image, uiTestRegion);
                }

                // 获取异常类别描述
                String exceptionType = getExceptionType(uiTestType);

                // 构造异常区域信息Json对象
                JsonObject exceptionInfo = new JsonObject();
                exceptionInfo.addProperty("uiTestType", uiTestType);
                exceptionInfo.addProperty("exceptionType", exceptionType); // 添加异常类别描述
                exceptionInfo.addProperty("probability", probability);
                exceptionInfo.add("uiTestRegion", gson.toJsonTree(uiTestRegion));
                exceptionsArray.add(exceptionInfo);
            }

// 从URL中提取文件扩展名
            String fileExtension = PicUrl.substring(PicUrl.lastIndexOf(".") + 1);

// 根据文件扩展名确定图片格式，如果无法识别，则默认使用png格式
            String formatName = "png";
            if ("jpg".equalsIgnoreCase(fileExtension) || "jpeg".equalsIgnoreCase(fileExtension)) {
                formatName = "jpg";
            } else if ("png".equalsIgnoreCase(fileExtension)) {
                formatName = "png";
            } else if ("gif".equalsIgnoreCase(fileExtension)) {
                formatName = "gif";
            } else if ("bmp".equalsIgnoreCase(fileExtension)) {
                formatName = "bmp";
            }

// 指定结果图像的保存路径，这里使用原始文件的扩展名
            String resultImagePath = "result_image." + formatName;
            File resultImageFile = new File(resultImagePath);

// 保存图像
            if (image != null) {
                try {
                    ImageIO.write(image, formatName, resultImageFile);
                } catch (IOException e) {
                    e.printStackTrace();
                    System.out.println("保存图像失败");
                }
            }

            // 使用VenusUtil上传图片并获取URL
            VenusUtil venusUtil = new VenusUtil();
            String resultImageUrl = venusUtil.uploadPicture(resultImagePath);

            // 构造返回的信息，包括处理后的图像URL和异常区域信息
            JsonObject responseJson = new JsonObject();
            responseJson.addProperty("resultImageUrl", resultImageUrl); // 返回处理后的图像URL
            responseJson.add("exceptions", exceptionsArray); // 添加异常区域信息

            return gson.toJson(responseJson);
        } catch (Exception e) {
            // log.info(e.getMessage());
            return "处理过程中发生异常：" + e.getMessage();
        }
    }

    /**
     * 根据图片URL检测图片是否模糊，并返回处理结果。
     *
     * @param picUrl 图片的URL。
     * @return 如果图片模糊，返回"模糊"；否则返回"清晰"。
     */
    @Override
    public String blurDetect(String picUrl) {
        String segment = recognizeUiPageParse(picUrl); // 获取分割结果的JSON字符串
        Gson gson = new Gson();
        JsonObject jobject = gson.fromJson(segment, JsonObject.class);

        Mat image = loadMatImageFromURL(picUrl);
        int width = image.cols();
        int height = image.rows();

        // 检查分割结果的code值
        int code = jobject.get("code").getAsInt();
        JsonArray resultInfoList;
        if (code == 0) {
            // 如果分割成功，使用分割结果
            resultInfoList = jobject.getAsJsonObject("result_info").getAsJsonArray("result_info_list");
        } else {
            // 如果分割失败，构造等分区域的JsonArray
            resultInfoList = constructEqualParts(width, height);
        }

        List<JsonObject> blurredRegions = new ArrayList<>();
        for (JsonElement elem : resultInfoList) {
            JsonObject obj = elem.getAsJsonObject();
            JsonArray regionArray = obj.getAsJsonArray("elem_det_region");
            int[] region = gson.fromJson(regionArray, int[].class);
            // region数组四个参数分别是矩形左顶点x，y坐标和高度宽度四个值
            Rect rect = new Rect(region[0], region[1], region[2], region[3]);
            if (isRegionBlurred(image, rect, 300.0)) {
                // 在图片上标记模糊区域，使用红色矩形框标记
                rectangle(image, new Point(rect.x(), rect.y()),
                        new Point(rect.x() + rect.width(), rect.y() + rect.height()), Scalar.RED, 2, LINE_8, 0);
                // 计算模糊度，这里假设已经有一个方法calculateBlurValue返回模糊度的值
                double blurValue = calculateBlurExtentUsingJavaCV(image, rect);
                // 在模糊区域内部或附近标记清晰度值
                String blurText = String.format("清晰度: %.2f", blurValue);
                putText(image, blurText, new Point(rect.x(), rect.y() - 10), FONT_HERSHEY_SIMPLEX, 0.5, Scalar.GREEN, 1,
                        LINE_8, false);

                JsonObject blurredRegion = new JsonObject();
                blurredRegion.addProperty("topx", rect.x());
                blurredRegion.addProperty("topy", rect.y());
                blurredRegion.addProperty("width", rect.width());
                blurredRegion.addProperty("height", rect.height());
                blurredRegions.add(blurredRegion);
            }
        }

        // 保存处理后的图像
        String resultImagePath = "result_image.jpg"; // 指定结果图像的保存路径
        String uploadedImageUrl = saveUploadAndDelete(image, resultImagePath); // 上传图片并获取URL
        JsonObject result = new JsonObject();
        result.addProperty("imageUrl", uploadedImageUrl);
        result.add("blurredRegions", gson.toJsonTree(blurredRegions));

        return gson.toJson(result);
    }

    private Mat loadMatImageFromURL(String picUrl) {
        try {
            URL url = new URL(picUrl);
            BufferedImage image = ImageIO.read(url);
            if (image == null) {
                throw new IOException("无法从URL加载图像: " + picUrl);
            }
            Java2DFrameConverter frameConverter = new Java2DFrameConverter();
            org.bytedeco.javacv.Frame frame = frameConverter.convert(image);

            // 将Frame转换为Mat
            OpenCVFrameConverter.ToMat matConverter = new OpenCVFrameConverter.ToMat();
            Mat mat = matConverter.convert(frame);

            return mat;
        } catch (MalformedURLException e) {
            log.error("URL格式错误: " + e.getMessage());
        } catch (IOException e) {
            log.error("读取图像时发生IO异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("加载图像过程中发生未知异常: " + e.getMessage());
        }
        return null; // 发生异常时返回null
    }

    // 使用JavaCV检测指定区域是否模糊
    private boolean isRegionBlurred(Mat image, Rect rect, double threshold) {
        Mat region = new Mat(image, rect);
        Mat grayImage = new Mat();
        cvtColor(region, grayImage, COLOR_BGR2GRAY);
        Mat laplacianImage = new Mat();
        Laplacian(grayImage, laplacianImage, CV_64F);

        // 使用org.bytedeco.opencv.opencv_core.Mat替代org.opencv.core.MatOfDouble
        Mat mean = new Mat();
        Mat std = new Mat();
        meanStdDev(laplacianImage, mean, std);

        // 获取标准差，并计算方差
        double[] stdArr = new double[]{std.createIndexer().getDouble()};
        double variance = stdArr[0] * stdArr[0];

        return variance < threshold;
    }

    private JsonArray constructEqualParts(int width, int height) {
        JsonArray resultInfoList = new JsonArray();
        // 计算每个区域的宽度和高度
        int partWidth = width / 5;
        int partHeight = height / 10;

        // 遍历高度十等分
        for (int i = 0; i < 10; i++) {
            // 遍历宽度五等分
            for (int j = 0; j < 5; j++) {
                JsonObject part = new JsonObject();
                JsonArray elemDetRegion = new JsonArray();
                int x1 = j * partWidth;
                int y1 = i * partHeight;
                int x2 = x1 + partWidth;
                int y2 = y1 + partHeight;

                elemDetRegion.add(new JsonPrimitive(x1)); // x1
                elemDetRegion.add(new JsonPrimitive(y1)); // y1
                elemDetRegion.add(new JsonPrimitive(x2)); // x2
                elemDetRegion.add(new JsonPrimitive(y2)); // y2
                part.add("elem_det_region", elemDetRegion);
                resultInfoList.add(part);
            }
        }
        return resultInfoList;
    }

    /**
     * 保存并上传图像，如果上传成功则删除本地文件。
     *
     * @param matImage 处理后的图像。
     * @param baseDir  保存图像的基本目录。
     * @return 上传后的图像URL，如果上传失败则返回null。
     */
    public String saveUploadAndDelete(Mat matImage, String baseDir) {
        String baseFilename = "result_image";
        String extension = ".png";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        String resultImagePath = baseDir + baseFilename + "_" + timestamp + extension;
        File file = new File(resultImagePath);
        int counter = 1;
        while (file.exists()) {
            resultImagePath = baseDir + baseFilename + "_" + timestamp + "_" + counter + extension;
            file = new File(resultImagePath);
            counter++;
        }

        // 保存处理后的图像
        boolean isSuccess = imwrite(resultImagePath, matImage);
        if (!isSuccess) {
            System.out.println("保存图像失败");
            return null;
        }

        // 上传图片并获取URL
        VenusUtil venusUtil = new VenusUtil();
        String uploadedImageUrl = venusUtil.uploadPicture(resultImagePath);
        if (uploadedImageUrl != null) {
            System.out.println("上传成功，图像URL: " + uploadedImageUrl);
            // 上传成功后删除原图
            if (!file.delete()) {
                System.out.println("删除本地图像文件失败: " + resultImagePath);
            }
        } else {
            System.out.println("上传失败");
        }

        return uploadedImageUrl;
    }

    private double calculateBlurExtentUsingJavaCV(Mat image, Rect rect) {
        // 使用JavaCV提取指定区域
        Mat region = new Mat(image, rect);

        // 转换为灰度图
        Mat grayImage = new Mat();
        cvtColor(region, grayImage, COLOR_BGR2GRAY);

        // 应用拉普拉斯算子
        Mat laplacianImage = new Mat();
        Laplacian(grayImage, laplacianImage, CV_64F);

        // 计算标准差
        Mat mean = new Mat();
        Mat std = new Mat();
        meanStdDev(laplacianImage, mean, std);

        // 获取标准差，并计算方差
        double[] stdArr = new double[]{std.createIndexer().getDouble()};
        double variance = stdArr[0] * stdArr[0];
        return variance;
    }

    /**
     * @param picUrl
     * @return
     */
    @Override
    public String blurDetectByAI(String picUrl, int imageType, String type, float lineThreshold) throws TException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", picUrl);
        jsonObject.put("source", "com.sankuai.sigma.compass");
        jsonObject.put("image_type", imageType);
        jsonObject.put("type", type);
        jsonObject.put("line_threshold", lineThreshold);
        String req = jsonObject.toString();
        return ocr.docImageQuality(req);
    }

    private String getExceptionType(int uiTestType) {
        switch (uiTestType) {
            case -1:
                return "白屏";
            case -2:
                return "页面显示异常（纯色背景）";
            case -3:
                return "线框文字不居中";
            case -4:
                return "文字缺失像素缺失";
            case -5:
                return "线框线缺失";
            case -6:
                return "文字显示超出范围（暂时不支持）";
            default:
                return "未知异常";
        }
    }

    private void markRegion(BufferedImage image, int[] region) throws IOException {
        Graphics2D g2d = image.createGraphics();

        // 保存当前的图形上下文设置（颜色）
        Color originalColor = g2d.getColor();

        try {
            // 设置绘图颜色为红色
            g2d.setColor(Color.RED);
            // 直接使用width和height绘制矩形框来标记异常区域
            g2d.drawRect(region[0], region[1], region[2], region[3]);
        } finally {
            // 恢复图形上下文的初始设置
            g2d.setColor(originalColor);
            g2d.dispose();
        }
    }

    private void markRegion2(BufferedImage image, int[] region) throws IOException {
        Graphics2D g2d = image.createGraphics();

        // 保存当前的图形上下文设置（颜色、透明度和笔刷）
        Color originalColor = g2d.getColor();
        Composite originalComposite = g2d.getComposite();
        Stroke originalStroke = g2d.getStroke();

        try {
            // 设置颜色为红色
            g2d.setColor(Color.RED);
            // 设置透明度，增加可见性，其中0.5f表示半透明
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));
            // 设置笔刷以加粗边框，例如设置笔刷宽度为3
            g2d.setStroke(new BasicStroke(3));
            // 直接使用width和height填充矩形来标记异常区域
            g2d.fillRect(region[0], region[1], region[2], region[3]);
            // 绘制加粗的边框
            g2d.setColor(Color.RED); // 可以重新设置颜色，如果需要不同于填充色的边框色
            g2d.drawRect(region[0], region[1], region[2], region[3]);
        } finally {
            // 恢复图形上下文的初始设置
            g2d.setColor(originalColor);
            g2d.setComposite(originalComposite);
            g2d.setStroke(originalStroke);
            g2d.dispose();
        }
    }

    /**
     * AI团队提供的ocr模型，UI图片比对测试微服务：https://docs.sankuai.com/mt/vision/infra-horus-docs-web/master/ocr/ocr_ui_compare_thrift/
     *
     * @return 图片比对结果
     */
    @Override
    public String diff(String basePic, String testPic, String model, String cutModel, String lineConfidence,
                       String ignoreArea) throws TException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("burl", basePic);
        jsonObject.put("rurl", testPic);
        jsonObject.put("compareway", model);
        jsonObject.put("inputform", cutModel);
        jsonObject.put("textlineconfidence", lineConfidence);
        jsonObject.put("ignoreelementarea", ignoreArea);
        jsonObject.put("source", "com.sankuai.sigma.compass");
        String req = jsonObject.toString();
//        String res = ocr.UICompare(req);
        UICompareThriftRequest requestDiff = new UICompareThriftRequest(basePic, testPic, model, cutModel,
                lineConfidence, ignoreArea, "com.sankuai.sigma.compass");
        UICompareThriftResponse responseDiff = ocr.UICompareThrift(requestDiff);
        log.info("responseDiff: " + responseDiff.toString());
        Gson gson = new Gson();
        String jsonStr = gson.toJson(responseDiff);
        return jsonStr;
    }

    /**
     * AI团队提供的ocr模型，识别各种页面元素，比如返回按钮、关闭按钮：https://docs.sankuai.com/mt/vision/infra-horus-docs-web/master/ocr/ocr_ui_page_parse/
     *
     * @param picUrl
     * @return 各种元素的坐标，透传parseUiPageInfo的返回内容
     */
    @Override
    public String recognizeUiPageParse(String picUrl) {
        try {
            UiPageParseRequest request = new UiPageParseRequest();
            request.setUrl(picUrl);
            request.setSource("com.sankuai.sigma.compass");
            UiPageParseResponse result = ocr.parseUiPageInfo(request);
            Gson gson = new Gson();
            String jsonStr = gson.toJson(result);
            return jsonStr;
        } catch (Exception e) {
            log.error("recognizeCloseIcon--parseUiPageInfo 请求异常");
            log.error(e.getMessage());
            return null;
        }

    }

    @Override
    public String[] recognizeUIBugByOnePic(String picUrl) {
        String[] recognizeResult = new String[2];
        String result = "";
        String urlMerged = System.getProperty("user.dir") + "/tempUrl" + System.currentTimeMillis() + ".png";
        File mergeFile = new File(urlMerged);
        try {
            mergeFile.createNewFile();
        } catch (IOException e) {
            System.out.println("创建文件时发生错误。");
            e.printStackTrace();
        }
        HashMap<Integer, String> uiAutoTestExceptionType = new HashMap<Integer, String>();
        uiAutoTestExceptionType.put(-1, "白屏");
        uiAutoTestExceptionType.put(-2, "页面显示异常（纯色背景）");
        uiAutoTestExceptionType.put(-3, "线框文字不居中");
        uiAutoTestExceptionType.put(-4, "文字缺失像素缺失");
        uiAutoTestExceptionType.put(-5, "线框线缺失");
        uiAutoTestExceptionType.put(-6, "文字显示超出范围");
        if (picUrl.startsWith("http") || picUrl.startsWith("https")) {
            String ocr_result = recognizeUIBug(picUrl);
            JsonArray ocr_result_arr = new JsonParser().parse(ocr_result).getAsJsonObject().get("result_str")
                    .getAsJsonObject().get("result_info_list").getAsJsonArray();
            for (int i = 0; i < ocr_result_arr.size(); i++) {
                int uiTestType = ocr_result_arr.get(i).getAsJsonObject().get("ui_test_type").getAsInt();
                String uiTestRegion = ocr_result_arr.get(i).getAsJsonObject().get("ui_test_region").toString();
                result = result + i + ":" + uiAutoTestExceptionType.get(uiTestType) + ",区域：" + uiTestRegion + "\n";
                if (i == 0) {
                    urlMerged = addWaterMark(picUrl, urlMerged, Color.red, uiTestRegion, String.valueOf(i));
                } else {
                    urlMerged = addWaterMark(urlMerged, urlMerged, Color.red, uiTestRegion, String.valueOf(i));
                }
            }
            if (urlMerged != null && !urlMerged.isEmpty()) {
                urlMerged = uploadPicture(urlMerged);
            }
        }
        recognizeResult[0] = result;
        recognizeResult[1] = urlMerged;
        if (mergeFile.exists()) {
            mergeFile.delete();
        }
        return recognizeResult;
    }

    @Override
    public String[] recognizeP0UIBugByOnePic(String picUrl) {
        String[] recognizeResult = new String[2];
        String result = "";
        String urlMerged = System.getProperty("user.dir") + "/tempUrl" + System.currentTimeMillis() + ".png";
        File mergeFile = new File(urlMerged);
        try {
            mergeFile.createNewFile();
        } catch (IOException e) {
            System.out.println("创建文件时发生错误。");
            e.printStackTrace();
        }
        HashMap<Integer, String> uiAutoTestExceptionType = new HashMap<Integer, String>();
        uiAutoTestExceptionType.put(-1, "白屏");
        uiAutoTestExceptionType.put(-2, "页面显示异常（纯色背景）");
        uiAutoTestExceptionType.put(-3, "线框文字不居中");
        uiAutoTestExceptionType.put(-4, "文字缺失像素缺失");
        uiAutoTestExceptionType.put(-5, "线框线缺失");
        uiAutoTestExceptionType.put(-6, "文字显示超出范围");
        if (picUrl.startsWith("http") || picUrl.startsWith("https")) {
            String ocr_result = null;
            final int maxRetries = 3;
            int attempt = 0;
            while (attempt < maxRetries){
                attempt++;
                ocr_result = recognizeUIBug(picUrl);
                log.info("ocrresult:" + ocr_result);
                // 先判空
                if (ocr_result == null) {
                    // 如果结果为 null，说明这一次识别完全失败，进入下一次重试
                    log.warn("[OCR Retry] 第 " + attempt + " 次调用返回 null, 准备重试...");
                    continue;
                }
                // 此时 ocr_result 不为空，需要判断是否为合法 JSON 以及是否 code=0（假设成功码为0）
                JsonObject root = JsonParser.parseString(ocr_result).getAsJsonObject();
                if (!root.has("code")) {
                    // 如果连 "code" 字段都没有，就认为不合法
                    log.warn("[OCR Retry] 第 " + attempt + " 次调用结果不符合预期, 无 'code' 字段, 准备重试...");
                    continue;
                }
                int code = root.get("code").getAsInt();
                // 判断 code 是否为成功
                if (code != 0) {
                    // 说明是异常返回（如 -11），根据需要打印或记录错误信息
                    log.warn("[OCR Retry] 第 " + attempt + " 次调用 code=" + code + " (失败), 准备重试...");
                    continue;
                }
                break;
            }
            if (attempt == maxRetries) {
                log.warn("多次尝试后OCR服务依然异常");
                return null;
            }
            JsonArray ocr_result_arr = new JsonParser().parse(ocr_result).getAsJsonObject().get("result_str")
                    .getAsJsonObject().get("result_info_list").getAsJsonArray();
            for (int i = 0; i < ocr_result_arr.size(); i++) {
                int uiTestType = ocr_result_arr.get(i).getAsJsonObject().get("ui_test_type").getAsInt();
                if (uiTestType == -1 || uiTestType == -2){
                    String uiTestRegion = ocr_result_arr.get(i).getAsJsonObject().get("ui_test_region").toString();
                    result = result + i + ":" + uiAutoTestExceptionType.get(uiTestType) + ",区域：" + uiTestRegion + "\n";
                    if (i == 0) {
                        urlMerged = addWaterMark(picUrl, urlMerged, Color.red, uiTestRegion, String.valueOf(i));
                    } else {
                        urlMerged = addWaterMark(urlMerged, urlMerged, Color.red, uiTestRegion, String.valueOf(i));
                    }
                }
            }
            if (urlMerged != null && !urlMerged.isEmpty()) {
                urlMerged = uploadPicture(urlMerged);
            }
        }
        recognizeResult[0] = result;
        recognizeResult[1] = urlMerged;
        if (mergeFile.exists()) {
            mergeFile.delete();
        }
        return recognizeResult;
    }

    @Override
    public String[] recognizeTemplateUIBug(String mockId, String id, String template, int jobId) {
        String[] recognizeResult = new String[2];
        String result = "";
        String urlMerged = "";
        HashMap<Integer, String> uiAutoTestExceptionType = new HashMap<Integer, String>();
        uiAutoTestExceptionType.put(-1, "白屏");
        uiAutoTestExceptionType.put(-2, "页面显示异常（纯色背景）");
        uiAutoTestExceptionType.put(-3, "线框文字不居中");
        uiAutoTestExceptionType.put(-4, "文字缺失像素缺失");
        uiAutoTestExceptionType.put(-5, "线框线缺失");
        uiAutoTestExceptionType.put(-6, "文字显示超出范围");
        String picPath = comConstant.OUT_PUT + jobId + "/" + id + "/link/" + template + "/abnormal/" + mockId + ".txt";
        File file = new File(picPath);
        if (file.exists()) {
            String url = new FileUtil().read(picPath);
            String ocr_result = recognizeUIBug(url);
            JsonArray ocr_result_arr = new JsonParser().parse(ocr_result).getAsJsonObject().get("result_str")
                    .getAsJsonObject().get("result_info_list").getAsJsonArray();
            // ocr_result_arr有值说明有异常，需要进行图片合并处理
            if (ocr_result_arr.size() > 0) {
                urlMerged = System.getProperty("user.dir") + "/urlMerged" + System.currentTimeMillis() + ".png";
                File mergeFile = new File(urlMerged);
                try {
                    mergeFile.createNewFile();
                } catch (IOException e) {
                    System.out.println("创建文件时发生错误。");
                    e.printStackTrace();
                }
                for (int i = 0; i < ocr_result_arr.size(); i++) {
                    int uiTestType = ocr_result_arr.get(i).getAsJsonObject().get("ui_test_type").getAsInt();
                    String uiTestRegion = ocr_result_arr.get(i).getAsJsonObject().get("ui_test_region").toString();
                    result = result + i + ":" + uiAutoTestExceptionType.get(uiTestType) + ",区域：" + uiTestRegion + " ";
                    if (i == 0) {
                        urlMerged = addWaterMark(url, urlMerged, Color.red, uiTestRegion, String.valueOf(i));
                    } else {
                        urlMerged = addWaterMark(urlMerged, urlMerged, Color.red, uiTestRegion, String.valueOf(i));
                    }
                }
                if (urlMerged != null && !urlMerged.isEmpty()) {
                    urlMerged = uploadPicture(urlMerged);
                }
                System.out.println("result:" + result + "\n" + "urlMerged:" + urlMerged);
                if (mergeFile.exists()) {
                    mergeFile.delete();
                }
            }
        }
        recognizeResult[0] = result;
        recognizeResult[1] = urlMerged;
        return recognizeResult;
    }

    public String addWaterMark(String srcImg, String targetImg, Color markRectColor, String uiRegion, String waterMarkContent) {
        try {
            // 读取原图片信息
            BufferedImage srcImage;
            if (srcImg.startsWith("http") || srcImg.startsWith("https")) {
                URL url = new URL(srcImg);
                InputStream is = url.openStream();
                srcImage = ImageIO.read(is);
                is.close();
            } else {
                File srcImgFile = new File(srcImg);//得到文件
                srcImage = ImageIO.read(srcImgFile);//文件转化为图片
            }
            int srcImgWidth = srcImage.getWidth(null);//获取图片的宽
            int srcImgHeight = srcImage.getHeight(null);//获取图片的高

            // 加水印
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = bufImg.createGraphics();
            g.setStroke(new BasicStroke(2)); //设置画线的宽度
            g.drawImage(srcImage, 0, 0, srcImgWidth, srcImgHeight, null);

            // 画出边框
            uiRegion = uiRegion.replace("[", "").replace("]", "");
            String[] uiRegionArr = uiRegion.split(",");
            int x = Integer.parseInt(uiRegionArr[0]);
            int y = Integer.parseInt(uiRegionArr[1]);
            int width = Integer.parseInt(uiRegionArr[2]);
            int height = Integer.parseInt(uiRegionArr[3]);
            g.setStroke(new BasicStroke(2));
            g.setColor(markRectColor);
            g.drawRoundRect(x, y, width, height, 0, 0);

            // 添加文字，文字优先展示在边框外
            g.setColor(Color.MAGENTA);
            int markX = x - 10;
            if (markX < 0) {
                markX += 20;
            }
            final int FONT_SIZE = 30;
            g.setFont(new Font("Arial", Font.PLAIN, FONT_SIZE));
            g.drawString(waterMarkContent, markX, y + 15);

            // 释放资源
            g.dispose();
            // 输出图片
            FileOutputStream outImgStream = new FileOutputStream(targetImg);
            ImageIO.write(bufImg, "png", outImgStream);
            System.out.println("添加水印完成");
            outImgStream.flush();
            outImgStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return targetImg;
    }

    @Override
    public Resp getTextCenterPosByOcr(String text, String picUrl) {
        Resp resp = new Resp();
        if (text.isEmpty() || picUrl.isEmpty()) {
            resp.setCode(-1);
            resp.setMsg("Error!!Text or picUrl can't be empty!");
            return resp;
        }
        String uiInfo = recognizeUiPageParse(picUrl);
        JsonObject uiInfoJson = new JsonParser().parse(uiInfo).getAsJsonObject();
        JsonArray elemList = uiInfoJson.getAsJsonObject("result_info").getAsJsonArray("result_info_list");
        for (int i = 0; i < elemList.size(); i++) {
            JsonObject elem = elemList.get(i).getAsJsonObject();
            if (elem.has("elem_detail_info") && elem.get("elem_detail_info").getAsString().contains(text)) {
                JsonArray pos = elem.get("elem_det_region").getAsJsonArray();
                if (pos.size() > 3) {
                    float poscx = (pos.get(0).getAsFloat() + pos.get(2).getAsFloat()) / 2;
                    float poscy = (pos.get(1).getAsFloat() + pos.get(3).getAsFloat()) / 2;
                    resp.setCode(200);
                    resp.setMsg("success");
                    resp.setData(poscx + "," + poscy);
                    return resp;
                }

            }
        }
        resp.setCode(-1);
        resp.setMsg("未找到指定文本：" + text);
        return resp;
    }

    /**
     *
     * @return 识别是否有白屏、纯色屏等异常
     */
    @Override
    public String UiPageParseRequest(String PicUrl) {
        try {
            UiPageParseRequest requestUI = new UiPageParseRequest(PicUrl, "com.sankuai.sigma.compass", "");
            UiPageParseResponse result = ocr.parseUiPageInfo(requestUI);
            Gson gson = new Gson();
            return gson.toJson(result);
        } catch (Exception e) {
//            log.info(e.getMessage());
            return null;
        }
    }

    @Override
    public Resp getMatchPicLocation(String baseUrl, String targetUrl) throws IOException, InterruptedException {

        Resp resp = new Resp();

        //初始化参数
        List<Double> positionList = new ArrayList<>();
        double confidence = 0.0;

        double confidenceThreshold = 0.8;                         // 可根据需要调整
        double sizeThreshold = 0.2;                               // 可根据需要调整
        int maxAttempts = 3;
        String fullImageUrl = baseUrl;
        String partialImageUrl = targetUrl;
        String currentDirectory = System.getProperty("user.dir");
        logger.info("Current working directory: " + currentDirectory);

        String command = String.format(
                    "python3 ../image_compare.py %s %s --confidence_threshold %.2f --size_threshold %.2f --max_attempts %d",
                fullImageUrl, partialImageUrl, confidenceThreshold, sizeThreshold, maxAttempts
            );
//        String command = String.format("python3 script/VisionService/image_compare.py  %s %s --confidence_threshold %.2f --size_threshold %.2f --max_attempts %d",
//                fullImageUrl, partialImageUrl, confidenceThreshold, sizeThreshold, maxAttempts);

        logger.info("执行命令: " + command);
        Process p = Runtime.getRuntime().exec(command);

        // 读取Python脚本输出
        BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
        String line;
        BufferedReader stdError = new BufferedReader(new InputStreamReader(p.getErrorStream()));
        logger.info("错误信息:");
        while ((line = stdError.readLine()) != null) {
            logger.error(line);
        }
        while ((line = in.readLine()) != null) {
            logger.info("in.readLine()不为空");
            line = line.trim();
            logger.info("singleLine is : " + line);
            if (line.equals("NO_MATCH")) {
                logger.info("未找到有效匹配。");
                break;
            }
            String[] parts = line.split(",");
            if (parts.length != 9) {
                logger.info("输出格式不正确：" + line);
                continue;
            }

            try {
                double x1 = Double.parseDouble(parts[0].trim());
                double y1 = Double.parseDouble(parts[1].trim());
                double x2 = Double.parseDouble(parts[2].trim());
                double y2 = Double.parseDouble(parts[3].trim());
                double x3 = Double.parseDouble(parts[4].trim());
                double y3 = Double.parseDouble(parts[5].trim());
                double x4 = Double.parseDouble(parts[6].trim());
                double y4 = Double.parseDouble(parts[7].trim());
                confidence = Double.parseDouble(parts[8].trim());

                // 添加角点坐标到列表
                positionList.add(x1);
                positionList.add(y1);
                positionList.add(x2);
                positionList.add(y2);
                positionList.add(x3);
                positionList.add(y3);
                positionList.add(x4);
                positionList.add(y4);
            } catch (NumberFormatException e) {
                logger.info("解析数字时发生错误: " + e.getMessage());
            }

            int exitCode = p.waitFor();
            logger.info("进程退出码: " + exitCode);
        }
        in.close();
        resp.setCode(200);
        resp.setMsg("success");
        resp.setData(positionList);
        return resp;
    }



}
