package com.sankuai.mdp.compass.service.crane;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.entity.*;
import com.sankuai.mdp.compass.conan.service.*;
import com.sankuai.mdp.compass.entity.CheckOrder;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import com.sankuai.mdp.compass.queryPipeline.entity.Pipeline;
import com.sankuai.mdp.compass.queryPipeline.service.PipelineService;
import com.sankuai.mdp.compass.robust.entity.RobustAutoConfig;
import com.sankuai.mdp.compass.robust.entity.RobustBuild;
import com.sankuai.mdp.compass.robust.entity.RobustResult;
import com.sankuai.mdp.compass.robust.entity.RobustTriggerJob;
import com.sankuai.mdp.compass.robust.mapper.RobustAutoConfigMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustBuildMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustResultMapper;
import com.sankuai.mdp.compass.robust.mapper.RobustTriggerJobMapper;
import com.sankuai.mdp.compass.robust.service.RobustBuildService;
import com.sankuai.mdp.compass.robust.service.RobustResultService;
import com.sankuai.mdp.compass.service.CheckOrderService;
import com.sankuai.mdp.compass.service.DynamicProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

import static com.sankuai.mdp.compass.common.utils.SigmaUtil.getBranchStatus;

@Slf4j
@Service
@CraneConfiguration
public class ScheduleTask {
    @Autowired
    CheckOrderService checkOrderService;

    @Autowired
    DynamicProcessService dynamicProcessService;

    @Autowired
    CompatilityMapper compatilityMapper;

    @Autowired
    ConanCaseService conanCaseService;
    @Autowired
    MgeJobService mgeJobService;

    @Autowired
    ConanJobService conanJobService;

    @Autowired
    MgeTaskService mgeTaskService;

    @Autowired
    MgeCaseService mgeCaseService;

    @Autowired
    RobustResultService robustResultService;

    @Autowired
    RobustResultMapper robustResultMapper;

    @Autowired
    RobustBuildService robustBuildService;

    @Autowired
    RobustAutoConfigMapper robustAutoConfigMapper;

    @Autowired
    RobustTriggerJobMapper robustTriggerJobMapper;

    @Autowired
    RobustBuildMapper robustBuildMapper;

    @Autowired
    SigmaUtil sigmaUtil;

    @Autowired
    PipelineService pipelineService;

    ConanUtil conanUtil = new ConanUtil();
    TalosUtil talosUtil = new TalosUtil();
    OnesUtil onesUtil = new OnesUtil();
    TTUtil ttUtil = new TTUtil();
    JsonParser jsonParser = new JsonParser();
    private static String fileName = "./result.xlsx";
    public static final String CRASH_DETAIL_API = "https://crash.sankuai.com/token/api/v2/crashTrack/crash/detail?access_token=5a1b8b8ff4eed21d025ca72f&size=1&type=crash&eq=id%2CGUID-";
    public static final String SEND_NOTIFICATION_SUCCESS="0000";

    @Crane("compass_check_dync_template_event")
    public void scheduled() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            Date date = sdf.parse(sdf.format(new Date()));
            List<CheckOrder> list = checkOrderService.getPending(date);
            for (int i = 0; i < list.size(); i++) {
                CheckOrder checkOrder = list.get(i);
                Integer jobId = checkOrder.getJobId();
                CompatilityJob compatilityJob = compatilityMapper.selectById(jobId);
                String sql = checkOrder.getSqlStr();
                talosUtil.submit(sql, fileName);
                MSSUtil mssUtil = new MSSUtil();
                String objectUrl = mssUtil.uploadExcel(fileName);
                if (null != objectUrl) {
                    compatilityJob.setDownloadUrl(objectUrl.split("autotest")[1]);
                    compatilityMapper.updateById(compatilityJob);
                }
                checkOrder.setStatus("finished");
                checkOrderService.updateById(checkOrder);
            }
        } catch (Exception e) {

        }
    }

    @Crane("compass_operate_autotest_failing")
    public void daliyCheckAutotestResult() {
        if (EnvUtil.isOnline()) {
            try {
                Date date = new Date();
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                String startTime = df.format(new Date(date.getTime() - (long) 1 * 24 * 60 * 60 * 1000));

                String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
                Calendar cal = Calendar.getInstance();
                cal.setTime(date);
                Integer days = 1;
                Integer w = cal.get(Calendar.DAY_OF_WEEK) - 1;
                log.info("----w：" + w);
                if (0 == w || 6 == w) {
                    return;
                }
                if (1 == w) {
                    startTime = df.format(new Date(date.getTime() - (long) 3 * 24 * 60 * 60 * 1000));
                    days = 3;
                }

                log.info("开始日常检查自动化运行结果");
                createOnes(startTime, df.format(date), days, "iOS");
                createOnes(startTime, df.format(date), days, "Android");
            } catch (Exception e) {
                log.info("日常检查自动化运行结果失败", e);
            }
        }
    }

    public void createOnes(String startTime, String endTime, Integer days, String platform) {
        String dxDesc = "";
        String summary = "";
        if (days > 1) {
            dxDesc = "【UI自动化" + platform + "】" + startTime + " 至 " + endTime + " Case执行通过率低于60%";
            summary = "【" + platform + "】" + startTime + " Case适配";

        } else {
            dxDesc = "【UI自动化" + platform + "】" + startTime + " Case执行通过率低于60%";
            summary = "【" + platform + "】" + startTime + " 至 " + endTime + " Case适配";
        }

        List<GroupCase> failedCase = conanCaseService.getPassRateLowerCase(startTime, endTime, platform);
        log.info("failedCase" + failedCase);
        JsonObject needIssue = new JsonObject();
        for (int i = 0; i < failedCase.size(); i++) {
            String owner = failedCase.get(i).getCaseOwner();
            JsonObject tempJson = new JsonObject();
            tempJson.addProperty("caseName", failedCase.get(i).getCaseName());
            tempJson.addProperty("className", failedCase.get(i).getCaseClass());
            tempJson.addProperty("passRate", failedCase.get(i).getPassRate());
            if (null != owner) {
                if (needIssue.has(owner)) {
                    needIssue.get(owner).getAsJsonArray().add(tempJson);

                } else {
                    JsonArray jsonArray = new JsonArray();
                    needIssue.add(owner, jsonArray);
                    needIssue.get(owner).getAsJsonArray().add(tempJson);
                }
            }
        }
        Integer len = needIssue.size();
        if (len > 0) {
            JsonObject parentIssueParams = new JsonObject();
            parentIssueParams.addProperty("summary", dxDesc);
            parentIssueParams.addProperty("assignee", "PTQA");
            parentIssueParams.addProperty("description", "");
            String parentId = "";
            parentIssueParams.addProperty("parentId", parentId);

            String response = onesUtil.createIssue(parentIssueParams);
            if (null != response) {
                JsonObject jsonObject = new JsonParser().parse(response).getAsJsonObject();
                String id = (jsonObject.get("data")).getAsJsonObject().get("id").getAsString();
                parentId = id;

                for (Map.Entry<String, JsonElement> entry : needIssue.entrySet()) {
                    JsonElement value = entry.getValue();
                    if (value.isJsonArray()) {
                        String owner = entry.getKey();
                        JsonArray caseList = value.getAsJsonArray();
                        String description = "";
                        JsonObject params = new JsonObject();

                        for (int i = 0; i < caseList.size(); i++) {
                            String caseName = (caseList.get(i)).getAsJsonObject().get("caseName").getAsString();
                            String className = (caseList.get(i)).getAsJsonObject().get("className").getAsString();
                            Double passRate = (caseList.get(i)).getAsJsonObject().get("passRate").getAsDouble() * 100;
                            description = description + "<p>Case： " + caseName + "通过率为" + passRate.toString() + "%</p>";
                            String reportUrl = "https://conan.sankuai.com/v2/auto-function/appium/report/detail/";
                            description = description + "<p>【失败报告】：";
                            List<Integer> taskIdList = conanCaseService.getFailedCasesTaskIdList(startTime, platform, caseName, className);
                            for (int t = 0; t < taskIdList.size(); t++) {
                                Integer taskId = taskIdList.get(t);
                                String reportDesc = reportUrl + taskId + "?class=" + className + "&method=" + caseName;
                                description = description + "<a href=\"" + reportDesc + "\">报告" + t + "  </a>";

                            }
                            description = description + "</p>";
                        }
                        params.addProperty("summary", summary);
                        params.addProperty("assignee", owner);
                        params.addProperty("description", description);
                        params.addProperty("parentId", parentId);
                        onesUtil.createIssue(params);
                    }
                }

            }
        }
    }

    @Crane("compass_update_autotest_crash")
    public void collectUICrash() {
        if (EnvUtil.isOnline()) {
            Date date = new Date();
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String startTime = df.format(new Date(date.getTime() - (long) 1 * 24 * 60 * 60 * 1000));
            String endTime = df.format(date);
            conanJobService.collectUICrash(startTime, endTime);
        }
    }

    @Crane("compass_check_conan_ones")
    public void checkConanOnes() {
        if (EnvUtil.isOnline()) {
            Date date = new Date();
            Date startDate = new Date(date.getTime() - (long) 7 * 24 * 60 * 60 * 1000);
            robustResultService.checkConanOnes(startDate, date);
            //// TODO: 2022/12/27 遍历工单写评论 
//            RobustResult robustResult =  robustResultMapper.selectOne(new LambdaQueryWrapper<>());
//            robustResultService.getCrashResult(robustResult);
        }
    }


    @Crane("compass_mge_autotest")
    public void mgeAutoCheck() throws Exception {
        if (EnvUtil.isOnline()) {
            String version = mgeTaskService.version();
            List<MgeCase> mgeCaseList = mgeCaseService.select();
            int jobId = mgeJobService.setJobStart();
            int status = mgeTaskService.createASearch(mgeTaskService.startDate(), mgeTaskService.endDate(), version, jobId);
            mgeJobService.setJobEnd(status, jobId);
            mgeTaskService.readXlsNew(mgeCaseList, version, jobId);
            mgeTaskService.delete();
            mgeTaskService.post(status);
            mgeTaskService.postToGroup(status);
        }
    }

    @Crane("compass_mge_autotest_post")
    public void mgeAutoCheckResultPost() throws Exception {
        if (EnvUtil.isOnline()) {
            mgeTaskService.postToPersonalMgeResult(1, "lizhen39");
        }
    }

    @Crane("compass_mge_autotest_post_to_group")
    public void mgeAutoCheckResultPostToGroup() throws Exception {
        if (EnvUtil.isOnline()) {
            mgeTaskService.postToGroupMgeResult(1);
        }
    }


    @Crane("compass_mge_autotest_rerun")
    public void mgeAutoCheckRerun() throws Exception {
        if (EnvUtil.isOnline() && mgeJobService.checkRerun()) {
            String version = mgeTaskService.version();
            List<MgeCase> mgeCaseList = mgeCaseService.select();
            int jobId = mgeJobService.setJobStart();
            int status = mgeTaskService.createASearch(mgeTaskService.startDate(), mgeTaskService.endDate(), version, jobId);
            mgeJobService.setJobEnd(status, jobId);
            mgeTaskService.readXlsNew(mgeCaseList, version, jobId);
            mgeTaskService.delete();
        }
    }

    @Crane("compass_operate_ui_autotest_result")
    public void handleUIAutotestIssue() {
        if (EnvUtil.isOnline()) {
            String name = "美团App UI自动化问题值班";
            String itemId = "8310";
            String response = ttUtil.getTemplate(itemId);
            String content = null;
            try {
                JsonObject jsonObject = (JsonObject) new JsonParser().parse(response);
                if (200 == jsonObject.get("code").getAsInt() && null != jsonObject.getAsJsonObject("data")) {
                    content = jsonObject.getAsJsonObject("data").get("content").getAsString();
                }
            } catch (Exception e) {
            }
            ttUtil.create(name, content, itemId);
        }
    }

    @Crane("compass_operate_crash")
    public void handleCrashIssue() {
//        if (EnvUtil.isOnline()) {
//            String name = "美团App Crash指标观测值班";
//            String itemId = "8456";
//            String response = ttUtil.getTemplate(itemId);
//            String content = null;
//            try {
//                JsonObject jsonObject = (JsonObject) new JsonParser().parse(response);
//                if (200 == jsonObject.get("code").getAsInt() && null != jsonObject.getAsJsonObject("data")) {
//                    content = jsonObject.getAsJsonObject("data").get("content").getAsString();
//                }
//            } catch (Exception e) {
//            }
//            ttUtil.create(name, content, itemId);
//        }
        return;
    }

    @Crane("compass_process_notice")
    public void handleProcessNotice() {
        return;
//        dynamicProcessService.handleProcessNotice();
    }

    @Crane("compass_robust_auto_trigger")
    public void robustAutoTrigger() {
        triggerRobustnessTest();
    }


    @Crane("query_pipeline")
    public void fetchData(){
        log.info("开始执行定时任务");
        LocalDate date = LocalDate.now();
        ArrayList<Pipeline> dataList= pipelineService.fetchPipeWeekData(date);
        if(!dataList.isEmpty()){
            log.info("扫描Sigma数据库数据不为空，开始插入compass数据库...");
            pipelineService.insertPipeWeekData(dataList);
        }
    }

    public void triggerRobustnessTest() {
        final int REGRESSION_PROCESS = 7;
        final int NORMAL = 0;
        JsonObject jsonInfo = getBranchStatus("Android", "meituan");
        final String triggerTime = "regression";
        log.info("健壮性回归测试定时时间到，开始检测本周是否是回归测试周...\n");

        boolean isRegressionTime = false;
        JsonObject regressionJsonInfo = null;
        JsonArray recordList = jsonInfo.getAsJsonObject("data").getAsJsonArray("records");
        int recordNum = recordList.size();
        for (int i = 0; i <= recordNum - 1; i++) {
            JsonObject thisRecord = (JsonObject) recordList.get(i);
            try {
                if (thisRecord.get("processType").getAsInt() == REGRESSION_PROCESS
                        && thisRecord.get("iterationType").getAsInt() == NORMAL) {
                    isRegressionTime = true;
                    regressionJsonInfo = thisRecord;
                    break;
                }
            } catch (Exception e) {
                log.error(e.toString());
            }

        }
        if (!isRegressionTime) {
            log.info("本周无回归测试!\n");
            return;
        }
        log.info("本周有回归测试,开始检测之前是否进行过健壮性测试...\n");

        QueryWrapper<RobustAutoConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trigger_time", triggerTime);
        queryWrapper.eq("app_name", "meituan");
        List<RobustAutoConfig> triggerTimeRes = robustAutoConfigMapper.selectList(queryWrapper);
        List idOfRegressionList = new ArrayList<>();
        if (triggerTimeRes != null && triggerTimeRes.size() != 0) {
            for (int i = 0; i < triggerTimeRes.size(); i++) {
                idOfRegressionList.add(triggerTimeRes.get(i).getId());
            }
        }
        log.info("属于美团App回归测试的 auto_config_id 是:" + Arrays.toString(idOfRegressionList.toArray()));

        boolean alreadyTriggered = false;
        for (int i = 0; i <= idOfRegressionList.size() - 1; i++) {
            QueryWrapper<RobustTriggerJob> queryWrapper2 = new QueryWrapper<>();
            queryWrapper2.eq("auto_config_id", idOfRegressionList.get(i));
            queryWrapper2.orderByDesc("start_time");
            queryWrapper2.last("limit 1");
            List<RobustTriggerJob> recentStartTimeL = robustTriggerJobMapper.selectList(queryWrapper2);
            if (recentStartTimeL.size() != 0) {
                Date recentStartTime = recentStartTimeL.get(0).getStartTime();
                Date now = new Date();
                long delta = now.getTime() - recentStartTime.getTime();
                long deltaDay = delta / (24 * 60 * 60 * 1000);
                log.info("i=" + String.valueOf(i) + ", deltaDay=" + deltaDay);
                if (deltaDay < 10) {//只要有一次十天内的触发记录，就视为已经做过测试，程序在循环结束后退出。
                    alreadyTriggered = true;
                    break;
                }
            }
            //else：查询结果列表为空，表示这个id之前从未测试过，不用改变alreadyTriggered的值
            else {
                log.info("i=" + String.valueOf(i) + ", 之前从未测试过");
            }
        }

        if (alreadyTriggered) {
            log.info("alreadyTriggered!\n");
            return;
        }

        log.info("sendTask...\n");
        JsonObject bodyJson = new JsonObject();
        JsonObject contentJson = new JsonObject();
        contentJson.addProperty("versionNumber", regressionJsonInfo.get("version").getAsString());
        bodyJson.add("content", contentJson);
        robustBuildService.triggerRobustJob(
                "regression",
                null,
                "Android",
                "meituan",
                bodyJson.toString()
        );
        robustBuildService.triggerRobustJob(
                "regression",
                null,
                "iOS",
                "meituan",
                bodyJson.toString()
        );
        log.info("健壮性回归测试定时检测正常运行结束");
        if (EnvUtil.isOnline()) {
            new DxUtil().sendToPersionByCompass("健壮性回归测试定时检测正常运行结束", "sunkangtong", "zhaojunming02");
        }
    }


    @Crane("work_order_auto_operate")
    public void autoOperate() {
        workOrderAutoOperate();
    }

    enum AppName {
        meituan, youxuan
    }

    public void workOrderAutoOperate() {
        //一、新版工单巡检：只遍历一周内有崩溃且无工单的case
        log.info("\n工单自动运营开始执行...\n");
        Date currentDate = new Date();
        Date startDate = new Date(currentDate.getTime() - (long) 7 * 24 * 60 * 60 * 1000);
        QueryWrapper<RobustResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("case_result", 3);
        queryWrapper.isNull("issue_key");
        queryWrapper.between("start_time", startDate, currentDate);
        List<RobustResult> robustResultList = robustResultMapper.selectList(queryWrapper);

        // 更新一周内有崩溃且无工单的case的结果
        Set<String> conanJobSet = new HashSet<>();
        if (robustResultList != null && robustResultList.size() != 0) {
            for (RobustResult robustResultItem : robustResultList) {
                conanJobSet.add(String.valueOf(robustResultItem.getConanJobId()));
            }
        }
        log.info("\nConanJobSet为：" + conanJobSet);
        for (String jobId : conanJobSet) {
            JsonArray crashArray = conanUtil.getJobCrashInfo(jobId);
            // caseId - issueKey 的键值对
            HashMap<String, String> caseToIssue = new HashMap<>();
            for (int k = 0; k < crashArray.size(); ++k) {
                try {
                    caseToIssue.put(
                            crashArray.get(k).getAsJsonObject().get("caseId").getAsString(),
                            crashArray.get(k).getAsJsonObject().get("issue").getAsJsonObject().get("issueKey").getAsString());
                } catch (Exception e) {
                    log.info("解析云测返回的issue数组元失败");
                }
            }
            // 获取job的case崩溃信息
            // 把符合要更新的jobId 查询出来。
            QueryWrapper<RobustResult> robustResultQueryWrapperTemp = new QueryWrapper<>();
            robustResultQueryWrapperTemp.eq("conan_job_id", jobId);
            List<RobustResult> robustResultListTemp = robustResultMapper.selectList(robustResultQueryWrapperTemp);
            // 按case去更新jobId
            for (RobustResult robustResultTemp : robustResultListTemp) {
                String caseId = String.valueOf(robustResultTemp.getConanCaseId());
                if (caseToIssue.containsKey(caseId)) {
                    robustResultTemp.setIssueKey(caseToIssue.get(caseId));
                    robustResultMapper.updateById(robustResultTemp);
                    log.info("更新caseId->issueKey" + caseId + caseToIssue.get(caseId));
                }
            }
        }
        log.info("\n工单巡检结束，开始将四个crash相关字段入库...\n");
        //二、查找七天内崩溃的、crash_hash为null的case,将它们的crash_hash、crash_info、crash_uuid、crash_version入库
        QueryWrapper<RobustResult> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("case_result", 3);
        queryWrapper2.isNull("crash_hash");
        queryWrapper2.between("start_time", startDate, currentDate);
        List<RobustResult> robustResultList2 = robustResultMapper.selectList(queryWrapper2);
        if (robustResultList2 != null && robustResultList2.size() != 0) {
            for (RobustResult robustResultItem : robustResultList2) {
                log.info("这个case及其关联的crash相关字段将被入库："+robustResultItem.getId());
                String thisJobId = String.valueOf(robustResultItem.getConanJobId());
                String thisConanCaseId = String.valueOf(robustResultItem.getConanCaseId());
                Date startTime = robustResultItem.getStartTime();
                Date endTime = robustResultItem.getFinishTime();
                setCrashValueToDB(thisJobId, thisConanCaseId, startTime, endTime);
            }
        }
        log.info("\n字段入库结束，开始添加评论、运营工单...\n");
        //三、将CrashResult入库；对于所有当前版本未评论过的新增崩溃工单，添加评论，并通知sigma
        autoSetCrashResultAddCommentAndSendToSigma();
        log.info("\n工单自动运营执行正常结束...\n");
    }

    //对于当前版本的所有崩溃用例，1.将他们的crash_result入库 2.如果是新增崩溃，则添加评论并将工单运营（通知sigma）3.将所有运营失败的工单重试
    public void autoSetCrashResultAddCommentAndSendToSigma() {
        Date currentDate = new Date();
        Date startDate = new Date(currentDate.getTime() - (long) 7 * 24 * 60 * 60 * 1000);
        QueryWrapper<RobustTriggerJob> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.between("start_time", startDate, currentDate);
        queryWrapper1.orderByAsc("start_time");
        queryWrapper1.last("limit 1");
        List<RobustTriggerJob> minTriggerIDL = robustTriggerJobMapper.selectList(queryWrapper1);
        int minTriggerID= Integer.MAX_VALUE;
        try {
            minTriggerID = minTriggerIDL.get(0).getId();
        } catch (Exception e) {
            log.error("Error: " + e);
        }
        log.info("\n" + "minTriggerID is: " + minTriggerID + "\n");

        QueryWrapper<RobustBuild> queryWrapper3 = new QueryWrapper<>();
        queryWrapper3.select("id");
        queryWrapper3.eq("trigger_id", minTriggerID);
        queryWrapper3.orderByAsc("id");
        queryWrapper3.last("limit 1");
        List<RobustBuild> minBuildIDL = robustBuildMapper.selectList(queryWrapper3);
        int minBuildID = -1;
        try {
            minBuildID = minBuildIDL.get(0).getId();
        } catch (Exception e) {
            log.error("Error: " + e);
        }

        //将所有运营失败的工单重试
        QueryWrapper<RobustResult> queryWrapper_OperateRetry = new QueryWrapper<>();
        queryWrapper_OperateRetry.ge("build_id", minBuildID);//只为从当前版本中进行筛选，减少查询总数
        queryWrapper_OperateRetry.eq("case_result", 3);
        queryWrapper_OperateRetry.eq("comment_state", 1);
        List<RobustResult> allCrashListRetry = robustResultMapper.selectList(queryWrapper_OperateRetry);
        log.info("运营重试：需要处理的case个数为：" + allCrashListRetry.size());
        log.info("运营重试：详情为：" + allCrashListRetry);
        Set<String> alreadyCommentedSetRetry = new HashSet<>();
        for (RobustResult oneOldResult : allCrashListRetry) {
            log.info(oneOldResult.toString());
            try {
                int resultId = oneOldResult.getId();
                QueryWrapper<RobustResult> queryWrapper10 = new QueryWrapper<>();
                queryWrapper10.eq("id", resultId);
                List<RobustResult> newResultL = robustResultMapper.selectList(queryWrapper10);
                RobustResult oneResult = newResultL.get(0);
                String thisIssueKey = oneResult.getIssueKey();
                int thisCrashResult = robustResultService.getCrashResult(oneResult);//即使云测数据此时还没更新到数据库，异常被catch住也不会出问题
                log.info("CrashResult is: " + String.valueOf(thisCrashResult));

                //在循环中，每条case的comment_state可能已经发生了变化，因此需要在循环中仍进行去重判断
                if (thisCrashResult == 3 && !alreadyCommentedSetRetry.contains(thisIssueKey)) {
                    /*
                        commentState取值见 https://km.sankuai.com/collabpage/1551032746
                        0:全部失败 1:仅评论成功 2:仅通知sigma成功 3:全部成功
                    */
                    int commentStateValue = 1;
                    alreadyCommentedSetRetry.add(thisIssueKey);
                    commentStateValue += orderOperate(oneResult);
                    //更新此工单关联的所有case的comment_state值
                    QueryWrapper<RobustResult> queryWrapper11 = new QueryWrapper<>();
                    queryWrapper11.eq("issue_key", thisIssueKey);
                    List<RobustResult> thisIssueKeyList = robustResultMapper.selectList(queryWrapper11);
                    log.info("此工单关联的所有case是: " + thisIssueKeyList);
                    for (RobustResult robustResultItem : thisIssueKeyList) {
                        robustResultItem.setCommentState(commentStateValue);
                        robustResultMapper.updateById(robustResultItem);
                    }
                    log.info("工单运营重试结束，state=" + commentStateValue);
                }
            } catch (Exception e) {
                log.error("Error: " + e);
            }
        }

        //筛选出七天内崩溃的、comment_state==0的case
        QueryWrapper<RobustResult> queryWrapper4 = new QueryWrapper<>();
        queryWrapper4.ge("build_id", minBuildID);
        queryWrapper4.eq("case_result", 3);
        queryWrapper4.eq("comment_state", 0);
        List<RobustResult> allCrashList = robustResultMapper.selectList(queryWrapper4);
        log.info("需要处理的case个数为：" + allCrashList.size());
        log.info("详情为：" + allCrashList);
        Set<String> alreadyCommentedSet = new HashSet<>();
        for (RobustResult oneOldResult : allCrashList) {
            log.info(oneOldResult.toString());
            try {
                int resultId = oneOldResult.getId();
                QueryWrapper<RobustResult> queryWrapper10 = new QueryWrapper<>();
                queryWrapper10.eq("id", resultId);
                RobustResult oneResult = robustResultMapper.selectOne(queryWrapper10);
//                List<RobustResult> newResultL = robustResultMapper.selectList(queryWrapper10);
//                RobustResult oneResult = newResultL.get(0);
                String thisIssueKey = oneResult.getIssueKey();
                int thisCrashResult = robustResultService.getCrashResult(oneResult);//即使云测数据此时还没更新到数据库，异常被catch住也不会出问题
                log.info("CrashResult is: " + String.valueOf(thisCrashResult));
                //1.将他们的crash_result入库
                oneResult.setCrashResult(thisCrashResult);
                UpdateWrapper<RobustResult> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", resultId);
                robustResultMapper.update(oneResult, updateWrapper);

                //2.如果是新增崩溃，并且此工单此时此刻仍未被评论过，则添加评论并发送至sigma
                //在循环中，每条case的comment_state可能已经发生了变化，因此需要在循环中仍进行去重判断
                if (thisCrashResult == 3 && !alreadyCommentedSet.contains(thisIssueKey)) {
                    //先同步comment_state的值，避免issueKey的延迟入库导致重复评论、运营。（将此case的comment_state同步为同工单的comment_state的最大值）
                    QueryWrapper<RobustResult> queryWrapper5 = new QueryWrapper<>();
                    queryWrapper5.eq("issue_key", thisIssueKey);
                    queryWrapper5.orderByDesc("comment_state");
                    queryWrapper5.last("limit 1");
                    RobustResult biggestResult = robustResultMapper.selectOne(queryWrapper5);
                    int latestCommentState = biggestResult.getCommentState();
                    log.info("commentState同步： " + resultId + " 的latestCommentState= " + latestCommentState);
                    //更新后若发现本工单已被处理过，同步commentState信息，并跳过此case处理
                    if (latestCommentState != 0) {
                        oneResult.setCommentState(latestCommentState);
                        UpdateWrapper<RobustResult> updateWrapper2 = new UpdateWrapper<>();
                        updateWrapper2.eq("id", resultId);
                        robustResultMapper.update(oneResult, updateWrapper2);
                        continue;
                    }


                    log.info("这条工单是崭新的新增崩溃：" + thisIssueKey);
                    /*
                        commentState取值见 https://km.sankuai.com/collabpage/1551032746
                        0:全部失败 1:仅评论成功 2:仅通知sigma成功 3:全部成功
                    */
                    int commentStateValue = 0;
                    alreadyCommentedSet.add(thisIssueKey);
                    commentStateValue += orderComment(oneResult);
                    commentStateValue += orderOperate(oneResult);
                    //更新此工单关联的所有case的comment_state值
                    QueryWrapper<RobustResult> queryWrapper11 = new QueryWrapper<>();
                    queryWrapper11.eq("issue_key", thisIssueKey);
                    List<RobustResult> thisIssueKeyList = robustResultMapper.selectList(queryWrapper11);
                    log.info("此工单关联的所有case是: " + thisIssueKeyList);
                    for (RobustResult robustResultItem : thisIssueKeyList) {
                        robustResultItem.setCommentState(commentStateValue);
                        robustResultMapper.updateById(robustResultItem);
                    }
                    log.info("工单评论、运营结束，state=" + commentStateValue);
                }
            } catch (Exception e) {
                log.error("Error: " + e);
            }
        }
    }

    public int orderComment(RobustResult oneResult) {
        int thisBuildId = oneResult.getBuildId();
        String thisIssueKey = oneResult.getIssueKey();
        log.info("这条工单将被评论：" + thisIssueKey);
        String commentTextBefore = "平台健壮性工具发现了该崩溃，可能是一个健壮性问题。测试记录及复现数据，可进入测试报告页面获取。测试报告地址为：\n";
        String theURL = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/robustAutoTest/jobReport?id=" + thisBuildId;
        String theCommentLink = "<a class=\"ct-link\" href=\"" + theURL + "\" data-auto_update=\"0\">" + theURL + "</a>";
        JsonObject commentRes = onesUtil.addComments(Long.parseLong(thisIssueKey), commentTextBefore + theCommentLink);
        //如果添加评论成功，commentState+1
        if (commentRes.get("createdAt") != null) {
            return 1;
        } else return 0;
    }

    public int orderOperate(RobustResult oneResult) {
        int thisBuildId = oneResult.getBuildId();
        String thisJobId = String.valueOf(oneResult.getConanJobId());
        String thisConanCaseId = String.valueOf(oneResult.getConanCaseId());
        String thisIssueKey = oneResult.getIssueKey();
        String crashHash = oneResult.getCrashHash();
        if (StringUtil.isBlank(crashHash)) {
            crashHash = "未获取到崩溃名称";
        }
        String thisProject = getCaseProject(thisJobId, thisConanCaseId);
        String thisVersion="未获取到";
        try{
            thisVersion=conanUtil.getTaskInfo(thisJobId).get(0).getAsJsonObject().get("appVersion").getAsString();
        } catch (Exception e){
            log.error("Error: "+e);
        }
        if (new LionUtil().getBooleanValue("NotificationToSigma")) {
            String theURL = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/robustAutoTest/jobReport?id=" + thisBuildId;
            String pushedText = String.format("[该问题由健壮性工具发现|%s]", theURL);
//            if (pushedText.getBytes(StandardCharsets.UTF_8).length >= 127) {
//                pushedText = "该问题由健壮性工具发现";
//            }
            //如果通知sigma成功，commentState+2
            try {
                final String testItem="健壮性自动化";
                log.info("即将发送工单运营信息,参数如下：");
                log.info(String.join(", ", thisIssueKey,testItem,thisVersion, thisProject, crashHash, pushedText));
                JsonObject sigmaRes = sigmaUtil.sendNotificationToSigma(
                        thisIssueKey,
                        testItem,
                        thisVersion,
                        thisProject,
                        crashHash,
                        pushedText
                );
                log.info("SigmaResponse is: "+sigmaRes);
                if (Objects.equals(sigmaRes.get("code").getAsString(), SEND_NOTIFICATION_SUCCESS)) {
                    return 2;
                }
            } catch (Exception e) {
                log.error("工单运营失败：" + e);
            }
        }
        return 0;
    }

    public void setCrashValueToDB(String jobId, String conanCaseId, Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String startTimeStr = sdf.format(startTime);
        String endTimeStr = sdf.format(endTime);
        JsonArray jobCrashDetailList = conanUtil.getJobCrashInfo(jobId);
        if (null != jobCrashDetailList) {
            log.info("size of jobCrashDetailList(c) is: " + jobCrashDetailList.size());
            for (int c = 0; c < jobCrashDetailList.size(); c++) {
                try {
                    //***一条conan_case
                    JsonObject jsonObject = jobCrashDetailList.get(c).getAsJsonObject();

                    String caseId = jsonObject.get("caseId").getAsString();
                    if (!Objects.equals(caseId, conanCaseId)) continue;
                    log.info("筛选出了conan_case，id为：" + caseId);
                    String crashUUID = jsonObject.get("uuid").getAsString();
                    String project = jsonObject.get("issue").getAsJsonObject().get("project").getAsString();
                    JsonObject crashDetailObject;
                    Map<String, Object> crashDetail = new HashMap();
                    try {
                        String param = crashUUID + "&project=" + project + "&start=" + startTimeStr + "%2000:00:00&end=" + endTimeStr + "%2023:59:59";
                        log.info("setCrashValueToDB---getCrashDetail---param: " + param);
                        String result = HttpUtil.vGet(CRASH_DETAIL_API + param);
                        if (!"[]".equals(result)) {
                            log.info("setCrashValueToDB---getCrashDetail: CRASH_DETAIL_API返回不为空");
                            crashDetailObject = jsonParser.parse(result).getAsJsonArray().get(0).getAsJsonObject();
                            crashDetail.put("crash_hash", crashDetailObject.get("hash").getAsString());
                            crashDetail.put("default_bg_bu_chinese", crashDetailObject.get("default_bg_bu_chinese").getAsString());
                            crashDetail.put("default_component", crashDetailObject.get("default_component").getAsString());
                            crashDetail.put("type", crashDetailObject.get("type").getAsString());
                        }
                    } catch (Exception e) {
                        log.error("setCrashValueToDB---getCrashDetail:CRASH_DETAIL_API返回出错");
                        log.error(e.getMessage());
                    }
                    log.info("crashDetail is: " + crashDetail);
                    //ConanCase conanCase = conanCaseService.selectOneCase(caseId);
                    if (crashDetail.size() > 0) {
                        String crashHash = "未获取到";
                        String crashInfo = "未获取到";
                        String type = "未获取到";
                        String crashVersion = "未获取到";
                        try {
                            crashHash = crashDetail.get("crash_hash").toString();
                            crashInfo = "归属BU: " + crashDetail.get("default_bg_bu_chinese") + "\n归属组件: " + crashDetail.get("default_component");
                            type = crashDetail.get("type").toString();
                            crashVersion = conanUtil.getMaxMinCrashVersion(crashHash, type, project);
                        } catch (Exception e) {
                            log.error("Error: " + e);
                        }
                        //找到本条conan_case对应的崩溃result
                        QueryWrapper<RobustResult> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("conan_case_id", Integer.parseInt(caseId));
                        queryWrapper.eq("case_result", 3);
                        queryWrapper.isNull("crash_hash");
                        List<RobustResult> robustResultList = robustResultMapper.selectList(queryWrapper);
                        log.info("本条conan_case对应的崩溃result是：" + robustResultList);
                        if (robustResultList != null && robustResultList.size() != 0) {
                            for (RobustResult robustResultItem : robustResultList) {
                                robustResultItem.setCrashUuid(crashUUID);
                                robustResultItem.setCrashInfo(crashInfo);
                                robustResultItem.setCrashHash(crashHash);
                                robustResultItem.setCrashVersion(crashVersion);
                                robustResultMapper.updateById(robustResultItem);
                                log.info("保存数据库成功，robustResult信息为：" + robustResultItem);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("e.getMessage()：" + e.getMessage());
                }

            }
        }
    }

    /**
     * 获取app的真实名称，即perf平台上显示的名称
     * @param jobId
     * @param conanCaseId
     * @return
     */
    public String getCaseProject(String jobId, String conanCaseId) {
        String project = "获取失败";
        ConanUtil conanUtil = new ConanUtil();
        JsonArray jobCrashDetailList = conanUtil.getJobCrashInfo(jobId);
        if (null != jobCrashDetailList) {
            log.info("size of jobCrashDetailList(c) is: " + jobCrashDetailList.size());
            for (int c = 0; c < jobCrashDetailList.size(); c++) {
                try {
                    JsonObject jsonObject = jobCrashDetailList.get(c).getAsJsonObject();
                    String caseId = jsonObject.get("caseId").getAsString();
                    if (!Objects.equals(caseId, conanCaseId)) continue;
                    project = jsonObject.get("issue").getAsJsonObject().get("project").getAsString();
                    log.info("getCaseProject：conan_case_id为：" + caseId + "\nproject为：" + project);

                } catch (Exception e) {
                    log.error("e.getMessage()：" + e.getMessage());
                }
            }
        }
        return project;
    }
}
