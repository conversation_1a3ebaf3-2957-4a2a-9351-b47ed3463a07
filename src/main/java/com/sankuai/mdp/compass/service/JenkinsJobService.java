package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.entity.JenkinsJob;

/**
 * Created by xieyongrui on 2019/11/21.
 */
public interface JenkinsJobService extends IService<JenkinsJob> {

    void updateJobStartTime(<PERSON><PERSON><PERSON> jenkinsJob);

    void updateSubmitFinishTime(Jenkins<PERSON><PERSON> jenkinsJob);

    void updateCaseRunTime(Jenkins<PERSON><PERSON> jenkinsJob);

    void updateCaseFinishTime(Jenkins<PERSON><PERSON> jenkinsJob);

    void updateJobFinishTime(<PERSON><PERSON><PERSON> jenkinsJob);

    String add(<PERSON><PERSON><PERSON> jenkinsJob);

    void delete(<PERSON><PERSON><PERSON> jenkinsJob);
}
