package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.mdp.compass.entity.CheckOrder;
import com.sankuai.mdp.compass.mapper.CheckOrderMapper;
import com.sankuai.mdp.compass.service.CheckOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by xieyongrui on 2020/2/24.
 */
@Service
public class CheckOrderServiceImpl extends ServiceImpl<CheckOrderMapper,CheckOrder> implements CheckOrderService {
    @Override
    public void create(CheckOrder checkOrder) {
        this.save(checkOrder);
    }

    @Override
    public List<CheckOrder> getPending(Date date) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("status","pending");
        queryWrapper.eq("start_time",date);
        return this.listObjs(queryWrapper);
    }
}
