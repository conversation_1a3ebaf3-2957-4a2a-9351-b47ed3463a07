package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicTemplateData;
import com.sankuai.mdp.compass.mapper.DynamicTemplateDataMapper;
import com.sankuai.mdp.compass.service.DynamicTemplateDataService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

@Service
@Slf4j
public class DynamicTemplateDataServiceImpl extends ServiceImpl<DynamicTemplateDataMapper, DynamicTemplateData> implements DynamicTemplateDataService {
    @Autowired
    DynamicTemplateDataMapper dynamicTemplateDataMapper;

    // 上传动态布局模板数据
    @Override
    public JSONObject uploadData(DynamicTemplateData dynamicTemplateData) {
        JSONObject result = new JSONObject();
        int id = dynamicTemplateData.getId();
        String templateName = dynamicTemplateData.getTemplateName();
        String templateData = dynamicTemplateData.getTemplateData();
        String dataDesc = dynamicTemplateData.getDataDesc();
        if (templateName == null || templateName.isEmpty() || templateData == null || templateData.isEmpty()) {
            result.put("msg", "模板名称或模板数据不能为空");
            result.put("success", false);
            return result;
        }
        dynamicTemplateData.setTemplateName(templateName);
        dynamicTemplateData.setTemplateData(templateData);
        // 根据id是否为-1，判断数据是否为新增，-1表示新增，非-1表示替换对应id值的数据
        try {
            if (id == -1) {
                if (dataDesc == null || dataDesc.isEmpty()) {
                    result.put("msg", "新增模板数据，数据名称不能为空");
                    result.put("success", false);
                    return result;
                }
                dynamicTemplateData.setDataDesc(dataDesc);
                dynamicTemplateData.setCreatedAt(new Date(System.currentTimeMillis()));
                dynamicTemplateDataMapper.insert(dynamicTemplateData);
                result.put("msg", "模板数据新增成功");
                result.put("success", true);
            } else if (id > 0) {
                if (dataDesc != null && !dataDesc.isEmpty()) {
                    dynamicTemplateData.setDataDesc(dataDesc);
                }
                dynamicTemplateData.setUpdatedAt(new Date(System.currentTimeMillis()));
                dynamicTemplateDataMapper.updateById(dynamicTemplateData);
                result.put("msg", "模板数据更新成功");
                result.put("success", true);
            } else {
                result.put("msg", "id参数错误，当前id值为" + id);
                result.put("success", false);
            }
        } catch (Exception e) {
            log.error("上传动态布局模板数据失败", e);
            result.put("msg", "上传动态布局模板数据失败");
            result.put("success", false);
        }
        return result;
    }

    @Override
    public IPage<DynamicTemplateData> getTemplateData(QueryRequest request, DynamicTemplateData dynamicTemplateData) {
        try {
            LambdaQueryWrapper<DynamicTemplateData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DynamicTemplateData::getTemplateName, dynamicTemplateData.getTemplateName());
            Page<DynamicTemplateData> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取动态布局模板数据失败", e);
            return null;
        }
    }
}
