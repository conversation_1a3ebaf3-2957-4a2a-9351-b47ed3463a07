package com.sankuai.mdp.compass.service.impl;

import com.google.gson.*;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.meituan.travel.mop.ad.resops.thrift.*;
import com.sankuai.mdp.compass.adapter.GsonAdapter;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.entity.DynamicData;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import com.sankuai.mdp.compass.entity.CheckOrder;
import com.sankuai.mdp.compass.service.CheckOrderService;
import com.sankuai.mdp.compass.service.CustomService;
import com.sankuai.mdp.compass.service.DynamicDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.mdp.compass.common.domain.ComConstant;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by xieyongrui on 2019/11/16.
 */
@Service
@Slf4j
public class CustomServiceImpl implements CustomService {
    EvaUtil evaUtil = new EvaUtil();
    MbcUtil mbcUtil=new MbcUtil();
    ConanUtil conanUtil = new ConanUtil();
    FileUtil fileUtil = new FileUtil();
    ComUtil comUtil = new ComUtil();
    ComConstant comConstant = new ComConstant();
    DynamicTemplateServiceImpl dynamicTemplateService = new DynamicTemplateServiceImpl();
    @Autowired
    CompatilityMapper compatilityMapper;
    @Autowired
    CheckOrderService checkOrderService;

    @Autowired
    DynamicDataService dynamicDataService;

    @Autowired
    OpsService.Iface opsService;
    ResourceDataQueryReq resourceDataQueryReq = new ResourceDataQueryReq();
    ResourceDataRes resourceDataRes;

//    public static void main(String[] argv) {
//        String data = "{\"globalId\":\"c7b075d26079f5c95e03f8fca33fd468\",\"title\":\"猜你喜欢\",\"stid\":\"060782015142609627881335144353395372770_c3_ec7b075d26079f5c95e03f8fca33fd468\",\"data\":[],\"tab\":{\"allTabs\":[{\"index\":0,\"mge\":{\"tab_index\":0,\"tab_id\":\"homepage\",\"ext\":{}},\"tabContent\":\"猜你喜欢\",\"tabContentStyle\":\"text\",\"tabSubContent\":\"为你精选\",\"tabId\":\"homepage\"},{\"index\":1,\"mge\":{\"tab_index\":1,\"tab_id\":\"discount\",\"ext\":{}},\"tabContent\":\"聚便宜\",\"tabContentStyle\":\"text\",\"tabSubContent\":\"全场1折起\",\"tabId\":\"discount\"},{\"index\":2,\"mge\":{\"tab_index\":2,\"tab_id\":\"guess_content_tab\",\"ext\":{}},\"tabContent\":\"来种草\",\"tabContentStyle\":\"text\",\"tabSubContent\":\"达人经验\",\"tabId\":\"guess_content_tab\"}],\"selected\":\"homepage\"},\"sessionId\":\"cc46128771acb79ec51b6c53169fc341\",\"rolltop\":5,\"bottom\":false,\"styleType\":\"feed\",\"feedStyle\":\"twoColumn\",\"mge\":{\"tab_index\":0,\"ext\":{}},\"preNum\":3,\"refresh\":true,\"serverInfo\":{}}";
//        String tempApiDataStr = "{\"globalId\":\"318937460a5c9f754e7eac9604def780\",\"title\":\"猜你喜欢\",\"stid\":\"101213684199525797328851411102762582193_c8_e318937460a5c9f754e7eac9604def780\",\"data\":[],\"topData\":[],\"rolltop\":5,\"bottom\":false,\"styleType\":\"feed\",\"mge\":{\"tab_index\":0},\"preNum\":3,\"refresh\":true,\"serverInfo\":{}}";
//        JsonObject templateData = new JsonParser().parse(data).getAsJsonObject();
//        String listPath = ".data";
//        JsonArray jsonArray = new JsonParser().parse(JsonPath.parse(tempApiDataStr).read("['data']").toString()).getAsJsonArray();
//        jsonArray.add(templateData);
//        Configuration conf = Configuration.builder()
//                .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
//        DocumentContext json = JsonPath.using(conf).parse(StringEscapeUtils.unescapeJavaScript(tempApiDataStr));
//        JsonObject jsonObject = new JsonParser().parse(json.set("$"+listPath, (Object) jsonArray).jsonString()).getAsJsonObject();
//        JsonObject result = new JsonParser().parse(StringEscapeUtils.unescapeJavaScript(jsonObject.toString())).getAsJsonObject();
//        System.out.print(result);
//
//    }

    public Set<String> getModule(String address) {
//        String address="EVA";//这里应该是前端传过来的
        Set<String> set = new TreeSet<String>();
        JsonObject jsonObject = mbcUtil.getModule();
        JsonArray jsonArray = jsonObject.get("data").getAsJsonObject().getAsJsonArray("list");
        for(int i = 0; i < jsonArray.size(); i++){
            JsonElement jsonElement = jsonArray.get(i).getAsJsonObject().get("module");
            set.add(jsonElement.getAsJsonObject().get("cname").getAsString());
        }
        return set;
    }

    @Override
    public List<String> getTemplate(String module,String address) {
        Map<String,List<String>> map = new HashMap<>();
        Set<String> set = new TreeSet<String>();

        if(address.equals("MBC")){
            JsonObject jsonObject = mbcUtil.getTemplate();
            JsonArray jsonArray = jsonObject.get("data").getAsJsonObject().getAsJsonArray("list");
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonElement jsonElement = jsonArray.get(i).getAsJsonObject().get("module");
                set.add(jsonElement.getAsJsonObject().get("cname").getAsString());
            }
            for (String key : set){
                List<String> list = new ArrayList<>();
                for(int j = 0;j < jsonArray.size();j++){
                    String moduleNameCn = jsonArray.get(j).getAsJsonObject().get("module").getAsJsonObject().get("cname").getAsString();
                    if(moduleNameCn.equals(key)){
                        JsonObject itemJson = jsonArray.get(j).getAsJsonObject();
                        String item = itemJson.get("ename").getAsString()+":"+itemJson.get("minAppVersion").getAsString();
                        if(!list.contains(item)){
                            list.add(item);
                        }
                    }
                }
                map.put(key,list);
            }
        }else {
            JsonObject jsonObject = evaUtil.getTemplate();
            JsonArray jsonArray = jsonObject.getAsJsonArray("data");
            for(int i=0;i<jsonArray.size();i++){
                set.add(jsonArray.get(i).getAsJsonObject().get("moduleNameCn").getAsString());
            }
            for(String key :set) {
                List<String> list = new ArrayList<>();
                for(int j=0;j<jsonArray.size();j++){
                    String moduleNameCn = jsonArray.get(j).getAsJsonObject().get("moduleNameCn").getAsString();
                    if(moduleNameCn.equals(key)){
                        JsonObject itemJson = jsonArray.get(j).getAsJsonObject();
                        String item = itemJson.get("nameEn").getAsString()+":"+itemJson.get("startVersion").getAsString();
                        if(!list.contains(item)){
                            list.add(item);
                        }
                    }
                }
                map.put(key,list);
            }
        }
        return map.get(module);
    }

    @Override
    public String getBusinessEnNameByCnName(String name,String address) {
        String enName = null;
        if (address.equals("MBC")){
            JsonObject jsonObject = mbcUtil.getModule();
            JsonArray jsonArray = jsonObject.get("data").getAsJsonObject().getAsJsonArray("list");
            for(int i = 0; i < jsonArray.size(); i++){
                JsonObject itemJson = jsonArray.get(i).getAsJsonObject().get("module").getAsJsonObject();
                String moduleNameCn = itemJson.get("cname").getAsString();
                if (name.equals(moduleNameCn)){
                    enName = itemJson.get("ename").getAsString();
                    break;
                }
            }
        }else{
            JsonObject jsonObject = evaUtil.getModule();
            JsonArray jsonArray = jsonObject.getAsJsonArray("data");
            for(int i = 0; i < jsonArray.size(); i++){
                String moduleNameCn = jsonArray.get(i).getAsJsonObject().get("moduleNameCn").getAsString();
                if (name.equals(moduleNameCn)) {
                    enName = jsonArray.get(i).getAsJsonObject().get("moduleNameEn").getAsString();
                    break;
                }
            }
        }


        return enName;
    }

    @Override
    public Map<String, String> getBusinessInfo(String address) {
        Map<String,String> map = new HashMap<>();
        if(address.equals("MBC")) {
            JsonObject jsonObject = mbcUtil.getModule();
            JsonArray jsonArray = jsonObject.get("data").getAsJsonObject().getAsJsonArray("list");
            for(int i = 0; i < jsonArray.size(); i++){
                JsonObject itemJson = jsonArray.get(i).getAsJsonObject().get("module").getAsJsonObject();
                map.put(itemJson.get("cname").getAsString(),itemJson.get("ename").getAsString());
            }
        }else{
            JsonObject jsonObject = evaUtil.getModule();
            JsonArray jsonArray = jsonObject.getAsJsonArray("data");
            for(int i = 0; i < jsonArray.size(); i++){
                JsonObject itemJson = jsonArray.get(i).getAsJsonObject();
                map.put(itemJson.get("moduleNameCn").getAsString(),itemJson.get("moduleNameEn").getAsString());
            }
        }
        return map;
    }

    @Override
    public String getMockData(Integer mockId) {
        return MockUtil.get(mockId);
    }

    @Override
    public JsonArray getJsonKey(JsonObject body) {
        try {
            JsonObject jsonObject  = body.getAsJsonObject("payload");
            String string = jsonObject.get("data").toString();
            String data = StringEscapeUtils.unescapeJavaScript(string.substring(1,string.length()-1));
            return getJsonKey(data);
        } catch (Exception e) {
            log.error("json解析异常");
            return null;
        }

    }

    public JsonArray getJsonKey(String data) {
        Configuration conf = Configuration.builder()
                .options(Option.AS_PATH_LIST).build();
        List<String> list = JsonPath.using(conf).parse(data).read("$..*");
        JsonArray result = new JsonArray();
        for (int i = 0; i < list.size(); i++) {
            String path = list.get(i);
            String[] subPath = path.split("\'");
            String resultPath = "";
            for (int index = 0; index < subPath.length; index++) {
                String keyStr = subPath[index].replaceAll("\\[","").replaceAll("\\]","");
                Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
                Matcher isNum = pattern.matcher(keyStr);
                if ("".equals(keyStr) || "$".equals(keyStr)) {
                    continue;
                }
                if (isNum.matches()) {
                    resultPath += "[" + keyStr + "]";
                } else {
                    resultPath += "." + keyStr;
                }
            }
            result.add(resultPath);
        }
        return result;
    }

    @Override
    public JsonObject getOriginalKey(String templateName) {
        JsonObject result = null;
        DynamicData dynamicData = dynamicDataService.find(templateName);
        if (null != dynamicData) {
            result = new JsonParser().parse(dynamicData.getMockRule().toString()).getAsJsonObject();
        }
        return result;
    }

    @Override
    public JsonObject getMockRule(JsonObject body) {
        JsonObject result = new JsonObject();
        JsonParser jsonParser = new JsonParser();
        try {
            JsonObject jsonObject  = body.getAsJsonObject("payload");
            Object object = jsonObject.get("data");
            String data = object.toString();
            if (object instanceof JsonPrimitive) {
               data = ((JsonPrimitive) object).getAsString();
            }

//            String data = StringEscapeUtils.unescapeJavaScript(string.substring(1,string.length()-1));
            String templateName = jsonObject.get("templateName").getAsString();
            String business = jsonObject.get("business").getAsString();
            String source = jsonObject.get("source").getAsString();

            JsonArray longRule = new JsonArray();
            JsonArray blankRule = new JsonArray();
            JsonArray richTextRule = new JsonArray();
            JsonArray invalidUrlRule = new JsonArray();
            JsonArray all = getJsonKey(data);

            //获取templateName的xml
            if (source.equals("MBC")) {
                String xml = dynamicTemplateService.getXmlContentByTemplateName(business,templateName,"MBC", "");
                JSONObject mockRule = dynamicDataService.getSmokeCase(xml,".biz.");
                //获取模版数据所有key
                JsonArray longRuleArray = jsonParser.parse(mockRule.getJSONArray("long").toString()).getAsJsonArray();
                JsonArray blankRuleArray = jsonParser.parse(mockRule.getJSONArray("blank").toString()).getAsJsonArray();
//                JSONArray richTextArray = mockRule.getJSONArray("richText");
                JsonArray invalidUrlArray = jsonParser.parse(mockRule.getJSONArray("invalidUrl").toString()).getAsJsonArray();

                for (int i = 0; i < longRuleArray.size(); i++) {
                    if (all.contains(longRuleArray.get(i))) {
                        longRule.add(longRuleArray.get(i));
                    }
                }
                for (int i = 0; i < blankRuleArray.size(); i++) {
                    if (all.contains(blankRuleArray.get(i))) {
                        blankRule.add(blankRuleArray.get(i));
                    }
                }
//                for (int i = 0; i < richTextArray.length(); i++) {
//                    if (all.contains(jsonParser.parse(richTextArray.get(i).toString)))) {
//                        richTextRule.add(richTextArray.get(i).toString());
//                    }
//                }
                for (int i = 0; i < invalidUrlArray.size(); i++) {
                    if (all.contains(invalidUrlArray.get(i))) {
                        invalidUrlRule.add(invalidUrlArray.get(i));
                    }
                }
            } else {
                return null;
            }

            result.add("long",longRule);
            result.add("blank",blankRule);
            result.add("richText",richTextRule);
            result.add("invalidUrl",invalidUrlRule);
            result.add("all",all);

        } catch (Exception e) {
            log.error("json解析异常>>"+e.getMessage());
            return null;
        } finally {
            return result;
        }
    }

    @Override
    public Boolean submitSql(JsonObject body) {
        TalosUtil talosUtil = new TalosUtil();
        Date currentDate = new Date();
        Map<String,Object> map = (Map)body.get("payload");

        String fileName = "./result.xlsx";
        Integer jobId = (Integer) map.get("jobId");
        System.out.print(jobId);
        CompatilityJob compatilityJob = compatilityMapper.selectById(jobId);
        compatilityJob.setCheckStatus(2);
        compatilityMapper.updateById(compatilityJob);
        String dateStr = (String) map.get("date");
        SimpleDateFormat sdf =   new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = sdf.parse(dateStr);
            String sql = (String) map.get("sql");
            System.out.print(dateStr);
            System.out.print(date);
            CheckOrder checkOrder = new CheckOrder();
            checkOrder.setJobId(jobId);
            checkOrder.setStartTime(date);
            checkOrder.setSqlStr(sql);
            if (currentDate.after(date) || currentDate.equals(date)) {
                executeSql(sql,fileName,compatilityJob,checkOrder);
            } else {
                checkOrder.setStatus("pending");
                checkOrderService.create(checkOrder);
            }

        } catch (Exception e) {

        }
        return true;
    }

    public void executeSql(String sql, String fileName, CompatilityJob compatilityJob, CheckOrder checkOrder) {
        TalosUtil talosUtil = new TalosUtil();
        Boolean result = talosUtil.submit(sql, fileName);
        if (false == result) {
            checkOrder.setStatus("pending");
            checkOrderService.create(checkOrder);
        } else {
            MSSUtil mssUtil = new MSSUtil();
            String objectUrl = mssUtil.uploadExcel(fileName);
            //String objectUrl = "https://msstest.vip.sankuai.com/autotest/mge_report_1582515396304_2020-02-24.xlsx?AWSAccessKeyId=d52632a67bdb4ec98deb0ca488f9c5b5&Expires=1590311147&Signature=MX%2BBesdxb9FOZ4zJBUcW%2Ff24YpQ%3D";
            if (null != objectUrl) {
                compatilityJob.setDownloadUrl(objectUrl.split("autotest")[1]);
                compatilityJob.setCheckStatus(1);
                compatilityMapper.updateById(compatilityJob);
            }
            checkOrder.setStatus("finished");
            checkOrderService.create(checkOrder);
        }
    }

    @Override
    public JsonObject getLingLongData(Integer moduleId,String platform,String version) {
        String sql = "SELECT \n" +
                "ad_name,JSON_EXTRACT(data,'$.name') AS name,JSON_EXTRACT(data,'$.target') AS target,JSON_EXTRACT(data,'$.VERSION_SPLIT_10.9.200_SPLIT_sideSlipIconUrl') AS iconUrl,JSON_EXTRACT(data,'$.cateId') AS cateId,data\n" +
                "FROM mart_semantic.dim_opt_resource LEFT JOIN origindb.travelbconsumer_entry__resource_ptversion\n" +
                "ON dim_opt_resource.`ad_id`=travelbconsumer_entry__resource_ptversion.`resource_id`\n" +
                "WHERE module_id='171'\n" +
                "and start_time<'2021-08-17'\n" +
                "and end_time>'2021-08-17'\n" +
                "and ptversion='android_11.13.200'\n" +
                "and status='1'\n" +
                "and city_list<>'8000'\n" +
                "ORDER BY cateId";
        String fileName = "./linglong.xlsx";
//        TalosUtil talosUtil = new TalosUtil();
//        Boolean resultsql = talosUtil.submit(sql, fileName);

        //根据资源位ID分页查询资源数据信息接口：OpsService.queryResourceDataPageByModule
        //参数{"bizkey":"kingKong","pageNum":1,"pageSize":100,"moduleIds":[171]}
        //测试环境金刚区moduleIds改成 337
        Map map = new HashMap();
        JsonObject result = new JsonObject();
        List<JsonObject> itemArray = new ArrayList<>();
        JsonParser parser = new JsonParser();
        try {
            List<Integer> moduleIds = new ArrayList<>();
            String itemStr,itemName,itemUrl;
            JsonObject itemJson;
            moduleIds.add(moduleId);
            resourceDataQueryReq.setModuleIds(moduleIds);
            resourceDataQueryReq.setPageNum(1);
            resourceDataQueryReq.setPageSize(100);
            resourceDataQueryReq.setBizkey("kingKong");
            resourceDataRes = opsService.queryResourceDataPageByModule(resourceDataQueryReq);
            int count = resourceDataRes.getDataSize();
            map.put("count",count);
            for (int i=0; i<count; i++){
                itemStr = resourceDataRes.getData().get(i).getDataMap().get("data");
                itemJson = parser.parse(itemStr).getAsJsonObject();
                JsonObject itemObject = new JsonObject();
                itemObject.addProperty("name",itemJson.get("name").getAsString());
                itemObject.addProperty("url",itemJson.get("target").getAsString());
                itemArray.add(itemObject);
                log.info(itemObject.toString());
            }
            map.put("data",itemArray);
            Gson gson = new Gson();
            result = gson.toJsonTree(map).getAsJsonObject();
            log.info("玲珑数据返回");
        } catch (Exception e){
            log.error(e.getMessage());
            log.info("玲珑返回错误");
        }
        return result;
    }

    /**
     * platform:0-ios,1-android
     */
    @Override
    public String getCategoryData(int platform,String version) {
        String mockId;
        List<String> mockIds = new ArrayList<>();
        String cateId;
        List<String> cateIds = new ArrayList<>();
        JsonArray categoryList; //拼接前的金刚区实时数据
        List<JsonObject> categoryListAll = new ArrayList<>(); //组合后的金刚区数据(可能会合并两个及以上城市的金刚区数据)
        JsonParser parser = new JsonParser();
        Gson gson = new GsonAdapter().getGson();
        int[] baseMockIds = {333980,333981};//333980：iOS mock数据样式；333981：Android mock数据样式
        String api = "/mbc/homepage";
        JsonObject category;
        Configuration configuration = Configuration.builder().options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
        try {
            //step1：请求首页接口，获取最新金刚区数据
            String url_mbc = "https://gaea.meituan.com/mbc/homepage";
            String url = "http://apimobile.vip.sankuai.com/aggroup/homepage/display";
            String param = "";
            String baseParamios = "category_native_strategy=A&client=iphone&lat=40.007183&lng=116.481185&nocache=true&offset=0&position=40.007183%2C116.481185&refreshArea=cateCategory&requestType=pullToRefresh&showStyle=1&supportId=1&tab=homepage&utm_medium=iphone&utm_source=Alpha&waimai_position=40.007183%2C116.481185";
            String baseParamandr = "category_native_strategy=A&client=iphone&lat=40.007183&lng=116.481185&nocache=true&offset=0&position=40.007183%2C116.481185&refreshArea=cateCategory&requestType=pullToRefresh&showStyle=1&supportId=1&tab=homepage&utm_medium=iphone&utm_source=Alpha&waimai_position=40.007183%2C116.481185";
            String versionParam = "&utm_term="+version+"&version_name="+version;
            String ciParam = "&ci=";
            int[] citys = {1,10};//请求北京、上海的数据，取并集
            for (int i=0; i<citys.length; i++){
                if(0 == platform){
                    param = baseParamios + versionParam + ciParam +citys[i];
                } else {
                    param = baseParamandr + versionParam + ciParam +citys[i];
                }
//                String resp = HttpUtil.sendPost(url, param);
                log.info(param);
                String resp = HttpUtil.sendGet(url, param);
                if (null != resp) {
                    log.info(resp);
                    String categoryPath = "$.data.modules[?(@.name=='cateCategory')].proxyData.data.homepage";
                    DocumentContext respJson = JsonPath.using(configuration).parse(resp);
                    categoryList = parser.parse(respJson.read(categoryPath).toString()).getAsJsonArray().get(0).getAsJsonArray();
//                    categoryList = parser.parse(resp).getAsJsonObject().get("data").getAsJsonObject().get("groups").getAsJsonArray().get(0).getAsJsonObject().get("items").getAsJsonArray().get(0).getAsJsonObject().get("biz").getAsJsonObject().get("data").getAsJsonObject().get("homepage").getAsJsonArray();
                    for (int catei=0;catei<categoryList.size();catei++){
                        category = categoryList.get(catei).getAsJsonObject();
                        cateId = category.get("id").getAsString();
                        if(cateIds.size()==0 || !cateIds.contains(cateId)){
                            cateIds.add(cateId);
                            categoryListAll.add(categoryList.get(catei).getAsJsonObject());
                        }
                    }
                }
            }
            //step2：获取基准mock数据，为构造整体homepage数据做准备
            DocumentContext apiDataJson = JsonPath.using(configuration).parse(getMockData(baseMockIds[platform]));
            //step3：将基准mock数据中的金刚区数据替换为实时请求的数据
            int screenNum=0;

            String listPath = "$.data.groups[1].items[0].biz.data.homepage";
            for(screenNum=0; screenNum<(categoryListAll.size()-1)/15; screenNum++){
                //金刚区数据15个为1屏
                JsonObject newHomepageData = parser.parse(apiDataJson.set(listPath, gson.fromJson(categoryListAll.subList(0+screenNum*15,15+screenNum*15).toString(), Object.class)).jsonString()).getAsJsonObject();
                JsonObject mockResult = MockUtil.create(api, newHomepageData);
                if(null != mockResult && mockResult.get("code").getAsInt() == 1){
                    mockId = mockResult.get("data").getAsJsonObject().get("mockId").getAsString();
                    mockIds.add(mockId);
                }
                log.info(newHomepageData.toString());
            }
            //最后一屏数据
            JsonObject newHomepageData = parser.parse(apiDataJson.set(listPath, gson.fromJson(categoryListAll.subList(0+screenNum*15,categoryListAll.size()).toString(), Object.class)).jsonString()).getAsJsonObject();
            JsonObject mockResult = MockUtil.create(api, newHomepageData);
            if(null != mockResult && mockResult.get("code").getAsInt() == 1){
                mockId = mockResult.get("data").getAsJsonObject().get("mockId").getAsString();
                mockIds.add(mockId);
            }
            log.info(newHomepageData.toString());

        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return mockIds.toString();
    }

    // 直接请求首页接口并生成mock
    @Override
    public String getCategoryList(int platform, String version, String cities) {
        String mockId;
        List<String> mockIds = new ArrayList<>();
        String api = "/aggroup/homepage/display";
        try {
            String url = "https://apimobile.meituan.com/aggroup/homepage/display";
            String param = "";
            String baseParamandr = "requestType=pullToRefresh&page=0&limit=15&offset=0&mbcDebug=true&wifi-cur=0&wifi-name=Meituan-Test%08HD-Pwifi16%08Meituan-Guest%08Meituan-Internet%08&wifi-strength=-56%08-46%08-53%08-55%08&wifi-mac=1c%3A28%3Aaf%3A5e%3A91%3A20%0868%3Ad7%3A9a%3A3e%3A6b%3Aa8%08f0%3A61%3Ac0%3A18%3A3d%3A63%08f0%3A61%3Ac0%3A18%3A3d%3A62%08&userId=1589295871&token=AgEKIm2_TF7bztwuzXX1kz_gXFFk1spaLsC9bwzPGnxrD3UgXYHXaKVgzQkIVwr1IExvl3ZNZUxBuwAAAAAvGQAAh-FUdkvRAsxa0_pptCAn-2zqP9t7U49uoN4N6G7rB73m6E9EhMuZl1LHApwipY0P&mbc_core_version=0.3.106&mbc_business_version=0.3.106&client=android&screen_width=1080&screen_height=2295&firstPageAbtest=old&topic_session_id=321ec9d6-fe77-4b78-b0bc-3876b416d903&categoryViewPager=true&abStrategy=d&&accessibility=0&position=40.0082187%2C116.4873196&latlng=40.0082187%2C116.4873196&lng=116.4873196&lat=40.0082187&secretMd5Str=7C593D5BA227C8897DB15A51297DE9BA&utm_source=meituaninternaltest&utm_medium=android&utm_content=31e23934bf554c99925b5705a850443ea164988804916692613&utm_campaign=AgroupBgroupC0D100E0&msid=31e23934bf554c99925b5705a850443ea1649888049166926131687328103790&uuid=000000000000098671D04F1994C2F93223D90F6C85919B165404797200596702&userid=1589295871&p_appid=10&__reqTraceID=d9edf402-14a4-4102-9979-9d242ed27c90";
            String baseParamios = "__reqTraceID=AA8A3876-7FD0-451C-A9AC-D7D27456F1E8&accuracy=35&allowToTrack=1&clearTimeStamp=-1&firstPageAbtest=default&language=zh-CN&latlng=40.008435%2C116.487352&msid=31F382D4-CF7A-4FDD-ACD6-6969191AAF821687331665810129&mtPtLawSettings=%7B%22contentSwitch%22%3Afalse%2C%22adSwitch%22%3Afalse%7D&secretMd5Str=7C593D5BA227C8897DB15A51297DE9BA&token=AgHOJDViSlUExyH0uXrzDkKRWu_A92ZKhEfUy_8ZRQe25EK0HFg5rxbZdc436kzyjNciNGjL838P2wAAAAARGQAA5sSDjww8AsmLQOKomLeadgLBvf7mie376UZfg9h_vdNEHmdJYv4mcUK8RxUE_DL9&userid=1589295871&utm_campaign=AgroupBgroupD100H0&utm_content=000000000000075FA4186AE0449C9B2FB316C8C3527BCA165747692968784119&utm_medium=iphone&utm_source=Alpha&uuid=000000000000075FA4186AE0449C9B2FB316C8C3527BCA165747692968784119&wifi-cur=0&wifi-mac=f0%3A61%3Ac0%3A18%3A3d%3A71&wifi-name=Meituan&wifi-strength=&csecpkgname=com.meituan.imeituan-beta&csecplatform=2&csecversion=1.0.6&csecversionname=12.11.200";
            String versionParam = "&utm_term=" + version + "&version_name=" + version;
            String ciParam = "&ci=";
            int[] cityArray;
            if (cities == null || cities.isEmpty()) {
                cityArray = new int[] {1, 10}; // 默认请求北京、上海的数据，取并集
            } else {
                String[] cityStrArray = cities.split("、");
                cityArray = new int[cityStrArray.length];
                for (int i = 0; i < cityStrArray.length; i++) {
                    cityArray[i] = Integer.parseInt(cityStrArray[i]);
                }
            }
            for (int i = 0; i < cityArray.length; i++) {
                if (platform == 1) {
                    param = baseParamandr + versionParam + ciParam + cityArray[i] ;
                } else {
                    param = baseParamios + versionParam + ciParam + cityArray[i] ;
                }
                try {
                    String resp = HttpUtil.sendGet(url, param);
                    if (null != resp) {
                        JsonObject mockData = new JsonParser().parse(resp).getAsJsonObject();
                        JsonObject mockResult = MockUtil.create(api, mockData);
                        if (null != mockResult && mockResult.get("code").getAsInt() == 1) {
                            mockId = mockResult.get("data").getAsJsonObject().get("mockId").getAsString();
                            mockIds.add(mockId);
                        }
                        else {
                            log.error("请求首页接口出现异常：" + mockResult.get("msg").getAsString());
                            return "[3643117, 3643118]";
                        }
                    }
                } catch (Exception e) {
                    log.error("请求首页接口出现异常：" + e.getMessage());
                    return "[3643117, 3643118]";
                }

            }
        return mockIds.toString();
    } catch (Exception e) {
        log.error(e.getMessage());
    }
    // 零售专区下线，所以这里换两个mockId
    return "[3643117, 3643118]";
}



    public static void main(String[] args) {
        try {

            String url = "http://apimobile.vip.sankuai.com/aggroup/homepage/display";
            String param = "";
            String baseParamios = "category_native_strategy=A&client=iphone&lat=40.007183&lng=116.481185&nocache=true&offset=0&position=40.007183%2C116.481185&refreshArea=cateCategory&requestType=pullToRefresh&showStyle=1&supportId=1&tab=homepage&utm_medium=iphone&utm_source=Alpha&waimai_position=40.007183%2C116.481185";
            String baseParamandr = "category_native_strategy=A&client=iphone&lat=40.007183&lng=116.481185&nocache=true&offset=0&position=40.007183%2C116.481185&refreshArea=cateCategory&requestType=pullToRefresh&showStyle=1&supportId=1&tab=homepage&utm_medium=iphone&utm_source=Alpha&waimai_position=40.007183%2C116.481185";
            String versionParam = "&utm_term=" + "12.1.200" + "&version_name=" + "12.1.200";
            String ciParam = "&ci=1";
            param = baseParamios + versionParam + ciParam ;

            String resp = HttpUtil.sendGet(url, param);
            System.out.println(resp);
        }catch (Exception e){

        }






    }



}
