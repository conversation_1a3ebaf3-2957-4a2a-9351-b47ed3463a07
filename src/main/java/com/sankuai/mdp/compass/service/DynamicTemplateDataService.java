package com.sankuai.mdp.compass.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.entity.DynamicTemplateData;
import org.json.JSONObject;

public interface DynamicTemplateDataService {
    JSONObject uploadData(DynamicTemplateData dynamicTemplateData);
    IPage<DynamicTemplateData> getTemplateData(QueryRequest request, DynamicTemplateData dynamicTemplateData);
}
