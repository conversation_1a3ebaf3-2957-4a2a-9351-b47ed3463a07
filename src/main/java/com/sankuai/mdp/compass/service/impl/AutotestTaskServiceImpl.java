package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.entity.AutotestTask;
import com.sankuai.mdp.compass.mapper.AutotestTaskMapper;
import com.sankuai.mdp.compass.service.AutotestTaskSevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;


/**
 * Created by xieyongrui on 2019/12/7.
 */

@Service
public class AutotestTaskServiceImpl extends ServiceImpl<AutotestTaskMapper,AutotestTask> implements AutotestTaskSevice {
    @Autowired
    AutotestTaskMapper autotestTaskMapper;

    @Override
    public void updateTaskFinishTime(AutotestTask autotestTask) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("jobId",autotestTask.getJobId());
        queryWrapper.eq("deviceModel",autotestTask.getDeviceModel());
        AutotestTask autotestTask1 = autotestTaskMapper.selectOne(queryWrapper);
        Integer id = autotestTask1.getId();
        autotestTask.setId(id);
        Date date = new Date();
        autotestTask.setTaskFinishAt(date);
        autotestTaskMapper.updateById(autotestTask);
    }

    @Override
    public void add(AutotestTask autotestTask) {
        Date date = new Date();
        autotestTask.setTaskRunAt(date);
        autotestTaskMapper.insert(autotestTask);
    }


}
