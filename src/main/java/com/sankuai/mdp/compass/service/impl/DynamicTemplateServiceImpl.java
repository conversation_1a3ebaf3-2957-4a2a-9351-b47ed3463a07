package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.*;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.it.iam.common_base.utils.*;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Profession;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.common.utils.FileUtil;
import com.sankuai.mdp.compass.common.utils.StringUtil;
import com.sankuai.mdp.compass.controller.MockRuleController;
import com.sankuai.mdp.compass.entity.*;
import com.sankuai.mdp.compass.mapper.*;
import com.sankuai.mdp.compass.service.DynamicProcessService;
import com.sankuai.mdp.compass.service.DynamicTemplateService;
import com.sankuai.mdp.compass.service.OcrService;
import io.swagger.util.Json;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.mortbay.log.Log;
import org.mortbay.log.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.jayway.jsonpath.JsonPath.using;


/**
 * Created by xieyongrui on 2019/11/23.
 */

@Slf4j
@Service
public class DynamicTemplateServiceImpl implements DynamicTemplateService {
    ConanUtil conanUtil = new ConanUtil();
    static EvaUtil evaUtil = new EvaUtil();
    static MbcUtil mbcUtil = new MbcUtil();
    static ValueUtil valueUtil = new ValueUtil();
    static FileUtil fileUtil = new FileUtil();
    static ComConstant comConstant = new ComConstant();
    static StringUtil stu = new StringUtil();

    private Boolean hasResultUI = false;
    private Boolean hasResultEvent = false;

    DxUtil dxUtil = new DxUtil();

    @Autowired
    FailedReasonMapper failedReasonMapper;

    @Autowired
    CompatilityMapper compatilityMapper;

    @Autowired
    JobDetailMapper jobDetailMapper;

    @Autowired
    LayoutMgeDetailMapper layoutMgeDetailMapper;

    @Autowired
    MockRuleMapper mockRuleMapper;

    @Autowired
    DynamicDataMapper dynamicDataMapper;

    @Autowired
    OcrService ocrService;
    @Autowired
    NoticeUtil noticeUtil;

    @Autowired
    DynamicProcessService dynamicProcessService;

    OceanUtil oceanUtil = new OceanUtil();

    MockRuleController mockRuleController = new MockRuleController();

    ComUtil comUtil = new ComUtil();

    JsonParser jsonParser = new JsonParser();
    Configuration configuration = Configuration.builder()
            .options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
    Configuration asPathListConf = Configuration.builder()
            .options(Option.AS_PATH_LIST).build();
    Configuration confForList = Configuration.builder()
            .options(Option.REQUIRE_PROPERTIES).build();

    private static String DIFF = "/opt/meituan/script/diff/ssim.py";

    private static String OVERLAP = "/opt/meituan/script/diff/overlap.py";

    private String ROOT_JSON_PATH = "$.";

    //为适配标准mbc数据结构，把这里强写成biz
    private String ITEM_JSON_PATH = "biz.";

    private static String OS = "操作系统";

    public JsonObject getMockData(Integer mockId) {
        return new JsonParser().parse(MockUtil.get(mockId)).getAsJsonObject();
    }

    private MockRule getMockRule(Integer mockId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mock_id", mockId);
        MockRule mockRule = mockRuleMapper.selectOne(queryWrapper);
        return mockRule;
    }


    public String getXmlContentByTemplateName(String business, String templateName, String source, String scenes) {
        String xmlContent = null;
        try {
            String zipUrl;
            zipUrl = mbcUtil.getTemplateZipApi(templateName, scenes);
            if (null != zipUrl) {
                xmlContent = getXmlContentByZipUrl(templateName, zipUrl);
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        } finally {
            FileUtil.delFolder(comConstant.OUT_PUT);
            return xmlContent;
        }
    }

    @Override
    public String getXmlContentByZipUrl(String templateName, String zipUrl) {
        String xmlContent = null;
        try {
            if (null != zipUrl) {
                FileUtil.downloadAndReadFile(zipUrl, comConstant.OUT_PUT, templateName);
                ArrayList<String> fileList = new ArrayList<>();
                String filePath = comConstant.OUT_PUT + "/" + templateName + "/";
                fileUtil.getFileList(filePath, "", fileList);
                for (int i = 0; i < fileList.size(); i++) {
                    String fileName = fileList.get(i);
                    xmlContent = FileUtil.linesToStr(filePath + fileName);
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        } finally {
            FileUtil.delFolder(comConstant.OUT_PUT);
            return xmlContent;
        }
    }

    @Override
    public Boolean isContaintMge(String business, String templateName, String source, String scenes) {
        Boolean result = false;
        try {
            String xmlContent = getXmlContentByTemplateName(business, templateName, source, scenes);

            if (null != xmlContent) {
                if ((xmlContent.contains("auto-mge") || xmlContent.contains("bid")) &&
                        (xmlContent.contains("see-mge4-report") || xmlContent.contains("click-mge4-report"))) {
                    result = true;
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
        } finally {
            return result;
        }
    }

    public Boolean isMvEventType(int type) {
        if (7 == type || 6 == type || 2 == type) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean isMvRepeatReport(String business, String templateName, String source, JsonArray cfgMge, JsonArray mvArray, JsonObject mockData) {
        Boolean repeat = false;
        int size = mvArray.size();
        int exceptedCount = 0;
        String xml = getXmlContentByTemplateName(business, templateName, source, "");
        for (int i = 0; i < cfgMge.size(); i++) {
            JsonObject jsonObject = cfgMge.get(i).getAsJsonObject();
            Integer type = jsonObject.get("mgeType").getAsInt();
            if (!isMvEventType(type)) {
                continue;
            } else {
                String dPath = jsonObject.get("dpath").getAsString();
                if (dPath.contains("For.~")) {
                    String data = XmlUtil.getLoopVar(xml);
                    if (data != null) {
                        DocumentContext mockDataJson = JsonPath.using(configuration).parse(mockData.toString());
                        String path = ROOT_JSON_PATH + ITEM_JSON_PATH + data;
                        if (!mockData.has("biz")) {
                            path = ROOT_JSON_PATH + data;
                        }
                        JsonArray jsonArray = jsonParser.parse(mockDataJson.read(path).toString()).getAsJsonArray();
                        exceptedCount = exceptedCount + jsonArray.size();

                    }
                } else {
                    exceptedCount = exceptedCount + 1;
                }
            }
        }
        if (size > exceptedCount) {
            repeat = true;
        }
        return repeat;

    }

    private void resetFlag() {
        this.hasResultEvent = false;
        this.hasResultUI = false;
    }

    //云测回调
    @Override
    public String collectDynamicTestResult(JSONObject conanBody) throws Exception {
        try {
            JsonParser jsonParser = new JsonParser();
//            log.info(conanBody.toString());
            resetFlag();

            //解析云测回调参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            String conanId = jobData.get("id").getAsString();
            log.info("云测reportId：" + conanId);

            //根据云测reportId找到对应的动态布局测试job
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.last("where report like '%" + conanId + "%' or event_conan_id like '%" + conanId + "%'");
            CompatilityJob compatilityJob = compatilityMapper.selectOne(queryWrapper);
            if (null == compatilityJob) {
                log.info("job不存在");
            }
            int status = compatilityJob.getStatus();
            if (status == 2) {
                return "不收集已取消任务数据了";
            }

            log.info("对应job：" + compatilityJob.getId());
            Integer testType = compatilityJob.getType();
            String business = compatilityJob.getBusiness();
            String template = compatilityJob.getTemplateName();
            Integer jobId = compatilityJob.getId();
            JsonObject mock = jsonParser.parse(compatilityJob.getMockRule()).getAsJsonObject();
            String createdBy = compatilityJob.getCreatedBy();
            String scenes = compatilityJob.getScenes();
            String createdAt = sdf.format(compatilityJob.getCreatedAt());
            String address = compatilityJob.getAddress();
            String uiConanId = compatilityJob.getReport();
            String eventConanId = compatilityJob.getEventConanId();
            String misId = compatilityJob.getMisId();

            //埋点错误计数器
            int faultnum = 0;
            Boolean isUI = true;
            if (uiConanId != null && uiConanId.contains(conanId)) {
                isUI = true;
            } else if (eventConanId != null && eventConanId.contains(conanId)) {
                isUI = false;
            }

            //文件下载完成后存放的目录
            String dirPath = comConstant.OUT_PUT + jobId;

            String listPath = "";

            if (business.contains("search")) {
                listPath = ".gathers[0].items";
                business = "search";
            } else if (business.contains("groupbuy")) {
                //适配团购业务
                if (template != null && new LionUtil().getListValue("dynamicTopcardList").contains(template)) {
                    listPath = ".topCards";
                } else {
                    listPath = ".gathers[0].items";
                }
                business = "groupbuy";
            } else if (business.contains("shoppingcart")) {
                listPath = ".data.groups[0].items";
                business = "shoppingcart";
            } else if (business.contains("staggered_feed")) {
                listPath = ".data";
                business = "staggered_feed";
            } else {
                listPath = ".data.groups[1].items";
                business = "mine";
            }
            try {
                JsonArray taskArray = conanUtil.getTaskInfo(conanId);
                for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
                    JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
                    String id = task.get("id").getAsString();

                    //跳过已落库task
                    QueryWrapper<JobDetail> detailQueryWrapper = new QueryWrapper<JobDetail>().eq("task_id", id);
                    int detailUpdateFlag = jobDetailMapper.selectCount(detailQueryWrapper);
                    if (detailUpdateFlag > 0) {
                        log.info("id为 " + id + " 的任务已校验下载入库完成过了，跳过");
                        continue;
                    }

                    /**
                     * 1.获取文件下载链接
                     */
                    String deviceModel = task.get("deviceModel").getAsString();
                    String downloadURL = conanUtil.getDownloadURL(id);

                    if (null != downloadURL) {
                        /**
                         * 2.下载解压文件
                         */
                        if (downloadURL.contains("s3-img")) {
                            downloadURL = downloadURL.replace("s3-img", "s3");
                        }
                        if (!EnvUtil.isOnline() && downloadURL.contains("s3.meituan.net")) {
                            downloadURL = downloadURL.replace("s3.meituan.net", "s3plus.sankuai.com");
                        }
                        //解压测试报告文件
                        fileUtil.downloadAndReadFile(downloadURL, dirPath, id);

                        if (!isUI) {
                            log.info("埋点测试");
                            //记录埋埋点校验的异常数量
                            faultnum = mgeNew(id, mock.get(business).getAsJsonArray(), deviceModel, jobId, conanId, address, dirPath, listPath, scenes);
                            log.info("faultnum:", faultnum);
                        } else {
                            picNew(dirPath, task, conanId, jobId, template, testType);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("1.get output err");
                log.error(e.getMessage());
            } finally {
                synchronized (this) {
                    QueryWrapper queryWrapper2 = new QueryWrapper();
                    queryWrapper2.last("where report like '%" + conanId + "%' or event_conan_id like '%" + conanId + "%'");

                    String finishType = "UI";
                    CompatilityJob compatilityJob2 = compatilityMapper.selectOne(queryWrapper2);
                    uiConanId = compatilityJob2.getReport();
                    eventConanId = compatilityJob2.getEventConanId();
                    Boolean finishFlag = false;
                    if (uiConanId != null && uiConanId.contains(conanId)) {
                        finishFlag = handleUIProcess(compatilityJob2, conanId);
                    } else if (eventConanId != null && eventConanId.contains(conanId)) {
                        finishFlag = handleEventProcess(compatilityJob2, conanId);
                        finishType = "埋点";
                    }

                    if (finishFlag) {
                        fileUtil.delFolder(dirPath);
                    }
                    String typeStr = testType == 1 ? "埋点" : (testType == 0 ? "UI" : "UI&埋点");
                    DynamicProcess dynamicProcess = dynamicProcessService.getProcessByTemplateName(template);
                    Integer processId = dynamicProcess.getId();
                    String reportUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/dlautotest/job/JobDetail?processId=" + processId;

                    if (createdBy.equals("it_mbc") || createdBy.equals("it_compass")) {
                        createdBy = "liujiao11";
                    }
                    String testScenes = "测试";
                    if (scenes != "" && scenes.equals("smoke")) {
                        testScenes = "冒烟测试";
                    }

                    QueryWrapper queryWrapper3 = new QueryWrapper();
                    queryWrapper3.eq("id", jobId);
                    CompatilityJob compatilityJob3 = compatilityMapper.selectOne(queryWrapper3);
                    uiConanId = compatilityJob3.getReport();
                    eventConanId = compatilityJob3.getEventConanId();
                    int uiTotal = 0;
                    if (uiConanId != null) {
                        uiTotal = uiConanId.split("_").length;
                    }
                    int eventTotal = 0;
                    if (eventConanId != null) {
                        eventTotal = eventConanId.split("_").length;
                    }

                    JsonArray rdList = new MbcUtil().getPermission(template, Profession.rdList);
                    JsonArray qaList = new MbcUtil().getPermission(template, Profession.qaList);
                    JsonArray dataList = new MbcUtil().getPermission(template, Profession.dataList);
                    //解析设备信息 在插入detail后执行更新 (todo 后期可以做成异步)
                    JsonArray deviceListArray = jsonParser.parse(conanBody.get("devicelist").toString()).getAsJsonArray();
                    for (JsonElement deviceElement : deviceListArray) {
                        String deviceResolution = "";
                        String deviceModel = "";
                        JsonObject deviceObject = deviceElement.getAsJsonObject();
                        //兜底防止更新异常
                        if (null != deviceObject && deviceObject.has("deviceResolution")) {
                            deviceResolution = deviceObject.get("deviceResolution").getAsString();
                        }
                        if (null != deviceObject && deviceObject.has("deviceModel")) {
                            deviceModel = deviceObject.get("deviceModel").getAsString();
                        }
                        //更新jobdetail中的设备分辨率信息
                        updateJobDetailDeviceResolution(conanId, deviceModel, deviceResolution);
                    }

                    if (!finishFlag) {
                        //计算已完成的conanId个数
                        String uifinishedId = compatilityJob3.getFinishedId();
                        int uiFinishedCount = 0;
                        if (uifinishedId != null) {
                            uiFinishedCount = uifinishedId.split("_").length;
                        }
                        String eventFinishedId = compatilityJob3.getEventFinishedId();
                        int eventFinishedCount = 0;
                        if (eventFinishedId != null) {
                            eventFinishedCount = eventFinishedId.split("_").length;
                        }
                        int total = uiTotal + eventTotal;
                        int finish = uiFinishedCount + eventFinishedCount;

                        int leftCount = total - finish;


                        //已录入结果个数
                        QueryWrapper eventQueryWrapper = new QueryWrapper<>();
                        eventQueryWrapper.eq("job_id", jobId);
                        int eventResultCount = layoutMgeDetailMapper.selectCount(eventQueryWrapper);
                        QueryWrapper uiQueryWrapper = new QueryWrapper<>();
                        uiQueryWrapper.eq("job_id", jobId);
                        int uiResultCount = jobDetailMapper.selectCount(uiQueryWrapper);

                        //hasResultEvent和hasResultUI表示本次回调是否有结果录入
                        //修改为由spring框架创建对象
                        //NoticeUtil nt = new NoticeUtil();
                        //if(finishType.equals("埋点") && eventFinishedCount == 4 && eventResultCount > 0 && business!="search")
                        if (finishType.equals("埋点") && eventResultCount > 0 && eventFinishedCount == 4) {
                            // 本地调试将环境取反
                            if (EnvUtil.isOnline()) {
                                if (!misId.isEmpty()) {
                                    noticeUtil.alertNotice(compatilityJob, template, typeStr, testScenes, finishType, leftCount, createdAt, reportUrl, misId, false);
                                } else {
                                    //非search业务 向bp通知进度
                                    if (!business.contains("search")) {
                                        // if (business != "search"){
                                        //非search业务从lion获取对应bp列表 todo 后期可以采用"业务名称"+"bplist"拼接的方式支持动态调整业务及对应bplist,但需要考虑一致性的问题
                                        List<String> bpList = new LionUtil().getListValue("otherBpList");
                                        if (null != bpList && bpList.size() > 0 && faultnum > 0) {
                                            for (String s : bpList) {
                                                noticeUtil.alertNotice(compatilityJob, template, typeStr, testScenes, finishType, leftCount, createdAt, reportUrl, s, false);
                                            }
                                        }
                                    } else {
                                        //search业务从lion获取bp列表
                                        List<String> bpList = new LionUtil().getListValue("searchBpList");
                                        if (null != bpList && bpList.size() > 0 && faultnum > 0) {
                                            for (String s : bpList) {
                                                noticeUtil.alertNotice(compatilityJob, template, typeStr, testScenes, finishType, leftCount, createdAt, reportUrl, s, false);
                                            }
                                        }
                                    }
                                }
                            }
                        } else if ((finishType.equals("UI") && uiFinishedCount == 4 && uiResultCount > 0)) {
                            if (!misId.isEmpty()) {
                                noticeUtil.processNotice(compatilityJob, template, typeStr, testScenes, finishType, leftCount, createdAt, reportUrl, misId);
                            } else {
                                for (JsonElement user : qaList) {
                                    // 本地调试将环境取反
                                    if (EnvUtil.isOnline()) {
                                        noticeUtil.processNotice(compatilityJob, template, typeStr, testScenes, finishType, leftCount, createdAt, reportUrl, user.getAsString());
                                    }
                                }
                            }
                        }
                    } else {
                        String wordUrl = "https://km.sankuai.com/page/634769563";
                        // 本地调试将环境取反
                        if (EnvUtil.isOnline()) {
                            if (!misId.isEmpty()) {
                                noticeUtil.finishNotice(compatilityJob, template, typeStr, testScenes, createdAt, sdf.format(new Date()), reportUrl, wordUrl, misId);
                            } else {
                                for (JsonElement user : rdList) {
                                    noticeUtil.finishNotice(compatilityJob, template, typeStr, testScenes, createdAt, sdf.format(new Date()), reportUrl, wordUrl, user.getAsString());
                                }
                                for (JsonElement user : qaList) {
                                    noticeUtil.finishNotice(compatilityJob, template, typeStr, testScenes, createdAt, sdf.format(new Date()), reportUrl, wordUrl, user.getAsString());
                                }
                            }
                        }
                        //通知当前阶段负责人
                        dynamicProcessService.inform(template);

                    }
                    return conanId;
                }
            }
        } catch (Exception e) {
            log.info(e.toString());
            // 本地调试将环境取反
            if (EnvUtil.isOnline()) {
                dxUtil.sendToPersionByCompass("MBC模版自动化测试提醒：\n 云测回调发生异常，请及时处理！！！\n 异常信息：" + e.getMessage() + "\n conanBody：" + conanBody.toString(), "liujiao11");

            }
            return null;
        }

    }

    /**
     * 处理图片
     *
     * @param dirPath
     * @param task
     * @param conanId
     * @param jobId
     * @param template
     * @param testType
     */
    public void picNew(String dirPath, JsonObject task, String conanId, Integer jobId, String template, Integer testType) {
        String deviceVersion = task.get("deviceVersion").getAsString();
        String appVersion = task.get("appVersion").getAsString();
        Double totalTime = task.get("totalTime").getAsDouble();
        String deviceModel = task.get("deviceModel").getAsString();
        String id = task.get("id").getAsString();
        JsonObject taskPicLink = new JsonObject();
        JsonObject picLink = new JsonObject();


        /**
         * 1.获取所有图片文件
         */
        ArrayList<String> fileList = new ArrayList<>();
        String picPath = dirPath + "/" + id + "/link/";
        fileUtil.getFileList(picPath, "", fileList);
        /**
         * 2.融合图片 （7.6日更新：融合逻辑需要适配图片链接形式）
         */
//        if (fileList.size() > 0) {
//            try {
//                String templateName = fileList.get(0).split("/")[0];
//                File folderAbnormalMerge = new File(picPath + templateName + "/abnormalmerge");
//                if (folderAbnormalMerge.exists() && folderAbnormalMerge.isDirectory()) {
//                    new UiUtil().doMerge(picPath, templateName, "abnormal");
//                    fileUtil.delFolder(picPath + templateName + "/abnormalmerge");
//                }
//                File folderNormalMerge = new File(picPath + templateName + "/normalmerge");
//                if (folderNormalMerge.exists() && folderNormalMerge.isDirectory()) {
//                    new UiUtil().doMerge(picPath, templateName, "normal");
//                    fileUtil.delFolder(picPath + templateName + "/normalmerge");
//                }
//            } catch (Exception e) {
//                log.error("图片拼接错误>" + e.getMessage());
//            }
//        }

        for (int i = 0; i < fileList.size(); i++) {
            /**
             * 3.读图片链接
             */
            String fileName = fileList.get(i);
            String link = fileUtil.read(picPath + fileName);
            String[] list = fileName.split("\\.txt");
            String key = list[0];

            /**
             * 4.根据文件目录解析生成插入生成json
             */
            if (null != link) {
                taskPicLink = manipulatePic(key, taskPicLink, link);
            }
        }
        /**
         * 5.遍历图片 json 分别将正常图、数据构造异常图、跳转图插入数据库
         * 每个 mock 数据对应一条 jobDetail记录
         */

        Map<String, Object> uiSet = new HashMap<String, Object>();
        Map<String, String> mergedUrlSet = new HashMap<String, String>();

        for (Map.Entry entry : taskPicLink.entrySet()) {
            String key = entry.getKey().toString();
            JsonObject value = jsonParser.parse(entry.getValue().toString()).getAsJsonObject();

            for (Map.Entry typeEntry : value.entrySet()) {
                String type = typeEntry.getKey().toString();

                if ("operation".equals(type)) {
                    setOperationPicUrl(jsonParser.parse(typeEntry.getValue().toString()).getAsJsonObject(), conanId, deviceModel, deviceVersion, appVersion, jobId, key, id, totalTime, testType);

                } else {
                    if ("normal".equals(type)) {
                        for (Map.Entry normalEntry : jsonParser.parse(typeEntry.getValue().toString()).getAsJsonObject().entrySet()) {
                            picLink.addProperty(key, jsonParser.parse(normalEntry.getValue().toString()).getAsString());
                        }
                    } else {
                        UiUtil uiUtil = new UiUtil();
                        for (Map.Entry abnormalEntry : jsonParser.parse(typeEntry.getValue().toString()).getAsJsonObject().entrySet()) {
                            picLink.addProperty(key, abnormalEntry.getValue().toString());
                            //这里的逻辑是：根据mockID把【重叠】【OCR】两部分的返回放进list，再把list按mockID放进uiSet，在setPicUrl的时候再按mockID拆开落库。
                            try {
                                ArrayList<Object> uiList = new ArrayList<>();
                                String mockId = abnormalEntry.getKey().toString();
                                //重叠校验
                                uiList = uiUtil.overlapJudgment(mockId, id, jobId);
                                //文字展示校验
                                String textContent = ocrService.textShowJudement(mockId, id, template, jobId);
                                uiList.add(textContent);
                                //TODO 通用异常校验
                                String[] bugContent = ocrService.recognizeTemplateUIBug(mockId, id, template, jobId);
                                uiList.add(bugContent[0]);
                                uiSet.put(mockId, uiList);
                                mergedUrlSet.put(mockId, bugContent[1]);
                            } catch (Exception e) {
                                log.error("conanId：" + conanId + "，文字校验错误>>" + e.getMessage());
                            }
                        }
                    }
                    addPicContent(type, jsonParser.parse(typeEntry.getValue().toString()).getAsJsonObject(), conanId, deviceModel, deviceVersion, appVersion, jobId, key, id, totalTime, testType, uiSet, mergedUrlSet);
                }
            }

        }
    }

    /**
     * 更新UI测试进度
     *
     * @param compatilityJob
     * @param conanId
     * @return 整个测试任务是否结束
     */
    private Boolean handleUIProcess(CompatilityJob compatilityJob, String conanId) {
        Boolean result = false;
        String finishedId = compatilityJob.getFinishedId();
        String uiConanId = compatilityJob.getReport();
        String finishFlag = "0";
        //判断UI测试是否完成
        if (finishedId == null) {
            finishedId = "";
        }
        if (finishedId == "") {
            finishedId = conanId;
            compatilityJob.setFirstFinish(new Date());
        } else if (!finishedId.contains(conanId)) {
            finishedId = finishedId + "_" + conanId;
        }
        if (finishedId.length() == uiConanId.length()) {
            compatilityJob.setFinishAt(new Date());

            String eventConanId = compatilityJob.getEventConanId();
            String eventFinishedId = compatilityJob.getEventFinishedId();

            if (eventConanId != null) {
                if (eventFinishedId != null && eventFinishedId.length() == eventConanId.length()) {
                    compatilityJob.setStatus(1);
                    result = true;
                }
            } else {
                compatilityJob.setStatus(1);
                result = true;
            }
        }

        compatilityJob.setFinishedId(finishedId);
        compatilityMapper.updateById(compatilityJob);
        return result;

    }

    /**
     * 更新埋点测试进度
     *
     * @param compatilityJob
     * @param conanId
     * @return 整个测试任务是否结束
     */
    private Boolean handleEventProcess(CompatilityJob compatilityJob, String conanId) {
        Boolean result = false;
        String eventfinishedId = compatilityJob.getEventFinishedId();
        String eventConanId = compatilityJob.getEventConanId();
        String finishFlag = "0";
        //判断UI测试是否完成
        if (eventfinishedId == null) {
            eventfinishedId = "";
        }
        if (eventfinishedId == "") {
            eventfinishedId = conanId;
            compatilityJob.setEventFirstFinish(new Date());
        } else if (!eventfinishedId.contains(conanId)) {
            eventfinishedId = eventfinishedId + "_" + conanId;
        }
        if (eventfinishedId.length() == eventConanId.length()) {
            compatilityJob.setEventFinishAt(new Date());

            String uiConanId = compatilityJob.getReport();
            String uiFinishedId = compatilityJob.getFinishedId();
            if (uiConanId != null) {
                if (uiFinishedId != null && uiFinishedId.length() == uiConanId.length()) {
                    compatilityJob.setStatus(1);
                    result = true;
                }
            } else {
                compatilityJob.setStatus(1);
                result = true;
            }
        }

        compatilityJob.setEventFinishedId(eventfinishedId);
        compatilityMapper.updateById(compatilityJob);
        return result;
    }


    /**
     * 插入单个埋点case的上报结果
     *
     * @param cfgBid
     * @param jobId
     * @param eventDesc
     * @param mockData
     * @param name
     * @param taskId
     * @param reportIds
     * @param cfgObject
     * @param nm
     * @param dirPath
     * @param target
     * @param deviceModel
     * @param extra
     */
    public void addEventContent(String cfgBid, Integer jobId, String eventDesc, String eventType, JsonObject mockData,
                                String name, String taskId, String reportIds, JsonObject cfgObject, String nm,
                                String dirPath, JsonObject target, String deviceModel, String extra, String picLinkPath) {
        JsonObject content = target;
        String picPath = "";
        String componentName = "";
        boolean shouldInsert = true;
        if (content != null && content.has("operationComponent")) {
            componentName = content.get("operationComponent").getAsString();
        }
        log.info("componentName:{}", componentName);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("bid", cfgBid);
        queryWrapper.eq("job_id", jobId);
        queryWrapper.eq("event_desc", eventDesc);
        queryWrapper.eq("event_type", eventType);
        LayoutMgeDetail mgeDetail = layoutMgeDetailMapper.selectOne(queryWrapper);
        if (mgeDetail == null) {
            mgeDetail = new LayoutMgeDetail();
            mgeDetail.setMockData(mockData.toString());
            mgeDetail.setTemplateName(name);
            mgeDetail.setEventDesc(eventDesc);
            mgeDetail.setTaskId(taskId);
            mgeDetail.setJobId(jobId);
            mgeDetail.setConanId(reportIds);
            mgeDetail.setBid(cfgBid);
            mgeDetail.setConfig(cfgObject.toString());
            mgeDetail.setOceanMge(oceanUtil.getEntityIdentifierInfo(cfgBid).toString());
            mgeDetail.setEventType(nm);
            // 适配：有点击无埋点上报场景，若该控件已经有埋点落表了，则该条数据不需要落表
            if (componentName != null && !componentName.isEmpty()) {
                QueryWrapper queryWrapperNew = new QueryWrapper();
                queryWrapperNew.eq("job_id", jobId);
                queryWrapperNew.eq("event_type", eventType);
                queryWrapperNew.eq("event_desc", eventDesc);
                queryWrapperNew.like("operation_component", "%" + componentName.split("_")[0] + "%");
                LayoutMgeDetail mgeDetailNew = layoutMgeDetailMapper.selectOne(queryWrapperNew);
                if (mgeDetailNew != null) {
                    log.info(componentName + "控件已落库，不重复插入");
                    shouldInsert = false;
                    if (!cfgBid.contains("component") && mgeDetailNew.getBid().contains("component")) {
                        mgeDetail.setId(mgeDetailNew.getId());
                    }
                }
            }
            if (shouldInsert) {
                layoutMgeDetailMapper.insert(mgeDetail);
            }
        }
        if (mgeDetail.getPic() == null || mgeDetail.getPic() == "") {
            try {
                String picLink = null;
                if (nm.equals("MV")) {
                    picLink = fileUtil.read(picLinkPath);
                } else {
                    picLinkPath = dirPath + "/" + taskId + "/link/" + name + "/operation/" + (componentName.isEmpty() ? "click" : componentName + "/click") + ".txt";
                    // 若没有图片链接，埋点也需要落库，避免空指针异常
                    if (!new File(picLinkPath).exists()) {
                        picLink = "";
                    } else {
                        picLink = FileUtil.read(picLinkPath);
                    }
                }
                //ComponentExtra的埋点可能是重复的
                if (!componentName.contains("ComponentExtra")) {
                    if (null != picLink && "" != picLink) {
                        if (deviceModel.contains("iPhone")) {
                            mgeDetail.setIosPic(picLink);
                        } else {
                            mgeDetail.setAndroidPic(picLink);
                        }
                    }
                }
                // componentName不为空才设置，避免设置后被""覆盖；若有多个控件上报的埋点相同，则拼接控件名称
                if (!componentName.isEmpty()) {
                    String currentOperationComponent = mgeDetail.getOperationComponent();
                    if (null == currentOperationComponent) {
                        mgeDetail.setOperationComponent(componentName);
                    } else if (!currentOperationComponent.contains(componentName.split("_")[0])) {
                        mgeDetail.setOperationComponent(mgeDetail.getOperationComponent() + "_" + componentName);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                //本方法没有对异常进行处理所以将捕获到的异常抛给上游调用方,由调用方捕获并做对应处理
                throw e;
            }
        }

        if (deviceModel.contains("iPhone")) {
            if (mgeDetail.getIosContent() != null && mgeDetail.getIosContent() != "" && mgeDetail.getIosResult().length() < 1) {

            } else {
                //已有上报仅字段错误，本次判断为未上报，则不更新
                if (mgeDetail.getIosResult() != null && mgeDetail.getIosResult().length() > 1 && extra.contains("未上报")) {
                } else {
                    mgeDetail.setIosContent(content == null ? null : content.toString());
                    mgeDetail.setIosResult(extra);
                    // 如果是控件方式查找埋点，若Android无问题记录，则设置为""，避免测试报告展示异常;不为null表示已有值，则不修改
                    if (cfgBid.contains("component") && null == mgeDetail.getAndroidResult()) {
                        mgeDetail.setAndroidResult("");
                        mgeDetail.setAndroidContent("");
                    }
                }
            }
        } else {
            if (mgeDetail.getAndroidResult() != null && mgeDetail.getAndroidContent() != null && mgeDetail.getAndroidContent() != "" && mgeDetail.getAndroidResult().length() < 1) {

            } else {
                //已有上报仅字段错误，本次判断为未上报，则不更新
                if (mgeDetail.getAndroidResult() != null && mgeDetail.getAndroidResult().length() > 1 && extra.contains("未上报")) {
                } else {
                    mgeDetail.setAndroidContent(content == null ? null : content.toString());
                    mgeDetail.setAndroidResult(extra);
                    // 如果是控件方式查找埋点，若iOS无问题记录，则设置为""，避免测试报告展示异常;不为null表示已有值，则不修改
                    if (cfgBid.contains("component") && null == mgeDetail.getIosResult()) {
                        mgeDetail.setIosResult("");
                        mgeDetail.setIosContent("");
                    }
                }
            }
        }
        log.info("extra：", extra);
        layoutMgeDetailMapper.updateById(mgeDetail);
        hasResultEvent = true;
    }

    /**
     * 校验埋点结果
     *
     * @param taskId
     * @param mock
     * @param deviceModel
     * @param jobId
     * @param reportIds
     * @param address
     * @param dirPath
     * @param listPath
     */
    public int mgeNew(String taskId, JsonArray mock, String deviceModel, Integer jobId,
                      String reportIds, String address, String dirPath, String listPath, String scenes) {
        ArrayList<String> mgeFileList = new ArrayList<>();
        String mgePath = dirPath + "/" + taskId + "/mge/";
        //获取mge(埋点)路径下所有文件,用mgeFileList接收list
        fileUtil.getFileList(mgePath, "", mgeFileList);
        //埋点错误计数器
        int faultnum = 0;
        if (mgeFileList.size() <= 0) {
            return faultnum;
        }
        ArrayList<String> picFileList = new ArrayList<>();
        String picPath = dirPath + "/" + taskId + "/link/";
        String picName = "";
        fileUtil.getFileList(picPath, "", picFileList);
        if (picFileList.size() > 0) {
            for (int i = 0; i < picFileList.size(); i++) {
                if (picFileList.get(i).contains("beforeJump")) {
                    picName = picPath + picFileList.get(i);
                    break;
                }
            }
            if (picName == "") {
                picName = picPath + picFileList.get(0);
            }
        }

        int mockId = 0;

        String output = fileUtil.read(mgePath + mgeFileList.get(0));
        if (null != output) {
            log.info("fileList：" + mgeFileList.get(0));
            String[] tempMge = sortMgeByBidTest(output);
            String mge = "";
            String clickedNoMge = "";
            if (null != tempMge[0]) {
                mge = tempMge[0];
            }
            // 处理mge为空的情况，即埋点测试未获取到有效埋点
            if (mge.isEmpty()) {
                throw new IllegalArgumentException("mge为空，未获取到有效埋点 ");
            }
            JsonObject jsonObject = jsonParser.parse(mge).getAsJsonObject();
            // name为模板名，reported为埋点
            for (Map.Entry entry : jsonObject.entrySet()) {
                String name = entry.getKey().toString(); //模版名称
                JsonObject reported = jsonObject.getAsJsonObject(name);

                //get mock数据
                JsonObject mockData = null;
                try {
                    JsonArray mockArray = mock.get(0).getAsJsonObject().get("mock").getAsJsonArray();
                    for (int mockIndex = 0; mockIndex < mockArray.size(); mockIndex++) {
                        if ("normal".equals(mockArray.get(mockIndex).getAsJsonObject().get("type").getAsString())) {
                            mockId = mockArray.get(mockIndex).getAsJsonObject().get("mockId").getAsInt();
                            break;
                        }
                    }
                    mockData = getMockData(mockId);
                    DocumentContext documentContext = using(configuration).parse(mockData.toString());
//                        log.info(mockData.toString());
                    JsonArray array = jsonParser.parse(documentContext.read("$" + listPath).toString()).getAsJsonArray();
                    mockData = jsonParser.parse(array.get(0).toString()).getAsJsonObject();
                } catch (Exception e) {
                    log.error("获取mock数据出错", e.getMessage());
                }

                //根据MBC平台的配置数据，检查是否有漏报bid
                JsonArray cfgMgeJson = getMgeConfiguration(name, address, scenes).get("mgeCfgList").getAsJsonArray();

                //筛出配置的所有tag
                JsonObject cfgTagRecord = new JsonObject();
                JsonObject reportedTag = new JsonObject();
                for (int i = 0; i < cfgMgeJson.size(); i++) {
                    JsonObject cfgObject = cfgMgeJson.get(i).getAsJsonObject();
                    int mgeType = cfgObject.get("mgeType").getAsInt();

                    if (mgeType == 3) {
                        String dpath = cfgObject.get("dpath").getAsString();
                        cfgTagRecord.add(dpath, cfgObject);
                    }

                }

                //遍历配置列表，校验每个配置是否有对应上报
                for (int i = 0; i < cfgMgeJson.size(); i++) {
                    String extra = "";
                    JsonObject cfgObject = cfgMgeJson.get(i).getAsJsonObject();

                    int mgeType = cfgObject.get("mgeType").getAsInt();
                    String cfgBid = handleExpression(mockData, cfgObject.get("bid").getAsString());
                    if (cfgBid != null && cfgBid.equals(OS)) {
                        //带操作系统判断的暂时跳过
                        continue;
                    }

                    String cfgCid = handleExpression(mockData, cfgObject.get("cid").getAsString());
                    String dPath = cfgObject.get("dpath").getAsString();
                    String eventDesc = cfgObject.get("name").getAsString();

                    String nm = "";
                    if (isMvEventType(mgeType)) {
                        nm = "MV";
                    } else {
                        nm = "MC";
                    }

                    JsonObject target = new JsonObject();

                    if (mgeType == 3) {  //跳过tag
                        continue;
                    } else {
                        extra += checkBid(reported, cfgBid, cfgCid, nm, target, cfgObject, mockData, listPath);
                        if (!extra.isEmpty()) {
                            //埋点校验错误记录
                            faultnum++;
                        }
                    }

                    if (target != null && target.has("data")) {
                        target = target.getAsJsonObject("data");
                    }
                    try {
                        //保存结果 捕获调用代码中可能出现的异常
                        addEventContent(cfgBid, jobId, eventDesc, nm, mockData, name, taskId, reportIds, cfgObject, nm, dirPath, target, deviceModel, extra, picName);
                    } catch (Exception e) {
                        log.error("埋点数据入库error", e.getMessage());
                        //埋点数据插入错误记录
                        //faultnum++;
                    }
                    try {
                        //校验MC带的tag:一级group是否正确、二级cid是否正确、三级业务参数是否正确
                        if (nm.equals("MC") && target != null) {
                            //根据dpath找到关联bid和tag
                            nm = "TAG";
                            String dpath = cfgObject.get("dpath").getAsString();
                            if (cfgTagRecord != null && cfgTagRecord.has(dpath)) {
                                JsonObject tagCfgObject = cfgTagRecord.getAsJsonObject(dpath);
                                if (tagCfgObject != null) {
                                    String tagExtra = "";
                                    if (target == null) {
                                        tagExtra = "ERROR：tag未上报\n";
                                        //未上报错误记录
                                        faultnum++;
                                    } else {
                                        JsonObject actualTagMockData = getActualMockData(target, listPath);
                                        if (actualTagMockData != null) {
                                            mockData = actualTagMockData;
                                        }
                                        tagExtra = checkTag(target, tagCfgObject, mockData);
                                        if (!"".equals(tagExtra)) {
                                            //埋点校验错误记录
                                            faultnum++;
                                        }
                                    }
                                    String tagDesc = tagCfgObject.get("name").getAsString();
                                    addEventContent(cfgBid, jobId, tagDesc, nm, mockData, name, taskId, reportIds, cfgObject, nm, dirPath, target, deviceModel, tagExtra, null);
                                    reportedTag.addProperty(dpath, true);
                                }
                            }

                        }
                    } catch (Exception e) {
                        log.error("校验tag出错>" + e.getMessage());
                        //埋点校验错误记录
                        //faultnum++;
                    }

                }

                //检查是否有未上报tag
                Iterator it = cfgTagRecord.keySet().iterator();
                while (it.hasNext()) {
                    String path = it.next().toString();
                    JsonObject object = cfgTagRecord.getAsJsonObject(path);
                    String nm = "TAG";
                    String tagExtra = "";
                    if (!reportedTag.has(path)) {
                        tagExtra = "ERROR：tag未上报";
                        //未上报错误记录
                        faultnum++;
                        String cfgBid = handleExpression(mockData, object.get("bid").getAsString());
                        String tagDesc = object.get("name").getAsString();
                        JsonObject target = new JsonObject();
                        try {
                            //增加异常捕获逻辑
                            addEventContent(cfgBid, jobId, tagDesc, nm, mockData, name, taskId, reportIds, object, nm, dirPath, target, deviceModel, tagExtra, null);
                        } catch (Exception e) {
                            log.error("addEventContent err:", e.getMessage());
                            //埋点插入错误记录
                            //faultnum++;
                        }

                    }
                }
            }
            // 增加有点击无埋点上报的处理
            if (null != tempMge[1]) {
                clickedNoMge = tempMge[1];
                JsonObject noMgeReport = jsonParser.parse(clickedNoMge).getAsJsonObject();
                for (Map.Entry<String, JsonElement> entry : noMgeReport.entrySet()) {
                    String tempName = entry.getKey();
                    JsonArray reported = entry.getValue().getAsJsonArray();
                    for (int i = 0; i < reported.size(); i++) {
                        faultnum++;
                        JsonObject target = reported.get(i).getAsJsonObject();
                        String bid = target.get("operationComponent").getAsString().split("_")[0];
                        String nm = target.get("nm").getAsString();
                        String extra = target.get("desc").getAsString();
                        try {
                            //保存结果 捕获调用代码中可能出现的异常
                            addEventContent(bid, jobId, "", nm, new JsonObject(), tempName, taskId, reportIds, new JsonObject(), nm, dirPath, target, deviceModel, extra, picName);
                        } catch (Exception e) {
                            log.error("埋点数据入库error" + e.getMessage());
                        }
                    }
                }
            }
        }
        return faultnum;
    }

    /**
     * 校验单个埋点case：bid、cid、lab、tag
     *
     * @param reported  已上报数据
     * @param cfgBid    配置bid
     * @param cfgCid    配置cid
     * @param nm        埋点类型
     * @param target
     * @param cfgObject 配置元数据
     * @param mockData  模版json数据
     * @return
     */
    public String checkBid(JsonObject reported, String cfgBid, String cfgCid, String nm,
                           JsonObject target, JsonObject cfgObject, JsonObject mockData, String listPath) {
        JsonArray mgeSet = null;

        //No1.判断bid是否有上报
        String extra = "";
        if (!reported.has(cfgBid)) {
            extra = "ERROR：" + cfgBid + "未上报\n";
        } else {
            mgeSet = reported.getAsJsonArray(cfgBid);
            //判断个数，是否少于配置的个数（bid相同有几种情况：a.大卡片只报一个；b.小卡片报多个；c.有缓存所有报多次）
            //--------------配置的个数计算方法：不带for循环+for循环的列表长度

            JsonArray mgeSetCopy = mgeSet.deepCopy();

            String tempExtra = "";
            for (int index = 0; index < mgeSetCopy.size(); index++) {
                boolean isThisMge = false;
                tempExtra = "";
                JsonObject item = mgeSetCopy.get(index).getAsJsonObject();
                target.add("data", item.deepCopy());

                //No2.校验cid
                JsonObject data = item.getAsJsonObject("data");
                String cid = data.get("val_cid").getAsString();
                if (!cid.equals(cfgCid)) {
                    tempExtra += "ERROR：cid上报错误。应为" + cfgCid + " 实为" + cid + "\n";
                }


                //No3.校验业务参数：与ocean方法对比，主要看是否有漏报参数
                JsonObject diff = diffFromOcean(data, cfgBid, nm);
                tempExtra += diff.get("extra").getAsString();

                //No4.校验值：透传字段与mock数据对比
                JsonObject lab = data.get("val_lab").getAsJsonObject();
                // 更新mockData数据：埋点测试结果中已匹配对应的mockId，优先直接从报告中获取对应的mockId，用于后续的埋点value校验，否则使用默认的第一条normal数据
                JsonObject actualMockData = getActualMockData(item, listPath);
                if (actualMockData != null) {
                    mockData = actualMockData;
                }
                tempExtra += checkLab(mockData, cfgObject.getAsJsonArray("valLab"), lab);
                if (tempExtra.contains("buttonNameEqual")) {
                    tempExtra = tempExtra.replace("buttonNameEqual", "");
                    isThisMge = true;
                }

                if (tempExtra.isEmpty() || isThisMge) {
                    reported.remove(cfgBid);
                    mgeSetCopy.remove(index);
                    if (mgeSetCopy.size() > 0) {
                        reported.add(cfgBid, mgeSetCopy);
                    }
                    break;
                }

            }
            if (!tempExtra.isEmpty()) {
                extra += tempExtra;
            }
        }
        return extra;
    }

    public JsonObject getActualMockData(JsonObject item, String listPath) {
        JsonObject mockData = null;
        if (item.has("mockId")) {
            mockData = getMockData(item.get("mockId").getAsInt());
            DocumentContext documentContext = using(configuration).parse(mockData.toString());
            JsonArray array = jsonParser.parse(documentContext.read("$" + listPath).toString()).getAsJsonArray();
            mockData = jsonParser.parse(array.get(0).toString()).getAsJsonObject();
        } else {
            log.info("mockId不存在");
        }
        return mockData;
    }

    /**
     * 校验tag：一级group是否正确、二级cid是否正确、三级业务参数是否正确
     *
     * @param reported
     * @param cfgObject
     * @param mockData
     * @return
     */
    public String checkTag(JsonObject reported, JsonObject cfgObject, JsonObject mockData) {
        String cfgProperty = cfgObject.get("property").getAsString();
        cfgProperty = handleExpression(mockData, cfgProperty);
        String cfgCid = cfgObject.get("cid").getAsString();
        cfgCid = handleExpression(mockData, cfgCid);

        JsonObject data = reported.getAsJsonObject("data");

        String tempExtra = "";

        //判断上报MC中是否带tag
        if (data == null || !data.has("tag")) {
            tempExtra += "ERROR：tag未上报";
            return tempExtra;
        }

        //tag一级key是否正确
        JsonObject reportedTag = data.getAsJsonObject("tag");
        Boolean tagGroupCorrect = false;
        Iterator iterator = reportedTag.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next().toString();

            if (key.equals(cfgProperty)) {
                tagGroupCorrect = true;
                break;
            }
        }

        if (!tagGroupCorrect) {
            tempExtra += "ERROR：tag未上报";
            return tempExtra;
        }

        //tag二级key是否正确（cid）
        JsonObject level2Object = reportedTag.getAsJsonObject(cfgProperty);
        Boolean tagCidCorrect = false;
        Iterator cidIterator = level2Object.keySet().iterator();
        while (cidIterator.hasNext()) {
            String key = cidIterator.next().toString();

            if (key.equals(cfgCid)) {
                tagCidCorrect = true;
                break;
            }
        }

        if (!tagCidCorrect) {
            tempExtra += "ERROR：tag 二级参数(cid)上报错误或未上报。应为 " + cfgCid + "\n";
            return tempExtra;
        }

        //tag三级业务参数是否正确
        JsonObject reportedTagLab = level2Object.getAsJsonObject(cfgCid);
        tempExtra += checkLab(mockData, cfgObject.getAsJsonArray("valLab"), reportedTagLab);

        //上报的tag.bid VS 上报的val_bid
        String valBid = data.get("val_bid").getAsString();
        if (!reportedTagLab.has("bid")) {
            tempExtra += "ERROR：tag三级参数bid未上报。应为 " + valBid + "\n";
        } else if (!reportedTagLab.get("bid").getAsString().equals(valBid)) {
            tempExtra += "ERROR：tag三级参数bid上报错误。应为 " + valBid + "\n";

        }
        tempExtra = tempExtra.replace("buttonNameEqual", "");

        return tempExtra;
    }

    /**
     * 校验业务参数：透传字段上报值是否正确
     *
     * @param mockData  mock数据
     * @param configLab 配置数据
     * @param lab       实际上报数据
     * @return
     */
    public String checkLab(JsonObject mockData, JsonArray configLab, JsonObject lab) {
        String result = "";
        for (int i = 0; i < configLab.size(); i++) {
            JsonObject jsonObject = configLab.get(i).getAsJsonObject();
            String key = jsonObject.get("key").getAsString();
            if (!lab.has(key)) {
                result += "ERROR：lab缺失参数" + key + "\n";
                continue;
            }
            String cfgValue = jsonObject.get("val").getAsString();

            String indexPattern = "(\\{[a-z]})";
            Pattern p = Pattern.compile(indexPattern);
            Matcher m1 = p.matcher(cfgValue);
            Boolean hasIndex = false;
            while (m1.find()) {
                String s = m1.group();
                log.info(s);
                hasIndex = true;
                break;
            }
            if (hasIndex) {
                continue;
            }

            if (cfgValue.contains("环境变量")) {
                continue;
            }
            //跳过带For循环的配置
            String pattern = "(\\{For\\d+})";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(cfgValue);
            if (m.find()) {
                String s = m.group();
                log.info("带For循环：" + s);
                continue;
            }

            //专门适配搜索：跳过客户端传值这种，目前已知搜索固定三个字段为客户端传值（extra、gatherTrace、strategyTrace）
            if (cfgValue.contains("extra.") || cfgValue.contains("gatherTrace.") || cfgValue.contains("strategyTrace.")) {
                continue;
            }


            String expected = handleExpression(mockData, cfgValue);
            String actual = lab.get(key).toString();

            if (!actual.contains("{")) {
                actual = actual.replaceAll("\"", "");
                try {
                    BigDecimal bd = new BigDecimal(expected);
                    expected = bd.toPlainString();
                    if (new StringUtil().isZero(expected)) {
                        expected = "0";
                    }
                } catch (Exception e) {
                    //避免出现科学计数法问题，强转下试试
                }
                boolean vauleEqual = false;
                if (stu.isDouble(actual) && stu.isDouble(expected)) {
                    vauleEqual = stu.numEqual(actual, expected);
                }
                if (!actual.equals(expected) && !vauleEqual) {
                    if (key.equals("button_name")) {
                        result += "WARNING：没有录制到button_name为 " + expected + " 的埋点，请人工确认" + "\n";
                    } else {
                        result += "ERR：lab参数" + key + "值错误。应为" + expected + " 实为" + actual + "\n";
                    }
                } else if (key.equals("button_name")) {
                    result += "buttonNameEqual";
                }
            } else { //有两种包含{}的情况，例如1.trace为json结构（正常情况） 2.直接上报了配置代码（bug）
                if (actual.equals(cfgValue)) {
                    result += "ERR：lab参数" + key + "值错误。\n";
                }
            }
        }

        return result;
    }

    /**
     * 处理n元表达式，返回表达式取值内容
     *
     * @param data
     * @param expression
     * @return
     */
    public String handleExpression(JsonObject data, String expression) {
        expression = expression.replaceAll(" ", "").replaceAll("\"", "");
        String result = expression;
        try {
            // @{{status}?{status}:-999}
            if (!expression.contains("{") && !expression.contains("?")) {
                result = expression;
            } else {
                if (expression.contains("?")) {
                    expression = expression.replace("@", "");
                    expression = expression.substring(1, expression.length() - 1);
                    String condition = "";
                    if (expression.contains("==")) {
                        condition = "==";
                    } else if (expression.contains("!=")) {
                        condition = "!=";
                    } else if (!expression.contains("<") && !expression.contains(">")) {  //{{stid}?{stid}:{ct_poi}}
                        condition = "isNotEmpty";
                    } else if (expression.contains(">")) {
                        if (!expression.contains(">=")) {
                            condition = ">";
                        } else {
                            condition = ">=";
                        }
                    } else if (expression.contains("<")) {
                        if (!expression.contains("<=")) {
                            condition = "<";
                        } else {
                            condition = "<=";
                        }
                    }

                    String[] arr = expression.split("\\?");
                    String exp = arr[0];
                    String[] valueSet = arr[1].split(":");
                    String first = valueSet[0];
                    String second = valueSet[1];
                    if (first.contains("{") && !first.equals("{}")) {
                        first = getValueFromMockData(data, first);
                    }
                    if (second.contains("{") && !second.equals("{}")) {
                        second = getValueFromMockData(data, second);
                    }

                    if (condition.equals("isNotEmpty")) {
                        String actual = getValueFromMockData(data, exp);
                        boolean bool = valueUtil.isTrue(actual);
                        if (actual != null && actual.length() > 0 && bool) {
                            result = first;
                        } else {
                            result = second;
                        }
                    }
                    if (exp.contains(condition)) {
                        String compareValue = exp.split(condition)[1];
                        String path = exp.split(condition)[0];
                        String actual = getValueFromMockData(data, path);
                        if (actual != null && actual.equals(OS)) {
                            //带操作系统判断的暂时跳过
                            result = OS;
                            return result;
                        }
                        if (condition.equals("==") && actual.equals(compareValue)) {
                            if (actual == null) {
                                if (compareValue == null || compareValue.equals("null")) {
                                    result = first;
                                } else {
                                    result = second;
                                }
                            } else {
                                if (actual.equals(compareValue)) {
                                    result = first;
                                }
                            }
                        } else if (condition.equals("!=")) {
                            if (actual == null) {
                                if (compareValue == null || compareValue.equals("null")) {
                                    result = second;
                                } else {
                                    result = first;
                                }
                            } else {
                                if (!actual.equals(compareValue)) {
                                    result = first;
                                }
                            }
                        } else if (condition.equals(">") && Long.parseLong(actual) > Long.parseLong(compareValue)) {
                            result = first;
                        } else if (condition.equals(">=") && Long.parseLong(actual) >= Long.parseLong(compareValue)) {
                            result = first;
                        } else if (condition.equals("<") && Long.parseLong(actual) < Long.parseLong(compareValue)) {
                            result = first;
                        } else if (condition.equals("<=") && Long.parseLong(actual) <= Long.parseLong(compareValue)) {
                            result = first;
                        } else {
                            result = second;
                        }
                    }
                } else {
                    result = getValueFromMockData(data, expression);
                }
            }
        } catch (Exception e) {
            log.info("解析出错：" + expression);
            log.error(e.getMessage());
        } finally {
            return result;
        }
    }

    /**
     * 从json数据获取内容
     *
     * @param data
     * @param path
     * @return
     */
    public String getValueFromMockData(JsonObject data, String path) {
        DocumentContext documentContext = using(configuration).parse(data.toString());
        path = path.replaceAll(" ", "").replaceAll("@", "").replaceAll("\"", "");
        path = path.replaceAll("\\{", "").replaceAll("}", "");

        if (path.equals("os")) {
            return OS;
        }
        String result = "";
        try {
            if (data.has("biz")) {
                result = documentContext.read(ROOT_JSON_PATH + ITEM_JSON_PATH + path).toString();
            } else {
                result = documentContext.read(ROOT_JSON_PATH + path).toString();

            }
        } catch (Exception e) {
            result = null;
        }
        return result;
    }


    /**
     * 埋点数据按模版名称、bid分类
     *
     * @param output
     * @return {"xxTemplate":{"bid1":[{}...],"bid2":[{}...]}}
     */
    public String[] sortMgeByBidTest(String output) {
        JsonObject result = new JsonObject();
        JsonObject clickNoMgeResult = new JsonObject();
        try {
            if (null != output) {
                JsonArray jsonArray = jsonParser.parse(output).getAsJsonObject().get("results").getAsJsonArray();
                for (int itemIndex = 0; itemIndex < jsonArray.size(); itemIndex++) {
                    JsonObject jsonObject = jsonArray.get(itemIndex).getAsJsonObject();
                    String bid = "";
                    String lab = "";
                    // 07.23判断埋点报告中是否有 有点击但是没有埋点上报的控件
                    if (jsonObject.has("desc")) {
                        // 以templateName作为key，把该埋点信息存入clickNoMgeResult
                        String tempName = jsonObject.get("templateName").getAsString();
                        if (!clickNoMgeResult.has(tempName)) {
                            clickNoMgeResult.add(tempName, new JsonArray());
                        }
                        clickNoMgeResult.get(tempName).getAsJsonArray().add(jsonObject);
                        continue;
                    }
                    //// TODO: 2021/7/19 需确认val_bid是一定会上报的么
                    try {
                        bid = jsonObject.get("data").getAsJsonObject().get("val_bid").getAsString();
                        lab = jsonObject.get("data").getAsJsonObject().get("val_lab").toString();
                    } catch (Exception e) {
                        continue;
                    }
                    DocumentContext labJsonDoc = using(confForList).parse(lab);
                    if (null != lab) {
                        String templateName = null;
                        List<String> templateNameList1 = labJsonDoc.read("$..template_name");
                        List<String> templateNameList2 = labJsonDoc.read("$..templateName");
                        List<String> templateNameList3 = labJsonDoc.read("$..qa_assist_tpl_name");
                        if (null != templateNameList1) {
                            for (int i = 0; i < templateNameList1.size(); i++) {
                                if (null != templateNameList1.get(i) && "" != templateNameList1.get(i)) {
                                    templateName = templateNameList1.get(i);
                                    break;
                                }
                            }
                        }
                        if (null == templateName && null != templateNameList2) {
                            for (int i = 0; i < templateNameList2.size(); i++) {
                                if (null != templateNameList2.get(i) && "" != templateNameList2.get(i)) {
                                    templateName = templateNameList2.get(i);
                                    break;
                                }
                            }
                        }

                        if (null != templateNameList3) {
                            for (int i = 0; i < templateNameList3.size(); i++) {
                                if (null != templateNameList3.get(i) && "" != templateNameList3.get(i)) {
                                    templateName = templateNameList3.get(i);
                                    break;
                                }
                            }
                        }

                        if (null != templateName) {
                            if (!result.has(templateName)) {
                                result.add(templateName, new JsonObject());
                            }
                            if (!result.get(templateName).getAsJsonObject().has(bid)) {
                                result.get(templateName).getAsJsonObject().add(bid, new JsonArray());
                            }
                            result.get(templateName).getAsJsonObject().get(bid).getAsJsonArray().add(jsonObject);
                        }
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("埋点数据异常");
        }
        return new String[]{result.toString(), clickNoMgeResult.toString()};
    }


    public String sortMgeByBid(String output) {
        JsonObject result = new JsonObject();
        try {
            if (null != output) {
                JsonArray jsonArray = jsonParser.parse(output).getAsJsonObject().get("results").getAsJsonArray();
                for (int itemIndex = 0; itemIndex < jsonArray.size(); itemIndex++) {
                    JsonObject jsonObject = jsonArray.get(itemIndex).getAsJsonObject();
                    String bid = "";
                    String lab = "";
                    //// TODO: 2021/7/19 需确认val_bid是一定会上报的么
                    try {
                        bid = jsonObject.get("data").getAsJsonObject().get("val_bid").getAsString();
                        lab = jsonObject.get("data").getAsJsonObject().get("val_lab").toString();
                    } catch (Exception e) {
                        continue;
                    }
                    DocumentContext labJsonDoc = using(confForList).parse(lab);
                    if (null != lab) {
                        String templateName = null;
                        List<String> templateNameList1 = labJsonDoc.read("$..template_name");
                        List<String> templateNameList2 = labJsonDoc.read("$..templateName");
                        List<String> templateNameList3 = labJsonDoc.read("$..qa_assist_tpl_name");
                        if (null != templateNameList1) {
                            for (int i = 0; i < templateNameList1.size(); i++) {
                                if (null != templateNameList1.get(i) && "" != templateNameList1.get(i)) {
                                    templateName = templateNameList1.get(i);
                                    break;
                                }
                            }
                        }
                        if (null == templateName && null != templateNameList2) {
                            for (int i = 0; i < templateNameList2.size(); i++) {
                                if (null != templateNameList2.get(i) && "" != templateNameList2.get(i)) {
                                    templateName = templateNameList2.get(i);
                                    break;
                                }
                            }
                        }

                        if (null != templateNameList3) {
                            for (int i = 0; i < templateNameList3.size(); i++) {
                                if (null != templateNameList3.get(i) && "" != templateNameList3.get(i)) {
                                    templateName = templateNameList3.get(i);
                                    break;
                                }
                            }
                        }

                        if (null != templateName) {
                            if (!result.has(templateName)) {
                                result.add(templateName, new JsonObject());
                            }
                            if (!result.get(templateName).getAsJsonObject().has(bid)) {
                                result.get(templateName).getAsJsonObject().add(bid, new JsonArray());
                            }
                            result.get(templateName).getAsJsonObject().get(bid).getAsJsonArray().add(jsonObject);
                        }
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("埋点数据异常");
        } finally {
            return result.toString();
        }
    }

    /**
     * @param mge
     * @param bid
     * @param eventType
     * @return
     */
    public JsonObject diffFromOcean(JsonObject mge, String bid, String eventType) {
        String extra = "";
        JsonObject result = new JsonObject();
        JsonObject jsonObject = oceanUtil.getEntityIdentifierInfo(bid);
        try {
            JsonArray oceanBussinessField = jsonObject.get("data").getAsJsonObject().get("businessFields").getAsJsonArray();
            for (int i = 0; i < oceanBussinessField.size(); i++) {
                String businessFieldIdentifier = oceanBussinessField.get(i).getAsJsonObject().get("businessFieldIdentifier").toString();
                JsonObject valLab = mge.get("val_lab").getAsJsonObject();
                DocumentContext valLabDoc = JsonPath.using(configuration).parse(valLab.toString());
                if (null != valLabDoc.read(ROOT_JSON_PATH + businessFieldIdentifier)) {
                    extra = extra + "ERROR：" + bid + "上报缺失" + businessFieldIdentifier + "字段\n";
                }
            }
            if ("MC".equals(eventType)) {
                JsonObject tag = mge.get("tag").getAsJsonObject();
                if (null != tag) {
                    DocumentContext tagDoc = JsonPath.using(configuration).parse(tag.toString());
                    JsonObject oceanTag = jsonObject.get("data").getAsJsonObject().get("tag").getAsJsonObject();
                    List<String> pathList = JsonPath.using(asPathListConf).parse(oceanTag).read("$..*");
                    String channel = pathList.get(0);
                    if (null != tagDoc.read(ROOT_JSON_PATH + channel)) {
                        extra = extra + "ERROR：" + bid + "上报tag 通道不符合预期\n";
                    }

                    JsonObject cidLevels = oceanTag.get(channel).getAsJsonObject();
                    pathList = JsonPath.using(asPathListConf).parse(cidLevels).read("$..*");
                    String cid = pathList.get(0);
                    String path = new ArrayList(JsonPath.using(asPathListConf).parse(oceanTag).read("$..*")).get(0).toString();
                    if (null != tagDoc.read(ROOT_JSON_PATH + path + "." + cid)) {
                        extra = extra + "ERROR：" + bid + "上报tag cid不符合预期\n";
                    }

                    Iterator oceanTagIterator = oceanTag.entrySet().iterator();
                    int levels = 0;
                    JsonObject oceanTagValLab = null;
                    while (oceanTagIterator.hasNext()) {
                        if (2 == levels) {
                            String key = oceanTagIterator.next().toString();
                            oceanTagValLab = oceanTag.get(key).getAsJsonObject();
                            break;
                        }
                        levels++;
                    }

                    Iterator tagIterator = tag.entrySet().iterator();
                    levels = 0;
                    JsonObject tagValLab = null;
                    while (tagIterator.hasNext()) {
                        if (2 == levels) {
                            String key = tagIterator.next().toString();
                            tagValLab = oceanTag.get(key).getAsJsonObject();
                            break;
                        }
                        levels++;
                    }

                    if (null != oceanTagValLab) {
                        oceanTagIterator = oceanTagValLab.entrySet().iterator();
                        while (oceanTagIterator.hasNext()) {
                            String key = oceanTagIterator.next().toString();
                            DocumentContext tagValLabDoc = JsonPath.using(configuration).parse(tagValLab.toString());
                            if (null != tagValLabDoc.read(ROOT_JSON_PATH + key)) {
                                extra = extra + "ERROR：" + bid + "上报tag 缺失" + key + "字段\n";
                            }
                        }
                    }


                }
            }
        } catch (Exception e) {

        }
        result.addProperty("extra", extra);
        result.add("oceanMge", jsonObject);

        return result;
    }

    /**
     * 上传本地图片到服务器
     *
     * @param path
     * @return 返回图片链接
     */
    public String uploadPicture(String path) {
        String result = null;
        try {
            String bucket = comConstant.BUCKET;
            String client_id = comConstant.CLIENT_ID;
            String client_secret = comConstant.CLIENT_SECRET;
            ImageUploadClient client = new ImageUploadClientImpl(bucket, client_id, client_secret);

            File file = new File(path);
            ImageRequest request = new ImageRequest(1, file, false);
            ImageResult res = client.postImage(request);
            result = res.getOriginalLink();
        } catch (Exception e) {
            log.error("图片上传失败>>" + e.getMessage());
        } finally {
            if (result == "") {
                return null;
            } else {
                return result;
            }
        }
    }

    public void setBasePic(String mockId, String picUrl) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mock_id", mockId);
        MockRule mockRule = mockRuleMapper.selectOne(queryWrapper);
        if (mockRule != null) {
            log.info("更新基准图》》" + picUrl);
            mockRule.setPic(picUrl);
            mockRuleMapper.updateById(mockRule);
        }
    }

    public String getBasePic(String mockId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mock_id", mockId);
        MockRule mockRule = mockRuleMapper.selectOne(queryWrapper);
        if (mockRule != null) {
            return mockRule.getPic();
        }
        return null;
    }

    /**
     * 获取ocean配置的埋点信息
     *
     * @param templateName
     * @param address
     * @return
     */
    public JsonObject getMgeConfiguration(String templateName, String address, String scenes) {
        JsonObject mgeConfiguration = mbcUtil.getMgeConfiguration(templateName, scenes);
        return mgeConfiguration;
    }

    public JsonObject manipulatePic(String key, JsonObject picLink, String link) {
//      key = "templete/type/component/time"
//      key = "templete/type/mockId
        String[] dirName = key.split("\\/");
        JsonObject actionList = new JsonObject();
        JsonObject componentList = new JsonObject();
        JsonObject mockList = new JsonObject();

        if (!picLink.has(dirName[0])) {
            picLink.add(dirName[0], new JsonObject());
        }

        if ("operation".equals(dirName[1])) {
            if (!picLink.get(dirName[0]).getAsJsonObject().has(dirName[1])) {
                actionList.addProperty(dirName[3], link);
                componentList.add(dirName[2], actionList);
                picLink.get(dirName[0]).getAsJsonObject().add(dirName[1], componentList);
            } else {
                componentList = picLink.get(dirName[0]).getAsJsonObject().get(dirName[1]).getAsJsonObject();
                if (!componentList.has(dirName[2])) {
                    actionList.addProperty(dirName[3], link);
                    picLink.get(dirName[0]).getAsJsonObject().get(dirName[1]).getAsJsonObject().add(dirName[2], actionList);

                } else {
                    picLink.get(dirName[0]).getAsJsonObject().get(dirName[1]).getAsJsonObject().get(dirName[2]).getAsJsonObject().addProperty(dirName[3], link);
                }
            }
        } else {
            if (!picLink.get(dirName[0]).getAsJsonObject().has(dirName[1])) {
                mockList.addProperty(dirName[2], link);
                picLink.get(dirName[0]).getAsJsonObject().add(dirName[1], mockList);
            } else {
                picLink.get(dirName[0]).getAsJsonObject().get(dirName[1]).getAsJsonObject().addProperty(dirName[2], link);
            }

        }

        return picLink;
    }


    /**
     * operation结果入库
     *
     * @param componentList
     * @param reportIds
     * @param deviceModel
     * @param deviceVersion
     * @param appVersion
     * @param jobId
     * @param name
     * @param taskId
     * @param totalTime
     * @param testType
     */
    public void setOperationPicUrl(JsonObject componentList, String reportIds, String deviceModel,
                                   String deviceVersion, String appVersion, Integer jobId, String name,
                                   String taskId, Double totalTime, Integer testType) {

        for (Map.Entry entry : componentList.entrySet()) {
            String component = entry.getKey().toString();
            String beforJumpPic = "";
            String afterJumpPic = "";
            String clickPic = "";
            try {
                beforJumpPic = jsonParser.parse(entry.getValue().toString()).getAsJsonObject().get("beforeJump").getAsString();
                afterJumpPic = jsonParser.parse(entry.getValue().toString()).getAsJsonObject().get("afterJump").getAsString();
                clickPic = jsonParser.parse(entry.getValue().toString()).getAsJsonObject().get("click").getAsString();
            } catch (Exception e) {
                log.error(e.getMessage());
            }

            JobDetail jobDetail = new JobDetail();
            jobDetail.setReportId(reportIds);
            jobDetail.setDeviceModel(deviceModel);
            jobDetail.setDeviceVersion(deviceVersion);
            jobDetail.setApkVersion(appVersion);
            jobDetail.setJobId(jobId);
            jobDetail.setTemplateName(name);
            jobDetail.setTaskId(taskId);
            jobDetail.setTotalTime(totalTime);
            jobDetail.setBeforeJumpPic(beforJumpPic);
            jobDetail.setAfterJumpPic(afterJumpPic);
            jobDetail.setClickPic(clickPic);
            jobDetail.setOperationComponent(component);
            jobDetail.setType(testType);
            jobDetail.setMergedUrl("");

            if (deviceModel.contains("iPhone")) {
                jobDetail.setPlatform("iOS");
            } else {
                jobDetail.setPlatform("Android");
            }
            jobDetailMapper.insert(jobDetail);
        }

    }

    /**
     * normal和abnormal结果入库
     *
     * @param type
     * @param abnormalList
     * @param reportIds
     * @param deviceModel
     * @param deviceVersion
     * @param appVersion
     * @param jobId
     * @param name
     * @param taskId
     * @param totalTime
     * @param testType
     * @param uiSet
     */
    public void addPicContent(String type, JsonObject abnormalList, String reportIds, String deviceModel,
                              String deviceVersion, String appVersion, Integer jobId, String name, String taskId,
                              Double totalTime, Integer testType, Map uiSet, Map mergedUrlSet) {

        String result = "";

        for (Map.Entry entry : abnormalList.entrySet()) {
            String mockId = entry.getKey().toString();
            String mergedUrl = (String) mergedUrlSet.get(mockId);
            if (mergedUrl == null) {
                mergedUrl = "";
            }
            String picUrl = jsonParser.parse(entry.getValue().toString()).getAsString();
            JsonObject mockData = getMockData(Integer.parseInt(mockId));

            JobDetail jobDetail = new JobDetail();

            jobDetail.setReportId(reportIds);
            jobDetail.setDeviceModel(deviceModel);
            jobDetail.setDeviceVersion(deviceVersion);
            jobDetail.setApkVersion(appVersion);
            jobDetail.setJobId(jobId);
            jobDetail.setTemplateName(name);
            jobDetail.setTaskId(taskId);
            jobDetail.setTotalTime(totalTime);
            jobDetail.setMergedUrl(mergedUrl);

            //基准图
            String basePic = getBasePic(mockId);
            if (null == basePic || "" == basePic) {
                setBasePic(mockId, picUrl);
                basePic = picUrl;
            }

            try {
                String diffResult = ocrService.diff(basePic, picUrl, "1", "Block", "0.2", "0");
                JsonObject jsonObject = new JsonParser().parse(diffResult).getAsJsonObject();
                jobDetail.setResultPic(jsonObject.get("resultrurl").getAsString());
                jobDetail.setScore(jsonObject.get("similarresult").getAsDouble());

            } catch (Exception e) {
                log.error("diff pic err>>" + e.getMessage());
            }

            jobDetail.setComparePic(picUrl);
            jobDetail.setMockType(type);
            jobDetail.setMockId(Integer.parseInt(mockId));
            jobDetail.setMockData(mockData.toString());
            jobDetail.setType(testType);

            if (deviceModel.contains("iPhone")) {
                jobDetail.setPlatform("iOS");
            } else {
                jobDetail.setPlatform("Android");
            }

            if ("abnormal".equals(type)) {
                //用来拆分要传进数据库的值， skt
                ArrayList vaildDetails = new ArrayList();
                try {
                    MockRule rule = getMockRule(Integer.parseInt(mockId));
                    jobDetail.setMockKey(rule.getRuleKey());
                    jobDetail.setMockRule(rule.getRuleValue());
                } catch (Exception e) {
                    log.error("abnormal err>>" + e.getMessage());
                }

                try {
                    Object arr = uiSet.get(mockId);
                    vaildDetails = (ArrayList) arr;
//                    int isValid = (int) vaildDetails.get(0);
//                    String overlapPic = (String) vaildDetails.get(1);
//                    String exra = (String) vaildDetails.get(2);
                    String textContent = "";
                    String tempDetails = "";
                    for (int i = 0; i < vaildDetails.size(); i++) {
                        tempDetails = vaildDetails.get(i).toString();
                        if (!StringUtils.isBlank(tempDetails)) {
                            textContent = textContent + tempDetails + " ";
                        }
                    }
                    //去掉中间和首尾多余空格，然后用换行替代空格
                    textContent = textContent.replace("  ", " ").trim().replace(" ", "\n");
//                    exra+=textContent;
//                    jobDetail.setIsValid(isValid);
//                    jobDetail.setOverlapPic(overlapPic);
                    jobDetail.setExra(textContent);
                } catch (Exception e) {
                    log.error("reportIds：" + reportIds + "，uiSet err>>" + e.getMessage());
                } finally {
                    //extra不为空说明有bug，将本次测试结果置为不通过
                    if (result != "") {
                        CompatilityJob compatilityJob = compatilityMapper.selectById(jobId);
                        compatilityJob.setAutoResult(false);
                        compatilityJob.setDescription("详见报告");
                        compatilityMapper.updateById(compatilityJob);
                    }
                }
            }
            jobDetailMapper.insert(jobDetail);
            hasResultUI = true;
        }
    }


    @Override
    public List<String> failedReasonForDynamicReportList() {
        List<FailedReason> failedReasonList = failedReasonMapper.selectList(new QueryWrapper<FailedReason>()
                .eq("failed_field", "Dynamic").orderByAsc("id"));
        List<String> result = new ArrayList<>();
        for (FailedReason column : failedReasonList) {
            result.add(column.getFailedDescription());
        }
        return result;
    }

    @Override
    public void failedReasonAdd(FailedReason failedReason) {
        failedReasonMapper.insert(failedReason);
    }

    /**
     * 更新JobDetail中的设备分辨率信息
     *
     * @param conanId          云测reportId
     * @param deviceModel      设备型号
     * @param deviceResolution 设备分辨率
     */
    private void updateJobDetailDeviceResolution(String conanId, String deviceModel, String deviceResolution) {
        // 更新逻辑:根据conanId、deviceModel定位JobDetail记录，如果该记录deviceResolution为空则update，非空则跳过
        List<JobDetail> jobDetails = jobDetailMapper.selectList(new QueryWrapper<JobDetail>()
                .eq("report_id", conanId)
                .eq("device_model", deviceModel));

        for (JobDetail jobDetail : jobDetails) {
            // 如果该记录的deviceResolution为空，则进行更新；如果非空，则跳过
            if (StringUtils.isBlank(jobDetail.getDeviceResolution())) {
                jobDetail.setDeviceResolution(deviceResolution);
                jobDetailMapper.updateById(jobDetail);
                log.info("conanId-deviceModel's deviceResolution updated");
            } else {
                log.info("conanId-deviceModel's deviceResolution exist skip.");
            }
        }
    }
}