package com.sankuai.mdp.compass.service;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.service.*;
import com.sankuai.mdp.compass.entity.CheckOrder;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.mapper.CompatilityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/2/24.
 */
@Slf4j
@Component
class ScheduledService {
    @Autowired
    CheckOrderService checkOrderService;

    @Autowired
    CompatilityMapper compatilityMapper;

    @Autowired
    ConanCaseService conanCaseService;
    @Autowired
    MgeJobService mgeJobService;

    @Autowired
    ConanJobService conanJobService;

    @Autowired
    MgeTaskService mgeTaskService;

    @Autowired
    MgeCaseService mgeCaseService;

    TalosUtil talosUtil = new TalosUtil();
    OnesUtil onesUtil = new OnesUtil();
    TTUtil ttUtil = new TTUtil();
    private static String fileName = "./result.xlsx";

//    @Scheduled(cron = "0 0 8 * * *")
    public void scheduled() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            Date date = sdf.parse(sdf.format(new Date()));
            List<CheckOrder> list = checkOrderService.getPending(date);
            for (int i = 0; i < list.size(); i++) {
                CheckOrder checkOrder = list.get(i);
                Integer jobId = checkOrder.getJobId();
                CompatilityJob compatilityJob = compatilityMapper.selectById(jobId);
                String sql = checkOrder.getSqlStr();
                talosUtil.submit(sql, fileName);
                MSSUtil mssUtil = new MSSUtil();
                String objectUrl = mssUtil.uploadExcel(fileName);
                if (null != objectUrl) {
                    compatilityJob.setDownloadUrl(objectUrl.split("autotest")[1]);
                    compatilityMapper.updateById(compatilityJob);
                }
                checkOrder.setStatus("finished");
                checkOrderService.updateById(checkOrder);
            }
        } catch (Exception e) {

        }
    }

//    @Scheduled(cron = "0 30 10 * * *")
    public void daliyCheckAutotestResult() {
        if (EnvUtil.isOnline()) {
            try {
                Date date = new Date();
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                String startTime = df.format(new Date(date.getTime() - (long) 1 * 24 * 60 * 60 * 1000));

                String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
                Calendar cal = Calendar.getInstance();
                cal.setTime(date);
                Integer days = 1;
                Integer w = cal.get(Calendar.DAY_OF_WEEK) - 1;
                log.info("----w：" + w);
                if (0 == w || 6 == w) {
                    return;
                }
                if (1 == w) {
                    startTime = df.format(new Date(date.getTime() - (long) 3 * 24 * 60 * 60 * 1000));
                    days = 3;
                }

                log.info("开始日常检查自动化运行结果");
                createOnes(startTime, df.format(date), days, "iOS");
                createOnes(startTime, df.format(date), days, "Android");
            } catch (Exception e) {
                log.info("日常检查自动化运行结果失败", e);
            }
        }
    }

    public void createOnes(String startTime, String endTime, Integer days, String platform) {
        String dxDesc = "";
        String summary = "";
        if (days > 1) {
            dxDesc = "【UI自动化" + platform + "】" + startTime + " 至 " + endTime + " Case执行通过率低于60%";
            summary = "【" + platform + "】" + startTime + " Case适配";

        } else {
            dxDesc = "【UI自动化" + platform + "】" + startTime + " Case执行通过率低于60%";
            summary = "【" + platform + "】" + startTime + " 至 " + endTime + " Case适配";
        }

        List<GroupCase> failedCase = conanCaseService.getPassRateLowerCase(startTime, endTime, platform);
        log.info("failedCase" + failedCase);
        JsonObject needIssue = new JsonObject();
        for (int i = 0; i < failedCase.size(); i++) {
            String owner = failedCase.get(i).getCaseOwner();
            JsonObject tempJson = new JsonObject();
            tempJson.addProperty("caseName", failedCase.get(i).getCaseName());
            tempJson.addProperty("className", failedCase.get(i).getCaseClass());
            tempJson.addProperty("passRate", failedCase.get(i).getPassRate());
            if (null != owner) {
                if (needIssue.has(owner)) {
                    needIssue.get(owner).getAsJsonArray().add(tempJson);

                } else {
                    JsonArray jsonArray = new JsonArray();
                    needIssue.add(owner, jsonArray);
                    needIssue.get(owner).getAsJsonArray().add(tempJson);
                }
            }
        }
        Integer len = needIssue.size();
        if (len > 0) {
            JsonObject parentIssueParams = new JsonObject();
            parentIssueParams.addProperty("summary", dxDesc);
            parentIssueParams.addProperty("assignee", "PTQA");
            parentIssueParams.addProperty("description", "");
            String parentId = "";
            parentIssueParams.addProperty("parentId", parentId);

            String response = onesUtil.createIssue(parentIssueParams);
            if (null != response) {
                JsonObject jsonObject = new JsonParser().parse(response).getAsJsonObject();
                String id = (jsonObject.get("data")).getAsJsonObject().get("id").getAsString();
                parentId = id;

                for (Map.Entry<String, JsonElement> entry : needIssue.entrySet()) {
                    JsonElement value = entry.getValue();
                    if (value.isJsonArray()) {
                        String owner = entry.getKey();
                        JsonArray caseList = value.getAsJsonArray();
                        String description = "";
                        JsonObject params = new JsonObject();

                        for (int i = 0; i < caseList.size(); i++) {
                            String caseName = (caseList.get(i)).getAsJsonObject().get("caseName").getAsString();
                            String className = (caseList.get(i)).getAsJsonObject().get("className").getAsString();
                            Double passRate = (caseList.get(i)).getAsJsonObject().get("passRate").getAsDouble() * 100;
                            description = description + "<p>Case： " + caseName + "通过率为" + passRate.toString() + "%</p>";
                            String reportUrl = "https://conan.sankuai.com/v2/auto-function/appium/report/detail/";
                            description = description + "<p>【失败报告】：";
                            List<Integer> taskIdList = conanCaseService.getFailedCasesTaskIdList(startTime, platform, caseName, className);
                            for (int t = 0; t < taskIdList.size(); t++) {
                                Integer taskId = taskIdList.get(t);
                                String reportDesc = reportUrl + taskId + "?class=" + className + "&method=" + caseName;
                                description = description + "<a href=\"" + reportDesc + "\">报告" + t + "  </a>";

                            }
                            description = description + "</p>";
                        }
                        params.addProperty("summary", summary);
                        params.addProperty("assignee", owner);
                        params.addProperty("description", description);
                        params.addProperty("parentId", parentId);
                        onesUtil.createIssue(params);
                    }
                }

            }
        }
    }

}