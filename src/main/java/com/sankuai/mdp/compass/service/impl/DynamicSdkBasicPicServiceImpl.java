package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.mdp.compass.entity.DynamicSdkBasicPic;
import com.sankuai.mdp.compass.mapper.DynamicSdkBasicPicMapper;
import com.sankuai.mdp.compass.service.DynamicSdkBasicPicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class DynamicSdkBasicPicServiceImpl implements DynamicSdkBasicPicService {

    @Autowired
    DynamicSdkBasicPicMapper dynamicSdkBasicPicMapper;

    @Override
    public DynamicSdkBasicPic find(String xml_name){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("xml_name",xml_name);
        DynamicSdkBasicPic result = dynamicSdkBasicPicMapper.selectOne(queryWrapper);
        return result;
    }
}
