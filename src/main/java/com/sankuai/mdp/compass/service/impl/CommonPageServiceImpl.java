package com.sankuai.mdp.compass.service.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dianping.zebra.util.StringUtils;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.robust.entity.CommonPage;
import com.sankuai.mdp.compass.robust.mapper.CommonPageMapper;
import com.sankuai.mdp.compass.service.CommonPageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Slf4j
@Service
public class CommonPageServiceImpl extends ServiceImpl<CommonPageMapper,CommonPage> implements CommonPageService {
    @Autowired
    CommonPageMapper commonPageMapper;



    @Override
    public List<String> getAllAppName() {
        QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
        List<String> res = new ArrayList<String>();
        List<CommonPage> commonPageList = commonPageMapper.selectList(queryWrapper.groupBy("app_name"));
        for (CommonPage commonPage : commonPageList) {
            res.add(commonPage.getAppName());
        }
        return res;
    }

    @Override
    public IPage<CommonPage> list(QueryRequest request, CommonPage commonPage) {
        try {
            LambdaQueryWrapper<CommonPage> queryWrapper = new LambdaQueryWrapper<>();

            if (null != commonPage.getPageDescription()) {
                queryWrapper.like(CommonPage::getPageDescription, commonPage.getPageDescription());
            }
            if (null != commonPage.getAppName()) {
                queryWrapper.eq(CommonPage::getAppName, commonPage.getAppName());
            }
            if (null != commonPage.getSchemaIos()) {
                queryWrapper.and(wrapper->wrapper.like(CommonPage::getSchemaIos, commonPage.getSchemaIos()).or().like(CommonPage::getSchemaAndroid, commonPage.getSchemaIos()));
            }


            Page<CommonPage> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

            log.error("获取列表失败", e);
            return null;
        }
    }
    @Override
    public Resp update(CommonPage commonPage) {
        QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
        Resp resp = new Resp();
        commonPageMapper.update(commonPage, queryWrapper
                .eq("id", commonPage.getId()));
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Resp delete(CommonPage commonPage) {
        Resp resp = new Resp();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", commonPage.getId());
        commonPageMapper.delete(queryWrapper);
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Resp add(CommonPage commonPage) {
        Resp resp = new Resp();
        log.info("插入的页面数据是：" + commonPage.toString());
        commonPageMapper.insert(commonPage);
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Map<String, String> getSchema(String bg, String bu, String biz, String pageDescription, String pageType) {
        Map<String, List<String>> params = new HashMap<>();
        // 根据参数构造查询条件
        if (StringUtils.isNotBlank(bg) && !"all".equals(bg)) {
            List<String> bgList = Arrays.asList(bg.replaceAll("\\[|\\]", "").split(","));
            params.put("bg", bgList);
        }
        if (StringUtils.isNotBlank(bu) && !"all".equals(bu)) {
            List<String> buList = Arrays.asList(bu.replaceAll("\\[|\\]", "").split(","));
            params.put("bu", buList);
        }
        if (StringUtils.isNotBlank(biz) && !"all".equals(biz)) {
            List<String> bizList = Arrays.asList(biz.replaceAll("\\[|\\]", "").split(","));
            params.put("biz", bizList);
        }
        if (StringUtils.isNotBlank(pageDescription) && !"all".equals(pageDescription)) {
            List<String> pageDescriptionList = Arrays.asList(pageDescription.replaceAll("\\[|\\]", "").split(","));
            params.put("page_description", pageDescriptionList);
        }
        if (StringUtils.isNotBlank(pageType) && !"all".equals(pageType)) {
            List<String> pageTypeList = Arrays.asList(pageType.replaceAll("\\[|\\]", "").split(","));
            params.put("page_type", pageTypeList);
        }

        QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_name", "meituan");
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, List<String>> entry : params.entrySet()) {
                String key = entry.getKey();
                List<String> value = entry.getValue();
                if (value != null && !value.isEmpty()) {
                    queryWrapper.in(key, value);
                }
            }
        }

        queryWrapper.select("DISTINCT bg", "bu", "biz", "page_description", "page_type", "schema_ios",
                "schema_android");
        List<CommonPage> commonPageList = commonPageMapper.selectList(queryWrapper);
        Map<String, String> schemaMap = new HashMap<>();
        for (CommonPage commonPage : commonPageList) {
            StringBuilder keyBuilder = new StringBuilder();
            if (commonPage.getBg() != null) {
                keyBuilder.append(commonPage.getBg()).append("-");
            }
            if (commonPage.getBu() != null) {
                keyBuilder.append(commonPage.getBu()).append("-");
            }
            if (commonPage.getBiz() != null) {
                keyBuilder.append(commonPage.getBiz()).append("-");
            }
            if (commonPage.getPageDescription() != null) {
                keyBuilder.append(commonPage.getPageDescription()).append("-");
            }
            if (commonPage.getPageType() != null) {
                keyBuilder.append(commonPage.getPageType()).append("-");
            }
            // 删除最后一个"-"
            if (keyBuilder.length() > 0) {
                keyBuilder.deleteCharAt(keyBuilder.length() - 1);
            }
            String key = keyBuilder.toString();
            String value = "";
            if (commonPage.getSchemaIos() != null) {
                value = commonPage.getSchemaIos();
            }
            if (commonPage.getSchemaAndroid() != null) {
            value = commonPage.getSchemaAndroid();
        }
        schemaMap.put(key, value);
    }
    return schemaMap;
}



    @Override
    public HashMap<String, Integer> getAllPageDescription() {
        QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
        HashMap<String, Integer> map = new HashMap<>();
        List<CommonPage> pageList = null;
        try {
            if (commonPageMapper != null) {
                pageList = commonPageMapper.selectList(queryWrapper.groupBy("page_description"));
            } else {
                return map;
            }
        } catch (Exception e) {
            map.put(e.toString(), 0);
            return map;
        }

        if (pageList != null && !pageList.isEmpty()) {
            for (CommonPage page : pageList) {
                String description = page.getPageDescription();
                if (description != null) {
                    int id = page.getId();
                    if (!map.containsKey(description)) {
                        map.put(description, id);
                }
            }
        }
    }
    return map;
}


    @Override
public HashMap<String, Integer> getAllBg() {
    QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
    HashMap<String, Integer> map = new HashMap<>();
    if (commonPageMapper == null) {
        return map;
    }
    List<CommonPage> pageList = commonPageMapper.selectList(queryWrapper.groupBy("bg"));
    if (pageList == null) {
        return map;
    }
    try {
        for (CommonPage page : pageList) {
            String bg = page.getBg();
            if (bg == null) {
                continue;
            }
            int id = page.getId();
            map.put(bg, id);
        }
    } catch (Exception e) {
        map.put(e.toString(), 0);
        return map;
    }
    return map;
}

@Override
public HashMap<String, Integer> getAllBu() {
    QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
    HashMap<String, Integer> map = new HashMap<>();
    if (commonPageMapper == null) {
        return map;
    }
    List<CommonPage> pageList = commonPageMapper.selectList(queryWrapper.groupBy("bu"));
    if (pageList == null) {
        return map;
    }
    try {
        for (CommonPage page : pageList) {
            String bu = page.getBu();
            if (bu == null) {
                continue;
            }
            int id = page.getId();
            map.put(bu, id);
        }
    } catch (Exception e) {
        map.put(e.toString(), 0);
        return map;
    }
    return map;
}

@Override
public HashMap<String, Integer> getAllBiz() {
    QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
    HashMap<String, Integer> map = new HashMap<>();
    if (commonPageMapper == null) {
        return map;
    }
    List<CommonPage> pageList = commonPageMapper.selectList(queryWrapper.groupBy("biz"));
    if (pageList == null) {
        return map;
    }
    try {
        for (CommonPage page : pageList) {
            String biz = page.getBiz();
            if (biz == null) {
                continue;
            }
            int id = page.getId();
            map.put(biz, id);
        }
    } catch (Exception e) {
        map.put(e.toString(), 0);
        return map;
    }
    return map;
}

@Override
public HashMap<String, Integer> getAllPageType() {
    QueryWrapper<CommonPage> queryWrapper = new QueryWrapper<>();
    HashMap<String, Integer> map = new HashMap<>();
    if (commonPageMapper == null) {
        return map;
    }
    List<CommonPage> pageList = commonPageMapper.selectList(queryWrapper.groupBy("page_type"));
    if (pageList == null) {
        return map;
    }
    try {
        for (CommonPage page : pageList) {
            String pageType = page.getPageType();
            if (pageType == null) {
                continue;
            }
            int id = page.getId();
            map.put(pageType, id);
        }
    } catch (Exception e) {
        map.put(e.toString(), 0);
        return map;
    }
    return map;
}

}
