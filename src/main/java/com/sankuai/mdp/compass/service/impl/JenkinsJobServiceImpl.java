package com.sankuai.mdp.compass.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.entity.JenkinsJob;
import com.sankuai.mdp.compass.entity.MockRule;
import com.sankuai.mdp.compass.mapper.JenkinsJobMapper;
import com.sankuai.mdp.compass.mapper.MockRuleMapper;
import com.sankuai.mdp.compass.service.JenkinsJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by xieyongrui on 2019/11/21.
 */

@Slf4j
@Service
public class JenkinsJobServiceImpl extends ServiceImpl<JenkinsJobMapper,JenkinsJ<PERSON> > implements JenkinsJobService {
    @Autowired
    JenkinsJobMapper jenkinsJobMapper;

    @Override
    public void updateJobStartTime(<PERSON><PERSON>ob jenkinsJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("build_number",jenkinsJob.getBuildNumber());
        queryWrapper.eq("url",jenkinsJob.getUrl());
        JenkinsJob jenkinsJob1 = jenkinsJobMapper.selectOne(queryWrapper);
        if (null != jenkinsJob1) {
            Date date = new Date();
            if (null != jenkinsJob.getJobStartAt()) {
                date = jenkinsJob.getJobStartAt();
                System.out.print(date);
            }
            jenkinsJob1.setJobStartAt(date);
            jenkinsJobMapper.update(jenkinsJob1,queryWrapper);
        }
        jenkinsJobMapper.updateById(jenkinsJob);
    }

    @Override
    public void updateSubmitFinishTime(JenkinsJob jenkinsJob) {
        Date date = new Date();
        jenkinsJob.setSubmitFinishAt(date);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("url",jenkinsJob.getUrl());
        queryWrapper.eq("build_number",jenkinsJob.getBuildNumber());
        try {
            jenkinsJobMapper.update(jenkinsJob,queryWrapper);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

    @Override
    public void updateCaseRunTime(JenkinsJob jenkinsJob) {
        Date date = new Date();
        jenkinsJob.setCaseRunAt(date);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("url",jenkinsJob.getUrl());
        queryWrapper.eq("build_number",jenkinsJob.getBuildNumber());
        try {
            jenkinsJobMapper.update(jenkinsJob,queryWrapper);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

    @Override
    public void updateCaseFinishTime(JenkinsJob jenkinsJob) {
        Date date = new Date();
        jenkinsJob.setCaseFinishAt(date);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("url",jenkinsJob.getUrl());
        queryWrapper.eq("build_number",jenkinsJob.getBuildNumber());
        try {
            jenkinsJobMapper.update(jenkinsJob,queryWrapper);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

    @Override
    public void updateJobFinishTime(JenkinsJob jenkinsJob) {
        Date date = new Date();
        jenkinsJob.setJobFinishAt(date);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("url",jenkinsJob.getUrl());
        queryWrapper.eq("build_number",jenkinsJob.getBuildNumber());
        try {
            jenkinsJobMapper.update(jenkinsJob,queryWrapper);
        } catch (Exception e) {
            log.info(e.getMessage());
        }
    }

    @Override
    public String add(JenkinsJob jenkinsJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("url",jenkinsJob.getUrl());
        queryWrapper.eq("build_number",jenkinsJob.getBuildNumber());
        List<JenkinsJob> list = jenkinsJobMapper.selectList(queryWrapper);
        if (list.isEmpty()) {
            Date date = new Date();
            jenkinsJob.setJobStartAt(date);
            jenkinsJobMapper.insert(jenkinsJob);
            return "插入成功";
        } else {
            return "插入失败，BUILDNUMBER/URL重复";
        }
    }

    @Override
    public void delete(JenkinsJob jenkinsJob) {
        jenkinsJobMapper.deleteById(jenkinsJob);
    }
}
