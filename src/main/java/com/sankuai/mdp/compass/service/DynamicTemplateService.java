package com.sankuai.mdp.compass.service;

import com.sankuai.mdp.compass.entity.FailedReason;
import net.minidev.json.JSONObject;

import java.util.List;

public interface DynamicTemplateService {
    String getXmlContentByTemplateName(String business, String templateName, String source, String scenes);
    String getXmlContentByZipUrl(String templateName, String zipUrl);
    Boolean isContaintMge(String business, String templateName, String source, String scenes);
    String collectDynamicTestResult(JSONObject body) throws Exception;
    List<String> failedReasonForDynamicReportList();
    void failedReasonAdd(FailedReason failedReason);
}
