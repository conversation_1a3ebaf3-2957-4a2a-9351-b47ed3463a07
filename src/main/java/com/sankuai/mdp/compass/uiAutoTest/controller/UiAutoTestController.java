package com.sankuai.mdp.compass.uiAutoTest.controller;


import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.uiAutoTest.service.AutoTest;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by liuyang359 on 2022-07-01.
 */
@RestController
@RequestMapping("/compass/api/autotest")
public class UiAutoTestController {
    @Autowired
    AutoTest autoTest;


    @PostMapping("/getblacklist")
    public String getBlacklist() {
        return autoTest.getDeviceBlacklist();
    }

    @PostMapping("/result")
    public Resp result(@RequestBody JSONObject conanBody) throws Exception {
        return autoTest.result(conanBody);
    }




}
