package com.sankuai.mdp.compass.uiAutoTest.service.impl;/* *************
 * @author: liuYang359
 * @date: 2022/10/13 10:45 上午
 * @description:
 */

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.getAPK.entity.ApkInfo;
import com.sankuai.mdp.compass.getAPK.service.IGetApkDataService;
import com.sankuai.mdp.compass.uiAutoTest.service.AutoTest;
import com.sankuai.mdp.compass.uiAutoTest.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;

@Slf4j
@Service
public class AutoTestImpl implements AutoTest {
    public String blackDeviceKey = "autoTestdeviceSNBlacklist";
    public String blackFirmKey = "autoTestdevicesManufacturerBlacklist";
    public LionUtil lionUtil = new LionUtil();
    DxUtil dxUtil = new DxUtil();
    ConanUtil conanUtil = new ConanUtil();
    public String conanDeviceUrlAndroid = "https://conan.sankuai.com/ci/device/Android";
    public String devicesParams = "useFor=2&misId=#misId&type=ONLINE&conanKey=#conanKey&brand=#brand";
    public String jenkinsandroidURL = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/aimeituan_regression_test_for_conan/buildWithParameters";
    public String jenkinsIOSURL = "https://jenkins.sankuai.com/job/%E5%B9%B3%E5%8F%B0%E4%B8%9A%E5%8A%A1%E7%BB%84/job/imeituan-appium-uitest/buildWithParameters";
    @Autowired
    IGetApkDataService iGetApkDataService;
    @Autowired
    ConanJobService conanJobService;

    @Async
    public boolean build(String apkUrl, String platform, String testPage) {/*
     * @param apkUrl:
     * @param platform:
     * @return: boolean
     * @author: liuyang359
     * @date: 2022/10/25 11:22 上午
     * @description:触发job
     */
        String token =TokenUtil.getToken("jenkins_token_autotest");
        if (platform.equals("android")) {
            ApkInfo apkInfo = new ApkInfo();
            apkInfo.setOs("Android");
            apkInfo.setAppName("com.sankuai.meituan");
            apkInfo.setBuildTypeName("Release 打包");
            apkInfo.setCurrentBranch(1);
            apkUrl = JsonUtil.getString(iGetApkDataService.getApkUrl(apkInfo), "appUrl");
            String params = "apkurl=" + apkUrl + "&abi=arm64-v8a" + "&testPage=" + testPage;

            try {
                HttpUtil.vpostbyToken(jenkinsandroidURL, params, TokenUtil.getNewJenkinsToken());
            } catch (IOException e) {
                log.info("发送jenkins post请求失败"+e.getMessage());
                return false;
            }
            return true;
        } else {
            String params = "apkurl=" + apkUrl + "&testPage=" + testPage;
            try {
                HttpUtil.vpostbyToken(jenkinsIOSURL, params, TokenUtil.getNewJenkinsToken());
            } catch (IOException e) {
               log.info("发送jenkins post请求失败"+e.getMessage());
                return false;
            }
            return true;
        }

    }


    public Resp result(JSONObject conanBody) {
        log.info("conanBody: " + conanBody);
        Resp resp = new Resp();
        log.info(String.valueOf(conanBody));
        ArrayList<String> deviceList = new ArrayList<>();
        deviceList.add("PPA-AL20");
        deviceList.add("JSC-AL50");
        try {
            JsonParser jsonParser = new JsonParser();
            // 解析云测回调参数
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            JsonArray devicesList = jsonParser.parse(conanBody.get("devicelist").toString()).getAsJsonArray();
            String deviceModel = devicesList.get(0).getAsJsonObject().get("deviceModel").getAsString();
            String conanId = jobData.get("id").getAsString();
            String reportUrl = jobData.get("report").getAsString();
            JsonArray userList = new JsonArray();
            userList.add("zhangyuchi02");
            if(conanUtil.getPassrate(conanId).get("pass").getAsFloat() == 1){
                resp.setCode(200);
                if (EnvUtil.isOnline()) {
                    if(deviceList.contains(deviceModel)){
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("✅✅ android账号自动化测试通过  ✅✅：\n" +
                                    "【查看报告】：[查看报告|" + reportUrl
                                    + "]\n"+ "如有问题请联系zhangyuchi02", user);
                        }
                        dxUtil.sendToGroupByCompass("✅✅ android账号自动化测试通过 ✅✅：\n" +
                                "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68714749072L);
                    }
                    else {
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("✅✅ ios账号自动化测试通过  ✅✅：\n" +
                                    "【查看报告】：[查看报告|" + reportUrl
                                    + "]\n"+ "如有问题请联系zhangyuchi02", user);
                        }
                        dxUtil.sendToGroupByCompass("✅✅ ios账号自动化测试通过 ✅✅：\n" +
                                "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68714749072L);
                    }
                }
            }
            else {
                if (EnvUtil.isOnline()) {
                    if(deviceList.contains(deviceModel)){
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("❌❌ android账号自动化测试不通过  ❌❌：\n" +
                                    "【查看报告】：[查看报告|" + reportUrl
                                    + "]\n"+ "如有问题请联系zhangyuchi02", user);
                        }
                        dxUtil.sendToGroupByCompass("❌❌ android账号自动化测试不通过 ❌❌：\n" +
                                "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68714749072L);
                    }
                    else {
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("❌❌ ios账号自动化测试不通过  ❌❌：\n" +
                                    "【查看报告】：[查看报告|" + reportUrl
                                    + "]\n"+ "如有问题请联系zhangyuchi02", user);
                        }
                        dxUtil.sendToGroupByCompass("❌❌ ios账号自动化测试不通过 ❌❌：\n" +
                                "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68714749072L);
                    }
                }
            }
        }catch (Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
        return Resp.success();
    }

    public String getDeviceBlacklist() {/*

     * @return: java.lang.String
     * @author: liuyang359
     * @date: 2022/10/13 11:05 上午
     * @description:返回黑名单列表
     */
        String blackList = "";
        blackList += getBlackFirmDeviceBlacklist();
        blackList += lionUtil.getValue(blackDeviceKey);
        return blackList;
    }

    public String getBlackFirmDeviceBlacklist() {/*

     * @return: java.lang.String
     * @author: liuyang359
     * @date: 2022/10/14 10:26 上午
     * @description:获得厂商黑名单，厂商名单通过lion获取
     */
        String firmValue = lionUtil.getValue(blackFirmKey);
        String[] firmList = firmValue.split(",");
        StringBuilder deviceBlacklist = new StringBuilder();
        for (String index : firmList) {
            deviceBlacklist.append(getBlackFirmDeviceBlacklistByHttp(index));
        }

        return deviceBlacklist.toString();
    }

    public String getBlackFirmDeviceBlacklistByHttp(String firmValue) {/*
     * @param firmValue:
     * @return: java.lang.String
     * @author: liuyang359
     * @date: 2022/10/14 10:27 上午
     * @description:请求云测获得厂商可用名单
     */
        String Token = TokenUtil.getToken("conanKey");
        StringBuilder blacklist = new StringBuilder();
        try {
            String misID = Token.split("_")[0];
            String conanKey = Token.split("_")[1];
            devicesParams = devicesParams.replace("#misId", misID);
            devicesParams = devicesParams.replace("#conanKey", conanKey);
            devicesParams = devicesParams.replace("#brand", firmValue);
            String response = HttpUtil.sendPost(conanDeviceUrlAndroid, devicesParams);
            JsonArray deviceJsonArray = new JsonParser().parse(response).getAsJsonArray();
            for (JsonElement index : deviceJsonArray) {
                blacklist.append(index.getAsJsonObject().get("sn").getAsString());
                blacklist.append(",");
            }
        } catch (Exception E) {
            return "";
        }
        return blacklist.toString();
    }


}
