package com.sankuai.mdp.compass.uiAutoTest.utils;/* *************
 * @author: liuYang359
 * @date: 2022/10/18 10:25 上午
 * @description:
 */

import com.google.gson.JsonObject;

public class JsonUtil {/*
 * @param null:
 * @return: null
 * @author: liuyang359
 * @date: 2022/10/19 3:24 下午
 * @description:谷歌gjson工具
 */

    public static String getString(JsonObject data, String key) {/*
     * @param data:
     * @param key:
     * @return: java.lang.String
     * @author: liuyang359
     * @date: 2022/10/19 3:24 下午
     * @description:谷歌gjson获得string方法，带判空，健壮性处理
     */
        if (data != null && data.has(key) && !data.get(key).isJsonNull())
            return data.get(key).getAsString();
        else
            return "";
    }

    public static int getInt(JsonObject data, String key) {
        /*
         * @param data:
         * @param key:
         * @return: int
         * @author: liuyang359
         * @date: 2022/10/19 3:24 下午
         * @description:谷歌gjson获得int方法，带判空，健壮性处理
         */
        if (data != null && data.has(key) && data.get(key).isJsonNull())
            return data.get(key).getAsInt();
        else
            return 0;
    }
}
