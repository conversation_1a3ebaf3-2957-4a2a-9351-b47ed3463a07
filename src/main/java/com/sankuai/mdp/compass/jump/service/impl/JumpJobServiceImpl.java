package com.sankuai.mdp.compass.jump.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.*;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.getAPK.service.IGetApkDataService;
import com.sankuai.mdp.compass.jump.entity.JumpDetail;
import com.sankuai.mdp.compass.jump.entity.JumpJob;
import com.sankuai.mdp.compass.jump.mapper.JumpDetailMapper;
import com.sankuai.mdp.compass.jump.mapper.JumpJobMapper;
import com.sankuai.mdp.compass.jump.service.JumpDetailService;
import com.sankuai.mdp.compass.jump.service.JumpJobService;
import com.sankuai.mdp.compass.service.OcrService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import net.minidev.json.JSONValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class JumpJobServiceImpl extends ServiceImpl<JumpJobMapper, JumpJob> implements JumpJobService {
    @Autowired
    JumpJobMapper jumpJobMapper;
    @Autowired
    JumpDetailMapper jumpDetailMapper;
    @Autowired
    JumpDetailService jumpDetailService;
    @Autowired
    OcrService ocrService;
    @Autowired
    ConanJobService conanJobService;
    @Autowired
    private Resp resp;
    @Autowired
    IGetApkDataService getApkDataService;

    ComConstant comConstant = new ComConstant();
    ConanUtil conanUtil = new ConanUtil();
    VenusUtil venusUtil = new VenusUtil();
    DxUtil dxUtil = new DxUtil();
    FileUtil fileUtil = new FileUtil();

    private String fileDir = "jump";

    private Boolean doDataAnalyse = true;
    private Boolean doNotification = false;
    private Boolean processAnyWay = false;

    @Override
    public Integer add(JumpJob jumpJob) {
        Date date = new Date();
        jumpJob.setCreateTime(date);

        jumpJob.setBaseAppUrl(getApkDataService.getPlist(jumpJob.getBaseApp()));
        jumpJob.setTestAppUrl(getApkDataService.getPlist(jumpJob.getTestApp()));
        log.info("jumpJob is :" + jumpJob);
        jumpJobMapper.insert(jumpJob);
        return jumpJob.getId();
    }

    @Override
    public Resp update(JumpJob jumpJob) {
        QueryWrapper<JumpJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", jumpJob.getId());
        JumpJob existingJob = jumpJobMapper.selectOne(queryWrapper);
        if (existingJob == null) {
            resp.setMsg("没有找到 " + jumpJob.getId() + "对应的内容");
            resp.setCode(-1);
            return resp;
        }

        boolean isUpdated = false;
        try {
            // 更新 baseReportId
            if (jumpJob.getBaseReportId() != null) {
                String updatedBaseReportId = appendReportId(existingJob.getBaseReportId(), jumpJob.getBaseReportId());
                if (!updatedBaseReportId.equals(existingJob.getBaseReportId())) {
                    existingJob.setBaseReportId(updatedBaseReportId);
                    isUpdated = true;
                }
            }
            // 更新 testReportId
            if (jumpJob.getTestReportId() != null) {
                String updatedTestReportId = appendReportId(existingJob.getTestReportId(), jumpJob.getTestReportId());
                if (!updatedTestReportId.equals(existingJob.getTestReportId())) {
                    existingJob.setTestReportId(updatedTestReportId);
                    isUpdated = true;
                }
            }
            // 更新 testMockid
            if (jumpJob.getTestMockid() != null) {
                String updatedTestMockid = appendReportId(existingJob.getTestMockid(), jumpJob.getTestMockid());
                if (!updatedTestMockid.equals(existingJob.getTestMockid())) {
                    existingJob.setTestMockid(updatedTestMockid);
                    isUpdated = true;
                }
            }
            // 更新 testStatus
            if (jumpJob.getTestStatus() != null) {
                existingJob.setTestStatus(jumpJob.getTestStatus());
                isUpdated = true;
            }
            if (isUpdated) {
                jumpJobMapper.updateById(existingJob);
                log.info("成功调用 update 接口更新 jumpJob 数据，对应 ID{} ", jumpJob.getId());
                resp.setMsg("成功调用 update 接口更新 jumpJob 数据");
                resp.setCode(1);
                return resp;
            } else {
                log.info("对应 ID {} 改动数据与先前一致，请查看是否为重复调用", jumpJob.getId());
                resp.setMsg("对应 ID " + jumpJob.getId() + "改动数据与先前一致，请查看是否为重复调用");
                resp.setCode(-1);
                return resp;
            }
        } catch (Exception e) {
            log.error("调用 update 接口更新 jumpJob 数据失败，对应 ID {}", jumpJob.getId(), e);
            resp.setMsg("调用 update 接口更新 jumpJob 数据失败，对应 ID 为: " + jumpJob.getId());
            resp.setCode(-1);
            return resp;
        }
    }

    @Override
    public Resp updateTestConanId(JumpJob jumpJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", jumpJob.getId());
        JumpJob jumpJob1 = jumpJobMapper.selectOne(queryWrapper);
        if (null != jumpJob1) {
            jumpJob1.setTestReportId(jumpJob.getTestReportId());
            jumpJobMapper.updateById(jumpJob1);
        }
        return null;
    }

    @Override
    public Resp updateBaseConanId(JumpJob jumpJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", jumpJob.getId());
        JumpJob jumpJob1 = jumpJobMapper.selectOne(queryWrapper);
        if (null != jumpJob1) {
            jumpJob1.setTestReportId(jumpJob.getBaseReportId());
            jumpJobMapper.updateById(jumpJob1);
        }
        return null;
    }

    public IPage<JumpJob> list2(QueryRequest request, JumpJob jumpJob) {
        try {
            LambdaQueryWrapper<JumpJob> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.last("order by create_time DESC");
            Page<JumpJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public Resp result(JSONObject conanBody) {
        Resp resp = new Resp();

        parseTestModeParameters(conanBody);
        // parseTestModeParameters 将从 conanBody 的 test 字段中读取 doDataAnalyse, doNotification, processAnyWay 的值
        // 如果 test 字段不存在或字段缺失，则使用默认值

        try {
            log.info("云测回调信息为: " + conanBody.toString());
            String conanId = parseConanId(conanBody);
            conanJobService.collectResult(conanBody);

            // 根据 ConanId 获取对应 JumpJob
            JumpJob jumpJob = findJumpJobByConanId(conanId);
            if (jumpJob == null) {
                log.warn("没有找到关联job，无法继续处理云测任务，云测ID：" + conanId);
                resp.setMsg("关联job未找到");
                resp.setCode(-1);
                return resp;
            } else {
                log.info("对应job：" + jumpJob);
            }

            String type = jumpJob.getType();//取值：popUpInspection 或 homePageCheck
            boolean isPopTest = Objects.equals(type, "popUpInspection");
            String dirPath = comConstant.OUT_PUT + fileDir + jumpJob.getId();
            log.info("本次巡检类型为："+ type);
            log.info("对应文件路径：" + dirPath);

            JsonObject conanJobInfo = conanUtil.getJobInfo(conanId);
            if (conanJobInfo == null) {
                log.warn("未能获取云测任务详情，云测ID：" + conanId);
                resp.setMsg("云测任务详情获取失败");
                resp.setCode(-1);
                return resp;
            }

            if (doDataAnalyse) {
                processAllTasks(conanJobInfo, dirPath, jumpJob, conanId, isPopTest);
            }

            // 更新任务状态并判断是否全部完成
            boolean finishFlag = finalizeJobStatus(jumpJob, conanId, isPopTest);

            if (finishFlag) {
                handleFinishNotificationAndCleanup(jumpJob, isPopTest);
            }

            resp.setMsg("success");
            resp.setData(jumpJob.getId());
            resp.setCode(200);
            return resp;
        } catch (Exception e) {
            log.info(e.toString());
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
    }

    // 【新增方法】: 从 conanBody 的 test 字段中解析新参数
    private void parseTestModeParameters(JSONObject conanBody) {
        if (conanBody.containsKey("test")) {
            String testString = (String) conanBody.get("test");
            JSONObject testObj = (JSONObject) JSONValue.parse(testString);
            // doDataAnalyse
            if (testObj.containsKey("doDataAnalyse")) {
                this.doDataAnalyse = Objects.equals(testObj.getAsString("doDataAnalyse"), "1");
            }
            // doNotification
            if (testObj.containsKey("doNotification")) {
                this.doNotification = Objects.equals(testObj.getAsString("doNotification"), "1");
            }
            // processAnyWay
            if (testObj.containsKey("processAnyWay")) {
                this.processAnyWay = Objects.equals(testObj.getAsString("processAnyWay"), "1");
            }
        }
        // 若 test 字段不存在或字段缺失，使用默认值：doDataAnalyse=true, doNotification=false, processAnyWay=false
    }

    private String parseConanId(JSONObject conanBody) {
        JsonParser jsonParser = new JsonParser();
        JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
        return jobData.get("id").getAsString();
    }

    private JumpJob findJumpJobByConanId(String conanId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.last("where test_report_id like '%" + conanId + "%' or base_report_id like '%" + conanId + "%'");
        return jumpJobMapper.selectOne(queryWrapper);
    }

    private void processAllTasks(JsonObject conanJobInfo, String dirPath, JumpJob jumpJob, String conanId, boolean isPopTest) throws Exception {
        JsonArray taskArray = conanJobInfo.getAsJsonArray("taskList");
        String platform = conanJobInfo.get("platform").getAsString();
        log.info("##############" + taskArray.toString());
        for (JsonElement taskElement : taskArray) {
            JsonObject task = taskElement.getAsJsonObject();
            processSingleTask(task, dirPath, jumpJob, conanId, platform, isPopTest);
        }
    }

    private void processSingleTask(JsonObject task, String dirPath, JumpJob jumpJob, String conanId, String platform, boolean isPopTest) throws Exception {
        String taskId = task.get("id").getAsString();
        // 如果 processAnyWay = false 且任务已处理过，则跳过
        // 如果 processAnyWay = true 则即使已处理过也继续执行
        if (!processAnyWay && taskAlreadyProcessed(taskId)) {
            log.info("id为 " + taskId + " 的任务已校验下载入库完成过了，跳过");
            return;
        }
        // 下载文件
        String downloadURL = conanUtil.getDownloadURL(taskId);
        if (downloadURL == null) {
            throw new RuntimeException("下载链接获取失败，任务ID：" + taskId);
        }
        downloadURL = adjustDownloadURL(downloadURL);
        downloadAndExtract(downloadURL, dirPath, taskId);

        // 根据类型分支处理逻辑
        if (isPopTest) {
            processPopUpTestData(dirPath, taskId, jumpJob, conanId, platform);
        } else {
            processHomePageTestData(dirPath, taskId, jumpJob, conanId, platform);
        }
    }

    private boolean taskAlreadyProcessed(String taskId) {
        QueryWrapper<JumpDetail> detailQueryWrapper = new QueryWrapper<JumpDetail>().eq("conan_task", taskId);
        int detailUpdateFlag = jumpDetailMapper.selectCount(detailQueryWrapper);
        return detailUpdateFlag > 0;
    }

    private String adjustDownloadURL(String downloadURL) {
        downloadURL = downloadURL.replace("s3-img","s3");
        if (!EnvUtil.isOnline() && downloadURL.contains("s3.meituan.net")) {
            downloadURL = downloadURL.replace("s3.meituan.net", "s3plus.sankuai.com");
        }
        log.info("文件下载地址为: " + downloadURL);
        return downloadURL;
    }

    private void downloadAndExtract(String downloadURL, String dirPath, String taskId) throws Exception {
        int maxRetryTimes = 3;
        int retryTimes = 0;
        while (retryTimes < maxRetryTimes) {
            try {
                FileUtil.downloadAndReadFileNew(downloadURL, dirPath, taskId);
                break;
            } catch (Exception e) {
                retryTimes++;
                log.error("文件下载或读取失败，重试次数：" + retryTimes, e);
                if (retryTimes >= maxRetryTimes) {
                    throw new RuntimeException("文件下载或读取失败，已达最大重试次数，URL：" + downloadURL + "，任务ID：" + taskId, e);
                }
            }
        }
    }

    /**
     * 处理弹窗巡检数据的逻辑
     * 对应原先 if (Objects.equals(type, "popUpInspection")) { ... } 中针对弹窗的处理
     */
    private void processPopUpTestData(String dirPath, String taskId, JumpJob jumpJob, String conanId, String platform) throws Exception {
        log.info("现在进入的是弹窗");
        // 这里需要读取对应目录的文件列表，找到 txt 文件，对其中的数据进行处理
        String picPath = dirPath + "/" + taskId + "/jumpPic/";
        ArrayList<String> fileList = new ArrayList<>();
        FileUtil.getFileList(picPath, "", fileList);

        // 只需要对 txt 文件进行解析，判断是否有弹窗，并将数据入库
        for (String fileName : fileList) {
            if (!fileName.contains(".txt")) {
                continue;
            }

            String txtName = fileName.split(".txt")[0];
            String caseName = "PopUp";
            String delimiter = "##|\\|";
            // 读取 txt 文件内容
            List<List<Object>> casePics = FileUtil.readFileByLines(picPath + fileName, delimiter);
            for (List<Object> casePic : casePics) {
                if (casePic.size() < 5) {
                    log.error("数据格式错误，跳过此行数据: " + casePic);
                    continue;
                }
                String jumpLink = casePic.get(0).toString();
                String bg = casePic.get(1).toString();
                String bu = casePic.get(2).toString();
//               游戏数据多一项，时间记录在多出的一项，需要单独处理
                String picName = casePic.size() == 6 ? caseName + "/" + casePic.get(4) : caseName + "/" + casePic.get(4) + "_" + casePic.get(5);
                String picUrl = null;
                if (casePic.get(casePic.size() - 1).toString().startsWith("http://")) {
                    picUrl = casePic.get(casePic.size() - 1).toString();
                }

                JumpDetail jumpDetail = new JumpDetail();
                jumpDetail.setIsPopUp(txtName.contains("has_dialog"));
                jumpDetail.setPlatform(platform);
                jumpDetail.setJumpLink(jumpLink);
                jumpDetail.setConanTask(taskId);
                jumpDetail.setBuildId(jumpJob.getId());
                jumpDetail.setCaseName(caseName);
                jumpDetail.setPicName(picName);
                jumpDetail.setBg(bg);
                jumpDetail.setBu(bu);

                // 判断是基准组还是测试组
                String baseReportId = jumpJob.getBaseReportId();
                String testReportId = jumpJob.getTestReportId();
                if (baseReportId != null && baseReportId.contains(conanId)) {
                    if (picUrl != null) {
                        jumpDetail.setBasePic(picUrl);
                    }
                    jumpDetail.setBaseResolution("N/A");
                    jumpDetail.setType(0);
                } else if (testReportId != null && testReportId.contains(conanId)) {
                    if (picUrl != null) {
                        String[] res = ocrService.recognizeP0UIBugByOnePic(picUrl);
                        if (res[1].length() > 1) {
                            picUrl = res[1];
                            jumpDetail.setErrorInfo(res[0]);
                        }
                        jumpDetail.setTestPic(picUrl);
                    }
                    jumpDetail.setTestResolution("N/A");
                    jumpDetail.setType(1);
                }

                jumpDetailService.insert(jumpDetail);
            }
            log.info("保存弹窗图片链接");
        }
    }

    /**
     * 处理首页跳转巡检数据的逻辑
     * 对应原先 else { ... } (即首页跳转巡检) 的处理部分。
     * 包括读取 jumpAllResults.json、jumpUrls.json，对截图进行 OCR 检测，存入 JumpDetail。
     */
    private void processHomePageTestData(String dirPath, String taskId, JumpJob jumpJob, String conanId, String platform) throws Exception {
        log.info("现在进入的是金刚区巡检");
        String picPath = dirPath + "/" + taskId + "/jumpPic/";
        ArrayList<String> fileList = new ArrayList<>();
        FileUtil.getFileList(picPath, "", fileList);
        ObjectMapper objectMapper = new ObjectMapper();

        // 读取 jumpAllResults.json、jumpUrls.json
        String jsonFileName = "jumpAllResults.json";
        File jsonFile = new File(picPath + jsonFileName);

        String jumpUrlFileName = "jumpUrls.json";
        File jumpUrlFile = new File(picPath + jumpUrlFileName);

        HashMap<String, ArrayList<Boolean>> jumpTestFromHomepageResult = objectMapper.readValue(jsonFile, HashMap.class);
        HashMap<String, String> jumpUrl = objectMapper.readValue(jumpUrlFile, HashMap.class);

        // 统计跳转失败业务数
        int jumpFailNum = 0;
        for (Map.Entry<String, ArrayList<Boolean>> entry : jumpTestFromHomepageResult.entrySet()) {
            ArrayList<Boolean> resultList = entry.getValue();
            if (resultList.contains(false)) {
                jumpFailNum++;
            }
        }
        log.info("跳转失败业务数:" + jumpFailNum);

        // 获取 base/test 相关数据
        String testReportId = jumpJob.getTestReportId();
        String jumpPicFileName = "category.txt";
        // 同原逻辑
        String caseName = jumpPicFileName.split(".txt")[0];
        List<List<Object>> casePics = FileUtil.readFileByLines(picPath + jumpPicFileName, "\\|");
        for (List<Object> casePic : casePics) {
            if (Objects.equals(casePic.get(0).toString(), "系统桌面") || casePic.get(0).toString().contains("崩溃")) {
                log.info("当前截图为系统桌面或崩溃对应图片,跳过此行数据: " + casePic);
                continue;
            }
            if (casePic.size() < 2)  {
                log.error("数据格式错误，跳过此行数据: " + casePic);
                continue;
            }
            String bg = "美团平台";
            String bu = "平台业务";
            String picName = caseName + "/" + casePic.get(0);
            String picUrl = casePic.get(1).toString();
            JumpDetail jumpDetail = new JumpDetail();
            jumpDetail.setPlatform(platform);
            jumpDetail.setConanTask(taskId);
            jumpDetail.setBuildId(jumpJob.getId());
            jumpDetail.setCaseName(caseName);
            jumpDetail.setPicName(picName);
            jumpDetail.setBg(bg);
            jumpDetail.setBu(bu);
            // 用 diffUrl 来暂存一下云测 id 吧
            jumpDetail.setDiffUrl(conanId);
            // 用 BaseResolution 来暂存一下mock id 吧
            String mockId = extractMockId(jumpJob, conanId);
            jumpDetail.setBaseResolution(mockId);

            // 判断是测试组
            if (testReportId != null && testReportId.contains(conanId)) {
                jumpDetail.setTestResolution("N/A");
                jumpDetail.setType(1);
            }

            String errorInfo = "";
            // 过滤掉包含"金刚区"字样的 casePic（原逻辑里有个continue条件）
            if ((casePic.get(0).toString().contains("金刚区"))) {
                continue;
            }
            // 检查跳转结果
            String noIndexName = casePic.get(0).toString().split("_")[1];
            log.info(noIndexName);
            try{
                if (!jumpTestFromHomepageResult.get(noIndexName).get(0)) {
                    errorInfo += "跳转失败!跳过白屏检测";
                    jumpDetail.setErrorInfo(errorInfo);
                } else {
                    errorInfo += "跳转成功!";
                    String[] res = ocrService.recognizeP0UIBugByOnePic(picUrl);
                    if (res == null || res.length == 0) {
                        errorInfo += "OCR服务调取失败";
                        jumpDetail.setErrorInfo(errorInfo);
                    }
                    else if (res[1].length() > 1) {
                        picUrl = res[1];
                        String[] errorLines = res[0].split("\n");
                        boolean whiteErrorFlag = false;
                        for (String line : errorLines) {
                            if (line.contains("白屏")) {
                                whiteErrorFlag = true;
                                break;
                            }
                        }
                        if (whiteErrorFlag) {
                            errorInfo += "存在白屏错误:\n";
                        } else {
                            errorInfo += "存在疑似异常:\n";
                        }
                        errorInfo += res[0];
                        jumpDetail.setErrorInfo(errorInfo);
                    } else {
                        jumpDetail.setErrorInfo(errorInfo);
                    }
                }
                jumpDetail.setTestPic(picUrl);
                // 保存跳链
                jumpDetail.setJumpLink(jumpUrl.get(noIndexName));
                jumpDetailService.insert(jumpDetail);
                log.info("保存图片链接");

            }
            catch (Exception e) {
                log.error("出现异常:", e); // 或者 e.printStackTrace() 也行
            }

        }
    }

    private String extractMockId(JumpJob jumpJob, String conanId) {
        String testReportId = jumpJob.getTestReportId();
        String mockIds = jumpJob.getTestMockid();
        if (testReportId != null && !testReportId.isEmpty()) {
            String[] ids = testReportId.split("_");
            if (mockIds != null && !mockIds.isEmpty()) {
                String[] mockIdArr = mockIds.split("_");
                for (int i = 0; i < ids.length; i++) {
                    if (Objects.equals(conanId, ids[i]) && i < mockIdArr.length) {
                        return mockIdArr[i];
                    }
                }
            }
        }
        return "N/A";
    }

    private boolean finalizeJobStatus(JumpJob jumpJob, String conanId, boolean isPopTest) {
        synchronized (this) {
            QueryWrapper queryWrapper2 = new QueryWrapper();
            queryWrapper2.last("where base_report_id like '%" + conanId + "%' or test_report_id like '%" + conanId + "%'");
            JumpJob jumpJob1 = jumpJobMapper.selectOne(queryWrapper2);

            String baseConanId = jumpJob1.getBaseReportId();
            String testConanId = jumpJob1.getTestReportId();
            boolean finishFlag = false;

            if (baseConanId != null && baseConanId.contains(conanId)) {
                finishFlag = handleBaseProcess(jumpJob1, conanId);
            } else if (testConanId != null && testConanId.contains(conanId)) {
                if (isPopTest) {
                    finishFlag = handlePopTestProcess(jumpJob1, conanId);
                } else {
                    finishFlag = handleTestProcess(jumpJob1, conanId);
                }
            }

            if (baseConanId == null || baseConanId.isEmpty()) {
                finishFlag = true;
            }

            return finishFlag;
        }
    }

    private void handleFinishNotificationAndCleanup(JumpJob jumpJob, boolean isPopTest) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        FileUtil.delFolder(comConstant.OUT_PUT + fileDir + jumpJob.getId());

        String type = jumpJob.getType();
        String reportUrl;
        if (Objects.equals(type, "popUpInspection")) {
            reportUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/uiautotest/detail?id=" + jumpJob.getId();
        } else {
            reportUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/uiautotest/jumpDetail?id=" + jumpJob.getId();
        }

        JsonArray userList = new JsonArray();

        if (isPopTest) {
            // 弹窗巡检通知逻辑
            userList.add("hubiao06");
            userList.add("zhangwenkai02");
            userList.add("guanxin04");
            userList.add("maonan03");
            String message = "※ 弹窗巡检自动化提醒 ※：\n 【任务ID】：" + jumpJob.getId() + "\n" + "【测试进度】：✅ 已完成，请及时对结果进行确认\n"
                    + "【开始时间】：" + sdf.format(jumpJob.getCreateTime()) + "\n" + "【完成时间】："
                    + sdf.format(new Date()) + "\n" + "【查看报告】：[查看报告|" + reportUrl + "]\n";
            if (EnvUtil.isOnline()) {
                for (JsonElement u : userList) {
                    String user = u.getAsString();
                    dxUtil.sendToPersionByCompass(message, user);
                }
            }
        } else {
            // 首页跳转巡检通知逻辑
            userList.add("guanxin04");
            userList.add("cuijie12");
            QueryWrapper queryWrapper3 = new QueryWrapper();
            queryWrapper3.eq("build_id", jumpJob.getId());
            List<JumpDetail> jumpDetailList = jumpDetailMapper.selectList(queryWrapper3);
            log.info("jumpDetailList: " + jumpDetailList);
            int normalCnt = 0;
            int jumpErrorCnt = 0;
            int whiteErrorCnt = 0;
            int otherMayBeErrorCnt = 0;
            for(JumpDetail detail : jumpDetailList){
                String errorInfo = detail.getErrorInfo();
                if (errorInfo.contains("跳转失败")) {
                    jumpErrorCnt++;
                } else if(errorInfo.contains("白屏")) {
                    whiteErrorCnt++;
                } else if(errorInfo.contains("疑似")) {
                    otherMayBeErrorCnt++;
                } else {
                    normalCnt++;
                }
            }
            int pageCnt = jumpErrorCnt + whiteErrorCnt + otherMayBeErrorCnt + normalCnt;
            String finalAnswer = "总页面数: " + pageCnt + "\n 跳转错误: " + jumpErrorCnt + "\n 白屏错误: "
                    + whiteErrorCnt + "\n 疑似错误: " + otherMayBeErrorCnt;
            jumpJob.setResultText(finalAnswer);
            jumpJobMapper.updateById(jumpJob);
            String message = null;
            if (jumpErrorCnt > 0 || whiteErrorCnt > 0 || otherMayBeErrorCnt > 0){
                message = "※ 页面跳转自动化测试提醒 ※：\n "
                        + "【任务ID】：" + jumpJob.getId() + "\n"
                        + "【测试进度】：已完成，结果如下:\n"
                        + " 共测试" + pageCnt + "个页面\n"
                        + " " + jumpErrorCnt + " 个页面跳转失败 \n"
                        + " " + whiteErrorCnt + " 个页面存在白屏异常\n"
                        + " " + otherMayBeErrorCnt + " 个页面存在疑似异常\n"
                        + "【开始时间】：" + sdf.format(jumpJob.getCreateTime()) + "\n" +
                        "【完成时间】：" + sdf.format(new Date()) + "\n" +
                        "【查看报告】：[查看报告|" + reportUrl + "]\n";
                long groupId = 66343402880L; //埋点测试结果通知小组大象群
                if(doNotification && EnvUtil.isOnline()){
                    dxUtil.sendToGroupByCompass(message,groupId);
                } else {
                    log.info("当前是测试模式，不通知大象群消息，测试消息:" + finalAnswer);
                }
            }
            if (message != null && EnvUtil.isOnline() && doNotification) {
                for (JsonElement u : userList) {
                    String user = u.getAsString();
                    dxUtil.sendToPersionByCompass(message, user);
                }
            }
        }
    }


    private Boolean handleBaseProcess(JumpJob jumpJob, String conanId) {
        Boolean result = false;
        String baseFinishedId = jumpJob.getBaseFinishId();
        String baseConanId = jumpJob.getBaseReportId();
        if (baseFinishedId == null) {
            baseFinishedId = "";
        }
        if (baseFinishedId.isEmpty()) {
            baseFinishedId = conanId;
        } else if (!baseFinishedId.contains(conanId)) {
            baseFinishedId = baseFinishedId + "_" + conanId;
        }
        if (baseFinishedId.length() == baseConanId.length()) {
            jumpJob.setBaseStatus(1);
            jumpJob.setBaseFinishTime(new Date());

            if (jumpJob.getTestStatus() == 1) {
                result = true;
            }
        }
        jumpJob.setBaseFinishId(baseFinishedId);
        jumpJobMapper.updateById(jumpJob);
        return result;
    }

    private Boolean handlePopTestProcess(JumpJob jumpJob, String conanId) {
        Boolean result = false;
        String testFinishedId = jumpJob.getTestFinishId();
        String testConanId = jumpJob.getTestReportId();
        if (testFinishedId == null) {
            testFinishedId = "";
        }
        if (testFinishedId.isEmpty()) {
            testFinishedId = conanId;
        } else if (!testFinishedId.contains(conanId)) {
            testFinishedId = testFinishedId + "_" + conanId;
        }
        if (testFinishedId.length() == testConanId.length()) {
            jumpJob.setTestStatus(1);
            jumpJob.setTestFinishTime(new Date());
            result = true;
        }

        jumpJob.setTestFinishId(testFinishedId);
        jumpJobMapper.updateById(jumpJob);
        return result;
    }

    private Boolean handleTestProcess(JumpJob jumpJob, String conanId) {
        Boolean result = false;
        String testFinishedId = jumpJob.getTestFinishId();
        String testConanId = jumpJob.getTestReportId();
        if (testFinishedId == null) {
            testFinishedId = "";
        }
        if (testFinishedId.isEmpty()) {
            testFinishedId = conanId;
        } else if (!testFinishedId.contains(conanId)) {
            testFinishedId = testFinishedId + "_" + conanId;
        }
        if (testFinishedId.length() == testConanId.length()) {
            jumpJob.setTestStatus(1);
            jumpJob.setTestFinishTime(new Date());
            if (jumpJob.getBaseStatus()!=null && jumpJob.getBaseStatus()==1) {
                result = true;
            }
        }

        jumpJob.setTestFinishId(testFinishedId);
        jumpJobMapper.updateById(jumpJob);
        return result;
    }

    private String appendReportId(String existingId, String newId) {
        if (existingId == null || existingId.isEmpty()) {
            return newId;
        }
        if (existingId.contains(newId)) {
            return existingId;
        }
        return existingId + "_" + newId;
    }

}