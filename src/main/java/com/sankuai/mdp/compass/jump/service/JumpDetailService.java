package com.sankuai.mdp.compass.jump.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.jump.entity.JumpDetail;
import com.sankuai.mdp.compass.jump.entity.JumpJob;
import org.apache.thrift.TException;

public interface JumpDetailService extends IService<JumpDetail> {
    void insert(JumpDetail jumpDetail) throws TException;
    IPage<JumpDetail> list(QueryRequest request, JumpDetail jumpDetail, String id);
}
