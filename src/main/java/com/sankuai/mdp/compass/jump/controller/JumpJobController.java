package com.sankuai.mdp.compass.jump.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.jump.entity.JumpJob;
import com.sankuai.mdp.compass.jump.service.JumpJobService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/jump/test")
public class JumpJobController extends BaseController {
    @Autowired
    JumpJobService jumpJobService;

    @PostMapping("/new")
    public Integer newBuild(JumpJob jumpJob) {
        return jumpJobService.add(jumpJob);
    }

    @PostMapping("/update")
    public Resp update(JumpJob jumpJob) {
        return jumpJobService.update(jumpJob);
    }

    @PostMapping("/result")
    public Resp result(@RequestBody JSONObject conanBody) throws Exception {
        return jumpJobService.result(conanBody);
    }

    @PutMapping("/updateTestConanId")
    public Resp updateTestConanId(JumpJob jumpJob) {
        return jumpJobService.updateTestConanId(jumpJob);
    }

    @PutMapping("/updateBaseConanId")
    public Resp updateBaseConanId(JumpJob jumpJob) {
        return jumpJobService.updateBaseConanId(jumpJob);
    }

    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, JumpJob jumpJob)  {
        IPage<JumpJob> JumpJobIPage = this.jumpJobService.list2(request, jumpJob);
        if (JumpJobIPage != null) {
            return getDataTable(JumpJobIPage);
        } else {
            return null;
        }
    }
}
