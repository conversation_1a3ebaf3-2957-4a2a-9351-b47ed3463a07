package com.sankuai.mdp.compass.jump.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import net.minidev.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.jump.entity.JumpJob;

public interface JumpJobService extends IService<JumpJob> {
    Integer add(JumpJob jumpJob);
    Resp update(JumpJob jumpJob);
    Resp result(JSONObject conanBody);
    Resp updateTestConanId(JumpJob jumpJob);
    Resp updateBaseConanId(JumpJob jumpJob);
    IPage<JumpJob> list2(QueryRequest request, JumpJob jumpJob);
}
