package com.sankuai.mdp.compass.jump.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.QueryRequest;

import com.sankuai.mdp.compass.jump.entity.JumpDetail;
import com.sankuai.mdp.compass.jump.mapper.JumpDetailMapper;
import com.sankuai.mdp.compass.jump.service.JumpDetailService;
import com.sankuai.mdp.compass.service.OcrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class JumpDetailServiceImpl extends ServiceImpl<JumpDetailMapper,JumpDetail> implements JumpDetailService {

    @Autowired
    JumpDetailMapper jumpDetailMapper;

    @Autowired
    OcrService ocrService;

    private static String compareModel = "1";
    private static String cutModel = "Complete";
    private static String lineConfidence = "0.2";
    private static String ignoreArea = "2";


    @Override
    public void insert(JumpDetail jumpDetail) throws TException {
        Integer type = jumpDetail.getType();
        String picName = jumpDetail.getPicName();
        String caseName = jumpDetail.getCaseName();
        String platform = jumpDetail.getPlatform();
        Integer buildId = jumpDetail.getBuildId();
        String testPic = jumpDetail.getTestPic();
        String basePic = jumpDetail.getBasePic();

        if (type == 1) {
            //基准组先结束
            QueryWrapper baseWrapper = new QueryWrapper();
            baseWrapper.eq("build_id",buildId);
            baseWrapper.eq("case_name",caseName);
            baseWrapper.eq("pic_name",picName);
            baseWrapper.eq("platform",platform);
            baseWrapper.isNotNull("base_pic");
            baseWrapper.last(" order by id asc limit 1");
            JumpDetail baseDetail = jumpDetailMapper.selectOne(baseWrapper);
            if (baseDetail != null) {
                basePic = baseDetail.getBasePic();
                String res = ocrService.diff(basePic, testPic, compareModel, cutModel, lineConfidence, ignoreArea);
                JsonObject jsonObject = new JsonParser().parse(res).getAsJsonObject();
                if (jsonObject.has("resultrurl") && jsonObject.has("similarresult")) {
                    try {
                        String diffUrl = jsonObject.get("resultrurl").getAsString();
                        Double similar = jsonObject.get("similarresult").getAsDouble();
                        jumpDetail.setDiffUrl(diffUrl);
                        jumpDetail.setSimilarity(similar);
                    } catch (Exception e){
                        log.error(e.getMessage());
                        log.error("diffUrl获取失败，jsonObject："+jsonObject.toString());
                    }
                }
            }
            jumpDetailMapper.insert(jumpDetail);

        } else if (type == 0) {
            jumpDetailMapper.insert(jumpDetail);

            //测试组先结束
            QueryWrapper testWrapper = new QueryWrapper();
            testWrapper.eq("build_id",buildId);
            testWrapper.eq("case_name",caseName);
            testWrapper.eq("pic_name",picName);
            testWrapper.eq("platform",platform);
            testWrapper.isNotNull("test_pic");
            testWrapper.isNull("diff_url");
            List<JumpDetail> testDetailList = jumpDetailMapper.selectList(testWrapper);
            for (int i = 0; i < testDetailList.size(); i++) {
                JumpDetail testDetail = testDetailList.get(i);
                testPic = testDetail.getTestPic();
                String res = ocrService.diff(basePic,testPic,compareModel,cutModel,lineConfidence,ignoreArea);
                JsonObject jsonObject = new JsonParser().parse(res).getAsJsonObject();
                if (jsonObject.has("resultrurl") && jsonObject.has("similarresult")) {
                    try {
                        String diffUrl = jsonObject.get("resultrurl").getAsString();
                        Double similar = jsonObject.get("similarresult").getAsDouble();
                        testDetail.setSimilarity(similar);
                        testDetail.setDiffUrl(diffUrl);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        log.info("diff error >> " + res);
                    }
                }
                jumpDetailMapper.updateById(testDetail);
            }
        }

    }

    @Override
    public IPage<JumpDetail> list(QueryRequest request, JumpDetail jumpDetail, String id) {
        try {
            return testPicList(id);
        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
    }
}

    private Page<JumpDetail> testPicList(String id) {
        Page<JumpDetail> result = new Page<>();
        LambdaQueryWrapper<JumpDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JumpDetail::getBuildId, id);
        queryWrapper.isNotNull(JumpDetail::getTestPic);
        queryWrapper.orderByAsc(JumpDetail::getId);
        Page<JumpDetail> page = new Page<>(1, 1500);
        IPage<JumpDetail> iPage = this.page(page, queryWrapper);
        List<JumpDetail> list = new ArrayList<>();
        JSONObject flagObject = new JSONObject();
        JSONObject baseObject = new JSONObject();
        int count = 0;
        for (int i = 0; i < iPage.getRecords().size(); i++) {
            JumpDetail record = iPage.getRecords().get(i);
            String picName = record.getPicName()+"_"+ count++;
            Boolean isPopUp = record.getIsPopUp();
            if (isPopUp == null) {
                isPopUp = false; // 或其他默认值
            }
            String testPic = record.getTestPic();
            String diffPic = record.getDiffUrl();
            String resolution = record.getTestResolution();
            Double similarity = record.getSimilarity();
            String errorInfo = record.getErrorInfo();
            String jumpUrl = record.getJumpLink();
            String mockId = record.getBaseResolution();
            if (!flagObject.has(picName)) {
                flagObject.put(picName, new JSONObject());
            }
            String platform = record.getPlatform();
            String basePic = null;
            String baseResolution = null;
            if (baseObject.has(picName + "-" + platform)) {
                basePic = baseObject.getJSONObject(picName + "-" + platform).getString("url");
                baseResolution = baseObject.getJSONObject(picName + "-" + platform).getString("resolution");
            } else {
                JumpDetail jumpDetail = getBaseOne(id, picName, platform);
                basePic = jumpDetail.getBasePic();
                baseResolution = jumpDetail.getBaseResolution();
                baseObject.put(picName + "-" + platform, new JSONObject());
                baseObject.getJSONObject(picName + "-" + platform).put("url", basePic);
                baseObject.getJSONObject(picName + "-" + platform).put("resolution", baseResolution);

            }
            if (!flagObject.getJSONObject(picName).has(platform)) {
                flagObject.getJSONObject(picName).put(platform, new JSONObject());
            }
            if (!flagObject.getJSONObject(picName).getJSONObject(platform).has("basePic")) {
                flagObject.getJSONObject(picName).getJSONObject(platform).put("basePic", new JSONObject());
                flagObject.getJSONObject(picName).getJSONObject(platform).getJSONObject("basePic").put("url", basePic);
                flagObject.getJSONObject(picName).getJSONObject(platform).getJSONObject("basePic").put("resolution",
                        baseResolution);
            }
            if (!flagObject.getJSONObject(picName).getJSONObject(platform).has("testPic")) {
                flagObject.getJSONObject(picName).getJSONObject(platform).put("testPic", new JSONArray());
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("rurl", testPic);
            jsonObject.put("durl", diffPic);
            jsonObject.put("resolution", resolution);
            jsonObject.put("similarity", similarity);
            jsonObject.put("isPopUp", isPopUp);
            jsonObject.put("errorInfo",errorInfo);
            jsonObject.put("jumpUrl",jumpUrl);
            jsonObject.put("mockId",mockId);
            flagObject.getJSONObject(picName).getJSONObject(platform).getJSONArray("testPic").put(jsonObject);
        }
        Iterator iterator = flagObject.keys();
        while (iterator.hasNext()) {
            String key = iterator.next().toString();
            JSONObject jsonObject = flagObject.getJSONObject(key);
            String baseResolution = "";
            Iterator pIt = jsonObject.keys();
            while (pIt.hasNext()) {
                String platform = pIt.next().toString();

                JumpDetail jumpDetail = new JumpDetail();
                jumpDetail.setPicName(key);
                jumpDetail.setPlatform(platform);
                baseObject = jsonObject.getJSONObject(platform).getJSONObject("basePic");
                baseResolution = baseObject.getString("resolution");
                String basePic = baseObject.getString("url");
                jumpDetail.setBaseResolution(baseResolution);
                jumpDetail.setBasePic(basePic);
                List<Object> testPicList = jsonObject.getJSONObject(platform).getJSONArray("testPic").toList();
                jumpDetail.setTestPicObject(testPicList);

                list.add(jumpDetail);
            }
        }
        Collections.sort(list, new JumpDetailComparator());
        result.setRecords(list);
        result.setTotal((long)list.size());
        return result;
    }

    private int getPriority(JumpDetail jd) {
        boolean hasJumpFail = false;
        boolean hasWhiteScreen = false;
        boolean hasSuspicious = false;

        List<Object> testPicObjects = (List<Object>) jd.getTestPicObject();
        if (testPicObjects != null) {
            for (Object obj : testPicObjects) {
                if (obj instanceof Map) {
                    Map<String, Object> testPicMap = (Map<String, Object>) obj;
                    String error = (String) testPicMap.get("errorInfo");
                    if (error != null) {
                        if (error.contains("跳转失败")) {
                            hasJumpFail = true;
                        }
                        if (error.contains("白屏")) {
                            hasWhiteScreen = true;
                        }
                        if (error.contains("疑似异常")) {
                            hasSuspicious = true;
                        }
                    }
                }
            }
        }

        if (hasJumpFail) return 1;
        if (hasWhiteScreen) return 2;
        if (hasSuspicious) return 3;
        return 4;
    }

    private int countUniqueErrorInfo(JumpDetail jd, String keyword) {
        Set<String> uniqueErrors = new HashSet<>();
        List<Object> testPicObjects = (List<Object>) jd.getTestPicObject();
        if (testPicObjects != null) {
            for (Object obj : testPicObjects) {
                if (obj instanceof Map) {
                    Map<String, Object> testPicMap = (Map<String, Object>) obj;
                    String error = (String) testPicMap.get("errorInfo");
                    if (error != null && error.contains(keyword)) {
                        // **解析具体异常信息**
                        // 假设 errorInfo 格式如下：
                        // "跳转成功!存在疑似异常:\n0:异常描述1\n1:异常描述2\n..."
                        String[] lines = error.split("\n");
                        for (String line : lines) {
                            if (line.contains(keyword)) {
                                continue; // 跳过标题行
                            }
                            int colonIndex = line.indexOf(":");
                            if (colonIndex != -1 && colonIndex + 1 < line.length()) {
                                String exception = line.substring(colonIndex + 1).trim();
                                uniqueErrors.add(exception);
                            }
                        }
                    }
                }
            }
        }
        return uniqueErrors.size();
    }

    private long getMinDurl(JumpDetail jd) {
        long minDurl = Long.MAX_VALUE;
        List<Object> testPicObjects = (List<Object>) jd.getTestPicObject();
        if (testPicObjects != null) {
            for (Object obj : testPicObjects) {
                if (obj instanceof Map) {
                    Map<String, Object> testPicMap = (Map<String, Object>) obj;
                    Object durlObj = testPicMap.get("durl");
                    if (durlObj != null) {
                        try {
                            long durl = Long.parseLong(durlObj.toString());
                            if (durl < minDurl) {
                                minDurl = durl;
                            }
                        } catch (NumberFormatException e) {
                            // 如果 durl 无法转换为数字，则忽略
                        }
                    }
                }
            }
        }
        return minDurl;
    }

    private class JumpDetailComparator implements Comparator<JumpDetail> {

        @Override
        public int compare(JumpDetail jd1, JumpDetail jd2) {
            // 获取优先级
            int priority1 = getPriority(jd1);
            int priority2 = getPriority(jd2);

            // 按优先级排序
            if (priority1 != priority2) {
                return Integer.compare(priority1, priority2);
            }

            // 如果优先级相同且为 "疑似异常"，按数量排序
            if (priority1 == 3) { // 3 表示 "疑似异常"
                int count1 = countUniqueErrorInfo(jd1,"疑似异常"); // **修改**
                int count2 = countUniqueErrorInfo(jd2,"疑似异常"); // **修改**
                if (count1 != count2) {
                    return Integer.compare(count2, count1); // 降序
                }
            }

            // 比较最小 durl（升序）
            long minDurl1 = getMinDurl(jd1);
            long minDurl2 = getMinDurl(jd2);

            int durlCompare = Long.compare(minDurl1, minDurl2);
            if (durlCompare != 0) {
                return durlCompare;
            }

            // 如果最小 durl 也相等，则比较 picName（升序，字符串自然顺序）
            String picName1 = jd1.getPicName();
            String picName2 = jd2.getPicName();

            // 如果项目中可能出现 null，需要额外防范
            if (picName1 == null && picName2 == null) {
                return 0;
            } else if (picName1 == null) {
                return 1; // 把 null 排后面，或根据需要自行定义
            } else if (picName2 == null) {
                return -1;
            }

            // 最后再按字符串 compareTo 升序比较
            return picName1.compareTo(picName2);


        }
    }

    public JumpDetail getBaseOne(String buildId, String picName, String platform) {
        // 使用 lastIndexOf 和 substring 去掉 picName 尾部的 "_108"
        String basePicName = picName.substring(0, picName.lastIndexOf("_"));

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("build_id", buildId);
        queryWrapper.eq("pic_name", basePicName);
        queryWrapper.eq("platform", platform);
        queryWrapper.isNotNull("base_pic");
        queryWrapper.last(" limit 1");
        JumpDetail jumpDetail = jumpDetailMapper.selectOne(queryWrapper);
        if (jumpDetail == null) {
            jumpDetail = new JumpDetail();
            jumpDetail.setBaseResolution("");
            jumpDetail.setBasePic("");
        }
        return jumpDetail;
    }

}
