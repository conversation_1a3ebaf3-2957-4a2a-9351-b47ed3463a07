package com.sankuai.mdp.compass.jump.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.yetus.audience.InterfaceAudience;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("autotest_jump_build")
public class JumpJob implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer jenkinsId;

    private String platform;

    private String type;

    private String baseReportId;

    private Integer baseStatus;

    private String baseApp;

    private String testReportId;

    private Integer testStatus;

    private String testApp;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String baseFinishId;

    private String testFinishId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date baseStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date testStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date baseFinishTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date testFinishTime;

    private String resultText; // 新加结果字段

    private String testMockid; // 新加 mockid 字段

    /**
     * 新增字段：baseAppUrl 和 testAppUrl
     */
    private String baseAppUrl;

    private String testAppUrl;

}
