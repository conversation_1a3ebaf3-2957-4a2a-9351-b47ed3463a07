package com.sankuai.mdp.compass.jump.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("autotest_jump_pic_diff")
public class JumpDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer buildId;

    private String bg;

    private String bu;

    private String platform;

    private String picName;

    private String caseName;

    private String basePic;

    private String baseResolution;

    private String testResolution;

    private String testPic;

    private Double similarity;

    private Boolean isPopUp;

    private String diffUrl;

    private String conanTask;

    private String jumpLink; //新增截屏页面跳链

    //OCR识别的图片错误信息
    private String errorInfo;

    /**
     * 0：基准
     * 1：测试
     */
    @TableField(exist = false)
    private Integer type;

    @TableField(exist = false)
    private Object testPicObject;

    @TableField(exist = false)
    private Object basePicObject;
}
