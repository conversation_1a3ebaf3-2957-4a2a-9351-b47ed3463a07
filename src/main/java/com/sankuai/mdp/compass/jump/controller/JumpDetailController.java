package com.sankuai.mdp.compass.jump.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.jump.entity.JumpDetail;
import com.sankuai.mdp.compass.jump.service.JumpDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/detail")
public class JumpDetailController extends BaseController {
    @Autowired
    JumpDetailService jumpDetailService;


    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, JumpDetail jumpDetail,@RequestParam("id") String processId)  {
        IPage<JumpDetail> JumpDetaulIPage = this.jumpDetailService.list(request, jumpDetail,processId);
        if (JumpDetaulIPage != null) {
            return getDataTable(JumpDetaulIPage);
        } else {
            return null;
        }
    }

}
