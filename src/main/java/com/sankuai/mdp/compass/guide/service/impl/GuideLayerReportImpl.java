package com.sankuai.mdp.compass.guide.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.guide.entity.GuideLayerReport;
import com.sankuai.mdp.compass.guide.mapper.GuideLayerReportMapper;
import com.sankuai.mdp.compass.guide.service.GuideLayerJobService;
import com.sankuai.mdp.compass.guide.service.GuideLayerReportService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GuideLayerReportImpl extends ServiceImpl<GuideLayerReportMapper,GuideLayerReport> implements GuideLayerReportService {
    @Autowired
    GuideLayerJobService guideLayerJobService;

    @Override
    public IPage<GuideLayerReport> list(QueryRequest queryRequest, GuideLayerReport guideLayerReport, String type) {
        try {
            LambdaQueryWrapper<GuideLayerReport> queryWrapper = new LambdaQueryWrapper<>();

            Integer jobId = guideLayerReport.getJobId();
            if (guideLayerReport.getPicType() != null) {
                queryWrapper.eq(GuideLayerReport::getPicType, type);
            }
            if (jobId == -1) {
                GuideLayerJob guideLayerJob = new GuideLayerJob();
                guideLayerJob.setTemplateName(guideLayerReport.getTemplateName());
                jobId = guideLayerJobService.lastestJobId(guideLayerJob);
            }
            queryWrapper.eq(GuideLayerReport::getJobId, jobId);

            Page<GuideLayerReport> page = new Page<>(queryRequest.getPageNum(), 100);

            IPage<GuideLayerReport> iPage = this.page(page, queryWrapper);
            IPage<GuideLayerReport> customPage = null;
            long total = iPage.getTotal();
            if ("show".equals(type)) {
                customPage = showDetailList(iPage,total);
            } else if ("operation".equals(type)) {
                customPage = operationDetailList(iPage,total);
            } else if ("mge".equals(type)) {
                customPage = mgeDetailList(iPage);
            }
            return customPage;

        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    private Page<GuideLayerReport> mgeDetailList(IPage<GuideLayerReport> page) {
        Page<GuideLayerReport> resultPage = new Page<>();
        List<GuideLayerReport> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        for(int i =0 ;i < page.getRecords().size();i++) {
            GuideLayerReport guideLayerReport = page.getRecords().get(i);

            String picType = guideLayerReport.getPicType();
            String platform = guideLayerReport.getPlatform();
            String beforePic = guideLayerReport.getBeforeClickPic();
            String taskId = guideLayerReport.getTaskId();
            String uuid = guideLayerReport.getUuid();

            if ("show".equals(picType)) {
                String mv = guideLayerReport.getMv();
                if (mv != null && mv != "") {
                    JSONObject json = new JSONObject(mv);
                    String bid = json.getJSONObject("data").getString("val_bid");
                    if (!jsonObject.has(bid)) {
                        jsonObject.put(bid, new JSONObject());
                        jsonObject.getJSONObject(bid).put("nm", "MV");

                    }
                    if (!jsonObject.getJSONObject(bid).has(platform)) {
                        jsonObject.getJSONObject(bid).put(platform, new JSONObject());
                        jsonObject.getJSONObject(bid).getJSONObject(platform).put("data", mv);
                        jsonObject.getJSONObject(bid).getJSONObject(platform).put("pic", beforePic);
                    } else {
                        continue;
                    }
                }

            }
            if ("operation".equals(picType)) {
                String clickPic = guideLayerReport.getClickPic();
                String mc = guideLayerReport.getMc();
                if (mc != null && mc != "") {
                    JSONObject json = new JSONObject(mc);
                    String bid = json.getJSONObject("data").getString("val_bid");
                    String component = json.getString("operationComponent");
                    if (component.contains("_")) {
                        component = component.split("_")[0];
                    }
                    if (!jsonObject.has(bid)) {
                        jsonObject.put(bid, new JSONObject());
                        jsonObject.getJSONObject(bid).put("nm", "MC");

                    }
                    if (!jsonObject.getJSONObject(bid).has(component)) {
                        jsonObject.getJSONObject(bid).put(component, new JSONObject());
                    }
                    if (!jsonObject.getJSONObject(bid).getJSONObject(component).has(platform)) {
                        jsonObject.getJSONObject(bid).getJSONObject(component).put(platform, new JSONObject());
                        jsonObject.getJSONObject(bid).getJSONObject(component).getJSONObject(platform).put("data", mc);
                        jsonObject.getJSONObject(bid).getJSONObject(component).getJSONObject(platform).put("pic", clickPic);

                    } else {
                        continue;
                    }
                }
            }
        }

        Iterator iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String key = iterator.next().toString();
            JSONObject object = jsonObject.getJSONObject(key);
            String nm = object.getString("nm");
            if (nm.equals("MV")) {
                if (object != null) {
                    GuideLayerReport itemDetail = new GuideLayerReport();
                    itemDetail.setNm("MV");
                    itemDetail.setBid(key);

                    if (object.has("Android")) {
                        String data = object.getJSONObject("Android").getString("data");
                        itemDetail.setAndroidPic(object.getJSONObject("Android").getString("pic"));
                        itemDetail.setAndroidContent(data);
                        if (data == null || data.length() == 0) {
                            itemDetail.setAndroidResult("未上报");
                        }
                    }
                    if (object.has("iOS")) {
                        String data = object.getJSONObject("iOS").getString("data");
                        itemDetail.setIosPic(object.getJSONObject("iOS").getString("pic"));
                        itemDetail.setIosContent(data);
                        if (data == null || data.length() == 0) {
                            itemDetail.setIosResult("未上报");
                        }
                    }
                    list.add(itemDetail);
                }
            } else if (nm.equals("MC")) {
                if (object != null) {
                    Iterator componentIterator = object.keys();
                    while (componentIterator.hasNext()) {
                        String component = componentIterator.next().toString();
                        if (component.equals("nm")) {
                            continue;
                        }
                        GuideLayerReport itemDetail = new GuideLayerReport();
                        itemDetail.setNm("MC");
                        itemDetail.setBid(key);
                        itemDetail.setComponent(component);
                        JSONObject componentObject = object.getJSONObject(component);
                        if (componentObject.has("Android")) {
                            String data = componentObject.getJSONObject("Android").getString("data");
                            itemDetail.setAndroidPic(componentObject.getJSONObject("Android").getString("pic"));
                            itemDetail.setAndroidContent(data);
                            if (data == null || data.length() == 0) {
                                itemDetail.setAndroidResult("未上报");
                            }
                        }
                        if (componentObject.has("iOS")) {
                            String data = componentObject.getJSONObject("iOS").getString("data");
                            itemDetail.setIosPic(componentObject.getJSONObject("iOS").getString("pic"));
                            itemDetail.setIosContent(data);
                            if (data == null || data.length() == 0) {
                                itemDetail.setIosResult("未上报");
                            }
                        }
                        list.add(itemDetail);
                    }
                }
            }
        }
        resultPage.setRecords(list);
        resultPage.setTotal((long)list.size());
        return resultPage;
    }

    private Page<GuideLayerReport> operationDetailList(IPage<GuideLayerReport> page,long total) {
        Page<GuideLayerReport> resultPage = new Page<>();
        List<GuideLayerReport> list = new ArrayList<>();
        for(int i =0 ;i < page.getRecords().size();i++){
            GuideLayerReport guideLayerReport = page.getRecords().get(i);
            GuideLayerReport itemDetail = new GuideLayerReport();

            String picType = guideLayerReport.getPicType();
            String taskId = guideLayerReport.getTaskId();
            String uuid = guideLayerReport.getUuid();

            if ("show".equals(picType)) {
                continue;
            }

            String platform = guideLayerReport.getPlatform();
            try {
                itemDetail.setTemplateName(guideLayerReport.getTemplateName());
                itemDetail.setPlatform(platform);
                itemDetail.setDeviceModel(guideLayerReport.getDeviceModel());
                itemDetail.setDeviceVersion(guideLayerReport.getDeviceVersion());
                itemDetail.setComponent(guideLayerReport.getComponent());
                itemDetail.setBeforeClickPic(guideLayerReport.getBeforeClickPic());
                itemDetail.setClickPic(guideLayerReport.getClickPic());
                itemDetail.setAfterClickPic(guideLayerReport.getAfterClickPic());
                itemDetail.setDeviceResolution1(Integer.valueOf(guideLayerReport.getDeviceResolution().split("x")[0]));
                itemDetail.setTaskId(guideLayerReport.getTaskId());
                list.add(itemDetail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Comparator<GuideLayerReport> byPlatform = Comparator.comparing(GuideLayerReport::getPlatform);
        Comparator<GuideLayerReport> bydeviceResolution1 = Comparator.comparing(GuideLayerReport::getDeviceResolution1).reversed();
        Comparator<GuideLayerReport> finalComparator = byPlatform.thenComparing(bydeviceResolution1);
        list = list.stream().sorted(finalComparator).collect(Collectors.toList());
        resultPage.setRecords(list);
        resultPage.setTotal(total);
        return resultPage;
    }

    private Page<GuideLayerReport> showDetailList(IPage<GuideLayerReport> page,long total) {
        Page<GuideLayerReport> resultPage = new Page<>();
        List<GuideLayerReport> list = new ArrayList<>();
        for(int i =0 ;i < page.getRecords().size();i++){
            GuideLayerReport guideLayerReport = page.getRecords().get(i);

            GuideLayerReport itemDetail = new GuideLayerReport();

            String picType = guideLayerReport.getPicType();

            if (!"show".equals(picType)) {
                continue;
            }

            String platform = page.getRecords().get(i).getPlatform();
            try {
                itemDetail.setTemplateName(guideLayerReport.getTemplateName());
                itemDetail.setPlatform(platform);
                itemDetail.setDeviceModel(guideLayerReport.getDeviceModel());
                itemDetail.setDeviceVersion(guideLayerReport.getDeviceVersion());
                itemDetail.setDeviceResolution(guideLayerReport.getDeviceResolution());
                itemDetail.setBeforeClickPic(guideLayerReport.getBeforeClickPic());
                itemDetail.setJobId(guideLayerReport.getJobId());
                itemDetail.setDeviceResolution1(Integer.valueOf(guideLayerReport.getDeviceResolution().split("x")[0]));
                itemDetail.setTaskId(guideLayerReport.getTaskId());
                itemDetail.setUuid(guideLayerReport.getUuid());
                list.add(itemDetail);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Comparator<GuideLayerReport> byPlatform = Comparator.comparing(GuideLayerReport::getPlatform);
        Comparator<GuideLayerReport> bydeviceResolution1 = Comparator.comparing(GuideLayerReport::getDeviceResolution1).reversed();
        Comparator<GuideLayerReport> finalComparator = byPlatform.thenComparing(bydeviceResolution1);
        list = list.stream().sorted(finalComparator).collect(Collectors.toList());
        resultPage.setRecords(list);
        resultPage.setTotal(total);
        return resultPage;
    }

}
