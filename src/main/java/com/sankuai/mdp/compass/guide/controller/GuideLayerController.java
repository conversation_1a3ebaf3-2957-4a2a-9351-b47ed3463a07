package com.sankuai.mdp.compass.guide.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.guide.service.GuideLayerJobService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/guide/test")
public class GuideLayerController extends BaseController {
    @Autowired
    GuideLayerJobService guideLayerJobService;

    /**
     * 触发新测试
     * @param guideLayerJob
     * @return
     */
    @PostMapping("/new")
    public Resp newTest(GuideLayerJob guideLayerJob) {
        return guideLayerJobService.newTest(guideLayerJob);
    }

    /**
     * 取消job
     * @param guideLayerJob
     * @return
     */
    @PostMapping("/cancel")
    public Resp cancel(GuideLayerJob guideLayerJob) {
        return guideLayerJobService.cancel(guideLayerJob);
    }

    /**
     * 云测回调
     * @param conanBody
     * @return
     * @throws Exception
     */
    @PostMapping("/result")
    public Resp result(@RequestBody JSONObject conanBody) throws Exception {
        return guideLayerJobService.result(conanBody);
    }

    /**
     * 更新云测报告ID
     * @param guideLayerJob
     * @return
     */
    @PostMapping("/updataReportId")
    public Resp updataReportId(GuideLayerJob guideLayerJob) {
        return guideLayerJobService.updateReportId(guideLayerJob);

    }

    /**
     * 获取指定templateName的最新jobId
     * @param guideLayerJob
     * @return
     */
    @GetMapping("/getLastestJobId")
    public Integer getLastestJobId(GuideLayerJob guideLayerJob) {
        return guideLayerJobService.lastestJobId(guideLayerJob);
    }

    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, GuideLayerJob guideLayerJob)  {
        IPage<GuideLayerJob> guideLayerJobIPage = this.guideLayerJobService.list(request, guideLayerJob);

        if (guideLayerJobIPage != null) {
            return getDataTable(guideLayerJobIPage);
        } else {
            return null;
        }
    }

    /**
     * templateName聚合报告列表
     * @param request
     * @param guideLayerJob
     * @return
     */
    @GetMapping("/list/aggregate")
    public Map<String, Object> aggList(QueryRequest request, GuideLayerJob guideLayerJob)  {
        IPage<GuideLayerJob> guideLayerJobIPage = this.guideLayerJobService.aggList(request, guideLayerJob);
        if (guideLayerJobIPage != null) {
            return getDataTable(guideLayerJobIPage);
        } else {
            return null;
        }
    }

    /**
     * 更新人工确认结果
     * @param guideLayerJob
     * @return
     */
    @PutMapping("/updateCheckStatus")
    public String updateCheckStatus(GuideLayerJob guideLayerJob){
        return guideLayerJobService.updateCheckStatus(guideLayerJob);
    }


    /**
     * 更新任务状态
     * @param guideLayerJob
     * @return
     */
    @PutMapping("/updateJobStatus")
    public Resp updateJobStatus(GuideLayerJob guideLayerJob) { return guideLayerJobService.updateStatusById(guideLayerJob); }

}

