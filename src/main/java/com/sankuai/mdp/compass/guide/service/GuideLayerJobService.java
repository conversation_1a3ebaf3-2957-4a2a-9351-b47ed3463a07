package com.sankuai.mdp.compass.guide.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import net.minidev.json.JSONObject;

public interface GuideLayerJobService extends IService<GuideLayerJob> {
    Integer lastestJobId(GuideLayerJob guideLayerJob);
    IPage<GuideLayerJob> list(QueryRequest request, GuideLayerJob guideLayerJob);
    IPage<GuideLayerJob> aggList(QueryRequest request, GuideLayerJob guideLayerJob);
    Resp newTest(GuideLayerJob guideLayerJob);
    Resp cancel(GuideLayerJob guideLayerJob);
    Resp result(JSONObject jsonObject) throws Exception;
    Resp updateReportId(GuideLayerJob guideLayerJob);
    Resp updateStatusById(GuideLayerJob guideLayerJob);
    String updateCheckStatus(GuideLayerJob guideLayerJob);

}
