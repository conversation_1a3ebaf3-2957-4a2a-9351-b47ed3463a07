package com.sankuai.mdp.compass.guide.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.guide.entity.GuideLayerReport;
import com.sankuai.mdp.compass.guide.mapper.GuideLayerReportMapper;
import com.sankuai.mdp.compass.guide.service.GuideLayerReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/guide/report/detail")
public class GuideLayerReportController extends BaseController {

    @Autowired
    GuideLayerReportMapper guideLayerReportMapper;

    @Autowired
    GuideLayerReportService guideLayerReportService;

    @GetMapping("/show")
    public Map<String, Object> getShowUIList(QueryRequest request, GuideLayerReport guideLayerReport) {

        IPage<GuideLayerReport> detailsJobIPage = this.guideLayerReportService.list(request, guideLayerReport ,"show");
        if (detailsJobIPage != null) {
            return getDataTable(detailsJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/operation")
    public Map<String, Object> getOperationList(QueryRequest request, GuideLayerReport guideLayerReport) {

        IPage<GuideLayerReport> jobDetailsJobIPage = this.guideLayerReportService.list(request, guideLayerReport ,"operation");
        if (jobDetailsJobIPage != null) {
            return getDataTable(jobDetailsJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/mge")
    public Map<String, Object> getMgeList(QueryRequest request, GuideLayerReport guideLayerReport) {

        IPage<GuideLayerReport> jobDetailsJobIPage = this.guideLayerReportService.list(request, guideLayerReport ,"mge");
        if (jobDetailsJobIPage != null) {
            return getDataTable(jobDetailsJobIPage);
        } else {
            return null;
        }
    }

}
