package com.sankuai.mdp.compass.guide.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("guide_layer_report")
public class GuideLayerReport {
    private static final long serialVersionUID = 1L;

    @TableId(value="id",type= IdType.AUTO)
    private Integer id;

    private String templateName;

    private Integer jobId;

    private String taskId;

    private String uuid;

    private String beforeClickPic;

    private String clickPic;

    private String afterClickPic;

    private String picType;

    private String mv;

    private String mc;

    private String component;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String deviceModel;

    private String deviceVersion;

    private String deviceResolution;

    private String platform;

    @TableField(exist = false)
    private String bid;

    @TableField(exist = false)
    private String nm;

    @TableField(exist = false)
    private String androidContent;

    @TableField(exist = false)
    private String iosContent;

    @TableField(exist = false)
    private String androidResult;

    @TableField(exist = false)
    private String iosResult;

    @TableField(exist = false)
    private String androidPic;

    @TableField(exist = false)
    private String iosPic;

    @TableField(exist = false)
    private Integer deviceResolution1;

}
