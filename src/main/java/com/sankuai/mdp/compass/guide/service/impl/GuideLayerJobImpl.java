package com.sankuai.mdp.compass.guide.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.entity.CompatilityJob;
import com.sankuai.mdp.compass.guide.entity.GuideLayerJob;
import com.sankuai.mdp.compass.guide.entity.GuideLayerReport;
import com.sankuai.mdp.compass.guide.mapper.GuideLayerJobMapper;
import com.sankuai.mdp.compass.guide.mapper.GuideLayerReportMapper;
import com.sankuai.mdp.compass.guide.service.GuideLayerJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.mdp.compass.conan.service.ConanJobService;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class GuideLayerJobImpl extends ServiceImpl<GuideLayerJobMapper,GuideLayerJob> implements GuideLayerJobService {

    @Autowired
    GuideLayerJobMapper guideLayerJobMapper;

    @Autowired
    GuideLayerReportMapper guideLayerReportMapper;

    @Autowired
    ConanJobService conanJobService;

    ComConstant comConstant = new ComConstant();

    ConanUtil conanUtil = new ConanUtil();

    FileUtil fileUtil = new FileUtil();

    VenusUtil venusUtil = new VenusUtil();

    DxUtil dxUtil = new DxUtil();

    JenkinsUtil jenkinsUtil = new JenkinsUtil();

    public Integer lastestJobId(GuideLayerJob guideLayerJob) {
        Integer jobId = -1;
        try {
            LambdaQueryWrapper<GuideLayerJob> queryWrapper = new LambdaQueryWrapper<>();

            queryWrapper.eq(GuideLayerJob::getTemplateName,guideLayerJob.getTemplateName());
            queryWrapper.last("order by id desc");
            Page<GuideLayerJob> page = new Page<>();
            IPage<GuideLayerJob> iPage = this.page(page, queryWrapper);
            if (iPage.getRecords().size() > 0) {
                jobId = iPage.getRecords().get(0).getId();
            }
        } catch (Exception e) {
//            log.error("获取列表失败", e);
            return -1;
        } finally {
            return jobId;
        }
    }

    @Override
    public IPage<GuideLayerJob> list(QueryRequest request, GuideLayerJob guideLayerJob) {
        try {
            LambdaQueryWrapper<GuideLayerJob> queryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(guideLayerJob.getTemplateName())) {
                queryWrapper.eq(GuideLayerJob::getTemplateName, guideLayerJob.getTemplateName());
            }
            queryWrapper.isNotNull(GuideLayerJob::getTemplateName);
            queryWrapper.last("and template_name != '' order by start_time DESC");
            Page<GuideLayerJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<GuideLayerJob> iPage = this.page(page, queryWrapper);
            return iPage;

        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public IPage<GuideLayerJob> aggList(QueryRequest request, GuideLayerJob guideLayerJob) {
        try {
            IPage<GuideLayerJob> iPage = list(request,guideLayerJob);
            Page<GuideLayerJob> result = new Page<>();
            List<GuideLayerJob> list = new ArrayList<>();
            Map<String, Integer> map = new HashMap<>();
            for (int i = 0; i < iPage.getRecords().size(); i++) {

                String templateName = iPage.getRecords().get(i).getTemplateName();
                Integer status = iPage.getRecords().get(i).getStatus();
                if (!map.containsKey(templateName)) {
                    map.put(templateName, status);
                } else {
                    if (null != status && status == 0) {
                        map.replace(templateName, status);
                    }
                }
            }
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                GuideLayerJob jobDetail = new GuideLayerJob();
                try {
                    jobDetail.setTemplateName(entry.getKey());
                    jobDetail.setStatus(entry.getValue());
                    list.add(jobDetail);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            result.setRecords(list);

            return result;

        } catch (Exception e) {
//            log.error("获取列表失败", e);
            return null;
        }
    }


    @Override
    public Resp newTest(GuideLayerJob guideLayerJob) {
        Resp resp = new Resp();
        JSONObject jsonObject = new JSONObject();
        MbcUtil mbcUtil = new MbcUtil();
        String operator = guideLayerJob.getOperator();
        guideLayerJob.setCreator(operator);
        String templateName = guideLayerJob.getTemplateName();
        String mock = guideLayerJob.getMock(); //MBC下发非融合接口mock数据
        String schema = guideLayerJob.getImeituanUrl();
        String api="/mop/entry/guideLayerAndIndexWindowEntry";
        String business = guideLayerJob.getBusiness();
        Map<String,Map<String,Object>> map = new HashMap<>();
        String path = "resourcesMap.guideInfoArea[0].materialMap";
        String guideType = guideLayerJob.getGuideType(); //获取是否为非全屏，数据库新建字段
        if (guideType==null) guideType="full";
        guideLayerJob.setGuideType(guideType);
        String mockNew = MockUtil.get(654686);;//融合接口mock基准数据

        /*获取非融合接口mock中锚点和浮层数据*/
        JsonArray mockNewArray = new JsonParser().parse("["+mockNew+"]").getAsJsonArray();
        JsonObject mockNewObject = mockNewArray.get(0).getAsJsonObject();
        JsonArray mockArray1 = new JsonParser().parse("["+mock+"]").getAsJsonArray();
        JsonObject mockObject = mockArray1.get(0).getAsJsonObject();
        JsonObject resourcesMap = mockObject.get("resourcesMap").getAsJsonObject();
        JsonArray guideInfoAreaArray = resourcesMap.getAsJsonArray("guideInfoArea");
        JsonArray anchorInfoAreaArray = resourcesMap.getAsJsonArray("anchorInfoArea");
        /* 将非融合接口mock数据中的anchorInfoArea和guideInfoArea添加到融合接口mockNew
         */
        mockNewObject.getAsJsonObject("resourcesMap").add("anchorInfoArea",anchorInfoAreaArray);
        /*判断MBC配置浮层是否为非全屏
         */
        if (guideType.equals("notfull")){
            mockNewObject.getAsJsonObject("resourcesMap").add("guideInfoNotFullArea",guideInfoAreaArray);
            mock=mockNewObject.toString();
            path= "resourcesMap.guideInfoNotFullArea[0].materialMap";
        }
        else {
            mockNewObject.getAsJsonObject("resourcesMap").add("guideInfoArea",guideInfoAreaArray);
            mock=mockNewObject.toString();
        }
        JSONObject value = new JSONObject();
        value.put("templateName",templateName);
        Map<String,Object> param = new HashMap<>();
        param.put("templateName",templateName);
        param.put("extra",value);
        param.put("pageKey",business);
        map.put(path,param);
        JsonObject data = mbcUtil.handleMockData(mock,map);
        JSONArray result = new JSONArray();

        JsonObject oneMockResp = MockUtil.create(api, data); //todo: offline create mock response
        if (null != oneMockResp) {
            JSONObject object = new JSONObject();
            String mockId = oneMockResp.getAsJsonObject("data").get("mockId").getAsString();
            JSONObject mockIdItem = new JSONObject();
            mockIdItem.put("mockId", mockId);
            JSONArray mockArray = new JSONArray();
            mockArray.put(mockIdItem);
            object.put("pageKey",business);
            object.put("schema",schema);
            object.put("templateName",templateName);
            object.put("mock",mockArray);
            result.put(object);
            guideLayerJob.setStatus(-1);
            /*新增mockId字段*/
            guideLayerJob.setMockId(mockId);//设置mockId
            guideLayerJob.setMock(result.toString());
            guideLayerJobMapper.insert(guideLayerJob);
            if (EnvUtil.isOnline()) {
                jenkinsUtil.newGuideLayerTestJob(guideLayerJob);

                //通知
                JsonArray users = new JsonArray();
                users.add(operator);
                users.add("liukun28");
                users.add("lizhen39");
                users.add("baohan07");
                users.add("xiaoxiaoyue");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                try {
                    for (JsonElement user : users) {
                        String u = user.getAsString();
                        dxUtil.sendToPersionByCompass("※ 引导浮层模版自动化测试提醒 ※：\n 【任务ID】：" + guideLayerJob.getId() + "\n" +
                                "【模版名称】：" + templateName + "\n" +
                                "【测试进度】： 开始测试\n" +
                                "【开始时间】：" + sdf.format(new Date()) + "\n" +
                                "如有问题请联系lizhen39", u);
                    }
                } catch (Exception e) {
//                    log.error("affirm err>>"+e.getMessage());
                }
            }
            jsonObject.put("report","");
            resp.setCode(200);
            resp.setData(result);
            resp.setMsg("success");
            return resp;
        }
        resp.setCode(-1);
        resp.setData(result);
        resp.setMsg("fail");
        return resp;
    }

    @Override
    public Resp cancel(GuideLayerJob guideLayerJob) {
        Integer id = guideLayerJob.getId();
        GuideLayerJob guideLayerJob1 = guideLayerJobMapper.selectById(id);
        guideLayerJob1.setCancelBy(guideLayerJob.getOperator());
        guideLayerJob1.setStatus(2);
        String[] conanIds = guideLayerJob1.getReportId().split("_");
        ConanUtil conanUtil = new ConanUtil();
        for (int i = 0; i < conanIds.length; i++) {
//            log.info("取消云测job："+conanIds[i]);
            conanUtil.cancelJob(conanIds[i]);
        }
        Date date = new Date();
        guideLayerJob1.setCancelTime(date);
        guideLayerJobMapper.updateById(guideLayerJob1);
        return Resp.success();
    }

    @Override
    public Resp result(JSONObject conanBody) throws Exception {
        Resp resp = new Resp();
        try {
//            log.info(conanBody.toString());
            JsonParser jsonParser = new JsonParser();

            //解析云测回调参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject timeData = jsonParser.parse(conanBody.get("timedata").toString()).getAsJsonObject();
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            String conanJobId = jobData.get("id").getAsString();
//            log.info("云测reportId："+conanJobId);

            //根据云测reportId找到对应的动态布局测试job
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.like("report_id", conanJobId);
            GuideLayerJob guideLayerJob = guideLayerJobMapper.selectOne(queryWrapper);
//            log.info("对应job：" + guideLayerJob);
            String business = guideLayerJob.getBusiness();
            String templateName = guideLayerJob.getTemplateName();
            Integer jobId = guideLayerJob.getId();
            String reportId = guideLayerJob.getReportId();
            String creator = guideLayerJob.getCreator();
            String startTime = sdf.format(guideLayerJob.getStartTime());

            //文件下载完成后存放的目录
            String dirPath = comConstant.OUT_PUT + jobId;
            conanJobService.collectResult(conanBody);

            try {
                JsonObject jobInfo = conanUtil.getJobInfo(conanJobId);
                if (jobInfo != null) {
                    JsonArray taskArray = jobInfo.getAsJsonArray("taskList");
                    String appVersion = jobInfo.get("appVersion").getAsString();
                    String platform = jobInfo.get("platform").getAsString();

                    for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
                        JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
                        String taskId = task.get("id").getAsString();

                        QueryWrapper<GuideLayerReport> detailQueryWrapper = new QueryWrapper<GuideLayerReport>().eq("task_id", taskId);
                        int detailUpdateFlag = guideLayerReportMapper.selectCount(detailQueryWrapper);
                        if (detailUpdateFlag > 0) {
//                            log.info("id为 " + taskId + " 的任务已校验下载入库完成过了，跳过");
                            continue;
                        }
                        //end here
                        /**
                         * 1.获取文件下载链接
                         */
                        String deviceModel = task.get("deviceModel").getAsString();
                        String deviceVersion = task.get("deviceVersion").getAsString();
                        String deviceResolution = task.get("resolution").getAsString();
                        Double totalTime = task.get("totalTime").getAsDouble();
                        String downloadURL = conanUtil.getDownloadURL(taskId);

                        if (null != downloadURL) {
                            /**
                             * 2.下载解压文件
                             */
                            fileUtil.downloadAndReadFile(downloadURL, dirPath, taskId);

                            /**
                             * 3.读埋点
                             */
                            JsonArray mvJson = new JsonArray();
                            JsonObject mcJson = new JsonObject();
                            try {
                                JsonObject result = new JsonObject();
                                String mge = fileUtil.read(dirPath + "/" + taskId + "/mge/data.log");
                                if (null != mge && "" != null) {
                                    JsonObject jsonObject = jsonParser.parse(mge).getAsJsonObject();
                                    JsonArray mgeJson = jsonObject.getAsJsonArray("results");
                                    result.add("MV", new JsonArray());
                                    result.add("MC", new JsonObject());
                                    for (int itemIndex = 0; itemIndex < mgeJson.size(); itemIndex++) {
                                        JsonObject json = mgeJson.get(itemIndex).getAsJsonObject();
                                        String nm = json.get("nm").getAsString();
                                        if (nm.equals("MV")) {
                                            result.getAsJsonArray("MV").add(json);
                                        }
                                        if (nm.equals("MC")) {
                                            String component = json.get("operationComponent").getAsString();
                                            result.getAsJsonObject("MC").add(component, json);
                                        }
                                    }
                                    mvJson = result.getAsJsonArray("MV");
                                    mcJson = result.getAsJsonObject("MC");
                                }
                            } catch (Exception e) {
//                                log.error(e.getMessage());
                            }
                            /**读uuid
                             *
                             */
                            String UUID = fileUtil.read(dirPath + "/" + taskId + "/uuid/uuid.txt");


                            /**
                             * 4.获取图片文件路径
                             *   将本地图片上传到服务器
                             *   将图片链接存入数据库
                             */
                            ArrayList<String> fileList = new ArrayList<>();
                            String picPath = dirPath + "/" + taskId + "/link/";
                            fileUtil.getFileList(picPath, "", fileList);

                            JsonObject picList = new JsonObject();
                            for (int i = 0; i < fileList.size(); i++) {
                                String fileName = fileList.get(i);
                                String link = fileUtil.read(picPath + fileName);
                                if (link != null && link != "") {
                                    if (fileName.contains("show")) {
                                        picList.addProperty("show", link);
                                    } else if (fileName.contains("operation")) {
                                        String[] list = fileName.split("\\.txt");
                                        String path = list[0];
                                        String[] pathList = path.split("/");
                                        Integer len = pathList.length;
                                        String componentName = pathList[len - 2];

                                        if (!picList.has(componentName)) {
                                            picList.add(componentName, new JsonObject());
                                        }

                                        String picName = pathList[len - 1];
                                        if (picName.equals("click")) {
                                            picList.getAsJsonObject(componentName).addProperty("click", link);
                                        }
                                        if (picName.equals("afterClick")) {
                                            picList.getAsJsonObject(componentName).addProperty("afterClick", link);
                                        }
                                        if (picName.equals("beforeClick")) {
                                            picList.getAsJsonObject(componentName).addProperty("beforeClick", link);
                                        }
                                    }
                                }
                            }

                            Iterator<String> iterator = picList.keySet().iterator();
                            while (iterator.hasNext()) {
                                String key = iterator.next();
                                Object value = picList.get(key);

                                GuideLayerReport guideLayerReport = new GuideLayerReport();
                                guideLayerReport.setJobId(jobId);
                                guideLayerReport.setTaskId(taskId);
                                guideLayerReport.setDeviceModel(deviceModel);
                                guideLayerReport.setDeviceVersion(deviceVersion);
                                guideLayerReport.setDeviceResolution(deviceResolution);
                                guideLayerReport.setPlatform(platform);
                                guideLayerReport.setUpdateTime(new Date());
                                guideLayerReport.setTemplateName(templateName);
                                guideLayerReport.setUuid(UUID);

                                if (key.contains("show")) {
                                    guideLayerReport.setPicType("show");
                                    guideLayerReport.setBeforeClickPic(value.toString().replace("\"", ""));

                                    /**
                                     * 5.埋点
                                     */
                                    try {
                                        JsonObject mv = mvJson.get(0).getAsJsonObject();
                                        guideLayerReport.setMv(mv.toString());
                                    } catch (Exception e) {
//                                        log.error(e.getMessage());
                                    }
                                }

                                if (key.contains("component")) {
                                    JsonObject jsonObject = jsonParser.parse(value.toString()).getAsJsonObject();
                                    guideLayerReport.setPicType("operation");
                                    guideLayerReport.setComponent(key);
                                    if (jsonObject.has("click")) {
                                        guideLayerReport.setClickPic(jsonObject.get("click").getAsString());
                                    }
                                    if (jsonObject.has("afterClick")) {
                                        guideLayerReport.setAfterClickPic(jsonObject.get("afterClick").getAsString());
                                    }
                                    if (jsonObject.has("beforeClick")) {
                                        guideLayerReport.setBeforeClickPic(jsonObject.get("beforeClick").getAsString());
                                    }

                                    /**
                                     * 5.埋点
                                     */
                                    try {
                                        if (mcJson != null) {
                                            Iterator it = mcJson.keySet().iterator();
                                            while (it.hasNext()) {
                                                String componentName = it.next().toString();
                                                String[] arr = componentName.split("_");
                                                if (arr.length > 0 && arr[0].equals(key)) {
                                                    JsonObject mc = mcJson.getAsJsonObject(componentName);
                                                    guideLayerReport.setMc(mc.toString());
                                                    break;
                                                }
                                            }
                                        }
                                    } catch (Exception e) {
//                                        log.error(e.getMessage());
                                    }
                                }


                                guideLayerReportMapper.insert(guideLayerReport);
                            }
                        }
                    }
                }
            } catch (Exception e) {
//                log.error("get output err", e);
            } finally {
                synchronized (this){
                    QueryWrapper queryWrapper2 = new QueryWrapper();
                    queryWrapper2.like("report_id", reportId);
                    GuideLayerJob finishedJob = guideLayerJobMapper.selectOne(queryWrapper2);
                    String finishedId = finishedJob.getFinishedId();
                    String finishFlag = "0";
                    if (finishedId == null) {
                        finishedId = "";
                    }
                    if (finishedId == "") {
                        finishedId = conanJobId;
                    } else if (!finishedId.contains(conanJobId)) {
                        finishedId = finishedId + "_" + conanJobId;
                    }
                    if (finishedId.length() == reportId.length()) {
                        finishFlag = "1";
                        guideLayerJob.setStatus(1);
                        guideLayerJob.setFinishTime(new Date());
                    }
                    guideLayerJob.setFinishedId(finishedId);
                    guideLayerJobMapper.updateById(guideLayerJob);
                    if (finishFlag.equals("1")) {
                        fileUtil.delFolder(dirPath);
                    }
                    String domain = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/magicpagetest/reportDetails?";
                    String reportUrl = domain+"templateName="+templateName+"&jobId=" + jobId;

                    JsonArray users = new JsonArray();
                    users.add(creator);
                    users.add("taoyiping");
                    users.add("lizhen39");
                    users.add("liukun28");
                    if (EnvUtil.isOnline()) {
                        if (finishFlag.equals("0")) {
                            int finishedCount = finishedId.split("_").length;
                            int leftCount = reportId.split("_").length - finishedId.split("_").length;
                            for (JsonElement user : users) {
                                String u = user.getAsString();
                                dxUtil.sendToPersionByCompass("※ 引导浮层模版自动化测试提醒 ※：\n 【任务ID】：" + guideLayerJob.getId() + "\n" +
                                        "【模版名称】：" + templateName + "\n" +
                                        "【测试进度】：⏳ " + "第" + finishedCount + "个设备完成，剩余" + leftCount + "个 \n" +
                                        "【开始时间】：" + startTime + "\n" +
                                        "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                        "如有问题请联系lizhen39", u);
                            }
                        } else if (finishFlag.equals("1")) {
                            for (JsonElement user : users) {
                                String u = user.getAsString();
                                dxUtil.sendToPersionByCompass("※ 引导浮层模版自动化测试提醒 ※：\n 【任务ID】：" + guideLayerJob.getId() + "\n" +
                                        "【模版名称】：" + templateName + "\n" +
                                        "【测试进度】：✅ 已完成\n" +
                                        "【开始时间】：" + startTime + "\n" +
                                        "【完成时间】：" + sdf.format(new Date()) + "\n" +
                                        "【查看报告】：[查看报告|" + reportUrl + "]\n" +
                                        "如有问题请联系lizhen39", u);
                            }
                        }
                    }
                    resp.setMsg("success");
                    resp.setData("reportId");
                    resp.setCode(200);
                    return resp;
                }
            }
        } catch (Exception e) {
//            log.info(e.toString());
            dxUtil.sendToPersionByCompass("引导浮层模版自动化测试提醒：\n 云测回调发生异常，请及时处理！！！\n 异常信息："+e.getMessage() , "lizhen39");
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
    }


    @Override
    public Resp updateReportId(GuideLayerJob guideLayerJob) {
        Integer id = guideLayerJob.getId();
        GuideLayerJob guideLayerJob1 = guideLayerJobMapper.selectById(id);
        guideLayerJob1.setReportId(guideLayerJob.getReportId());
        guideLayerJob1.setBuildUrl(guideLayerJob.getBuildUrl());
        guideLayerJobMapper.updateById(guideLayerJob);
        return Resp.success();
    }

    /**
     * 修改验收状态
     * @param guideLayerJob
     * @return
     */
    @Override
    public String updateCheckStatus(GuideLayerJob guideLayerJob) {
        Integer id = guideLayerJob.getId();
        Integer status = guideLayerJob.getCheckResult();
        GuideLayerJob guideLayerJob1 = guideLayerJobMapper.selectById(id);
        Integer templateId = guideLayerJob1.getId();
        guideLayerJob1.setCheckResult(status);
        guideLayerJob1.setCheckBy(guideLayerJob.getOperator());
        Date date = new Date();
        guideLayerJob1.setCheckTime(date);
        guideLayerJobMapper.updateById(guideLayerJob1);
        //回传状态 > MBC
        String result = MbcUtil.handleGuideLayerTestResult(templateId,status);
//        log.info(result);
        return result;
    }

    /**
     * 修改测试状态
     * @param guideLayerJob
     * @return
     */
    @Override
    public Resp updateStatusById(GuideLayerJob guideLayerJob) {
        Integer id = guideLayerJob.getId();
        GuideLayerJob guideLayerJob1 = guideLayerJobMapper.selectById(id);
        guideLayerJob1.setStatus(guideLayerJob.getStatus());
        guideLayerJobMapper.updateById(guideLayerJob1);
        return Resp.success();
    }
}
