package com.sankuai.mdp.compass.guide.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("guide_layer_job")
public class GuideLayerJob {
    private static final long serialVersionUID = 1L;

    @TableId(value="id",type= IdType.AUTO)
    private Integer id;

    private String buildUrl;
    /** 测试状态码说明
     * -1   排队
     * 0    测试中
     * 1    完成
     * 2    取消
     */
    private Integer status;

    private String business;

    private String templateName;

    private String templateId;

    private String templateUrl;

    private String imeituanUrl;

    private String api;

    private String config;

    private String mock;

    private String mockId;

    private String creator;

    private  String guideType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    private String cancelBy;

    private String checkBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 验收状态码说明
     * 0 不通过
     * 1 通过
     */
    private Integer checkResult;

    private String reportId;

    private String finishedId;

    @TableField(exist = false)
    private String operator;

}
