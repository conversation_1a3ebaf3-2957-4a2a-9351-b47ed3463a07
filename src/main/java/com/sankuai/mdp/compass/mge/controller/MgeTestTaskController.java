package com.sankuai.mdp.compass.mge.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.ErrorEnum;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.mge.entity.MgeTestTask;
import com.sankuai.mdp.compass.mge.service.MgeTestTaskService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2021-12-31
 */
@RestController
@RequestMapping("/compass/api/living/mgeTestTask")
public class MgeTestTaskController {

    private static final Logger logger = LoggerFactory.getLogger(MgeTestTaskController.class);

    @Autowired
    MgeTestTaskService mgeTestTaskService;

    @PostMapping("/add")
    @ResponseBody
    public Resp add(MgeTestTask entity){
        logger.info("MgeTestTaskController add {}");
        try {
            //entity.setCreateBy(JWTUtil.getUsername());
            mgeTestTaskService.createEntity(entity);
            return Resp.success(entity);
        }catch (Exception e){
            logger.error("MgeTestTaskController add: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/delete")
    @ResponseBody
    public Resp delete(String ids){
        logger.info("MgeTestTaskController delete {}");
        try {
            mgeTestTaskService.deleteEntity(ids.split(","));
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestTaskController delete: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/update")
    @ResponseBody
    public Resp update(MgeTestTask entity){
        logger.info("MgeTestTaskController update {}");
        try {
            //entity.setUpdateBy(JWTUtil.getUsername());
            mgeTestTaskService.updateEntity(entity);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestTaskController update: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

}
