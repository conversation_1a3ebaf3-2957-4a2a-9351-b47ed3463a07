package com.sankuai.mdp.compass.mge.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 *
 * <AUTHOR> 2021-12-31
 */
@Data
@TableName("mge_base_case")
public class MgeBaseCase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用例id主键
     */
    @TableId(value = "case_id", type = IdType.AUTO)
    private Long caseId;

    /**
     * 用例描述
     */
    private String description;

    /**
     * 业务
     */
    private String business;

    /**
     * 负责人
     */
    private String principle;

    /**
     * 是否登录,0 未登录，1 已登录, 默认是登录态
     */
    private Integer isLogin;

    /**
     * 是否校验val参数，0 不需要，1 需要校验
     */
    private Integer valCheck;

    /**
     * bid
     */
    private String bid;

    /**
     * cid
     */
    private String cid;

    /**
     * 埋点case枚举值：ios、android、all
     */
    private String os;

    /**
     * 类型mv/mc等
     */
    private String type;

    /**
     * 基准数据
     */
    private String caseDetail;

    /**
     * 广告埋点基准数据
     */
    private String caseAddDetail;

    /**
     * 页面描述
     */
    private String pageDescription;

    /**
     * 模块描述
     */
    private String moduleDescription;

    /**
     * 页面
     */
    private String pageKey;

    /**
     * 模块
     */
    private String moduleKey;

    /**
     * 模块索引
     */
    private String moduleIndex;

    /**
     * 触发时机
     */
    private String triggerTime;

    /**
     * 元素文案查找路径
     */
    private String path;

    /**
     * 触发条件
     */
    private String triggerCondition;

    /**
     * mock规则
     */
    private String mockId;

    /**
     * 实验策略
     */
    private String abKey;

    /**
     * horn
     */
    private String horn;

    /**
     * 阶段
     */
    private String stage;

    /**
     * case创建（更新）时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 有效/失效默认有效
     */
    private Integer caseStatus;

}
