package com.sankuai.mdp.compass.mge.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 *
 * <AUTHOR> 2021-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mge_test_job")
public class MgeTestJob implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id主键
     */
    @TableId(value = "job_id", type = IdType.AUTO)
    private Long jobId;

    /**
     * 任务触发人
     */
    private String createBy;

    /**
     * 任务开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 任务结束时
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 任务执行时长
     */
    private Integer jobTotalTime;

    /**
     * task任务id列表
     */
    private String mgeTaskTotalId;

    /**
     * task(云测)任务回调列表
     */
    private String mgeTaskFinishId;

    /**
     * 业务
     */
    private String business;

    /**
     * 端
     */
    private String os;

    /**
     * 阶段
     */
    private String stage;

    /**
     * 版本
     */
    private String version;

    /**
     * 测试包链接
     */
    private String appUrl;

    /**
     * 埋点任务状态：0是执行中，1是执行结束，-1执行失败。PS：更多场景后续再追加
     */
    private Integer jobStatus;


}
