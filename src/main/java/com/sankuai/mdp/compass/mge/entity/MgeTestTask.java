package com.sankuai.mdp.compass.mge.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *
 *
 * <AUTHOR> 2021-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mge_test_task")
public class MgeTestTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    private Long jobId;

    private String conanJobId;

    private String mgeCaseTotalId;

    private String mgeCaseFinishId;

    /**
     * 云测任务测试报告链接
     */
    private String conanReport;


    /**
     * 云测任务开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 云测任务结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 云测任务状态：0是执行中，1是执行结束，-1执行失败。PS：更多场景后续再追加
     */
    private Integer conanJobStatus;

    /**
    * case集合
    */
    private String mgeCaseArray;


}
