package com.sankuai.mdp.compass.mge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.ErrorEnum;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.mge.entity.MgeBaseCase;
import com.sankuai.mdp.compass.mge.entity.MgeTestJob;
import com.sankuai.mdp.compass.mge.service.MgeTestJobService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR> 2021-12-31
 */
@RestController
@RequestMapping("/compass/api/living/mgeTestJob")
public class MgeTestJobController {

    private static final Logger logger = LoggerFactory.getLogger(MgeTestJobController.class);

    @Autowired
    MgeTestJobService mgeTestJobService;

    @PostMapping("/startJob")
    //服务调用回归、发布任务
    public Resp startJob(@RequestBody JSONObject entity){
        logger.info("MgeTestJobController startJob {}");
        try {
            //entity.setCreateBy(JWTUtil.getUsername());
            mgeTestJobService.startJob(entity);
            return Resp.success(entity);
        }catch (Exception e){
            logger.error("MgeTestJobController startJob: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/mgeConanJobCallBack")
    //云测任务回调接口（回写埋点任务表mge_test_job、mge_test_task）
    public Resp mgeConanJobCallBack(@RequestBody JSONObject body){
        logger.info("MgeTestJobController startDesignJob {}");
        try {
            mgeTestJobService.mgeConanJobCallBack(body);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestJobController startDesignJob: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/resultCallBack")
    public Resp resultCallBack(@RequestBody JSONObject entity){
        logger.info("MgeTestResultController list {}");
        try {
            mgeTestJobService.resultCallBack(entity);;
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestResultController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/uiJobCallBack")
    public Resp uiJobCallBack(@RequestParam  Long taskId, @RequestParam  String conanJobId){
        logger.info("MgeTestResultController list {}");
        try {
            mgeTestJobService.uiJobCallBack(taskId,conanJobId);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestResultController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/delete")
    @ResponseBody
    public Resp delete(String ids){
        logger.info("MgeTestJobController delete {}");
        try {
            mgeTestJobService.deleteEntity(ids.split(","));
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestJobController delete: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/update")
    @ResponseBody
    public Resp update(MgeTestJob entity){
        logger.info("MgeTestJobController update {}");
        try {
            //entity.setUpdateBy(JWTUtil.getUsername());
            mgeTestJobService.updateEntity(entity);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestJobController update: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/list")
    public Resp list(MgeTestJob entity, QueryRequest queryRequest){
        logger.info("MgeTestJobController list {}");
        try {
            IPage<MgeTestJob> page = mgeTestJobService.findList(entity, queryRequest);
            return Resp.success(page);
        }catch (Exception e){
            logger.error("MgeTestJobController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/getCount")
    public MgeCount count(@RequestParam String version) {
        return mgeTestJobService.count(version);
    }

    @GetMapping("/getMgeCaseById")
    public String getMgeCaseById(String jobid){
        return mgeTestJobService.getMgeCaseById(jobid);
    }

    @GetMapping("/getAllVersion")
    public List<String> getAllVersions() {
        return mgeTestJobService.getAllVersions();
    }
}
