package com.sankuai.mdp.compass.mge.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.ErrorEnum;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.mge.entity.MgeBaseCase;
import com.sankuai.mdp.compass.mge.service.MgeBaseCaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> 2021-12-31
 */
@RestController
@RequestMapping("/compass/api/living/mgeBaseCase")
public class MgeBaseCaseController {

    private static final Logger logger = LoggerFactory.getLogger(MgeBaseCaseController.class);

    @Autowired
    MgeBaseCaseService mgeBaseCaseService;

    @PostMapping("/add")
    @ResponseBody
    public Resp add(@RequestBody MgeBaseCase entity){
        logger.info("MgeBaseCaseController add {}");
        try {
            //entity.setCreateBy(JWTUtil.getUsername());
            mgeBaseCaseService.createEntity(entity);
            return Resp.success(entity);
        }catch (Exception e){
            logger.error("MgeBaseCaseController add: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/delete")
    @ResponseBody
    public Resp delete(MgeBaseCase mgeBaseCase){
        logger.info("MgeBaseCaseController delete {}");
        try {
            //mgeBaseCaseService.deleteEntity(ids.split(","));
            mgeBaseCaseService.deleteById(mgeBaseCase);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeBaseCaseController delete: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/update")
    @ResponseBody
    public Resp update(MgeBaseCase entity){
        logger.info("MgeBaseCaseController update {}");
        try {
            //entity.setUpdateBy(JWTUtil.getUsername());
            mgeBaseCaseService.updateEntity(entity);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeBaseCaseController update: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/list")
    @ResponseBody
    public Resp list(MgeBaseCase entity, QueryRequest queryRequest){
        logger.info("MgeBaseCaseController list {}");
        try {
            IPage<MgeBaseCase> page = mgeBaseCaseService.findList(entity, queryRequest);
            return Resp.success(page);
        }catch (Exception e){
            logger.error("MgeBaseCaseController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/findId")
    @ResponseBody
    public Resp findId(Long caseId){
        logger.info("MgeBaseCaseController list {}");
        try {
            MgeBaseCase mgeBaseCase = mgeBaseCaseService.findByCaseId(caseId);
            return Resp.success(mgeBaseCase);
        }catch (Exception e){
            logger.error("MgeBaseCaseController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/findBid")
    @ResponseBody
    public Resp findBid(Long bid){
        logger.info("MgeBaseCaseController list {}");
        try {
            List<MgeBaseCase> mgeBaseCases = mgeBaseCaseService.findByBid(bid);
            return Resp.success(mgeBaseCases);
        }catch (Exception e){
            logger.error("MgeBaseCaseController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/findIds")
    @ResponseBody
    public Resp findIds(List<Long> caseIds){
        logger.info("MgeBaseCaseController list {}");
        try {
            List<MgeBaseCase> mgeBaseCases = mgeBaseCaseService.findByCaseIds(caseIds);
            return Resp.success(mgeBaseCases);
        }catch (Exception e){
            logger.error("MgeBaseCaseController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/designFind")
    @ResponseBody
    public Resp designFind(String business,String page,String module,String stage,String os){
        logger.info("MgeBaseCaseController list {}");
        try {
            List<MgeBaseCase> mgeBaseCases = mgeBaseCaseService.findByDesignCondition(business,page,module,stage,os);
            return Resp.success(mgeBaseCases);
        }catch (Exception e){
            logger.error("MgeBaseCaseController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }


    @GetMapping("/getAllPrincipal")
    public List<String> getAllPrincipal() {
        return mgeBaseCaseService.getAllPrincipal();
    }

}
