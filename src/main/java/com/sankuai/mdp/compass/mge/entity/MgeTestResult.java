package com.sankuai.mdp.compass.mge.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Repository;

/**
 *
 *
 * <AUTHOR> 2021-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mge_test_result")
public class MgeTestResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "result_id", type = IdType.AUTO)
    private Long resultId;

    private Long taskId;

    private Long caseId;
    /**
     * 用例描述
     */
    private String description;

    /**
     * 负责人
     */
    private String principle;


    /**
     * bid
     */
    private String bid;

    /**
     * 锚点sdk回执状态.1代表回执了结果
     */
    private Integer sdkStatus;

    /**
     * 埋点上报/锚点回执时间
     */
    private String sdkReportTime;

    /**
     * 埋点结果更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date resultUpdateTime;

    /**
     * 实时数据
     */
    private String testDetail;

    /**
     * 基准数据
     */
    private String caseDetail;

    /**
     * 埋点校验结果：-1代表时机上报错误，0代表漏报，1代表校验通过，2代表多报，3代表参数（bid、cid、val_lab、tag等）的value错误，
     *          4代表参数类型错误(目前没做)，5代表埋点上报类型错误，6代表锚点sdk返回数据有问题（目前没做如何评判锚点数据有问题？）,7代表其他异常
     */
    private Integer testResult;

    private String testMessage;


}
