package com.sankuai.mdp.compass.mge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.mge.entity.MgeBaseCase;
import com.sankuai.mdp.compass.mge.mapper.MgeBaseCaseMapper;
import com.sankuai.mdp.compass.mge.service.MgeBaseCaseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.*;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
public class MgeBaseCaseServiceImpl extends ServiceImpl<MgeBaseCaseMapper, MgeBaseCase> implements MgeBaseCaseService {

    private static final Logger logger = LoggerFactory.getLogger(MgeBaseCaseServiceImpl.class);

    @Override
    public void createEntity(MgeBaseCase entity) throws Exception {
        /*entity.setCreateTime(new Date());
        entity.setUpdateBy(entity.getCreateBy());*/
        logger.info("埋点实时用例生成人：" + entity.getPrinciple());
        entity.setUpdateTime(new Date());

        if (entity.getPageDescription().equals("首页")) {

            entity.setPageKey("imeituan://www.meituan.com/home");

            if (entity.getModuleDescription().equals("搜索热词")) {
                entity.setModuleKey("searchHotWord");
            }

            if (entity.getModuleDescription().equals("导航栏")) {
                entity.setModuleKey("homepage_actionbar_item");
            }

           /* if (entity.getModuleDescription().equals("功能区")) {
                entity.setModuleKey("utilArea");
            }*/
            if (entity.getModuleDescription().equals("功能区")) {
                entity.setModuleKey("utilConvenienceEntry");
            }
           /* if (entity.getModuleDescription().equals("金刚区")) {
                entity.setModuleKey("homepageCateCategoryNative");
            }*/
            if (entity.getModuleDescription().equals("改版金刚区")) {
                entity.setModuleKey("cateCategory");
            }

            if (entity.getModuleDescription().equals("零售专区")) {
                entity.setModuleKey("retailArea");
            }

           /* if (entity.getModuleDescription().equals("大促专区")) {
                entity.setModuleKey("indexSimplifiedPromotion");
            }*/
            if (entity.getModuleDescription().equals("大促专区")) {
                entity.setModuleKey("indexNewPromotionDynamicLayout");
            }
            /*if (entity.getModuleDescription().equals("优选专区")) {
                entity.setModuleKey("youxuan_0");
            }*/
            if (entity.getModuleDescription().equals("优选专区")) {
                entity.setModuleKey("youxuanLayout");
            }

            if (entity.getModuleDescription().equals("新人专区")) {
                entity.setModuleKey("homepageCoupon");
            }

            if (entity.getModuleDescription().equals("猜喜")) {
                entity.setModuleKey("feed");
            }

            if (entity.getModuleDescription().equals("底部Tab")) {
                entity.setModuleKey("bottomTab");
            }
        }
        if (entity.getPageDescription().equals("消息")) {
            entity.setPageKey("imeituan://www.meituan.com/message");
            if (entity.getModuleDescription().equals("饭小圈")) {
                entity.setModuleKey("tuanFriendModule");
            }
            if (entity.getModuleDescription().equals("消息列表")) {
                entity.setModuleKey("collectModule");
            }
        }
        if (entity.getPageDescription().equals("订单")) {
            entity.setPageKey("imeituan://www.meituan.com/orderTab");
        }
        if (entity.getPageDescription().equals("我的")) {
            entity.setPageKey("imeituan://www.meituan.com/homemine");
            if (entity.getModuleDescription().equals("饭小圈")) {
                entity.setModuleKey("tuanFriendModule");
            }
            if (entity.getModuleDescription().equals("消息列表")) {
                entity.setModuleKey("collectModule");
            }
        }
        String type = "";
        String index = "";
        if (entity.getType().equals("MV")) {
            type = "曝光";
        } else if (entity.getType().equals("MC")) {
            type = "点击";
        } else if (entity.getType().equals("PV")) {
            type = "页面PV";
        } else {
            type = entity.getType();
        }
        if (entity.getModuleIndex().length() > 0) {
            index = "第".concat(entity.getModuleIndex()).concat("个元素");
        } else {
            index = entity.getModuleIndex();
        }

        String triggerTime = entity.getTriggerTime();

        String description = entity.getPageDescription().concat(entity.getModuleDescription()).concat(entity.getPath()).concat(index).concat(triggerTime).concat(entity.getTriggerCondition()).concat(type).concat("埋点上报");
        entity.setDescription(description);
        entity.setCaseStatus(1);
        if (entity.getCaseDetail().equals("[]")) {
            entity.setValCheck(0);
            //entity.setCaseDetail("[]");
        }else{
            entity.setValCheck(1);
        }
        this.save(entity);
        if(!entity.getTriggerCondition().equals("") && entity.getBusiness().equals("平台")){
            reverseMvCase(entity);
        }else{
            logger.info("埋点实时校验触发时机为空");
        }
    }

    /*
    如果是曝光埋点有时机校验的自动生成2个反向case：露出程度、露出时长 2个维度
    */
    public void reverseMvCase(MgeBaseCase entity){

        if(entity.getTriggerCondition().equals("0.7,500")){
            MgeBaseCase mgeBaseCase1 = entity;
            mgeBaseCase1.setTriggerCondition("0.2,500");
            mgeBaseCase1.setDescription(entity.getDescription().replace("0.7,500","0.2,500"));
            mgeBaseCase1.setValCheck(1);
            mgeBaseCase1.setCaseDetail("[]");
            mgeBaseCaseMapper.insert(mgeBaseCase1);

            MgeBaseCase mgeBaseCase2 = entity;
            mgeBaseCase2.setTriggerCondition("0.7,100");
            mgeBaseCase2.setDescription(entity.getDescription().replace("0.2,500","0.7,100"));
            mgeBaseCase2.setValCheck(1);
            mgeBaseCase2.setCaseDetail("[]");
            mgeBaseCaseMapper.insert(mgeBaseCase2);

        }else if(entity.getTriggerCondition().equals("1px,500")){
            MgeBaseCase mgeBaseCase3 = entity;
            mgeBaseCase3.setTriggerCondition("1px,100");
            mgeBaseCase3.setDescription(entity.getDescription().replace("1px,500","1px,100"));
            mgeBaseCase3.setValCheck(1);
            mgeBaseCase3.setCaseDetail("");
            mgeBaseCaseMapper.insert(mgeBaseCase3);
        }
    }

    @Autowired
    MgeBaseCaseMapper mgeBaseCaseMapper;

    @Override
    public void deleteEntity(String[] ids) {
        List<Long> list = new ArrayList<>();
        for (String id : ids){
            list.add(Long.valueOf(id));
        }
        this.removeByIds(list);
    }

    @Override
    public void deleteById(MgeBaseCase mgeBaseCase) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("case_id", mgeBaseCase.getCaseId());
        mgeBaseCaseMapper.delete(queryWrapper);
    }

    @Override
    public IPage<MgeBaseCase> findList(MgeBaseCase entity, QueryRequest queryRequest) {
        try {
            LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();

            if (null != entity.getPrinciple()) {
                queryWrapper.eq(MgeBaseCase::getPrinciple, entity.getPrinciple());
            }
            if (null != entity.getBusiness()) {
                queryWrapper.like(MgeBaseCase::getBusiness, entity.getBusiness());
            }
            if (null != entity.getBid()) {
                queryWrapper.like(MgeBaseCase::getBid, entity.getBid());
            }
            Page<MgeBaseCase> page = new Page<>(queryRequest.getPageNum(), queryRequest.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            logger.error("MgeBaseCaseServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public List<MgeBaseCase> findList(MgeBaseCase entity) {
        try {
            LambdaQueryWrapper<MgeBaseCase> queryWrapper = getLambdaQueryWrapper(entity);
            List<MgeBaseCase> list = this.list(queryWrapper);
            for (MgeBaseCase domain:list){
                getDetail(domain);
            }
            return list;
        } catch (Exception e) {
            logger.error("MgeBaseCaseServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public MgeBaseCase find(MgeBaseCase entity) {
        LambdaQueryWrapper<MgeBaseCase> queryWrapper = getLambdaQueryWrapper(entity);
        queryWrapper.last("limit 1");
        return getDetail(this.getOne(queryWrapper));
    }

    @Override
    public void updateEntity(MgeBaseCase entity) throws Exception {
        if (entity.getCaseId() == null){
            throw new Exception("id为null");
        }
        //entity.setUpdateTime(new Date());
        this.updateById(entity);
    }

    @Override
    public MgeBaseCase findByCaseId(Long caseId){
        LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();
        if (caseId != null) {
            queryWrapper.in(MgeBaseCase::getCaseId, caseId);
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public List<MgeBaseCase> findByBid(Long bid){
        List<MgeBaseCase> arrayList = new ArrayList<>();
        LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();
        if (bid != null) {
            queryWrapper.in(MgeBaseCase::getBid, bid);
        }

        queryWrapper.eq(MgeBaseCase::getCaseStatus,1);
        arrayList = this.list(queryWrapper);
        return arrayList;

    }

    @Override
    public List<MgeBaseCase> list(MgeBaseCase mgeBaseCase){
        List<MgeBaseCase> arrayList = new ArrayList<>();

        LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();

        if (mgeBaseCase.getCaseId() != null) {
            queryWrapper.eq(MgeBaseCase::getCaseId, mgeBaseCase.getCaseId());
        }

        queryWrapper.eq(MgeBaseCase::getCaseStatus,1);
        arrayList = this.list(queryWrapper);
        return arrayList;
    }

    @Override
    public List<MgeBaseCase> findByCaseIds(List<Long> caseIds){
        List<MgeBaseCase> arrayList = new ArrayList<>();
        LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MgeBaseCase::getCaseId, caseIds);
        arrayList = this.list(queryWrapper);
        return arrayList;
    }

    @Override
    public List<MgeBaseCase> findByDesignCondition(String business,String page,String module,String stage,String os){
        List<MgeBaseCase> arrayList = new ArrayList<>();
        LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(MgeBaseCase::getCaseStatus,1);

        if(StringUtils.isNotBlank(page)){
            queryWrapper.eq(MgeBaseCase::getPageDescription,page);
        }
        if(module.length() > 0){
            queryWrapper.eq(MgeBaseCase::getModuleDescription,module);
        }
        if(stage.length() > 0){
            queryWrapper.like(MgeBaseCase::getStage,stage);
        }
        if(os.length()>0){
            queryWrapper.eq(MgeBaseCase::getOs,"all").or().eq(MgeBaseCase::getOs,os);
        }
        if(StringUtils.isNotBlank(business)){
            queryWrapper.eq(MgeBaseCase::getBusiness,business);
        }
        arrayList.addAll(this.list(queryWrapper));
        return arrayList;
    }


    @Override
    public List<String> getAllPrincipal() {
        QueryWrapper<MgeBaseCase> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<MgeBaseCase> mgelist = mgeBaseCaseMapper.selectList(queryWrapper
                .ne("principal", "Job")
                .groupBy("principal"));
        for (MgeBaseCase mgeBaseCase : mgelist) {
            list.add(mgeBaseCase.getPrinciple());
        }
        return list;
    }

    private LambdaQueryWrapper getLambdaQueryWrapper(MgeBaseCase entity){
        LambdaQueryWrapper<MgeBaseCase> queryWrapper = new LambdaQueryWrapper<>();
        if (entity.getCaseId() != null){
            queryWrapper.eq(MgeBaseCase::getCaseId, entity.getCaseId());
        }
       /* if (!StringUtil.isNullOrEmpty(entity.getCreateBy())){
            queryWrapper.eq(MgeBaseCase::getCreateBy, entity.getCreateBy());
        }*/
        return queryWrapper;
    }

    private MgeBaseCase getDetail(MgeBaseCase entity){
        if (entity == null){
            return null;
        }
        return entity;
    }

}
