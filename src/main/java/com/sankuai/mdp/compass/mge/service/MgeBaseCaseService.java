package com.sankuai.mdp.compass.mge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.mge.entity.MgeBaseCase;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
public interface MgeBaseCaseService extends IService<MgeBaseCase> {

    public void createEntity(MgeBaseCase entity) throws Exception;

    public void deleteEntity(String[] ids);

    public void deleteById(MgeBaseCase mgeBaseCase);

    public IPage<MgeBaseCase> findList(MgeBaseCase entity, QueryRequest queryRequest);

    public List<MgeBaseCase> findList(MgeBaseCase entity);

    public MgeBaseCase find(MgeBaseCase entity);

    public void updateEntity(MgeBaseCase entity) throws Exception;


    List<MgeBaseCase> list(MgeBaseCase mgeBaseCase);

    MgeBaseCase findByCaseId(Long caseId);

    List<MgeBaseCase> findByCaseIds(List<Long> caseIds);

    List<MgeBaseCase> findByBid(Long bid);

    List<MgeBaseCase> findByDesignCondition(String business,String page,String module,String stage,String os);

    List<String> getAllPrincipal();

}
