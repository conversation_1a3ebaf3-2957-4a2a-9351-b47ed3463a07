package com.sankuai.mdp.compass.mge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.mge.entity.MgeTestTask;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
public interface MgeTestTaskService extends IService<MgeTestTask> {

    public void createEntity(MgeTestTask entity) throws Exception;

    public void deleteEntity(String[] ids);

    public IPage<MgeTestTask> findList(MgeTestTask entity, QueryRequest queryRequest);

    public List<MgeTestTask> findList(MgeTestTask entity);

    public MgeTestTask find(MgeTestTask entity);

    public MgeTestTask find(Long taskId);

    public MgeTestTask findConanId(String conanId);

    public void updateEntity(MgeTestTask entity) throws Exception;


}
