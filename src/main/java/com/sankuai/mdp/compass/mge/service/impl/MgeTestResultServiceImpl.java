package com.sankuai.mdp.compass.mge.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.mge.entity.MgeTestResult;
import com.sankuai.mdp.compass.mge.entity.MgeTestTask;
import com.sankuai.mdp.compass.mge.mapper.MgeTestResultMapper;
import com.sankuai.mdp.compass.mge.mapper.MgeTestTaskMapper;
import com.sankuai.mdp.compass.mge.service.MgeTestResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.*;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
@Slf4j
public class MgeTestResultServiceImpl extends ServiceImpl<MgeTestResultMapper, MgeTestResult> implements MgeTestResultService {

    private static final Logger logger = LoggerFactory.getLogger(MgeTestResultServiceImpl.class);

    @Autowired
    MgeTestTaskMapper mgeTestTaskMapper;

    @Autowired
    MgeTestResultMapper mgeTestResultMapper;

    @Override
    public void createEntity(MgeTestResult entity) throws Exception {
       /* entity.setCreateTime(new Date());
        entity.setUpdateBy(entity.getCreateBy());
        entity.setUpdateTime(new Date());*/
        this.save(entity);
    }

    @Override
    public void deleteEntity(String[] ids) {
        List<Long> list = new ArrayList<>();
        for (String id : ids){
            list.add(Long.valueOf(id));
        }
        this.removeByIds(list);
    }

    @Override
    public IPage<MgeTestResult> findList(MgeTestTask entity, QueryRequest queryRequest) {
        try {
            LambdaQueryWrapper<MgeTestTask> queryWrapperTask = new LambdaQueryWrapper<>();
            //根据jobid找到所有taskid
            queryWrapperTask.eq(MgeTestTask::getJobId, entity.getJobId());
            List<MgeTestTask> taskList = mgeTestTaskMapper.selectList(queryWrapperTask);
            List<Long> taskIds = new ArrayList<>();
            //遍历每个taskid找到所有resultid
            for (MgeTestTask mgeTestTask : taskList) {
                taskIds.add(mgeTestTask.getTaskId());
            }
            LambdaQueryWrapper<MgeTestResult> queryWrapperResult = new LambdaQueryWrapper<>();
            queryWrapperResult.in(MgeTestResult::getTaskId, taskIds);
            if (queryRequest.getSwitchStatus()) {
                queryWrapperResult.eq(MgeTestResult::getTestResult, "0");
            }
            queryWrapperResult.orderByDesc((MgeTestResult::getTestResult));
            Page<MgeTestResult> page = new Page<>(queryRequest.getPageNum(), queryRequest.getPageSize());
            return this.page(page, queryWrapperResult);
        } catch (Exception e) {
            logger.error("MgeTestResultServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public List<MgeTestResult> findList(MgeTestResult entity) {
        try {
            LambdaQueryWrapper<MgeTestResult> queryWrapper = getLambdaQueryWrapper(entity);
            List<MgeTestResult> list = this.list(queryWrapper);
            for (MgeTestResult domain:list){
                getDetail(domain);
            }
            return list;
        } catch (Exception e) {
            logger.error("MgeTestResultServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public MgeTestResult find(MgeTestResult entity) {
        LambdaQueryWrapper<MgeTestResult> queryWrapper = getLambdaQueryWrapper(entity);
        queryWrapper.last("limit 1");
        return getDetail(this.getOne(queryWrapper));
    }

    @Override
    public  MgeTestResult findId(Long resultId){
        LambdaQueryWrapper<MgeTestResult> queryWrapper = new LambdaQueryWrapper<>();
        if (resultId != null) {
            queryWrapper.in(MgeTestResult::getResultId, resultId);
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateEntity(MgeTestResult entity) throws Exception {
        if (entity.getTaskId() == null){
            throw new Exception("id为null");
        }
        //entity.setUpdateTime(new Date());
        this.updateById(entity);
    }

    private LambdaQueryWrapper getLambdaQueryWrapper(MgeTestResult entity){
        LambdaQueryWrapper<MgeTestResult> queryWrapper = new LambdaQueryWrapper<>();
        if (entity.getTaskId() != null){
            queryWrapper.eq(MgeTestResult::getTaskId, entity.getTaskId());
        }
       /* if (!StringUtil.isNullOrEmpty(entity.getCreateBy())){
            queryWrapper.eq(MgeTestResult::getCreateBy, entity.getCreateBy());
        }*/
        return queryWrapper;
    }

    private MgeTestResult getDetail(MgeTestResult entity){
        if (entity == null){
            return null;
        }
        return entity;
    }

}
