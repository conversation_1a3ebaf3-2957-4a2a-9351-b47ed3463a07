package com.sankuai.mdp.compass.mge.service;


import com.baomidou.mybatisplus.extension.service.IService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.mge.entity.MgeTestResult;
import com.sankuai.mdp.compass.mge.entity.MgeTestTask;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
public interface MgeTestResultService extends IService<MgeTestResult> {

    public void createEntity(MgeTestResult entity) throws Exception;

    public void deleteEntity(String[] ids);

    public IPage<MgeTestResult> findList(MgeTestTask entity, QueryRequest queryRequest);

    public List<MgeTestResult> findList(MgeTestResult entity);

    public MgeTestResult find(MgeTestResult entity);

    public MgeTestResult findId(Long jobId);

    public void updateEntity(MgeTestResult entity) throws Exception;

}
