package com.sankuai.mdp.compass.mge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.SortUtil;
import com.sankuai.mdp.compass.mge.entity.MgeTestTask;
import com.sankuai.mdp.compass.mge.mapper.MgeTestTaskMapper;
import com.sankuai.mdp.compass.mge.service.MgeTestTaskService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.*;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Slf4j
@Service
public class MgeTestTaskServiceImpl extends ServiceImpl<MgeTestTaskMapper, MgeTestTask> implements MgeTestTaskService {

    private static final Logger logger = LoggerFactory.getLogger(MgeTestTaskServiceImpl.class);

    @Override
    public void createEntity(MgeTestTask entity) throws Exception {
        //entity.setCreateTime(new Date());
        //entity.setUpdateBy(entity.getCreateBy());
        //entity.setUpdateTime(new Date());
        this.save(entity);
    }

    @Override
    public void deleteEntity(String[] ids) {
        List<Long> list = new ArrayList<>();
        for (String id : ids){
            list.add(Long.valueOf(id));
        }
        this.removeByIds(list);
    }

    @Override
    public IPage<MgeTestTask> findList(MgeTestTask entity, QueryRequest queryRequest) {
        try {
            LambdaQueryWrapper<MgeTestTask> queryWrapper = getLambdaQueryWrapper(entity);
            Page<MgeTestTask> page = new Page<>(queryRequest.getPageNum(), queryRequest.getPageSize());
            //SortUtil.handlePageSort(queryRequest, page, "id", FebsConstant.ORDER_ASC, false);
            IPage<MgeTestTask> iPage = this.page(page, queryWrapper);
            for (MgeTestTask domain: iPage.getRecords()){
                getDetail(domain);
            }
            return iPage;
        } catch (Exception e) {
            logger.error("MgeTestTaskServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public List<MgeTestTask> findList(MgeTestTask entity) {
        try {
            LambdaQueryWrapper<MgeTestTask> queryWrapper = getLambdaQueryWrapper(entity);
            List<MgeTestTask> list = this.list(queryWrapper);
            for (MgeTestTask domain:list){
                getDetail(domain);
            }
            return list;
        } catch (Exception e) {
            logger.error("MgeTestTaskServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public MgeTestTask find(MgeTestTask entity) {
        LambdaQueryWrapper<MgeTestTask> queryWrapper = getLambdaQueryWrapper(entity);
        queryWrapper.last("limit 1");
        return getDetail(this.getOne(queryWrapper));
    }



    @Override
    public MgeTestTask find(Long taskId){
        MgeTestTask mgeTestTask = this.getById(taskId);
        return getDetail(mgeTestTask);
    }

    @Override
    public MgeTestTask findConanId(String conanId){
        LambdaQueryWrapper<MgeTestTask> queryWrapper = new LambdaQueryWrapper<>();
        if (conanId != null) {
            queryWrapper.in(MgeTestTask::getConanJobId, conanId);
        }
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateEntity(MgeTestTask entity) throws Exception {
        if (entity.getTaskId() == null){
            throw new Exception("id为null");
        }
        //entity.setUpdateTime(new Date());
        this.updateById(entity);
    }

    private LambdaQueryWrapper getLambdaQueryWrapper(MgeTestTask entity){
        LambdaQueryWrapper<MgeTestTask> queryWrapper = new LambdaQueryWrapper<>();
        if (entity.getTaskId() != null){
            queryWrapper.eq(MgeTestTask::getTaskId, entity.getTaskId());
        }
      /*  if (!StringUtil.isNullOrEmpty(entity.getCreateBy())){
            queryWrapper.eq(MgeTestTask::getCreateBy, entity.getCreateBy());
        }*/
        return queryWrapper;
    }

    private MgeTestTask getDetail(MgeTestTask entity){
        if (entity == null){
            return null;
        }
        return entity;
    }

}
