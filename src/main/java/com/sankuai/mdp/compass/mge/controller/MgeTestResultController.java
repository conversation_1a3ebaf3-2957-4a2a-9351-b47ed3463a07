package com.sankuai.mdp.compass.mge.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.ErrorEnum;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.mge.entity.MgeTestResult;
import com.sankuai.mdp.compass.mge.entity.MgeTestTask;
import com.sankuai.mdp.compass.mge.service.MgeTestResultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2021-12-31
 */
@RestController
@RequestMapping("/compass/api/living/mgeTestResult")
public class MgeTestResultController {

    private static final Logger logger = LoggerFactory.getLogger(MgeTestResultController.class);

    @Autowired
    MgeTestResultService mgeTestResultService;

    @PostMapping("/add")
    @ResponseBody
    public Resp add(MgeTestResult entity){
        logger.info("MgeTestResultController add {}");
        try {
            //entity.setCreateBy(JWTUtil.getUsername());
            mgeTestResultService.createEntity(entity);
            return Resp.success(entity);
        }catch (Exception e){
            logger.error("MgeTestResultController add: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/delete")
    @ResponseBody
    public Resp delete(String ids){
        logger.info("MgeTestResultController delete {}");
        try {
            mgeTestResultService.deleteEntity(ids.split(","));
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestResultController delete: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @PostMapping("/update")
    @ResponseBody
    public Resp update(MgeTestResult entity){
        logger.info("MgeTestResultController update {}");
        try {
            //entity.setUpdateBy(JWTUtil.getUsername());
            mgeTestResultService.updateEntity(entity);
            return Resp.success();
        }catch (Exception e){
            logger.error("MgeTestResultController update: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

    @GetMapping("/list")
    @ResponseBody
    public Resp list(MgeTestTask entity, QueryRequest queryRequest){
        logger.info("MgeTestResultController list {}");
        try {
            IPage<MgeTestResult> page = mgeTestResultService.findList(entity, queryRequest);
            return Resp.success(page);
        }catch (Exception e){
            logger.error("MgeTestResultController list: {}", e);
            return Resp.error(ErrorEnum.S_400, e.getMessage());
        }
    }

}
