package com.sankuai.mdp.compass.mge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.mge.entity.MgeTestJob;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created by lizhen<PERSON> on 2021/12/30.
 */
@Mapper
public interface MgeTestJobMapper extends BaseMapper<MgeTestJob> {

    @Select("SELECT version as version,count(*) as total,\n" +
            "sum(case when job_status != '1' then 1 else 0 end) as failed,\n" +
            "sum(case when job_status = '1' then 1 else 0 end) as success,\n" +
            "FROM mge_test_job \n" +
            "WHERE version = #{version}")
    MgeCount count(@Param("version") String version);

}
