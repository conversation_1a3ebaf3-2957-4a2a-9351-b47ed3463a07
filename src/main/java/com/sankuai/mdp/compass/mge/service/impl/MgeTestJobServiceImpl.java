package com.sankuai.mdp.compass.mge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.*;
import com.google.gson.JsonParser;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.JenkinsUtil;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.mge.entity.*;
import com.sankuai.mdp.compass.mge.mapper.MgeTestJobMapper;
import com.sankuai.mdp.compass.mge.mapper.MgeTestResultMapper;
import com.sankuai.mdp.compass.mge.mapper.MgeTestTaskMapper;
import com.sankuai.mdp.compass.mge.service.MgeBaseCaseService;
import com.sankuai.mdp.compass.mge.service.MgeTestJobService;
import com.sankuai.mdp.compass.mge.service.MgeTestResultService;
import com.sankuai.mdp.compass.mge.service.MgeTestTaskService;
import com.zaxxer.hikari.pool.HikariProxyResultSet;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
public class MgeTestJobServiceImpl extends ServiceImpl<MgeTestJobMapper, MgeTestJob> implements MgeTestJobService {

    private static final Logger logger = LoggerFactory.getLogger(MgeTestJobServiceImpl.class);

    @Autowired
    MgeBaseCaseService mgeBaseCaseService;

    @Autowired
    MgeTestTaskService mgeTestTaskService;

    @Autowired
    MgeTestJobService mgeTestJobService;

    @Autowired
    MgeTestResultService mgeTestResultService;

    @Autowired
    MgeTestJobMapper mgeTestJobMapper;

    @Autowired
    MgeTestTaskMapper mgeTestTaskMapper;

    @Autowired
    MgeTestResultMapper mgeTestResultMapper;

    JenkinsUtil jenkinsUtil = new JenkinsUtil();


    @Override
    public JsonObject startJob(JSONObject entity) throws Exception {
        logger.info("埋点实时校验触发的job详情：" + entity.toString());
        MgeTestJob mgeTestJob = new MgeTestJob();
        mgeTestJob.setCreateBy(entity.getString("createBy"));
        mgeTestJob.setStartTime(new Date());//只要有人触发埋点自动化，就认为埋点测试任务开始，记录时间
        mgeTestJob.setJobStatus(0);
        if (StringUtils.isBlank(entity.getString("business")) || StringUtils.isBlank(entity.getString("appUrl")) || StringUtils.isBlank(entity.getString("version"))) {
            throw new Exception("business or appUrl or version is null ,please check and retry !");
        }
        mgeTestJob.setBusiness(entity.getString("business"));
        mgeTestJob.setAppUrl(entity.getString("appUrl"));
        mgeTestJob.setVersion(entity.getString("version"));
        String business = entity.getString("business");
        String os = entity.get("os").toString();
        mgeTestJob.setOs(os);
        String stage = entity.get("stage").toString();
        mgeTestJob.setStage(stage);
        this.save(mgeTestJob);
        //圈选回归、发布阶段所有埋点case
        List<MgeBaseCase> mgeList = new ArrayList<>();
        //如果是回归、发布阶段按照P0\P1\P2圈选埋点case
        if (stage.equals("回归") || stage.equals("全量发布") || stage.equals("灰度发布")) {
            mgeList = mgeBaseCaseService.findByDesignCondition(business, "", "", stage, os);
        }
        //如果是用户自定义任务，按照前端的入参（页面or模块）圈选埋点case
      /*  if(entity.getString("pageList").length() > 0){
            logger.info("预留前端用户的入口");
        }*/
        //每10个为一组生成task表的数据，并同步生成result表的数据
        double caseLength = mgeList.size();
        Integer conanJobNum = (int) Math.ceil(caseLength / 10);
        Integer index = 0;
        Integer flag = index + 10;
        JsonArray uiConanArray = new JsonArray();
        String mgeTaskTotalIds = "";
        for (int i = 0; i < conanJobNum; i++) {
            JsonObject uiConanObject = new JsonObject();
            JsonArray caseDetailArray = new JsonArray();
            MgeTestTask mgeTestTask = new MgeTestTask();
            mgeTestTask.setJobId(mgeTestJob.getJobId());
            mgeTestTaskMapper.insert(mgeTestTask);
            String taskId = mgeTestTask.getTaskId().toString();
            if (StringUtils.isNotBlank(mgeTestJob.getMgeTaskTotalId())) {
                mgeTaskTotalIds = mgeTestJob.getMgeTaskTotalId().concat(",").concat(taskId);
                mgeTestJob.setMgeTaskTotalId(mgeTaskTotalIds);
                mgeTestJobMapper.updateById(mgeTestJob);
            } else {
                mgeTaskTotalIds = taskId;
                mgeTestJob.setMgeTaskTotalId(mgeTaskTotalIds);
                mgeTestJobMapper.updateById(mgeTestJob);
            }
            uiConanObject.addProperty("taskId", mgeTestTask.getTaskId());
            String caseids = "";
            String findMethod = "findByText";
            String callBackUrl = "";
            if (EnvUtil.isOnline()) {
                callBackUrl = "http://qaassist.sankuai.com/compass/api/living/mgeTestJob/resultCallBack";
            } else {
                callBackUrl = "http://172.18.169.231:8080/compass/api/living/mgeTestJob/resultCallBack";
            }
            for (int j = index; j < flag; j++) {
                if (j < mgeList.size()) {
                    JsonObject caseDetailObject = new JsonObject();
                    String schemaUrl = "";
                    MgeTestResult mgeTestResult = new MgeTestResult();
                    mgeTestResult.setTaskId(mgeTestTask.getTaskId());
                    MgeBaseCase mgeBaseCase = mgeList.get(j);
                    String pageKey = mgeBaseCase.getPageKey();
                    String moduleKey = mgeBaseCase.getModuleKey();
                    String moduleIndex = mgeBaseCase.getModuleIndex();
                    String path = StringUtils.isBlank(mgeBaseCase.getPath()) ? "" : findMethod.concat("(").concat(mgeBaseCase.getPath()).concat(")");
                    String mgeType = mgeBaseCase.getType();
                    String triggerTime = mgeBaseCase.getTriggerTime();
                    String triggerCondition = mgeBaseCase.getTriggerCondition();
                    String mgeId = mgeBaseCase.getBid();
                    String envAbtest = mgeBaseCase.getAbKey();
                    String envHorn = mgeBaseCase.getHorn();
                    String mockId = mgeBaseCase.getMockId();
                    String isAdd = "";
                    Integer isLogin = mgeBaseCase.getIsLogin();
                    if (mgeBaseCase.getBusiness().equals("广告")) {
                        isAdd = "1";
                    } else {
                        isAdd = "0";
                    }
                    Long caseId = mgeBaseCase.getCaseId();
                    mgeTestResult.setCaseId(caseId);
                    mgeTestResult.setBid(mgeBaseCase.getBid());
                    mgeTestResult.setPrinciple(mgeBaseCase.getPrinciple());
                    mgeTestResult.setDescription(mgeBaseCase.getDescription());
                    mgeTestResult.setCaseDetail(mgeBaseCase.getCaseDetail());
                    mgeTestResultMapper.insert(mgeTestResult);
                    String resultId = mgeTestResult.getResultId().toString();
                    //mgeTestResult.setTaskId(mgeTestTask.getTaskId());
                    if (StringUtils.isNotBlank(mgeTestTask.getMgeCaseTotalId())) {
                        caseids = mgeTestTask.getMgeCaseTotalId().concat(",").concat(caseId.toString());
                        mgeTestTask.setMgeCaseTotalId(caseids);
                        mgeTestTaskMapper.updateById(mgeTestTask);
                    } else {
                        caseids = caseId.toString();
                        mgeTestTask.setMgeCaseTotalId(caseids);
                        mgeTestTaskMapper.updateById(mgeTestTask);
                    }
                    //concat("&isLogin=").concat(isLogin.toString())
                    schemaUrl = pageKey.concat("?").concat("moduleKey=").concat(moduleKey).concat("&moduleIndex=").concat(moduleIndex).concat("&path=").concat(path).concat("&mgetype=").concat(mgeType).concat("&triggerTime=").concat(triggerTime).concat("&triggerCondition=").concat(triggerCondition).concat("&resultId=").concat(resultId).concat("&mgeId=").concat(mgeId).concat("&callbackurl=").concat(callBackUrl).concat("&isAdd=").concat(isAdd);
                    caseDetailObject.addProperty("schemaUrl", schemaUrl);
                    caseDetailObject.addProperty("env_abtest", envAbtest);
                    caseDetailObject.addProperty("env_hornDic", envHorn);
                    caseDetailObject.addProperty("mock_id", mockId);
                    caseDetailArray.add(caseDetailObject);
                } else {
                    break;
                }
            }
            uiConanObject.add("case", caseDetailArray);
            uiConanArray.add(uiConanObject);
            mgeTestTask.setMgeCaseArray(uiConanObject.toString());
            mgeTestTaskMapper.updateById(mgeTestTask);
            index += 10;
            flag = index + 10;
        }
        JsonObject mgestartJobResult = new JsonObject();
        entity.put("jobid", mgeTestJob.getJobId());
        JsonObject uiJob = new JsonObject();
        for (Object key : entity.keySet()) {
            uiJob.addProperty(key.toString(), entity.getString(key.toString()));
        }
        //请求自动化服务触发云测job
        if (uiMethod(uiJob).equals("success")) {
            //UI自动化触发成功，返回埋点任务id
            mgestartJobResult.addProperty("code", 200);
            mgestartJobResult.addProperty("message", "success");
            mgestartJobResult.addProperty("jobId", mgeTestJob.getJobId());
        } else {
            mgestartJobResult.addProperty("code", 400);
            mgestartJobResult.addProperty("message", "fail");
            mgestartJobResult.addProperty("jobId", "");
        }
        return mgestartJobResult;
    }

    /**
     * 云测回调接口
     */
    @Override
    public void mgeConanJobCallBack(JSONObject body) {
        JsonParser jsonParser = new JsonParser();
        logger.info(body.toString());
        try {
            JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
            //云测任务id
            String conanJobId = jobData.get("id").toString();
            String reportUrl = jobData.get("report").getAsString();
            //根据云测任务id找到对应的task数据，并更新
            MgeTestTask mgeTestTask = mgeTestTaskService.findConanId(conanJobId);//根据云测任务id反差taskid主键，如果云测任务id查出来多个？
            //mgeTestTask.setConanReport(reportUrl);
            mgeTestTask.setEndTime(new Date());//拿到云测任务回调，即task任务结束，记录时间
            mgeTestTask.setConanJobStatus(1);
            mgeTestTaskMapper.updateById(mgeTestTask);
            //埋点任务id
            Long jobID = mgeTestTask.getJobId().longValue();
            QueryWrapper queryWrapperJob = new QueryWrapper();
            queryWrapperJob.eq("job_id", jobID);
            //根据jobId找到埋点任务数据，并更新
            MgeTestJob mgeTestJob = mgeTestJobMapper.selectOne(queryWrapperJob);//取第一个task数据
            String taskId = mgeTestTask.getTaskId().toString();
            String mgeTaskFinishIds = "";
            if (StringUtils.isNotBlank(mgeTestJob.getMgeTaskFinishId())) {
                mgeTaskFinishIds = mgeTestJob.getMgeTaskFinishId().concat(",").concat(taskId);
                mgeTestJob.setMgeTaskFinishId(mgeTaskFinishIds);
                mgeTestJobMapper.updateById(mgeTestJob);
            } else {
                mgeTaskFinishIds = taskId;
                mgeTestJob.setMgeTaskFinishId(mgeTaskFinishIds);
                mgeTestJobMapper.updateById(mgeTestJob);
            }
            if (mgeTestJob.getMgeTaskTotalId().length() == mgeTestJob.getMgeTaskFinishId().length()) {
                mgeTestJob.setEndTime(new Date());//埋点任务对应的所有task任务（云测任务）执行完，即埋点任务结束，记录时间
                //Duration spendTime = Duration.between(mgeTestJob.getStartTime().getTime(),mgeTestJob.getEndTime());
                float time = mgeTestJob.getEndTime().getTime() - mgeTestJob.getStartTime().getTime();
                int minutesTime = (int) time / (60 * 1000);
                mgeTestJob.setJobTotalTime(minutesTime);
                mgeTestJob.setJobStatus(1);
            }
            mgeTestJobMapper.updateById(mgeTestJob);
        } catch (Exception e) {
            logger.error("mgeCollectResult error>>" + e.getMessage());
        }
    }


    /**
     * sdk回调实时埋点数据上报结果，门户后台落库进行校验
     */
    @Override
    public void resultCallBack(JSONObject entity) {
        logger.info("埋点实时校验锚点sdk回调结果详情：" + entity.toString());
        //根据resultid拿到任务id和用例id
        Long taskId = mgeTestResultService.findId(entity.getLong("resultId")).getTaskId();
        Long caseId = mgeTestResultService.findId(entity.getLong("resultId")).getCaseId();
        //根据任务id查到对应的任务详情
        MgeTestTask mgeTestTask = mgeTestTaskService.find(taskId);
        //将拿到结果的的case的id写到finishid里
        String mgeCaseFinishIds = "";
        if (StringUtils.isNotBlank(mgeTestTask.getMgeCaseFinishId())) {
            mgeCaseFinishIds = mgeTestTask.getMgeCaseFinishId().concat(",").concat(caseId.toString());
            mgeTestTask.setMgeCaseFinishId(mgeCaseFinishIds);
            mgeTestTaskMapper.updateById(mgeTestTask);
        } else {
            mgeCaseFinishIds = caseId.toString();
            mgeTestTask.setMgeCaseFinishId(mgeCaseFinishIds);
            mgeTestTaskMapper.updateById(mgeTestTask);
        }
        //根据resultid取到result整条数据，并将锚点sdk上报结果set到测试结果里
        MgeTestResult mgeTestResult = mgeTestResultService.getById(entity.get("resultId").toString());
        mgeTestResult.setTestDetail(entity.toString());
        JsonParser parser = new JsonParser();
        //status=1说明成功得到锚点sdk回执
        mgeTestResult.setSdkStatus(1);
        mgeTestResult.setSdkReportTime(entity.get("sdkReportTime").toString());
        mgeTestResult.setResultUpdateTime(new Date());
        mgeTestResultMapper.updateById(mgeTestResult);
        //根据刚才得到的caseid查到case详情
        MgeBaseCase mgeBaseCase = mgeBaseCaseService.findByCaseId(caseId);
        // 锚点sdk上报的数据详情
        JSONArray caseDetailArray = entity.getJSONArray("caseDetail");
        String testMessage = "校验通过";
        Integer status = 1;
        String business = mgeBaseCase.getBusiness();
        //-1代表时机上报错误，0代表漏报，1代表校验通过，2代表多报，3代表参数（bid、cid、val_lab、tag等）的value错误，
        // 4代表参数类型错误(目前没做)，5代表埋点上报类型错误，6代表锚点sdk返回数据有问题（目前没做如何评判锚点数据有问题？）,7代表其他异常
        //MVP只是校验猜喜广告卡片，只会请求1次接口
        if (business.equals("广告")) {
            logger.info("开始校验广告埋点：");
            //遍历 取出实时上报数据里的addResult集合
            JSONArray addArray = new JSONArray();
            for (int i = 0; i < caseDetailArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) caseDetailArray.get(i);
                addArray = jsonObject.getJSONArray("addResult");
            }
            //多报、漏报校验：
            //多上报了广告埋点
            if (addArray.size() > 1) {
                testMessage += "多上报了广告埋点";
                status = 2;
                mgeTestResult.setTestResult(status);
                mgeTestResult.setTestMessage(testMessage);
                mgeTestResultMapper.updateById(mgeTestResult);
                return;
            }
            if (addArray.size() <= 0) {
                testMessage += "没有上报广告埋点数据";
                status = 0;
                mgeTestResult.setTestResult(status);
                mgeTestResult.setTestMessage(testMessage);
                mgeTestResultMapper.updateById(mgeTestResult);
                return;
            }
            //参数校验：
            //只上报了1个广告埋点，匹配mapi接口数据:act=2代表MC，act=3代表MV，adidx就是卡片位置index
            if (mgeBaseCase.getType().equals("MV")) {
                if (addArray.getJSONObject(0).getString("data").contains("act%3D3")) {
                    logger.info("广告埋点曝光类型act上报没问题");
                } else {
                    testMessage += "act参数上报有误";
                    status = 3;
                }
            } else if (mgeBaseCase.getType().equals("MC")) {
                if (addArray.getJSONObject(0).getString("data").contains("act%3D2")) {
                    logger.info("广告埋点点击类型act上报没问题");
                } else {
                    testMessage += "act上报有误";
                    status = 3;
                }
            } else {
                testMessage += "广告埋点类型异常";
                status = 7;
            }
            if (mgeBaseCase.getModuleIndex() != null && mgeBaseCase.getModuleIndex() != "") {
                String addIndex = mgeBaseCase.getModuleIndex();
                if (addArray.getJSONObject(0).getString("data").contains("adidx%3D" + addIndex)) {
                    logger.info("广告埋点位置adidx上报正确");
                } else {
                    testMessage += "adidx参数上报有误";
                    status = 3;
                }
            } else {
                testMessage += "卡片index数据异常";
                status = 7;
            }
        }
        if (business.equals("平台")) {
            logger.info("开始校验灵犀埋点：");
            //判断锚点回调的是否是对应的bid数据
            //if (caseDetailArray.getJSONObject(0).getString("bidName").equals(mgeBaseCase.getBid())) {
            //基准数据组成的对象数组baseArray
            String mgeDetail = mgeBaseCase.getCaseDetail();
            JsonArray baseArray = parser.parse(mgeDetail).getAsJsonArray();
            Integer valCheck = mgeBaseCase.getValCheck();
            //遍历取出实时上报数据里的灵犀埋点数据result集合：array，[]、[{},{}]
            JSONArray array = new JSONArray();
            for (int i = 0; i < caseDetailArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) caseDetailArray.get(i);
                JSONArray currentResultArray = jsonObject.getJSONArray("result");
                for (int j = 0; j < currentResultArray.size(); j++) {
                    JSONObject object = (JSONObject) currentResultArray.get(j);
                    array.add(object);
                }
            }
            logger.info("实时上报数据的result集合：" + array.toString());
            if (array.size() == 0 && baseArray.size() == 0 && valCheck == 1) {//反向曝光case，此时都为空
                testMessage = "反向曝光case测试通过";
                mgeTestResult.setTestMessage(testMessage);
                mgeTestResult.setTestResult(1);
                mgeTestResultMapper.updateById(mgeTestResult);
                return;
            } else if (array.size() > 0 && baseArray.size() == 0 && valCheck == 1) { //反向曝光case，实时数据不为空，时机不对
                testMessage = "时机不对，此时不应该上报埋点";
                mgeTestResult.setTestMessage(testMessage);
                mgeTestResult.setTestResult(-1);
                mgeTestResultMapper.updateById(mgeTestResult);
                return;
            } else if (array.size() == 0) {
                testMessage = "该埋点未上报";
                mgeTestResult.setTestMessage(testMessage);
                mgeTestResult.setTestResult(0);
                mgeTestResultMapper.updateById(mgeTestResult);
                return;
            } else {
                for (int i = 0; i < array.size(); i++) {
                    JSONObject result = (JSONObject) array.get(i);
                    String baseType = mgeBaseCase.getType();
                    String resultType = result.getString("nm");
                    if (!resultType.equals(baseType)) {
                        testMessage = "埋点上报类型不匹配";
                        status = 5;
                        mgeTestResult.setTestResult(status);
                        mgeTestResult.setTestMessage(testMessage);
                        mgeTestResultMapper.updateById(mgeTestResult);
                        return;
                    }
                    String baseBid = mgeBaseCase.getBid();
                    String resultBid = result.getString("val_bid");
                    if (!resultBid.equals(baseBid)) {
                        testMessage = "bid不匹配";
                        status = 3;
                        mgeTestResult.setTestResult(status);
                        mgeTestResult.setTestMessage(testMessage);
                        mgeTestResultMapper.updateById(mgeTestResult);
                        return;
                    }
                    String baseCid = mgeBaseCase.getBid();
                    String resultCid = result.getString("val_cid");
                    if (resultCid.equals(baseCid)) {
                        testMessage = "cid不匹配";
                        status = 3;
                        mgeTestResult.setTestResult(status);
                        mgeTestResult.setTestMessage(testMessage);
                        mgeTestResultMapper.updateById(mgeTestResult);
                        return;
                    }
                }
                if (mgeBaseCase.getValCheck() == 1) {
                    //多报、漏报、时机校验：
                    if (array.size() > baseArray.size()) {
                        testMessage = "实际上报埋点存在多报";
                        status = 1;
                        mgeTestResult.setTestResult(status);
                        mgeTestResult.setTestMessage(testMessage);
                        mgeTestResultMapper.updateById(mgeTestResult);
                        return;
                    } else if (array.size() < baseArray.size()) { //测试数据为空,灵犀sdk埋点基准数据不为空
                        testMessage = "实际上报埋点存在漏报";
                        status = 0;
                        mgeTestResult.setTestResult(status);
                        mgeTestResult.setTestMessage(testMessage);
                        mgeTestResultMapper.updateById(mgeTestResult);
                        return;
                    } else {
                        //可能存在某个场景下，该bid会报5个数据，但是用户只关注其中2个报没报，所以只填了2个，那按照目前的逻辑永远都是校验错误
                        // 所以严格的个数匹配不能很灵活满足用户需求
                        logger.info("基准数据和实时上报数据个数匹配");
                    }
                    logger.info("*********开始校验具体参数************");
                    for (int j = 0; j < array.size(); j++) {
                        JsonObject baseResult = (JsonObject) baseArray.get(j);
                        for (int k = 0; k < baseArray.size(); k++) {
                            JSONObject result = (JSONObject) array.get(j);
                            //如果是MC类型的埋点，需要校验tag，注意：消息卡片有些mc埋点是不报tag的！！！
                            if (mgeBaseCase.getType().equals("MC")) {
                                JsonObject baseMgeTag = baseResult.get("tag").getAsJsonObject();
                                logger.info("MC埋点的baseTag：" + baseMgeTag.toString());
                                JsonObject resultMgeTag = parser.parse(result.getString("tag")).getAsJsonObject();
                                logger.info("MC埋点的resultTag：" + resultMgeTag.toString());
                                if (baseMgeTag.equals(resultMgeTag)) {
                                    logger.info("tag匹配");
                                } else {
                                    logger.info("tag不匹配");
                                    status = 3;
                                    continue;
                                }
                            }
                            //校验val_lab
                            JsonObject valLabBaseDetail = baseResult.get("val_lab").getAsJsonObject();
                            JsonObject valLabResultDetail = parser.parse(result.getString("val_lab")).getAsJsonObject();
                            if (valLabBaseDetail.equals(valLabResultDetail)) {
                                baseArray.remove(baseResult);
                                array.remove(result);
                                break;
                            } else {
                                logger.info("val_lab不匹配");
                                status = 3;
                                continue;
                            }
                        }
                    }
                    //被测对象遍历完后，检查被测数组和基准数组的长度，判断是多报还是少报
                    if (array.size() > 0 && baseArray.size() > 0) {
                        for (Object notMatchResult : array) {
                            testMessage += "以下埋点上报数据有误，未匹配到基准数据：" + notMatchResult.toString() + "\n";
                        }
                    }
                    if (array.size() > 0 && baseArray.size() == 0) {
                        for (Object unnecessaryResult : array) {
                            testMessage += "多报以下埋点上报数据" + unnecessaryResult.toString() + "\n";
                        }
                    }
                    if (array.size() == 0 && baseArray.size() > 0) {
                        for (Object missingResult : baseArray) {
                            testMessage += "少报以下埋点上报数据" + missingResult.toString() + "\n";
                        }
                    }
                }
            }
            mgeTestResult.setTestResult(status);
            mgeTestResult.setTestMessage(testMessage);
            mgeTestResultMapper.updateById(mgeTestResult);
            return;
        }
    }


    /**
     * UI自动化拿到云测任务id后的回调接口
     */
    public void uiJobCallBack(Long taskId, String conanJobId) {
        QueryWrapper queryWrapperTask = new QueryWrapper();
        queryWrapperTask.eq("task_id", taskId);
        MgeTestTask mgeTestTask = mgeTestTaskMapper.selectOne(queryWrapperTask);
        mgeTestTask.setStartTime(new Date());//云测任务开始执行，task才算开始，记录开始时间
        mgeTestTask.setConanJobId(conanJobId);
        //回调云测任务id，入库report_url
        String conanReportUrl = "https://conan.sankuai.com/v2/auto-function/report/".concat(conanJobId);
        mgeTestTask.setConanReport(conanReportUrl);
        mgeTestTaskMapper.updateById(mgeTestTask);
        //云测任务提交开始，conan_job_status开始，0为开始
        mgeTestTask.setConanJobStatus(0);
    }

    @Override
    public void deleteEntity(String[] ids) {
        List<Long> list = new ArrayList<>();
        for (String id : ids) {
            list.add(Long.valueOf(id));
        }
        this.removeByIds(list);
    }


    @Override
    public MgeCount count(String version) {
//        log.info("version>>>"+version);
        MgeCount count = mgeTestJobMapper.count(version);
//        log.info(count.toString());
        return count;

    }

    @Override
    public IPage<MgeTestJob> findList(MgeTestJob entity, QueryRequest queryRequest) {
        try {
            LambdaQueryWrapper<MgeTestJob> queryWrapper = new LambdaQueryWrapper<>();
            //queryWrapper.orderByDesc(MgeTestJob::getVersion);
            if (null != entity.getBusiness()) {
                queryWrapper.eq(MgeTestJob::getBusiness, entity.getBusiness());
            }
            if (null != entity.getVersion()) {
                queryWrapper.eq(MgeTestJob::getVersion, entity.getVersion());
            }
            if ("android".equals(entity.getOs()) || "ios".equals(entity.getOs())) {
                queryWrapper.eq(MgeTestJob::getOs, entity.getOs());
            }
            if (queryRequest.getSwitchStatus()) {
                queryWrapper.ne(MgeTestJob::getJobStatus, "1");
            }
            String version = queryRequest.getVersion();
            if (version != null && !version.equals("")) {
                queryWrapper.eq(MgeTestJob::getVersion, version);
            }
            queryWrapper.orderByDesc(MgeTestJob::getStartTime);
            Page<MgeTestJob> page = new Page<>(queryRequest.getPageNum(), queryRequest.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            logger.error("MgeTestJobServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public List<MgeTestJob> findList(MgeTestJob entity) {
        try {
            LambdaQueryWrapper<MgeTestJob> queryWrapper = getLambdaQueryWrapper(entity);
            List<MgeTestJob> list = this.list(queryWrapper);
            for (MgeTestJob domain : list) {
                getDetail(domain);
            }
            return list;
        } catch (Exception e) {
            logger.error("MgeTestJobServiceImpl findList查询异常", e);
            return null;
        }
    }

    @Override
    public MgeTestJob find(Long jobId) {
        MgeTestJob mgeTestJob = this.getById(jobId);
        return getDetail(mgeTestJob);
    }

    @Override
    public MgeTestJob find(MgeTestJob entity) {
        LambdaQueryWrapper<MgeTestJob> queryWrapper = getLambdaQueryWrapper(entity);
        queryWrapper.last("limit 1");
        return getDetail(this.getOne(queryWrapper));
    }

    @Override
    public void updateEntity(MgeTestJob entity) throws Exception {
        if (entity.getJobId() == null) {
            throw new Exception("id为null");
        }
        //entity.setUpdateTime(new Date());
        this.updateById(entity);
    }

    /**
     * 触发UI自动化任务
     */
    public String uiMethod(JsonObject mgeTestJob) {
        String env = "";
        if (EnvUtil.isOnline()) {
            env = "prod";
        } else {
            env = "test";
        }
        mgeTestJob.addProperty("env", env);
        jenkinsUtil.newMgeTestJob(mgeTestJob);
        return "success";
    }


    private LambdaQueryWrapper getLambdaQueryWrapper(MgeTestJob entity) {
        LambdaQueryWrapper<MgeTestJob> queryWrapper = new LambdaQueryWrapper<>();
        if (entity.getJobId() != null) {
            queryWrapper.eq(MgeTestJob::getJobId, entity.getJobId());
        }
       /* if (!StringUtil.isNullOrEmpty(entity.getCreateBy())){
            queryWrapper.eq(MgeTestJob::getCreateBy, entity.getCreateBy());
        }*/
        return queryWrapper;
    }

    private MgeTestJob getDetail(MgeTestJob entity) {
        if (entity == null) {
            return null;
        }
        return entity;
    }

    @Override
    public String getMgeCaseById(String jobid) {
        JSONArray caseArray = new JSONArray();
        JSONObject uiConanObject = new JSONObject();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("job_id", jobid);
        List<MgeTestTask> mgeTestTaskList = mgeTestTaskMapper.selectList(queryWrapper);
        for (int taskIndex = 0; taskIndex < mgeTestTaskList.size(); taskIndex++) {
            MgeTestTask mgeTestTask = mgeTestTaskList.get(taskIndex);
            caseArray.add(JSONObject.fromObject(mgeTestTask.getMgeCaseArray()));
        }
        uiConanObject.put("count", caseArray.size());
        uiConanObject.put("caseArray", caseArray);
        return uiConanObject.toString();
    }

    @Override
    public List<String> getAllVersions() {
        QueryWrapper<MgeTestJob> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<MgeTestJob> mgelist = mgeTestJobMapper.selectList(queryWrapper
                .ne("business", "Job")
                .groupBy("version").orderByDesc("start_time"));
        for (MgeTestJob mgeTestJob : mgelist) {
            list.add(mgeTestJob.getVersion());
        }
        return list;
    }


}
