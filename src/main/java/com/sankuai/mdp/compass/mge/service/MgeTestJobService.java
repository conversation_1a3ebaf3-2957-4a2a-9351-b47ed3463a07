package com.sankuai.mdp.compass.mge.service;


import com.baomidou.mybatisplus.extension.service.IService;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.mge.entity.MgeTestJob;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by lizhen39 on 2021/12/31.
 */
@Service
public interface MgeTestJobService extends IService<MgeTestJob> {

    public JsonObject startJob(JSONObject entity) throws Exception;

    public void mgeConanJobCallBack(JSONObject body);

    void resultCallBack(JSONObject entity);

    public void uiJobCallBack(Long taskId, String conanJobId);

    public void deleteEntity(String[] ids);

    public IPage<MgeTestJob> findList(MgeTestJob entity, QueryRequest queryRequest);

    MgeCount count(String version);

    public List<MgeTestJob> findList(MgeTestJob entity);

    public MgeTestJob find(Long jobId);

    public MgeTestJob find(MgeTestJob entity);

    public void updateEntity(MgeTestJob entity) throws Exception;

    public String getMgeCaseById(String jobid);

    List<String> getAllVersions();

}
