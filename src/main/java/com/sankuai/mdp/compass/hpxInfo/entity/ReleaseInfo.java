package com.sankuai.mdp.compass.hpxInfo.entity;/* *************
 * @author: liuYang359
 * @date: 2022/3/23 6:49 下午
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("HPX_merge_info")
public class ReleaseInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("os")
    private String plantForm;
    @TableField("branch")
    private String branch;
    @TableField("misId")
    private String misId;
    @TableField("resultTime")
    private String resultTime;


    @TableField("component")
    private String componentName;


    @TableField("versionNew")
    private String versionNew;

    @TableField("bgName")
    private String bgName;

    @TableField("buName")
    private String buName;

    @TableField("gitDiffUrl")
    private String gitDiffUrl;

    @TableField("QA")
    private String QA;
}
