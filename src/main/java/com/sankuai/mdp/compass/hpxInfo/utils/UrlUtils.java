package com.sankuai.mdp.compass.hpxInfo.utils;/* *************
 * @author: liuYang359
 * @date: 2022/3/29 10:50 上午
 * @description:
 */

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UrlUtils {
    public static String getURLEncoderString(String str, String type) {
        String result = "";
        if (null == str) {
            return "";
        }
        try {
            if (type == null || "".equals(type))
                result = java.net.URLEncoder.encode(str, "UTF-8");
            else
                result = java.net.URLEncoder.encode(str, type);
        } catch (UnsupportedEncodingException e) {
            log.error("url编码失败");
        }
        return result;
    }

    public static String URLDecoderString(String str, String type) {
        String result = "";
        if (null == str) {
            return "";
        }
        try {
            if (type == null || "".equals(type))
                result = java.net.URLDecoder.decode(str, "UTF-8");
            else
                result = java.net.URLDecoder.decode(str, type);
        } catch (UnsupportedEncodingException e) {
            log.error("url解码失败");
        }
        return result;
    }

    private static String TruncateUrlPage(String strURL) {
        String strAllParam = null;
        String[] arrSplit = null;
        strURL = strURL.trim().toLowerCase();
        arrSplit = strURL.split("[?]");
        if (strURL.length() > 1) {
            if (arrSplit.length > 1) {
                for (int i = 1; i < arrSplit.length; i++) {
                    strAllParam = arrSplit[i];
                }
            }
        }
        return strAllParam;
    }


    public static Map<String, String> urlSplit(String URL) {
        /*
         * @param URL:
         * @return: java.util.Map<java.lang.String,java.lang.String>
         * @author: liuyang359
         * @date: 2022/3/31 9:12 下午
         * @description:解析url中的参数返回map数组
         */
        Map<String, String> mapRequest = new HashMap<String, String>();
        String[] arrSplit = null;
        String strUrlParam = TruncateUrlPage(URL);
        if (strUrlParam == null) {
            return mapRequest;
        }
        arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = null;
            arrSplitEqual = strSplit.split("[=]");
            //解析出键值
            if (arrSplitEqual.length > 1) {
                //正确解析
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);
            } else {
                if (arrSplitEqual[0] != "") {
                    //只有参数没有值，不加入
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }
}