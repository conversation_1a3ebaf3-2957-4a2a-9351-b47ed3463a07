package com.sankuai.mdp.compass.hpxInfo.controller;/* *************
 * @author: liuYang359
 * @date: 2022/3/23 6:04 下午
 * @description:
 */


import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.hpxInfo.entity.ComponentDependency;
import com.sankuai.mdp.compass.hpxInfo.service.HpxInfoService;
import com.sankuai.mdp.compass.hpxInfo.service.impl.HpxInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.jute.compiler.JString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/compass/api/hpxinfo")
@Slf4j
public class HpxInfoController {
    @Autowired
    HpxInfoService hpxInfoService;

    @PostMapping("/list")
    public String gethpxinfo(
            @RequestBody
                    String data) {
        /*
         * @param data:请求bodu：json
         * @return: java.lang.String
         * @author: liuyang359
         * @date: 2022/3/24 11:27 上午
         * @description:hook 分支组件变动
         */
        log.info("hpx数据返回" + data);
        if (data != null && !"".equals(data))
            return hpxInfoService.getReleaseData(new JsonParser().parse(data).getAsJsonObject());
        else
            return "hpx返回为空";
    }

    @PostMapping("/buAdd")
    public String addBuList(@RequestBody String buName) {

        return hpxInfoService.addBuName(buName);
    }

    @PostMapping("/reInsertPullRequest")
    public String reInsertPullRequest(String startTime, String endTime) {
        hpxInfoService.reInsertPullRequest(startTime, endTime);
        return "任务开始触发";
    }

    @PostMapping("/new")
    public String newTest(
            @RequestBody String data){
        /*
         * @param data:请求body：json
         * @return: java.lang.String
         * @author: fengenci
         * @date: 2023/2/7 11:25 上午
         * @description: 组件集成触发自动化
         */
        log.info("hpx数据返回" + data);
        if (data != null && !"".equals(data))
            return hpxInfoService.newTest(new JsonParser().parse(data).getAsJsonObject()); //todo 调用方法获得本次变更组件
        else
            return "hpx返回为空";
    }

    @PostMapping("/update")
    public Resp update(ComponentDependency componentDependency){
        return hpxInfoService.updateAll(componentDependency);
    }

}
