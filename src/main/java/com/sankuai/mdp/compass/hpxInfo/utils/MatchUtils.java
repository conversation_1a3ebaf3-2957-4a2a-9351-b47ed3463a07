package com.sankuai.mdp.compass.hpxInfo.utils;/* *************
 * @author: liuYang359
 * @date: 2022/6/2 11:39 上午
 * @description:
 */

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MatchUtils {
    public static final String onesRegex="ones-([0-9]\\d*)|ONES-([0-9]\\d*)";
    public static final String pullRequestRegex="#([0-9]\\d*)";

    public  static String match(String data,String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(data);
        while (matcher.find()) {
            String group = matcher.group();
           return group;
        }
        return "";
    }

    public static  String getOnesId(String data){
        String matchResult = match(data,onesRegex);
        if("".equals(matchResult))
        return "";
        else
            return matchResult.split("-")[1];
    }
    public static  String getPullRequestid(String data)
    {
        String matchResult = match(data,pullRequestRegex);
        if("".equals(matchResult))
            return "";
        else
        return matchResult.split("#")[1];
    }

}
