package com.sankuai.mdp.compass.hpxInfo.entity;/* *************
 * @author: liuYang359
 * @date: 2022/3/31 9:47 下午
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("HPX_pull_requests_info")
public class PrInfo {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField("hpxId")
    private Integer hpxId;
    @TableField("prId")
    String prId;
    @TableField("prUrl")
    private String prUrl;
    @TableField("name")
    String name;
    @TableField("prRecivers")
    String prRecivers;
    @TableField("onesId")
    String onesId;
    @TableField("onesUrl")
    String onesUrl;
    @TableField("title")
    String title;
    @TableField("description")
    String description;
    @TableField("onesTitle")
    String onesTitle;
}
