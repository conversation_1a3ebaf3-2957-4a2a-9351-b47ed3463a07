package com.sankuai.mdp.compass.hpxInfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
@Data
@TableName("component_dependency")
public class ComponentDependency {

        @TableId(value = "id", type = IdType.AUTO)
        private Integer id;

        @TableField("os")
        private String os;

        @TableField("repository_url")
        private String repositoryUrl;

        @TableField("dependencies")
        private String dependencies;

        @TableField("component")
        private String componentName;

        @TableField("bu_name")
        private String buName;

        @TableField("branch")
        private String branch;

        @TableField("component_package")
        private String componentPackage;

}
