package com.sankuai.mdp.compass.hpxInfo.service;/* *************
 * @author: liuYang359
 * @date: 2022/3/23 6:46 下午
 * @description:
 */

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.hpxInfo.entity.ComponentDependency;

import java.util.List;

public interface HpxInfoService {
    String getReleaseData(JsonObject data);
    String addBuName(String buName);
    String reInsertPullRequest(String startTime, String endTime);

    Resp updateAll(ComponentDependency componentDependency);

    String newTest(JsonObject data);

    String generateTestPage(List<String> testComponents);

}