package com.sankuai.mdp.compass.hpxInfo.entity;/* *************
 * @author: liuYang359
 * @date: 2022/6/2 5:34 下午
 * @description:
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("HPX_ComplianceInfo")
public class ComplianceInfo {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField("hpxId")
    private Integer hpxId;
    @TableField("component")
    private String component;
    @TableField("isCompliance")
    private boolean isCompliance;
    @TableField("complianceReason")
    private String complianceReason;
    @TableField("commitIdList")
    private String commitIdList;

}
