package com.sankuai.mdp.compass.hpxInfo.utils;/* *************
 * @author: liuYang359
 * @date: 2022/3/30 8:14 下午
 * @description:
 */

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.HttpUtil;
import com.sankuai.mdp.compass.hpxInfo.entity.ReleaseInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HpxUtils {

    public static boolean isTargetBu(String data, String buList)
    {
        if(data ==null ||buList==null) {
            return false;
        }
        else
        {
            String[] strArray = buList.split(",");
            for (String s : strArray) {
                if (data.contains(s))
                    return true;
            }
        }
        return false ;
    }
    public static String getTargetBu(String data, String buList)
    {
        if(data ==null ||buList==null) {
            return "";
        }
        else
        {
            String[] strArray = buList.split(",");
            for (String s : strArray) {
                if (data.contains(s))
                    return s;
            }
        }
        return "" ;
    }
    public static String findNumber(String str)
    {
        /*
         * @param str:
         * @return: java.lang.String
         * @author: liuyang359
         * @date: 2022/4/1 9:13 下午
         * @description:匹配字符串最长数字
         */
        int max = 0,count=0,end=0;
        for(int i=0;i<str.length();i++){
            if(str.charAt(i)>='0' && str.charAt(i)<='9'){
                count++;
                if(max<count){
                    max= count;
                    end = i;
                }
            }
            else{
                count = 0;
            }
        }
        return str.substring(end-max+1,end+1);
    }



}
