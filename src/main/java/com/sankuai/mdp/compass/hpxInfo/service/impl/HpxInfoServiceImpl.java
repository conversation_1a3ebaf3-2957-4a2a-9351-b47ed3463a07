package com.sankuai.mdp.compass.hpxInfo.service.impl;/* *************
 * @author: liuYang359
 * @date: 2022/3/23 6:30 下午
 * @description:
 */


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.HttpUtil;
import com.sankuai.mdp.compass.common.utils.LionUtil;
import com.sankuai.mdp.compass.common.utils.StringUtil;
import com.sankuai.mdp.compass.common.utils.TokenUtil;
import com.sankuai.mdp.compass.hpxInfo.entity.*;
import com.sankuai.mdp.compass.hpxInfo.mapper.*;
import com.sankuai.mdp.compass.hpxInfo.service.HpxInfoService;
import com.sankuai.mdp.compass.hpxInfo.utils.HpxUtils;
import com.sankuai.mdp.compass.hpxInfo.utils.MatchUtils;
import com.sankuai.mdp.compass.hpxInfo.utils.UrlUtils;
import com.sankuai.mdp.compass.robust.entity.CommonPage;
import com.sankuai.mdp.compass.robust.mapper.CommonPageMapper;
import com.sankuai.mdp.compass.uiAutoTest.service.AutoTest;
import com.sankuai.mdp.compass.uiAutoTest.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class HpxInfoServiceImpl implements HpxInfoService {

    @Autowired
    ReleaseInfoMapper releaseInfoMapper;
    @Autowired
    BuInfoMapper buInfoMapper;
    @Autowired
    PrInfoMapper prInfoMapper;

    @Autowired
    ComplianceInfoMapper complianceInfoMapper;

    @Autowired
    ComponentDependencyMapper componentDependencyMapper;//新增组件依赖表

    @Autowired
    CommonPageMapper commonPageMapper;

    @Autowired
    AutoTest autoTest;

    @Override
    public String getReleaseData(JsonObject data) {
        /*
         * @param data:返回体
         * @return: java.lang.String
         * @author: liuyang359
         * @date: 2022/3/24 11:32 上午
         * @description:处理返回数据插入数据库
         */
        ReleaseInfo releaseInfo = new ReleaseInfo();
        int flag = 0;
        log.info("hpx接口返回:" + data.toString());
        if (data.has("buInfo")) {
            JsonObject buInfo = data.getAsJsonObject("buInfo");
            if (buInfo.has("bgName")) {
                releaseInfo.setBgName(buInfo.get("bgName").getAsString());
                flag++;
            }
            if (buInfo.has("buName")) {
                releaseInfo.setBuName(buInfo.get("buName").getAsString());
                flag++;
            }
        }
        if ("美团平台".equals(releaseInfo.getBgName())) {
            if (data.has("os")) {
                releaseInfo.setPlantForm(data.get("os").getAsString());
                flag++;
            }
            if (data.has("branch")) {
                releaseInfo.setBranch(data.get("branch").getAsString());
                flag++;
            }
            if (data.has("misId")) {
                releaseInfo.setMisId(data.get("misId").getAsString());
                flag++;
            }
            if (data.has("endTime")) {
                Date date = new Date(data.get("endTime").getAsLong() * 1000);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateString = formatter.format(date);
                releaseInfo.setResultTime(dateString);
                flag++;
            }
            int insertFlag = 0;
            if (data.has("paramInfo")) {
                JsonObject paramInfo = data.getAsJsonObject("paramInfo");
                JsonArray integrationList = paramInfo.getAsJsonArray("integrationList");
                for (int index = 0; index < integrationList.size(); index++) {
                    JsonObject jsonObjectIndex = integrationList.get(index).getAsJsonObject();
                    String name = jsonObjectIndex.get("name").getAsString();
                    releaseInfo.setComponentName(name);
                    releaseInfo.setVersionNew(jsonObjectIndex.get("version").getAsString());
                    if (data.has("artifactInfo")) {
                        JsonObject artifactInfo = data.get("artifactInfo").getAsJsonObject();
                        JsonObject component_version_changed_diffUrl = artifactInfo.get("component_version_changed_diffUrl").getAsJsonObject();
                        String gitDiffUrl = "";
                        if (component_version_changed_diffUrl.has(name))
                            gitDiffUrl = component_version_changed_diffUrl.get(name).getAsString();
                        //System.out.println("哈哈"+gitDiffUrl);
                        releaseInfo.setGitDiffUrl(gitDiffUrl);
                    }
                    //"action"
                    int tempFlag = 0;
                    String componentName = HttpUtil.vGet("https://hpx.sankuai.com/api/open/getProjectList?os=" + releaseInfo.getPlantForm() + "&componentName=" + releaseInfo.getComponentName(), TokenUtil.getToken("hpx_token"));
                    try {
                        JsonObject qainfo = new JsonParser().parse(componentName).getAsJsonObject();
                        JsonArray qainfodata = qainfo.get("data").getAsJsonArray();
                        for (int idata = 0; idata < qainfodata.size(); idata++) {

                            JsonArray qainfoList = qainfodata.get(idata).getAsJsonObject().getAsJsonArray("qaOwners");
                            for (int qaindex = 0; qaindex < qainfoList.size(); qaindex++) {
                                if (releaseInfo.getQA() != null)
                                    releaseInfo.setQA(releaseInfo.getQA() + "," + qainfoList.get(qaindex).getAsJsonObject().get("misId").getAsString());
                                else
                                    releaseInfo.setQA(qainfoList.get(qaindex).getAsJsonObject().get("misId").getAsString());
                            }
                        }
                    } catch (Exception E) {
                        log.info("插入qa负责人失败" + E.toString());
                    }
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("id", 1);
                    BuInfo buInfo = buInfoMapper.selectOne(queryWrapper);
                    if (HpxUtils.isTargetBu(componentName, buInfo.getBuNamelist())) {
                        if (HpxUtils.getTargetBu(componentName, buInfo.getBuNamelist()).equals("业务链路优化") || componentName.contains("LinkBetter"))
                            releaseInfo.setBuName("业务链路优化");
                        else
                            releaseInfo.setBuName(HpxUtils.getTargetBu(componentName, buInfo.getBuNamelist()));

                        tempFlag = releaseInfoMapper.insert(releaseInfo);
                        QueryWrapper queryWrapper1 = new QueryWrapper();
                        queryWrapper1.eq("resultTime", releaseInfo.getResultTime());
                        queryWrapper1.eq("misId", releaseInfo.getMisId());
                        queryWrapper1.eq("gitDiffUrl", releaseInfo.getGitDiffUrl());
                        queryWrapper1.eq("component", releaseInfo.getComponentName());
                        releaseInfo = releaseInfoMapper.selectOne(queryWrapper1);

                        if (tempFlag > 0) {
                            insertFlag++;
                            try {
                                getPullRuquest(releaseInfo);
                            } catch (Exception e) {
                                log.info(releaseInfo.getComponentName() + "组件pr插入报错");
                                e.printStackTrace();

                            }
                        }
                        log.info("平台业务组件");
                    } else {
                        log.info("非平台业务组件变更");
                    }

                }
            }
            if (flag > 0 && insertFlag != 0)
                return "插入成功";
            else
                return "插入失败或插入部分失败";
        } else
            return "非平台业务组件";

    }

    @Override
    public String addBuName(String buName) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", 1);
        BuInfo buInfo = buInfoMapper.selectOne(queryWrapper);
        if (buInfo != null && buName != null) {
            buInfo.setBuNamelist(buInfo.getBuNamelist() + "," + buName);
            buInfoMapper.update(buInfo, queryWrapper);
            return "修改成功";
        } else if (buInfo == null && buName != null) {
            buInfo = new BuInfo();
            buInfo.setBuNamelist(buName);
            buInfo.setId(1);
            buInfoMapper.insert(buInfo);
            return "插入成功";
        } else {
            return "参数错误";
        }

    }

    @Async
    public boolean getPullRuquest(ReleaseInfo releaseInfo) {
        Map<String, String> tag = gitcommitTag(releaseInfo);
        String gitCommitIdSource = "";
        String gitCommitIdTarget = "";
        gitCommitIdSource = tag.get("gitCommitIdSource");
        gitCommitIdTarget = tag.get("gitCommitIdTarget");
        String group = "";
        String gitName = "";
        group = tag.get("group");
        gitName = tag.get("gitName");
        ComplianceInfo complianceInfo = new ComplianceInfo();
        //根据id拿到tag
        if (!"".equals(gitCommitIdSource) && !"".equals(gitCommitIdTarget)) {
            String commitUrl = "https://dev.sankuai.com/rest/api/2.0/projects/" + group + "/repos/" + gitName + "/commits?since=" + gitCommitIdTarget + "&until=" + gitCommitIdSource + "&start=0&limit=100&withIssues=true";
            String commitInfo = HttpUtil.vGet(commitUrl, TokenUtil.getToken("codeToken"));
            JsonObject gitCommitInfoMessage = new JsonParser().parse(commitInfo).getAsJsonObject();
            JsonArray gitCommitInfoMessageVlue = gitCommitInfoMessage.get("values").getAsJsonArray();
            //拿到diff之间所有的commit
            boolean isCompliance = false;
            Set<String> commitIdSet = new <String>HashSet();
            List<JsonObject> PRJOList = new ArrayList<JsonObject>();
            for (int index = 0; index < gitCommitInfoMessageVlue.size(); index++) {
                PrInfo prInfo = new PrInfo();
                String message = gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("message").getAsString();
                String strpullId = MatchUtils.getPullRequestid(message);
                try {// 过滤merge 版本号自动升级
                    if (!(gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("author").getAsJsonObject().get("name").getAsString().equals("ios_stash_bot") ||
                            gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("author").getAsJsonObject().get("name").getAsString().equals("aimeituan") ||
                            gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("message").getAsString().contains("Update version") ||
                            gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("message").getAsString().contains("版本号自动升级") ||
                            gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("message").getAsString().contains("Merge")))
                        commitIdSet.add(gitCommitInfoMessageVlue.get(index).getAsJsonObject().get("id").getAsString());
                    //添加commit 属组
                } catch (Exception e) {
                    log.error("获得提交id失败");
                    e.printStackTrace();
                }
                if (!"".equals(strpullId)) {
                    isCompliance = true;
                    prInfo.setHpxId(releaseInfo.getId());
                    prInfo.setPrId(strpullId);
                    if (prInfo.getPrId() != null) {
                        String prurl = "https://dev.sankuai.com/rest/api/2.0/projects/" + group + "/repos/" + gitName + "/pull-requests/" + prInfo.getPrId();
                        String prre = HttpUtil.vGet(prurl, TokenUtil.getToken("codeToken"));
                        JsonObject prreInfo = new JsonParser().parse(prre).getAsJsonObject();
                        PRJOList.add(prreInfo);
                        prInfo.setTitle(prreInfo.get("title").getAsString());
                        prInfo.setDescription(prreInfo.get("description").getAsString());
                        prInfo.setName(prreInfo.get("author").getAsJsonObject().get("user").getAsJsonObject().get("name").getAsString());
                        JsonArray reviewers = prreInfo.getAsJsonArray("reviewers");
                        for (int j = 0; j < reviewers.size(); j++) {
                            if (reviewers.get(j).getAsJsonObject().get("approved").getAsBoolean()) {
                                if ("".equals(prInfo.getPrRecivers()) || prInfo.getPrRecivers() == null) {
                                    prInfo.setPrRecivers(reviewers.get(j).getAsJsonObject().get("user").getAsJsonObject().get("name").getAsString());

                                } else {
                                    prInfo.setPrRecivers(prInfo.getPrRecivers() + "," + reviewers.get(j).getAsJsonObject().get("user").getAsJsonObject().get("name").getAsString());
                                }
                            }
                        }
                        if (prreInfo.has("title")) {
                            String onesNum = MatchUtils.getOnesId(prreInfo.get("title").getAsString());
                            if ("".equals(onesNum))
                                if (prreInfo.has("description")) {
                                    onesNum = MatchUtils.getOnesId(prreInfo.get("description").getAsString());
                                }
                            if ("".equals(onesNum)) {
                                onesNum = HpxUtils.findNumber(prreInfo.get("description").getAsString());
                            }
                            if (onesNum != null && !"".equals(onesNum)) {
                                prInfo.setOnesId(onesNum);
                                String onesUrl = "https://ones.sankuai.com/api/1.0/ones/issue/" + onesNum;
                                String onesInfo = HttpUtil.vGet(onesUrl, TokenUtil.getToken("ones_token"));
                                JsonObject onesInfoResponse = new JsonParser().parse(onesInfo).getAsJsonObject();
                                onesInfoResponse = onesInfoResponse.getAsJsonObject("data");
                                if (onesInfoResponse != null) {
                                    if (onesInfoResponse.has("projectId")) {
                                        prInfo.setOnesUrl("https://ones.sankuai.com/ones/product/" + onesInfoResponse.get("projectId").getAsString() + "/workItem/" + onesInfoResponse.get("type").getAsString().toLowerCase(Locale.ROOT) + "/detail/" + onesNum);
                                        if (onesInfoResponse.has("name"))
                                            prInfo.setOnesTitle(onesInfoResponse.get("name").getAsString());
                                    }
                                }
                            }

                        }
                        if (prInfo.getPrId() != null)
                            prInfo.setPrUrl("https://dev.sankuai.com/code/repo-detail/" + group + "/" +
                                    gitName + "/pr/" + prInfo.getPrId() + "/overview");
                        prInfoMapper.insert(prInfo);


                    }

                }

            }
            try {
                commitIdSet = removePullRequestCommitId(commitIdSet, PRJOList, group, gitName);
                if (!commitIdSet.isEmpty()) {
                    isCompliance = true;
                    complianceInfo.setComponent(releaseInfo.getComponentName());
                    complianceInfo.setHpxId(releaseInfo.getId());
                    complianceInfo.setComplianceReason("存在有直接push的commit");
                    complianceInfo.setCompliance(false);
                    complianceInfo.setCommitIdList(commitIdSet.toString().substring(1, commitIdSet.toString().length() - 1));
                    complianceInfoMapper.insert(complianceInfo);
                }
                if (!isCompliance) {

                    complianceInfo.setComponent(releaseInfo.getComponentName());
                    complianceInfo.setHpxId(releaseInfo.getId());
                    complianceInfo.setComplianceReason("PR获取失败");
                    complianceInfo.setCompliance(false);
                    complianceInfoMapper.insert(complianceInfo);
                }


            } catch (Exception E) {
                log.error("commitId失败");
                E.printStackTrace();
            }

        }
        return false;
    }

    @Async
    public String reInsertPullRequest(String startTime, String endTime) {
        QueryWrapper queryWrapper = new QueryWrapper();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = sdf.format(date);
        queryWrapper.between("resultTime", startTime, endTime);
        List<ReleaseInfo> releaseInfolList = releaseInfoMapper.selectList(queryWrapper);
        int insertNum = 0;
        int failNum = 0;
        if (releaseInfolList != null) {
            for (ReleaseInfo index : releaseInfolList) {
                try {
                    getPullRuquest(index);
                    log.info(index.getComponentName() + " " + index.getResultTime() + "插入成功");
                    insertNum++;
                } catch (Exception E) {
                    failNum++;
                    log.info(index.getComponentName() + " " + index.getResultTime() + "插入失败");
                }
            }
        } else
            return "相关时间内没有数据";
        return "插入成功" + insertNum + "条" + "\n插入失败" + failNum + "条";
    }


    public static Map<String, String> gitcommitTag(ReleaseInfo releaseInfo) {
        String gitUrl = releaseInfo.getGitDiffUrl();
        //获得diff 链接
        gitUrl = gitUrl.substring(40);
        String[] gitUrlList = gitUrl.split("/");
        String group = gitUrlList[0];
        String gitName = gitUrlList[1];
        //获得仓库组名和仓库名
        String gitCommitUrl = "https://dev.sankuai.com/rest/api/2.0/projects/" + group + "/repos/" + gitName + "/tags?start=0&limit=100&orderBy=MODIFICATION";
        String temp = HttpUtil.vGet(gitCommitUrl, TokenUtil.getToken("codeToken"));
        JsonObject gitCommitInfo = new JsonParser().parse(temp).getAsJsonObject();
        String gitCommitIdSource = "";
        String gitCommitIdTarget = "";
        String diffURL = UrlUtils.URLDecoderString(releaseInfo.getGitDiffUrl(), "");
        Map<String, String> params = UrlUtils.urlSplit(diffURL);
        JsonArray gitCommitInfoVlue = gitCommitInfo.get("values").getAsJsonArray();
        for (int index = 0; index < gitCommitInfoVlue.size(); index++) {
            if (gitCommitInfoVlue.get(index).getAsJsonObject().get("id").getAsString().equals(params.get("source")))
                gitCommitIdSource = gitCommitInfoVlue.get(index).getAsJsonObject().get("latestCommit").getAsString();
            if (gitCommitInfoVlue.get(index).getAsJsonObject().get("id").getAsString().equals(params.get("target")))
                gitCommitIdTarget = gitCommitInfoVlue.get(index).getAsJsonObject().get("latestCommit").getAsString();
            if (!gitCommitIdSource.equals("") && !gitCommitIdTarget.equals(""))
                break;
        }
        Map<String, String> result = new HashMap<>();
        result.put("gitCommitIdSource", gitCommitIdSource);
        result.put("gitCommitIdTarget", gitCommitIdTarget);
        result.put("group", group);
        result.put("gitName", gitName);
        return result;
    }

    public Set<String> removePullRequestCommitId(Set<String> commitList, List<JsonObject> PRInfoList, String group, String gitName) {

        for (JsonObject data : PRInfoList) {
            String gitCommitIdTarget = data.get("fromRef").getAsJsonObject().get("latestChangeset").getAsString();
            String gitCommitIdSource = data.get("toRef").getAsJsonObject().get("latestChangeset").getAsString();
            String commitUrl = "https://dev.sankuai.com/rest/api/2.0/projects/" + group + "/repos/" + gitName + "/commits?since=" + gitCommitIdSource + "&until=" + gitCommitIdTarget + "&start=0&limit=100&withIssues=true";
            String commitInfo = HttpUtil.vGet(commitUrl,TokenUtil.getToken("codeToken"));
            commitInfo = commitInfo.replace("\"", " ");
            String finalCommitInfo = commitInfo;
            commitList.removeIf(o -> finalCommitInfo.contains(o));
        }
        return commitList;
    }

    @Override
    public Resp updateAll(ComponentDependency componentDependency){
        Integer id = componentDependency.getId();
        ComponentDependency componentDependency1 = componentDependencyMapper.selectById(id);
        if (componentDependency1 != null){
            QueryWrapper<ComponentDependency> queryWrapper = new QueryWrapper<>();
            List<ComponentDependency> AllData = componentDependencyMapper.selectList(queryWrapper);
            List<String> AllComponents = new ArrayList<>();
            for(int i = 0; i < AllData.size(); i++){
                AllComponents.add(AllData.get(i).getComponentName());
            }

            AllComponents.add(componentDependency.getComponentName());
            String os = componentDependency.getOs();
            String repositoryUrl = componentDependency.getRepositoryUrl();
            String[] dependenciesList = componentDependency.getDependencies().replace("\"", "").split(System.lineSeparator());
            List<String> dependencies = new  ArrayList<>();
            for (String dependency : dependenciesList){
                if(AllComponents.contains(dependency)){
                    dependencies.add(dependency);
                }
            }

            String componentName = componentDependency.getComponentName();
            String bu_name = componentDependency.getBuName();
            String branch = componentDependency.getBranch();
            String componentPackage = componentDependency.getComponentPackage();
            String dependenciesStr = "";

            if(dependencies.size() != 0){
                dependenciesStr = StringUtil.join(dependencies, ",");
            }
            componentDependency1.setDependencies(dependenciesStr);
            componentDependency1.setComponentName(componentName);
            componentDependency1.setComponentPackage(componentPackage);
            componentDependency1.setOs(os);
            componentDependency1.setBranch(branch);
            componentDependency1.setRepositoryUrl(repositoryUrl);
            componentDependency1.setBuName(bu_name);
            componentDependencyMapper.updateById(componentDependency1);}
        else {
            componentDependencyMapper.insert(componentDependency);
            updateAll(componentDependency);//新增数据重新更新依赖
        }

        return  Resp.success();
    }

    @Override
    public String newTest(JsonObject data){
        /*
         * @param data:返回体
         * @return: java.lang.String
         * @author: fengenci
         * @date: 2023/2/7 17:17 下午
         * @description:新增接口用于组件集成触发自动化，与数据处理解耦
         */

        ReleaseInfo releaseInfo = new ReleaseInfo();
        LionUtil lionUtil = new LionUtil();
        String BuNameList = new LionUtil().getValue("BuNameList");
        boolean uiautotestflg = true;
        //ui自动化频次开关一次组件集成只触发一次job

        String apkURL = "";
        if (data.has("buInfo")) {
            JsonObject buInfo = data.getAsJsonObject("buInfo");
            if (buInfo.has("bgName")) {
                releaseInfo.setBgName(buInfo.get("bgName").getAsString());
            }
            if (buInfo.has("buName")) {
                releaseInfo.setBuName(buInfo.get("buName").getAsString());
            }
        }
        if ("美团平台".equals(releaseInfo.getBgName())) {
            if (data.has("os")) {
                releaseInfo.setPlantForm(data.get("os").getAsString());
            }
            }
        if (data.has("artifactInfo")) {
            JsonObject artifactInfo = data.get("artifactInfo").getAsJsonObject();
            apkURL = JsonUtil.getString(artifactInfo, "app_download_url");
        }
        List<ReleaseInfo> releases = new ArrayList<ReleaseInfo>();
        List<String> testComponents = new ArrayList<String>();//需要测试的组件
            if (data.has("paramInfo")) {
                JsonObject paramInfo = data.getAsJsonObject("paramInfo");
                JsonArray integrationList = paramInfo.getAsJsonArray("integrationList");
                for (int index = 0; index < integrationList.size(); index++) { //todo:改写变更组件触发时机，直接将获取组件存为List<String> 遍历查表判断是否为平台业务，之后查页面传出触发自动化测试。
                    JsonObject jsonObjectIndex = integrationList.get(index).getAsJsonObject();
                    String name = jsonObjectIndex.get("name").getAsString();//package

                    //"action"
                    String ProjectDatas = HttpUtil.vGet("https://hpx.sankuai.com/api/open/getProjectList?os=" + releaseInfo.getPlantForm() + "&componentName=" + name, TokenUtil.getToken("hpx_token"));

                    if (HpxUtils.isTargetBu(ProjectDatas, BuNameList)) {
                        if (HpxUtils.getTargetBu(ProjectDatas, BuNameList).equals("业务链路优化") || ProjectDatas.contains("LinkBetter"))
                            releaseInfo.setBuName("业务链路优化");
                        else
                            releaseInfo.setBuName(HpxUtils.getTargetBu(ProjectDatas, BuNameList));

                        JsonObject projInfo = new JsonParser().parse(ProjectDatas).getAsJsonObject();
                        JsonArray projInfoArray = projInfo.get("data").getAsJsonArray();
                        releaseInfo.setComponentName(projInfoArray.get(0).getAsJsonObject().get("name").getAsString());
                        testComponents.add(releaseInfo.getComponentName());
                        releases.add(releaseInfo);
                    }

                }
                String testPage = generateTestPage(testComponents);
                log.info("本次集成平台组件：" + testComponents);
                log.info("本次测试页面：" + testPage);
                if (!testPage.isEmpty()){
                    for (int i = 0; i < testComponents.size(); i++){
                        try {
                            if (releaseInfo.getBuName() != null && lionUtil.getBooleanValue("uiautoTestbuild") && uiautotestflg) {
                                if (releaseInfo.getBuName().equals("美团平台业务")) {
                                    boolean buildflag = false;
                                    if (releaseInfo.getPlantForm().equals("ios")) {
                                        autoTest.build(apkURL, releaseInfo.getPlantForm(), testPage);
                                        buildflag = true;
                                    } else if (releaseInfo.getPlantForm().equals("android")) {
                                        autoTest.build(apkURL, releaseInfo.getPlantForm(), testPage);
                                        buildflag = true;
                                    }
                                    if (buildflag && uiautotestflg)
                                        uiautotestflg = false;
                                }
                            }
                        } catch (Exception E) {
                            log.error("触发ui自动化异常" + E.getMessage());
                        }
                    }
                    return  "success";
                }
                else {
                    return "本次集成无测试页面";
                }

            }
            return "非平台业务组件";
    }

    public  String generateTestPage(List<String> testComponents){
        List<Integer> ids = new ArrayList<>();
        List<String> testPage = new ArrayList<>();

        for (int i = 0; i < testComponents.size(); i++){
            QueryWrapper<ComponentDependency> queryWrapper1 = new QueryWrapper<>();
            String testComponent = testComponents.get(i);
            List<ComponentDependency> id = componentDependencyMapper.selectList(queryWrapper1.select("id").eq("component", testComponent).or().like("dependencies", testComponent));
            System.out.println("id: " + id);
            for(int j = 0; j < id.size();  j++){
                int componentId = id.get(j).getId();
                if(!ids.contains(componentId)){
                    ids.add(componentId);
                }
            }
        }

        for (int k = 0; k < ids.size(); k++){
            QueryWrapper<CommonPage> queryWrapper2 = new QueryWrapper<>();
            List<CommonPage> commonPages= commonPageMapper.selectList(queryWrapper2.select("page_description").eq("Android_component_id", ids.get(k)).or().eq("ios_component_id", ids.get(k)));
            for (int j = 0; j<commonPages.size(); j++){
                String page=commonPages.get(j).getPageDescription();
                if(page != null){
                    testPage.add(page);
                }
            }

        }

        return chToEn(testPage);
    }

    public String chToEn(List<String> pages){
        HashMap map = new HashMap();
        map.put("首页", "homePage");
        map.put("个人中心", "minePage");
        map.put("消息", "messagePage");
        map.put("直达页", "newcomerPage");
        map.put("订单列表页", "orderPage");
        map.put("设置页", "settingPage");
        map.put("购物车", "shoppingcartPage");
        map.put("账号", "accountPage");
        map.put("足迹", "recentviewPage");

        List<String> testPageEnglish = new ArrayList<String>();
        for (int i = 0; i < pages.size(); i++) {
            testPageEnglish.add((String) map.get(pages.get(i)));
        }
        return String.join(",", testPageEnglish);
    }
}
