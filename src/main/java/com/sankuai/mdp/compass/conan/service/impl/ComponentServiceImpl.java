package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.kenai.jffi.ObjectParameterInfo;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.HpxUtil;
import com.sankuai.mdp.compass.conan.entity.Component;
import com.sankuai.mdp.compass.conan.mapper.ComponentMapper;
import com.sankuai.mdp.compass.conan.service.ComponentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by xieyongrui on 2020/6/12.
 */
@Slf4j
@Service
public class ComponentServiceImpl extends ServiceImpl<ComponentMapper,Component> implements ComponentService{
    @Autowired
    ComponentMapper componentMapper;
    @Override
    public Boolean add(JsonObject body) {
        try {
            System.out.print(body);
            JsonObject map = body.get("payload").getAsJsonObject();
            JsonArray compList = (JsonArray) map.get("selectComponentList");
            String category = map.get("selectCategoryList").getAsString();
            String assignee = map.get("assignee").getAsString();
            String platform = map.get("platform").getAsString();
            log.info(compList.toString());
            for (int i = 0; i < compList.size(); i++) {
                Component component =  new Component();
                String name = compList.get(i).getAsString();
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("name",name);
                queryWrapper.eq("platform",platform);
                Component old = componentMapper.selectOne(queryWrapper);
                component.setName(name);
                component.setAssignee(assignee);
                component.setCategory(category);
                component.setPlatform(platform);
                if (null != old) {
                    componentMapper.update(component,queryWrapper);
                } else {
                    componentMapper.insert(component);
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void update(Component component) {

    }

    @Override
    public void delete(Component component) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("name",component.getName());
        queryWrapper.eq("platform",component.getPlatform());
        try {
            componentMapper.delete(queryWrapper);
        } catch (Exception e) {

        }
    }

    @Override
    public IPage<Component> list(QueryRequest request, Component component) {
        try {
            LambdaQueryWrapper<Component> queryWrapper = new LambdaQueryWrapper<>();
            if (null != component.getPlatform() && "" != component.getPlatform()) {
                queryWrapper.eq(Component::getPlatform,component.getPlatform());
            }
            Page<Component> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public List<String> hpxComp(String platform) {
        HpxUtil hpxUtil = new HpxUtil();
        try {
            return hpxUtil.getCompList(platform);
        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public String searchCategory(String comp) {
        try {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("name", comp);
            Component component = componentMapper.selectOne(queryWrapper);
            if (component != null) {
                return component.getCategory();
            } else {
                return "";
            }
        } catch (Exception e) {
//            log.info("查询模块失败",e);
            return "";
        }
    }
}
