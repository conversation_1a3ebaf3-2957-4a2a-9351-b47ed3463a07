package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.InterfaceCaseAndTask;
import com.sankuai.mdp.compass.conan.entity.InterfaceTask;
import com.sankuai.mdp.compass.conan.service.InterfaceTaskService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * Created by xubangxi on 2020/6/21.
 */
@RestController
@RequestMapping("/compass/api/autotest/interfaceTask")
public class InterfaceTaskController extends BaseController {
    @Autowired
    InterfaceTaskService interfaceTaskService;

    @GetMapping("/list")
    public Map<String, Object> list(String jobId, QueryRequest request, InterfaceTask interfaceTask) {
        IPage<InterfaceTask> CompatilityJobIPage = this.interfaceTaskService.list(jobId, request, interfaceTask);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }
    @GetMapping("/listAll")
    public Map<String, Object> listAll(String jobId, QueryRequest request, InterfaceTask interfaceTask) {
        IPage<InterfaceCaseAndTask> CompatilityJobIPage = this.interfaceTaskService.getCaseAndTask(jobId, request, interfaceTask);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/getAllPrincipal")
    public List<String> getAllPrincipal() {
        return interfaceTaskService.getAllPrincipal();
    }

    @GetMapping("/getAllVersion")
    public List<String> getAllVersions() {
        return interfaceTaskService.getAllVersions();
    }

    //云测回调接口
    @PostMapping("/result")
    public void collectResult(@RequestBody JSONObject body) throws ParseException {
        interfaceTaskService.collectResultNew(body);
    }
    @PostMapping("/updateResult")
    public void updateResult(@RequestBody JSONObject body) throws ParseException {
        interfaceTaskService.updateResult(body);
    }
}
