package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Business;
import com.sankuai.mdp.compass.conan.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/6/15.
 */
@RestController
@RequestMapping("/compass/api/businessCategory")
public class BusinessController extends BaseController{
    @Autowired
    BusinessService businessService;

    @GetMapping("/list")
    public List<String> list(QueryRequest request, Business business)  {
        List<String> result = businessService.list(request,business);
        if (result != null) {
            return result;
        } else {
            return null;
        }
    }

    @PostMapping("/add")
    public Boolean add(Business business) {
        return businessService.add(business);
    }
}
