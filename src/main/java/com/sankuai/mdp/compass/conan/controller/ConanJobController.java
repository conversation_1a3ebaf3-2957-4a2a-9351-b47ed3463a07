package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.ConanJob;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.conan.service.InterfaceTaskService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/4/1.
 */
@RestController
@RequestMapping("/compass/api/autotest/job")
public class ConanJobController extends BaseController {
    @Autowired
    ConanJobService conanJobService;
    @Autowired
    InterfaceTaskService interfaceTaskService;

    @PostMapping("/add")
    public void add(Conan<PERSON>ob conanJob) {
        conanJobService.add(conanJob);
    }

    //云测回调接口
    @PostMapping("/result")
    public void collectResult(@RequestBody JSONObject body) {
        conanJobService.collectResult(body);
    }

    //自定义获取云测job结果
    @PostMapping("/taskAndCaseResultByDate")
    public void collectResultByDate(@RequestBody JsonObject body) {
        conanJobService.collectTaskAndCaseByJobId(body);
    }

    //自定义统计UI Crash结果
    @PostMapping("/collectUICrash")
    public void collectCrash(@RequestBody JSONObject body) {
        conanJobService.collectUICrash(body.get("startTime").toString(),body.get("endTime").toString());
    }
    //获得connanJob信息
    @GetMapping("/listInterface")
    public Map<String, Object> list(QueryRequest request, ConanJob conanJob) {
        IPage<ConanJob> CompatilityJobIPage = this.conanJobService.listInterface(request, conanJob);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }
    //云测回调ui自动化+接口自动化接口
    @PostMapping("/resultInterUI")
    public void collectResultInterUI(@RequestBody JSONObject body) throws ParseException {
        interfaceTaskService.collectResult(body);
    }

    //获得自动化跑出的问题个数
    @GetMapping("/collectCrashCount")
    public Map<String, Object> collectCrashCount(String startTime, String endTime,String jobType,String platform,String buildScene) {
        return conanJobService.collectCrashCount(startTime,  endTime, jobType,platform,buildScene);
    }

    //触发test_hook job
    @PostMapping("/triggerTestHook")
    public void triggerTestHook(@RequestBody JSONObject body) {
        conanJobService.triggerTestHook(body);
    }
}

