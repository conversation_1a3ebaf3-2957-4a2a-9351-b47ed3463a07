package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Interface;
import com.sankuai.mdp.compass.conan.service.InterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by xubangxi on 2020/6/21.
 */
@RestController
@RequestMapping("/compass/api/autotest/interface")
public class InterfaceController extends BaseController {
    @Autowired
    InterfaceService interfaceService;

    @GetMapping("/get")
    public String get() {
        return "true";
    }

    @PostMapping("/update")
    public void update(Interface interface1) {
        interfaceService.update(interface1);
    }

    @PostMapping("/add")
    public void add(Interface interface1) throws IOException {
        interfaceService.add(interface1);
    }


    @PostMapping("/delete")
    public void delete(Interface interface1) {
        interfaceService.delete(interface1);
    }

    @GetMapping("/select")
    public List<Interface> select() {
        return interfaceService.select();
    }
    @GetMapping("/getAllApi")
    public List<String> getAllApi() {
        return interfaceService.getAllApi();
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, Interface interface1) {
        interfaceService.updateContent();
        IPage<Interface> CompatilityJobIPage = this.interfaceService.list(request, interface1);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }
}
