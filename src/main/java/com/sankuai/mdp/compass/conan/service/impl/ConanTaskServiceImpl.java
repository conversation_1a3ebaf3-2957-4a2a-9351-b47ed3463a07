package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.conan.entity.ConanTask;
import com.sankuai.mdp.compass.conan.mapper.ConanTaskMapper;
import com.sankuai.mdp.compass.conan.service.ConanTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xieyongrui on 2020/4/2.
 */
@Slf4j
@Service
public class ConanTaskServiceImpl extends ServiceImpl<ConanTaskMapper,ConanTask> implements ConanTaskService{
    @Autowired
    ConanTaskMapper conanTaskMapper;

    @Override
    public void add(ConanTask conanTask) {
    }

    @Override
    public void insertObject(JsonObject jsonObject) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            ConanTask conanTask = new ConanTask();
            conanTask.setTaskId(jsonObject.get("id").getAsInt());
            conanTask.setJobId(jsonObject.get("jobId").getAsString());
            conanTask.setStatus(jsonObject.get("status").getAsInt());
            conanTask.setStatusStr(jsonObject.get("statusStr").getAsString());
            conanTask.setDeviceModel(jsonObject.get("deviceModel").getAsString());
            conanTask.setDeviceVersion(jsonObject.get("deviceVersion").getAsString());
            conanTask.setStartTime(sdf.parse(jsonObject.get("startTime").getAsString()));
            conanTask.setEndTime(sdf.parse(jsonObject.get("endTime").getAsString()));
            conanTask.setTotalTime(jsonObject.get("totalTime").getAsFloat());
            conanTask.setSerialNumber(jsonObject.get("serialNumber").getAsString());
            conanTaskMapper.insert(conanTask);
        } catch (Exception e) {
//            log.error("插入conanTask失败",e);
        }
    }

    @Override
    public ConanTask selectOneTask(String taskId){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("task_id", taskId);
        List<ConanTask> tasklist = (ArrayList) conanTaskMapper.selectList(queryWrapper);
        if(!tasklist.isEmpty()){
            return tasklist.get(0);
        }
        return null;
    }
}
