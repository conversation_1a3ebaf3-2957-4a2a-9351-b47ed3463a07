package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.InterfaceCaseAndTask;
import com.sankuai.mdp.compass.conan.entity.InterfaceTask;
import net.minidev.json.JSONObject;

import java.text.ParseException;
import java.util.List;

public interface InterfaceTaskService {
    void add(InterfaceTask interfaceTask);
    void collectResult(JSONObject body) throws ParseException;
    public IPage<InterfaceTask> list(String jobId,QueryRequest request, InterfaceTask interfaceTask);
    public List<String> getAllPrincipal();
    public List<String> getAllVersions();
    public IPage<InterfaceCaseAndTask> getCaseAndTask(String jobId,QueryRequest request, InterfaceTask interfaceTask);
    void updateResult(JSONObject body) throws ParseException;
    void collectResultNew(JSONObject body) throws ParseException;
}
