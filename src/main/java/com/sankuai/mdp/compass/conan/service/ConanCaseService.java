package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.conan.entity.ConanCase;
import com.sankuai.mdp.compass.conan.entity.GroupCase;

import java.util.List;

/**
 * Created by xieyongrui on 2020/4/2.
 */
public interface ConanCaseService extends IService<ConanCase>{
    void add(ConanCase conanCase);
    void insetObject(JsonObject jsonObject);
    List<GroupCase> getPassRateLowerCase(String startTime,String endTime,String platform);
    List<Integer> getFailedCasesTaskIdList(String startTime,String platform,String caseName,String className);
    ConanCase selectOneCase(String caseId);
}
