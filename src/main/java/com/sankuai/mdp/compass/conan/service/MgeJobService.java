package com.sankuai.mdp.compass.conan.service;

import com.sankuai.mdp.compass.conan.entity.MgeJob;

import java.text.ParseException;

public interface MgeJobService {
    int setJobStart();

    void setJobEnd(int status, int jobId);

    void add(MgeJob mgeJob);

    void update(MgeJob mgeJob);

    void updateForMge(int jobId, String qid);

    boolean checkRerun() throws ParseException;

    void updateToStart();


}
