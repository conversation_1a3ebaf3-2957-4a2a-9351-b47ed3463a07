package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by xieyongrui on 2020/4/2.
 */
@Data
@TableName("autotest_conan_case")
public class ConanCase {
    private Integer id;

    private Integer jobId;

    private Integer taskId;

    private String className;

    private String methodName;

    private Integer status;

    private String exceptionName;

    private String exceptionMessage;

    private String validCode;

    private Integer runCount;

    private String failReason;

    private String failReasonDesc;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private Float totalTime;

    private String issueKey;

    private String issueStatus;

    private String issueAssignee;

    private String crashInfo;

    private String crashHash;

    private String crashUuid;

    private String crashVersion;
}
