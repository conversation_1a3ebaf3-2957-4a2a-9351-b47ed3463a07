package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.InterfaceCase;
import net.minidev.json.JSONObject;

import java.io.IOException;
import java.util.List;

public interface InterfaceCaseService {
    String gethost(String caseName);
    List<InterfaceCase> getListByCaseName(String caseName);
    List<InterfaceCase> getApiByCaseNameAndApi(String caseName, String api);
    List<String> getAllPrincipal();
    String getApiByCaseName(String caseName);
    IPage<InterfaceCase> list(QueryRequest request, InterfaceCase interfaceCase);
    void add(InterfaceCase interfaceCase) throws IOException;
    void delete(InterfaceCase interfaceCase);
    void update(InterfaceCase interfaceCase);
    List<InterfaceCase> select();
    void updateContent();
    String getStatus(String caseName,String platform,String principal);
}
