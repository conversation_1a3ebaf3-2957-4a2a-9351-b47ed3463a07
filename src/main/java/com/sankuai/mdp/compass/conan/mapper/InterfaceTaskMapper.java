package com.sankuai.mdp.compass.conan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.conan.entity.InterfaceTask;
import com.sankuai.mdp.compass.conan.entity.InterfaceCaseAndTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

@Mapper
public interface InterfaceTaskMapper extends BaseMapper<InterfaceTask> {
    @Select("SELECT autotest_interfacecase.case_name,autotest_interfacecase.name,autotest_interfacecase.api,autotest_interfacecase.case_name,autotest_interfacecase.request_count,autotest_interfacecase.header,autotest_interfacecase.body,autotest_interfacecase.params,autotest_interfacetask.* FROM autotest_interfacecase,autotest_interfacetask WHERE autotest_interfacecase.id=autotest_interfacetask.case_id AND autotest_interfacetask.job_id=#{jobId}")
    List<InterfaceCaseAndTask> getCaseAndTask(IPage page, @Param("jobId")String jobId);
}
