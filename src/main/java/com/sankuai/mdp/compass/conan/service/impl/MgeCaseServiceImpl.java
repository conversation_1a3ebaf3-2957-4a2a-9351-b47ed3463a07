package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.mapper.MgeCaseMapper;
import com.sankuai.mdp.compass.conan.service.MgeCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xubangxi on 2020/05/18.
 */
@Slf4j
@Service
public class MgeCaseServiceImpl extends ServiceImpl<MgeCaseMapper, MgeCase> implements MgeCaseService {
    @Autowired
    MgeCaseMapper mgeCaseMapper;

    @Override
    public Resp add(MgeCase mgeCase) {
        JsonParser jsonParser = new JsonParser();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("bid",mgeCase.getBid());
        List<MgeCase> record = mgeCaseMapper.selectList(queryWrapper);
        Resp resp = new Resp();
        int code = 200;
        String message = "";
        String lab = mgeCase.getValLab();
        String tag = mgeCase.getTag();
        if (lab != null && lab.length() > 0) {
            if (!jsonParser.parse(lab).isJsonArray()) {
                code = 100;
                message = message + "业务参数lab格式错误，应为json数组。\n";
            }
        }
        if (tag != null && tag.length() > 0) {
            if (!jsonParser.parse(tag).isJsonObject()) {
                code = 100;
                message = message + "tag不是标准json格式\n";
            }
        }

        if (code == 100) {
            resp.setCode(code);
            resp.setMsg(message);
            return resp;
        }

        if (record == null || record.size() == 0) {
            mgeCaseMapper.insert(mgeCase);
            resp.setCode(200);
            resp.setMsg("success");
        } else {
            resp.setCode(100);
            resp.setMsg("已存在相同bid");
            resp.setData(record.get(0).getBusinessName());
        }
        return resp;
    }

    @Override
    public void delete(MgeCase mgeCase) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", mgeCase.getId());
        mgeCaseMapper.delete(queryWrapper);
    }

    @Override
    public Resp updatePrincipalCn(MgeCase mgeCase) {
        QueryWrapper<MgeCase> queryWrapper = new QueryWrapper<>();
        Resp resp = new Resp();
        String principal = mgeCase.getPrincipal();
        String principalCn = mgeCase.getPrincipalCn();
        if (principalCn != null && principalCn != "") {
            List<MgeCase> list = mgeCaseMapper.selectList(queryWrapper.eq("principal",principal));
            for (int i = 0; i < list.size(); i++) {
                MgeCase item = list.get(i);
                item.setPrincipalCn(principalCn);
                mgeCaseMapper.updateById(item);
            }
        }
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Resp updateModuleByPrincipal(MgeCase mgeCase) {
        QueryWrapper<MgeCase> queryWrapper = new QueryWrapper<>();
        Resp resp = new Resp();
        String principal = mgeCase.getPrincipal();
        String module = mgeCase.getModule();
        if (principal != null && principal != "") {
            List<MgeCase> list = mgeCaseMapper.selectList(queryWrapper.eq("principal",principal));
            for (int i = 0; i < list.size(); i++) {
                MgeCase item = list.get(i);
                item.setModule(module);
                mgeCaseMapper.updateById(item);
            }
        }
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public Resp update(MgeCase mgeCase) {
        QueryWrapper<MgeCase> queryWrapper = new QueryWrapper<>();
        JsonParser jsonParser = new JsonParser();
        Resp resp = new Resp();
        int code = 200;
        String message = "";
        String lab = mgeCase.getValLab();
        String tag = mgeCase.getTag();
        if (lab != null && lab.length() > 0) {
            if (!jsonParser.parse(lab).isJsonArray()) {
                code = 100;
                message = message + "业务参数lab格式错误，应为json数组。\n";
            }
        }
        if (tag != null && tag.length() > 0) {
            if (!jsonParser.parse(tag).isJsonObject()) {
                code = 100;
                message = message + "tag不是标准json格式\n";
            }
        }

        if (code == 100) {
            resp.setCode(code);
            resp.setMsg(message);
            return resp;
        }

        mgeCaseMapper.update(mgeCase, queryWrapper
                .eq("id", mgeCase.getId()));
        resp.setCode(200);
        resp.setMsg("success");
        return resp;
    }

    @Override
    public List<MgeCase> select() {
        QueryWrapper queryWrapper = new QueryWrapper();
        return mgeCaseMapper.selectList(queryWrapper);
    }

    @Override
    public MgeCase getMgeCaseByBid(String bid, String businessName) {
        QueryWrapper<MgeCase> queryWrapper = new QueryWrapper();
        MgeCase mgeCase = mgeCaseMapper.selectOne(queryWrapper
                .eq("bid",bid)
                .eq("business_name",businessName));
        return mgeCase;
    }

    @Override
    public List<String> getAllPrincipal() {
        QueryWrapper<MgeCase> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<MgeCase> mgelist = mgeCaseMapper.selectList(queryWrapper
                .groupBy("principal"));
        for (MgeCase mgeCase : mgelist) {
            list.add(mgeCase.getPrincipal());
        }
        return list;
    }


    @Override
    public IPage<MgeCase> list(QueryRequest request, MgeCase mgeCase) {
        try {
            LambdaQueryWrapper<MgeCase> queryWrapper = new LambdaQueryWrapper<>();

            if (null != mgeCase.getPrincipal()) {
                queryWrapper.eq(MgeCase::getPrincipal, mgeCase.getPrincipal());
            }
            if (null != mgeCase.getBusinessName()) {
                queryWrapper.like(MgeCase::getBusinessName, mgeCase.getBusinessName());
            }
            Page<MgeCase> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    public boolean is_alpha(String str) {
        if (str == null) return false;
        return str.matches("[a-zA-Z]+");
    }

    @Override
    public String updateVallab(String Vallab) {
        try {
            String[] contents = Vallab.split(";");
            String result = "[{";
            for (String content : contents) {
                result += "\"";
                result += content;
                result += "\":\"\",";
            }
            result = result.substring(0, result.length() - 1);
            result += "}]";
            return result;
        } catch (Exception e) {
//            log.info(Vallab + " meet an error.");
            return Vallab;
        }
    }

    @Override
    public String updateTag(String tag) {
        try {
            String result = "";

            String[] tags = tag.split(";");
            result = "{\"skipThisEle\":{\"skipThisEle\":{";
            for (String content : tags) {
                result += "\"";
                result += content;
                result += "\":\"\",";
            }
            result = result.substring(0, result.length() - 1);
            result += "}}}";
            return result;
        } catch (Exception e) {
//            log.info(tag + " meet an error.");
            return tag;
        }

    }

    @Override
    public MgeCase updateContent(MgeCase mgeCase) {

        //undefined correction

        //cid
        if (mgeCase.getCid().equals("undefined")) {
            mgeCase.setCid(null);
        }
        //set vallabkey to legal vallab
        if (mgeCase.getValLab().equals("undefined") || mgeCase.getValLab().equals("")) {
            mgeCase.setValLab("[{}]");

        } else if (mgeCase.getValLab().startsWith("{") && mgeCase.getValLab().endsWith("}")) {
            mgeCase.setValLab("[" + mgeCase.getValLab() + "]");

        } else if (is_alpha(mgeCase.getValLab().substring(0, 1))) {
            mgeCase.setValLab(updateVallab(mgeCase.getValLab()));
        }
        //set tagkey to a thirdleveltag and trasfer to a tag json.
        if (mgeCase.getTag().equals("undefined")) {
            mgeCase.setTag(null);
        } else if (mgeCase.getTag() != null) {
            if (mgeCase.getTag().length() > 1 && is_alpha(mgeCase.getTag().substring(0, 1)))
                mgeCase.setTag(updateTag(mgeCase.getTag()));
        }
//replace space
        mgeCase.setCid(mgeCase.getCid().replace(" ", ""));
        mgeCase.setBid(mgeCase.getBid().replace(" ", ""));

        //update result
        return mgeCase;
    }


}
