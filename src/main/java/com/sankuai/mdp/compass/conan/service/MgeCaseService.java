package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.MgeCase;

import java.util.List;

/**
 * Created by xubangxi on 2020/05/18.
 */
public interface MgeCaseService {
    Resp add(MgeCase mgeCase);
    void delete(MgeCase mgeCase);
    Resp update(MgeCase mgeCase);
    Resp updatePrincipalCn(MgeCase mgeCase);
    MgeCase getMgeCaseByBid(String bid, String businessName);
    Resp updateModuleByPrincipal(MgeCase mgeCase);
    List<MgeCase> select();
    IPage<MgeCase> list(QueryRequest request, MgeCase mgeCase);
    public List<String> getAllPrincipal();
    public MgeCase updateContent(MgeCase mgeCase);
    public String updateVallab(String Vallab);
    public String updateTag(String tag);
}
