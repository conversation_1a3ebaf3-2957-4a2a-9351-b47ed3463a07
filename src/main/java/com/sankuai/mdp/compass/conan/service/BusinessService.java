package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Business;

import java.util.List;

/**
 * Created by xieyongrui on 2020/6/12.
 */
public interface BusinessService extends IService<Business> {
    Boolean add(Business business);
    void update(Business business);
    void delete(Business business);
    List<String> list(QueryRequest request, Business business);

}
