package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.conan.entity.MgeTask;
import com.sankuai.mdp.compass.conan.service.MgeCaseService;
import com.sankuai.mdp.compass.conan.service.MgeJobService;
import com.sankuai.mdp.compass.conan.service.MgeTaskService;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * Created by xubangxi on 2020/05/18.
 */
@RestController
@RequestMapping("/compass/api/autotest/mgetask")


public class MgeTaskController extends BaseController {
    @Autowired
    MgeTaskService mgeTaskService;
    @Autowired
    MgeCaseService mgeCaseService;
    @Autowired
    MgeJobService mgeJobService;

    @GetMapping("/select")
    public List<MgeTask> select() {
        return mgeTaskService.select();
    }

    //this is for SQLtest in trst dev
//    @GetMapping("/read")
//    public void read() throws IOException {
//        String version = mgeTaskService.version();
//        List<MgeCase> mgeCaseList = mgeCaseService.select();
//        mgeTaskService.readXls(mgeCaseList, version, 0);
//    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, MgeTask mgeTask) {
        IPage<MgeTask> CompatilityJobIPage = this.mgeTaskService.list(request, mgeTask);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/failedTaskList")
    public Map<String, Object> Failedlist(QueryRequest request, MgeTask mgeTask) {
        IPage<MgeTask> CompatilityJobIPage = this.mgeTaskService.failedTasklist(request, mgeTask);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/getCount")
    public MgeCount count(@RequestParam String version) {
        return mgeTaskService.count(version);
    }

    @PostMapping("/skip")
    public void skip(int id, String operator) {
        mgeTaskService.skip(id, operator);
    }
    @PostMapping("/reportBug")
    public void reportBug(int id, String operator) {
        mgeTaskService.reportBug(id, operator);
    }
    @GetMapping("versionCheck")
    public String versionCheck() {
        return mgeTaskService.version();
    }
//    @GetMapping("/getpost")//未来可以实现推送个人
//    public void getpost() throws Exception {
//        mgeTaskService.getpost(1);
//    }
//
//    @GetMapping("/getpostgroup")//测试群组推送
//    public void getpostgroup() throws Exception {
//        mgeTaskService.getposttogroup();
//    }

    @GetMapping("/rerunMgeAutoCheck")
    public void rerun(@RequestParam String startDate, @RequestParam String endDate) throws IOException {
            String version = mgeTaskService.version();
            List<MgeCase> mgeCaseList = mgeCaseService.select();
            int jobId = mgeJobService.setJobStart();
            int status = mgeTaskService.createASearch(startDate, endDate, version,jobId);
            mgeJobService.setJobEnd(status, jobId);
            mgeTaskService.readXlsNew(mgeCaseList, version, jobId);
            mgeTaskService.delete();
            mgeTaskService.postToPersonalMgeResult(status, "lizhen39");
    }
//    Only run in test dev
//    @GetMapping("/rerunMgeAutoCheckTest")
//    public void reruntest(@RequestParam String startDate, @RequestParam String endDate) throws IOException {
//        String version = mgeTaskService.version();
//        List<MgeCase> mgeCaseList = mgeCaseService.select();
//        int jobId = mgeJobService.setJobStart();
//        int status = mgeTaskService.createASearch(startDate, endDate, version);
//        mgeJobService.setJobEnd(status, jobId);
//        mgeTaskService.readXls(mgeCaseList, version, jobId);
//        mgeTaskService.getRerunPost(status, "lizhen39");
//    }

    @GetMapping("/collectResultByQid")
    public void collectResultByQid(@RequestParam String qid) throws IOException, InvalidFormatException {
        mgeTaskService.collectResultByQid(qid);
    }

    @GetMapping("/getAllPrincipal")
    public List<String> getAllPrincipal() {
        return mgeTaskService.getAllPrincipal();
    }

    @GetMapping("/getAllVersion")
    public List<String> getAllVersions() {
        return mgeTaskService.getAllVersions();
    }

    @PostMapping("/updatePrincipal")
    public void updatePrincipal(@RequestParam String version) {
        mgeTaskService.updatePrincipal(version);
    }

    @GetMapping("/test")
    public void test() throws IOException {
        List<MgeCase> mgeCaseList = mgeCaseService.select();
        mgeTaskService.readXlsNew(mgeCaseList,"11.9.400",397);
    }

}
