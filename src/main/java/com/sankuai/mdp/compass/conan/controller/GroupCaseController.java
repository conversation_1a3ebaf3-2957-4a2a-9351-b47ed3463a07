package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import com.sankuai.mdp.compass.conan.service.GroupCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/4/15.
 */
@RestController
@RequestMapping("/compass/api/groupcase")
public class GroupCaseController extends BaseController {
    @Autowired
    GroupCaseService groupCaseService;

    @PostMapping("/add")
    public void add(GroupCase groupCase) {
        groupCaseService.add(groupCase);
    }

    @PostMapping("/delete")
    public void delete(GroupCase groupCase) {
        groupCaseService.delete(groupCase);
    }

    @PostMapping("/update")
    public void update(GroupCase groupCase) {
        groupCaseService.update(groupCase);
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, GroupCase groupCase) {
        IPage<GroupCase> CompatilityJobIPage = this.groupCaseService.list(request, groupCase);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/initialize")
    public String initialize() {
        return groupCaseService.caseStyleInitialize();
    }

    @PostMapping("/changeStatus")
    public void changeState(int id) {
        groupCaseService.changeStatus(id);
    }

    @GetMapping("/getAllPrincipal")
    public List<String> getAllPrincipal() {
        return groupCaseService.getAllPrincipal();
    }

    @PostMapping("/changeType")
    public void changeType(int id) {
        groupCaseService.changeType(id);
    }

    @PostMapping("/changeTypeAmount")
    public void changeTypeAmount(int id) {
        groupCaseService.changeTypeAmount(id);
    }

    @GetMapping("createXML")
    public String create(@RequestParam String type, @RequestParam String platform) {
        return groupCaseService.createXML(type, platform);
    }

    @GetMapping("/getAllCaseNames")
    public List<String> getAllCaseNames() {
        return groupCaseService.getAllCaseNames();
    }

    ;
}
