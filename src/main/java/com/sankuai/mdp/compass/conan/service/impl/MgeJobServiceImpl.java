package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.conan.entity.MgeJob;
import com.sankuai.mdp.compass.conan.mapper.MgeJobMapper;
import com.sankuai.mdp.compass.conan.service.MgeJobService;
import com.sankuai.mdp.compass.conan.service.MgeTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MgeJobServiceImpl extends ServiceImpl<MgeJobMapper, MgeJob> implements MgeJobService {
    @Autowired
    MgeJobMapper mgeJobMapper;
    @Autowired
    MgeJobService mgeJobService;
    @Autowired
    MgeTaskService mgeTaskService;
    DxUtil dxUtil = new DxUtil();

    @Override
    public void add(MgeJob mgeJob) {
        mgeJobMapper.insert(mgeJob);
    }

    @Override
    public void update(MgeJob mgeJob) {
        mgeJobMapper.updateById(mgeJob);
    }

    @Override
    public void updateForMge(int jobId, String qid) {
        MgeJob mgeJob = mgeJobMapper.selectById(jobId);
        mgeJob.setQid(qid);
        mgeJobMapper.updateById(mgeJob);
    }

    @Override
    public boolean checkRerun() throws ParseException {
        int year = Calendar.getInstance().get(Calendar.YEAR);
        int month = Calendar.getInstance().get(Calendar.MONTH) + 1;
        int day = Calendar.getInstance().get(Calendar.DATE);
        String datetime = year + "-" + month + "-" + day;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        long time = simpleDateFormat.parse(datetime).getTime();
        QueryWrapper<MgeJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("update_time", time + 8 * 60 * 60 * 1000, time + 17 * 60 * 60 * 1000);
        if (mgeJobMapper.selectCount(queryWrapper) > 0) {
            int signal = 0;
            for (MgeJob mgeJob : mgeJobMapper.selectList(queryWrapper)) {
                int res = mgeJob.getStatus();
                if (res == 1) {
                    log.info("找到成功记录");
                    return false;
                } else if (res == 101) {
                    log.info("成功找到记录，但未成功获得结果，尝试获得此次结果");
                    res = mgeTaskService.autoCollectUnfinishedResult(mgeJob.getQid(), mgeJob.getId());
                    if (res == 1) {
                        log.info("未找到成功记录，找到上次未完成记录，成功对接任务");
                        signal++;
                    }
                }
            }
            if (signal > 0) {
                log.info("未找到成功记录，找到上次未完成记录，成功对接任务" + signal + "次");
                return false;
            }
            log.info("未找到成功记录，准备重新运行");
            return true;
        }
        log.info("未找到任何记录，准备重新运行");
        try {
            dxUtil.sendToIndividualByCompassAuto
                    ("截止至16点，本日无任何埋点自动化运行记录" +
                                    "，请查看https://avatar.mws.sankuai.com/#/service/detail/host?appkey=com.sankuai.sigma.compass&env=prod"
                            , "lizhen39");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public void updateToStart() {
        List<MgeJob> mgeJobList = mgeJobMapper.selectList(new QueryWrapper<MgeJob>());
        for (MgeJob mgeJob : mgeJobList) {
            try {
                mgeJob.setStartTime(timeStampDate(mgeJob.getUpdateTime()));
                mgeJobMapper.updateById(mgeJob);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static Date timeStampDate(String time) throws ParseException {
        String format = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String stringResult = sdf.format(new Date(Long.parseLong(time)));
        return sdf.parse(stringResult);
    }

    @Override
    public int setJobStart() {
        MgeJob mgeJob = new MgeJob();
        mgeJob.setStatus(101);
        long time = new Date().getTime();
        String ti = Long.toString(time);
        mgeJob.setUpdateTime(ti);
        mgeJob.setStartTime(new Date());
        mgeJobService.add(mgeJob);
        String[] array = {"lizhen39"
                , "xieyongrui"
        };
        for (String name : array) {
            try {
                dxUtil.sendToIndividualByCompassAuto("New MgeAutocheck mission started.", name);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        QueryWrapper<MgeJob> queryWrapper = new QueryWrapper<>();
        return mgeJobMapper.selectOne(queryWrapper.eq("update_time", ti)).getId();
    }

    @Override
    public void setJobEnd(int status, int jobId) {
        QueryWrapper<MgeJob> queryWrapper = new QueryWrapper<>();
        MgeJob mgeJob = mgeJobMapper.selectOne(queryWrapper.eq("id", jobId));
        mgeJob.setStatus(status);
        mgeJobService.update(mgeJob);
        if (status != 1) {
            String statusStr;
            switch (status) {
                case -1:
                    statusStr = "运行超时";
                    break;
                case 3:
                    statusStr = "search运行成功，创建excel失败";
                    break;
                case 5:
                    statusStr = "Talos Session出现问题";
                    break;
                case 7:
                    statusStr = "new file (tmp.txt)失败，未存储数据";
                    break;
                case 9:
                    statusStr = "tmp.txt写入失败，未储存数据";
                    break;
                case 101:
                    statusStr = "线程被终止";
                    break;
                default:
                    statusStr = "未知错误";
            }
            String[] array = {"lizhen39"
                    , "xieyongrui"
            };
            for (String name : array) {
                try {
                    dxUtil.sendToIndividualByCompassAuto("本次自动化运行失败，可能的原因为： " + statusStr, name);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }
}
