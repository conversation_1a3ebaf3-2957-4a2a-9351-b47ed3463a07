package com.sankuai.mdp.compass.conan.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by lizhen39 on 2021/02/05.
 */
@Data
@TableName("et_auto")
public class EventTrackingAuto {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;//埋点自动化检测任务id

    private String apkUrl;//包下载地址

    private Integer jobId;//Zero-X触发任务时传递的id

    private String reportIds;//云测任务id（遍历&UI自动化）

    private String conanStatus;//标识云测任务执行状态

    private String version;//App版本

    private String buildNumber;//包号

    private String platform;// 设备操作系统（Android||iOS）

    private String oceanResult;//埋点检测结果-测试结果链接
}
