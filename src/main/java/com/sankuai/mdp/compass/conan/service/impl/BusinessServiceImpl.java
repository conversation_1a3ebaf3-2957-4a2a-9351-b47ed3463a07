package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Business;
import com.sankuai.mdp.compass.conan.mapper.BusinessMapper;
import com.sankuai.mdp.compass.conan.service.BusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xieyongrui on 2020/6/12.
 */
@Slf4j
@Service
public class BusinessServiceImpl extends ServiceImpl<BusinessMapper,Business> implements BusinessService{
    @Autowired
    BusinessMapper businessMapper;

    @Override
    public Boolean add(Business business) {
        try {
            businessMapper.insert(business);
        } catch (Exception e) {
//            log.error("插入item",e);
            return false;
        }
        return true;
    }

    @Override
    public void update(Business business) {

    }

    @Override
    public void delete(Business business) {

    }

    @Override
    public List<String> list(QueryRequest request, Business business) {
        try {
            LambdaQueryWrapper<Business> queryWrapper = new LambdaQueryWrapper<>();
            List<String> result = new ArrayList<>();
            Page<Business> page = new Page<>();
            IPage<Business> iPage = this.page(page, queryWrapper);
            for(int i= 0;i<iPage.getRecords().size();i++){
                result.add(iPage.getRecords().get(i).getName());
            }
            return result;
        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }
}
