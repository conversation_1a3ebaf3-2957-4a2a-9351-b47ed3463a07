package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.conan.entity.MgeTask;

import java.io.IOException;
import java.util.List;

/**
 * Created by xubangxi on 2020/05/18.
 */
public interface MgeTaskService {
    void add(MgeTask mgeTask);

    void update(MgeTask mgeTask);

    void skip(int id, String operator);

    void reportBug(int id, String operator);

    MgeCount count(String version);

    int createASearch(String start, String end, String version, int jobId);

    String getVersionFromApi(String last);

    void readXlsNew(List<MgeCase> mgeCaseList, String version, int jobId) throws IOException;

    String startDate();

    List<MgeTask> select();

    String endDate();

    void addContent(MgeTask mgeTask);

    String version();

    String getVersionFromsql();

    void delete();

    List<String> getAllVersions();

    void updatePrincipal(String version);

    IPage<MgeTask> list(QueryRequest request, MgeTask mgeTask);

    List<String> getAllPrincipal();

    void collectResultByQid(String qid);

    int autoCollectUnfinishedResult(String qid,int jobId);

    Boolean ifTestTime();

    List<MgeTask> selectFailedTask(String platform, String version, String module);

    void postToGroup(int status);

    void post(int status);

    IPage<MgeTask> failedTasklist(QueryRequest request, MgeTask mgeTask);

    void postToPersonalMgeResult(int status, String name);

    void postToGroupMgeResult(int status);
}
