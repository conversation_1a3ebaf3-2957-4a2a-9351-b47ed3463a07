package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.GroupCase;

import java.util.List;

/**
 * Created by xieyongrui on 2020/4/15.
 */
public interface GroupCaseService {
    void add(GroupCase groupCase);

    void delete(GroupCase groupCase);

    void update(GroupCase groupCase);

    IPage<GroupCase> list(QueryRequest request, GroupCase groupCase);

    List<String> getAllCaseNames();

    boolean changeType(int id);

    boolean changeTypeAmount(int id);

    boolean changeStatus(int id);

    boolean initialize();

    String caseStyleInitialize();

    List<String> getAllPrincipal();

    void getposttogroup(int status);

    void getpost(int status);

    String createXML(String type, String platform);

    String getCaseList(String className, String platform, String type);

    String getRelyOnString(String className, String platform, String type);

    void updateXMLCaseStatus(String className, String caseName, String casePlatform, String caseRunningType);

    List<String> getDetailList(String className, String platform, String type);

    List<GroupCase> getClassList(String platform, String type);

    List<String> getBaseClassList(String platform, String type);

    Boolean ifInnerList(String element,List<GroupCase> list);
}
