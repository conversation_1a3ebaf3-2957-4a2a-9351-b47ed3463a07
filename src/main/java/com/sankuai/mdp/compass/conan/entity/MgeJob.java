package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Created by xubangxi on 2020/05/18.
 */
@Data
@TableName("autotest_mge_job")
public class MgeJob {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    //bid

    private String updateTime;
    private Integer status;
    private Date startTime;
    private String qid;
    private String type;
}
