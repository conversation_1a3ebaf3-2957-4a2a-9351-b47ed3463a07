package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.ConanJob;
import net.minidev.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/4/1.
 */
public interface ConanJobService {
    void add(Conan<PERSON><PERSON> conanJob);
    void collectResult(JSONObject body);
    void collectTaskAndCaseByJobId(JsonObject body);
    void collectTaskAndCaseByJobId(String startTime, String endTime);
    List<ConanJob> getUiJobList(String startTime, String endTime,String platform);
    void collectCase(String jobId,String platform);
    void collectCase(String jobId);
    void collectTask(String jobId);
    void collectUICrash(String startTime, String endTime);
    Conan<PERSON><PERSON> selectOne(QueryWrapper queryWrapper);
    void update(Conan<PERSON>ob conanJob, QueryWrapper queryWrapper);
    IPage<ConanJob> listInterface(QueryRequest request, ConanJob conanJob);
    Map<String, Object> collectCrashCount(String startTime, String endTime, String jobType, String platform,String buildScene);
    void triggerTestHook(JSONObject body);
}
