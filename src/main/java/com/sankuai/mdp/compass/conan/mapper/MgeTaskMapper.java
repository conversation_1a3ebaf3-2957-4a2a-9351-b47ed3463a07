package com.sankuai.mdp.compass.conan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.conan.entity.MgeTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface MgeTaskMapper extends BaseMapper<MgeTask> {

    @Select("SELECT app_version as version,count(*) as total,\n" +
            "sum(case when status != 'succeed' then 1 else 0 end) as failed,\n" +
            "sum(case when status != 'succeed' and affirm_status = -1 then 1 else 0 end) as needAffirm,\n" +
            "sum(case when status = 'succeed' then 1 else 0 end) as success,\n" +
            "sum(case when is_bug = 1 then 1 else 0 end) as bug \n" +
            "FROM autotest_mge_task \n" +
            "WHERE app_version = #{version}")
    MgeCount count(@Param("version") String version);
}
/*
//初始化（两步）
insert into autotest_mge_task (case_number,business_name,bid,cid,val_lab,principal,actual_val_lab,case_status) values
('2','','','','','','','0');
update autotest_mge_task t set business_name=(select business_name from autotest_mge_case c where case_number=t.case_number),bid=(select bid from autotest_mge_case c where case_number=t.case_number),cid=(select cid from autotest_mge_case c where case_number=t.case_number),val_lab=(select val_lab from autotest_mge_case c where case_number=t.case_number),principal=(select principal from autotest_mge_case c where case_number=t.case_number);

//得到actual_val_lab后比较（两步）
update autotest_mge_task t, autotest_mge_case c set t.case_status=
(
 case when (t.bid = c.bid and t.cid =c.cid and t.`actual_val_lab`=t.val_lab)
 then '1'
 when  (t.bid = c.bid and t.cid = c.cid)
 then '-1'
 else '-2' end);
update autotest_mge_task t set t.case_status_str=
(case when t.case_status='1'
then '成功'
when t.case_status='-1'
then 'val_lab不同'
when t.case_status='-2'
then '未找到对应的case'
else '未知错误' end
);

*/