package com.sankuai.mdp.compass.conan.enums;
import lombok.Getter;

@Getter
public enum InterfaceCheckErrorCode {
    SUCCESS(0, ""),
    URLFAIL(1, "请求url错误"),
    METHODFAIL(2, "请求方式错误"),
    RCFAIL(3, "请求次数错误"),
    PARAMFAIL(4, "请求参数错误"),
    HEADERFAIL(5, "请求header错误"),
    BODYFAIL(6, "请求body错误"),
    PLATFORM_MISMATCH(7, "平台参数不匹配"),
    NOCHECK(8, "不校验请求次数"),
    CONAN_EX_FAIL(-1, "云测执行自动化的过程失败，需要重试或者忽略");

    private final Integer code;
    private final String msg;

    InterfaceCheckErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
