package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Component;

import java.util.List;

/**
 * Created by xieyongrui on 2020/6/12.
 */
public interface ComponentService extends IService<Component> {
    Boolean add(JsonObject jsonObject);
    void update(Component component);
    void delete(Component component);
    IPage<Component> list(QueryRequest request, Component component);
    List<String> hpxComp(String platform);
    String searchCategory(String comp);
}
