package com.sankuai.mdp.compass.conan.service.impl;
import com.jayway.jsonpath.JsonPath;
import com.sankuai.mdp.compass.conan.entity.InterfaceCase;
import com.sankuai.mdp.compass.conan.entity.Interface;
import com.sankuai.mdp.compass.conan.enums.InterfaceCheckErrorCode;
import com.sankuai.mdp.compass.conan.mapper.InterfaceTaskMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import org.everit.json.schema.Schema;
import org.everit.json.schema.ValidationException;
import org.everit.json.schema.loader.SchemaLoader;
import org.json.JSONObject;
import org.json.JSONTokener;
import java.util.List;
import java.util.Arrays;

public class Interfacecheck {

    @Autowired
    InterfaceTaskMapper interfaceTaskMapper;

    public InterfaceCheckErrorCode checkURL(Interface interface1, String host, String reqUrl, String api) {
        String Host=interface1.getHost();
        if ((host+reqUrl.split("\\?")[0]).equals(Host + api)) return InterfaceCheckErrorCode.SUCCESS;
        else return InterfaceCheckErrorCode.URLFAIL;
    }
    public InterfaceCheckErrorCode checkMethod(Interface interface1, String method) {
        String Method=interface1.getMethod();
        if (Method.equals(method)) return InterfaceCheckErrorCode.SUCCESS;
        else return InterfaceCheckErrorCode.METHODFAIL;
    }

    public InterfaceCheckErrorCode checkRequestCount(InterfaceCase interfaceCase1, Integer api_count) {
        if (interfaceCase1.getRequestCount() == null) {
            return InterfaceCheckErrorCode.NOCHECK;
        }
        if (interfaceCase1.getRequestCount().equals(api_count)) return InterfaceCheckErrorCode.SUCCESS;
    else return InterfaceCheckErrorCode.RCFAIL;
}

    public String paramToJson(String url){
        JSONObject obj = new JSONObject();
        String[] params= url.split("\\?")[1].split("&");
        for (String s : params) {
            String[] param = s.split("=");
            if (param.length >= 2) {
                String key = param[0];
                StringBuilder value = new StringBuilder(param[1]);
                for (int j = 2; j < param.length; j++) {
                    value.append("=").append(param[j]);
                }
                obj.put(key, value.toString());
            }
        }
        return obj.toString();

    }
    public String jsonCheckValueData(String params,String originData) {
        String jsonCheckValueData = originData;
        String[] arr = params.split(",");
        for (String s : arr) {
            jsonCheckValueData = JsonPath.parse(jsonCheckValueData).set(s, "true").jsonString();
        }

        return jsonCheckValueData;
    }
    public String checkJson(String json,String inputSchema) throws IOException {
        json=json.replace("-","_");
        String result="";
        try {
            JSONObject rawSchema = new JSONObject(new JSONTokener(inputSchema));
            JSONObject data = new JSONObject(new JSONTokener(json));
            Schema schema = SchemaLoader.load(rawSchema);
            schema.validate(data); // throws a ValidationException if this object is invalid
        } catch (ValidationException e) {
            List<String> allErrorMsg = e.getAllMessages();
            String[] tmp=new String[allErrorMsg.size()];
            int i=0;
            for (String item :allErrorMsg){
                tmp[i++]=item;
            }
            result=Arrays.toString(tmp).replace(": ,",":value is wrong,").replace(": ]",":value is wrong]");
        }
        return result;
    }

}