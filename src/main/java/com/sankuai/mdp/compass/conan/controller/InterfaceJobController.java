package com.sankuai.mdp.compass.conan.controller;

import net.minidev.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.InterfaceJob;
import com.sankuai.mdp.compass.conan.service.InterfaceJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/interfaceJob")
public class InterfaceJobController extends BaseController {
    @Autowired
    InterfaceJobService interfaceJobService;

    @GetMapping("/list")
    public Map<String,Object> list(InterfaceJob interfaceJob, QueryRequest request) {
        try {
            IPage<InterfaceJob> interfaceJobIPage = interfaceJobService.list(request, interfaceJob);
            if (interfaceJobIPage != null) {
                return getDataTable(interfaceJobIPage);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    @PutMapping("/updateStatus")
    public Resp updateStatus(InterfaceJob interfaceJob) {
        try {
            return interfaceJobService.updateStatusById(interfaceJob);
        } catch (Exception e) {
            return null;
        }
    }
    @GetMapping("getLastedJobId")
    public Integer getLastedJobId(InterfaceJob interfaceJob) {
        return interfaceJobService.lastestJobId(interfaceJob);
    }

    @PostMapping("/updateReportId")
    public Resp updateReportId(InterfaceJob interfaceJob) {
        return interfaceJobService.updateReportId(interfaceJob);
    }

    @PostMapping("/add")
    public Integer add(InterfaceJob interfaceJob){
        return interfaceJobService.add(interfaceJob);
    }


}
