package com.sankuai.mdp.compass.conan.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.mchange.v2.log.MLogger;
import com.sankuai.mdp.compass.build.entity.AutoTestBuild;
import com.sankuai.mdp.compass.build.service.AutoTestBuildService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.entity.ConanCase;
import com.sankuai.mdp.compass.conan.entity.ConanJob;
import com.sankuai.mdp.compass.conan.entity.ConanTask;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import com.sankuai.mdp.compass.conan.mapper.ConanJobMapper;
import com.sankuai.mdp.compass.conan.mapper.GroupCaseMapper;
import com.sankuai.mdp.compass.conan.service.ConanCaseService;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.conan.service.ConanTaskService;
import com.sankuai.mdp.compass.uiAutoTest.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by xieyongrui on 2020/4/1.
 */
@Slf4j
@Service
public class ConanJobServiceImpl extends ServiceImpl<ConanJobMapper, ConanJob> implements ConanJobService {
    private static final Logger logger = LoggerFactory.getLogger(ConanJobServiceImpl.class);
    @Autowired
    ConanJobMapper conanJobMapper;
    @Autowired
    GroupCaseMapper groupCaseMapper;

    ConanUtil conanUtil = new ConanUtil();

    @Autowired
    ConanTaskService conanTaskService;

    @Autowired
    ConanCaseService conanCaseService;

    @Autowired
    AutoTestBuildService autoTestBuildService;

    JenkinsUtil jenkinsUtil = new JenkinsUtil();

    DxUtil dxUtil = new DxUtil();


    @Override
    public void add(ConanJob conanJob) {
        conanJob.setStatus(0);
        conanJob.setStatusStr("运行中");
        conanJobMapper.insert(conanJob);
    }

    @Override
    public void collectResult(JSONObject body) {
        JsonParser jsonParser = new JsonParser();
        log.info(body.toString());
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject timeData = jsonParser.parse(body.get("timedata").toString()).getAsJsonObject();
            JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
            JsonArray devicelist = jsonParser.parse(body.get("devicelist").toString()).getAsJsonArray();
            String jobId = jobData.get("id").getAsString();
            Integer jobResult = jobData.get("jobResult").getAsInt();
            Date startTime = sdf.parse(timeData.get("startTime").getAsString());
            Date endTime = sdf.parse(timeData.get("endTime").getAsString());
            Float totalTime = timeData.get("totalTime").getAsFloat();
            String reportUrl = jobData.get("report").getAsString();
            String jobType = jobData.get("jobType").getAsString();
            Integer taskResult = jobData.get("taskResult").getAsInt();
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("job_id", jobId);
            ConanJob conanJob = conanJobMapper.selectOne(queryWrapper);
            if (null != conanJob) {
                log.info(conanJob.toString());
                conanJob.setStatus(1);
                conanJob.setStatusStr("已完成");
                conanJob.setJobResult(taskResult);
                conanJob.setStartTime(startTime);
                conanJob.setEndTime(endTime);
                conanJob.setTotalTime(totalTime);
                conanJob.setReportUrl(reportUrl);
                conanJobMapper.update(conanJob, queryWrapper);
            }
            collectTask(jobId);
            LionUtil lionUtil =new LionUtil();
            if(lionUtil.getBooleanValue("autoTestNotice")){
            if (devicelist.toString().contains("iPhone"))
                collectCase(jobId, "iOS");
            else
                collectCase(jobId, "Android");
            }
            else
                collectCase(jobId);
        } catch (Exception e) {
            log.error("collectResult error>>" + e.getMessage());

        }
    }

    @Override
    public void collectTask(String jobId) {
        JsonArray jobInfo = conanUtil.getAllTaskInfo(jobId);
        if (null != jobInfo) {
            for (int i = 0; i < jobInfo.size(); i++) {
                JsonObject jsonObject = jobInfo.get(i).getAsJsonObject();
                JsonArray taskList = jsonObject.get("taskList").getAsJsonArray();
                for (int t = 0; t < taskList.size(); t++) {
                    JsonObject taskObject = taskList.get(t).getAsJsonObject();
                    conanTaskService.insertObject(taskObject);
                }
            }
        }
    }

    @Override
    public void collectCase(String jobId, String platform) {
        JsonArray caseList = conanUtil.getAllCaseInfo(jobId);
        if (null != caseList) {
            for (int i = 0; i < caseList.size(); i++) {
                JsonObject jsonObject = caseList.get(i).getAsJsonObject();
                String url = "https://conan.sankuai.com/v2/auto-function/appium/report/detail/#taskID?class=#class&method=#method";
                url = url.replace("#taskID", JsonUtil.getString(jsonObject, "taskId"));
                url = url.replace("#class", JsonUtil.getString(jsonObject, "className"));
                url = url.replace("#method", JsonUtil.getString(jsonObject, "methodName"));
                String report = returnReport(jsonObject, url, platform);
                boolean flg = !report.contains("#result");
                if (flg) {
                    DxUtil dxUtil = new DxUtil();
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("case_name", JsonUtil.getString(jsonObject, "methodName"));
                    //  queryWrapper.eq("class_name", jsonObject.get("className").getAsString());
                    queryWrapper.eq("case_platform", platform);
                    queryWrapper.last("limit 1");
                    GroupCase groupCase = groupCaseMapper.selectOne(queryWrapper);
                    String misID = "";
                    if (groupCase != null)
                        misID = groupCase.getCaseOwner();
                    if (misID != null && !"".equals(misID))
                        dxUtil.sendToPersionByCompass(report, misID);
                    else
                        dxUtil.sendToPersionByCompass(report, "liuyang359");
                        dxUtil.sendToPersionByCompass(report,"fengenci");
                }
                try {
                    conanCaseService.insetObject(jsonObject);
                } catch (Exception e) {
                    log.error("collectResult error>>" + e.getMessage());
                }
            }
        }
    }

    @Override
    public void collectCase(String jobId) {
        JsonArray caseList = conanUtil.getAllCaseInfo(jobId);
        if (null != caseList) {
            for (int i = 0; i < caseList.size(); i++) {
                JsonObject jsonObject = caseList.get(i).getAsJsonObject();
                conanCaseService.insetObject(jsonObject);
            }
        }
    }

    public String returnReport(JsonObject jsonObject, String URL, String platform) {
        String report = "***UI自动化失败case提醒***\n" +
                "【任务ID】: #id\n" +
                "【移动端】: " + platform + "\n" +
                "【case名称】: #method\n" +
                "【测试结果】: #result\n" +
                "【失败原因】: #failReason\n" +
                "【原因描述】: #dsc\n" +
                "【开始时间】: #starttime\n" +
                "【结束时间】: #endtime\n" +
                "【报告链接】: [自动化报告|#url]\n";

        int status = jsonObject.get("status").getAsInt();
        report = report.replace("#id", JsonUtil.getString(jsonObject, "id"));
        report = report.replace("#starttime", JsonUtil.getString(jsonObject, "startTime"));
        report = report.replace("#method", JsonUtil.getString(jsonObject, "methodName"));
        report = report.replace("#starttime", JsonUtil.getString(jsonObject, "startTime"));
        report = report.replace("#failReason", JsonUtil.getString(jsonObject, "failReason"));
        report = report.replace("#dsc", JsonUtil.getString(jsonObject, "failReasonDesc"));
        report = report.replace("#endtime", JsonUtil.getString(jsonObject, "endTime"));
        report = report.replace("#url", URL);
        if (status == 0) {
            //失败case处理
            report = report.replace("#result", "case执行失败");
        } else if (status == 2) {//crash 处理
            report = report.replace("#result", "case发生崩溃");

        } else if (status == 3) {//crash 处理
            report = report.replace("#result", "case未执行");

        }

        return report;
    }

    @Override
    public void collectTaskAndCaseByJobId(JsonObject body) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String startTime = df.format(body.get("startTime"));
        String endTime = df.format(body.get("endTime"));
        collectTaskAndCaseByJobId(startTime, endTime);
    }

    @Override
    public void collectTaskAndCaseByJobId(String startTime, String endTime) {
        List<ConanJob> list = getUiJobList(startTime, endTime, null);
        for (int i = 0; i < list.size(); i++) {
            String jobId = list.get(i).getJobId();
            collectTask(jobId);
            collectCase(jobId);
        }
    }

    /**
     * 获取指定时间段的UI自动化测试job
     *
     * @param startTime
     * @param endTime
     * @param platform
     * @return 满足条件的jobId列表
     */
    @Override
    public List<ConanJob> getUiJobList(String startTime, String endTime, String platform) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.between("start_time", startTime, endTime);
        if (null != platform) {
            queryWrapper.eq("platform", platform);
        }
        List<ConanJob> list = conanJobMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public void collectUICrash(String startTime, String endTime) {
        log.info("collectUICrash---:开始执行");
        List<ConanJob> list = getUiJobList(startTime, endTime, null);
        for (int i = 0; i < list.size(); i++) {
            log.info("collectUICrash---size of list(i) is: " + list.size());
            JsonArray jobCrashList = conanUtil.getAllCrashInfo(list.get(i).getJobId());
            JsonArray jobCrashDetailList = conanUtil.getJobCrashInfo(list.get(i).getJobId());
            if (null != jobCrashList) {
                log.info("collectUICrash---size of jobCrashList(c) is: " + jobCrashList.size());
                for (int c = 0; c < jobCrashList.size(); c++) {
                    log.info("collectUICrash---i=" + i + ",c=" + c);
                    try {
                        JsonObject jsonObject = jobCrashDetailList.get(c).getAsJsonObject();
                        String caseId = jsonObject.get("caseId").getAsString();
                        String issueKey = jsonObject.get("issue").getAsJsonObject().get("issueKey").getAsString();
                        String issueStatus = jsonObject.get("issue").getAsJsonObject().get("status").getAsString();
                        String issueAssignee = jsonObject.get("issue").getAsJsonObject().get("assignee").getAsString();
                        String taskId = jsonObject.get("taskId").getAsString();
                        String crashUUID = jsonObject.get("uuid").getAsString();
                        String project = jsonObject.get("issue").getAsJsonObject().get("project").getAsString();
                        Map<String, Object> crashDetail = conanUtil.getCrashDetail(crashUUID, project, startTime, endTime);
                        log.info("collectUICrash---size of crashDetail: " + crashDetail.size());
                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.eq("issue_key", issueKey);
                        log.info("collectUICrash---issueKey: " + issueKey);
                        ConanCase conanCase = conanCaseService.selectOneCase(caseId);
                        if (null != conanCase) {
                            conanCase.setIssueKey(issueKey);
                            conanCase.setIssueStatus(issueStatus);
                            conanCase.setIssueAssignee(issueAssignee);
                            conanCase.setCrashUuid(crashUUID);
                            if (crashDetail.size() > 3) {
                                String crashHash = crashDetail.get("crash_hash").toString();
                                String type = crashDetail.get("type").toString();
                                String crashInfo = "归属BU: " + crashDetail.get("default_bg_bu_chinese") + "\n归属组件: " + crashDetail.get("default_component");
                                log.info("collectUICrash---crashInfo: " + crashInfo);
                                String crashVersion = conanUtil.getMaxMinCrashVersion(crashHash, type, project);
                                conanCase.setCrashInfo(crashInfo);
                                conanCase.setCrashHash(crashHash);
                                conanCase.setCrashVersion(crashVersion);
                                log.info("collectUICrash---设置数据库crash详情数据");
                            }
                            conanCaseService.updateById(conanCase);
                            log.info("collectUICrash---:保存数据库");
                        }

//                    在task表里也存一份crash信息，目的：兼容monkey这种没有case的场景
                        ConanTask conanTask = conanTaskService.selectOneTask(taskId);
                        if (null != conanTask) {
                            if (null != conanTask.getIssueKey() && !conanTask.getIssueKey().isEmpty()) {
                                if (Arrays.asList(conanTask.getIssueKey().split(",")).contains(issueKey)) {
                                    issueKey = conanTask.getIssueKey();
                                    issueStatus = conanTask.getIssueStatus();
                                    issueAssignee = conanTask.getIssueAssignee();
                                } else {
                                    issueKey = conanTask.getIssueKey() + "," + issueKey;
                                    issueStatus = conanTask.getIssueStatus() + "," + issueStatus;
                                    issueAssignee = conanTask.getIssueAssignee() + "," + issueAssignee;
                                }
                            }

                            conanTask.setIssueKey(issueKey);
                            conanTask.setIssueStatus(issueStatus);
                            conanTask.setIssueAssignee(issueAssignee);
                            conanTaskService.updateById(conanTask);
                        }
                    } catch (Exception e) {
                        log.info("collectUICrash---e.getMessage()：" + e.getMessage());
                    }

                }
            }
        }
    }

    @Override
    public Map<String, Object> collectCrashCount(String startTime, String endTime, String jobType, String
            platform, String buildScene) {
        int crash_count = 0;
        Map<String, Object> crash_result = new HashMap();

        List<ConanJob> list = getUiJobList(startTime, endTime, null);
        List<ConanJob> list_Job = new ArrayList<>();
        if (null != jobType && !jobType.isEmpty() && null != platform && !platform.isEmpty() && null != buildScene && !buildScene.isEmpty()) {
            for (ConanJob job : list) {
                AutoTestBuild autoTestBuild = autoTestBuildService.selectOneBuild(String.valueOf(job.getBuildId()));
                if (jobType.equals(job.getJobType()) && platform.equals(job.getPlatform()) && (buildScene.equals(autoTestBuild.getBuildScenes()))) {
                    list_Job.add(job);
                }
            }
        } else if (null == buildScene || buildScene.isEmpty()) {
            for (ConanJob job : list) {
                if (jobType.equals(job.getJobType()) && platform.equals(job.getPlatform())) {
                    list_Job.add(job);
                }
            }
        } else {
            crash_result.put("message", "参数缺失");
            return crash_result;
        }

        ArrayList<Map<String, Object>> issueCrashList = new ArrayList();
        for (int i = 0; i < list_Job.size(); i++) {
            logger.info("---jobid:" + list_Job.get(i).getJobId());
            JsonArray jobCrashList = conanUtil.getAllCrashInfo(list_Job.get(i).getJobId());

            Map<String, Object> job_crash = new HashMap();
            if (null != jobCrashList) {
                String task_ids = "";
                crash_count = crash_count + jobCrashList.size();
                for (int c = 0; c < jobCrashList.size(); c++) {
                    JsonObject jsonObject = jobCrashList.get(c).getAsJsonObject();
                    String issueKey = jsonObject.get("issueKey").getAsString();
                    task_ids = (c == jobCrashList.size() - 1) ? task_ids + issueKey : task_ids + issueKey + ",";
                }
                job_crash.put("jobId", list_Job.get(i).getJobId());
                job_crash.put("issueKey", task_ids);
            }

            if (!job_crash.isEmpty()) {
                issueCrashList.add(job_crash);
            }
        }
        crash_result.put("issueCount", crash_count);
        crash_result.put("detail", issueCrashList);

        return crash_result;
    }

    @Override
    public void triggerTestHook(JSONObject body) {
        //TODO:处理body内容，只保留需要的信息
        jenkinsUtil.newTestHookJob(body);
    }

    @Override
    public IPage<ConanJob> listInterface(QueryRequest request, ConanJob conanJob) {
        try {
            LambdaQueryWrapper<ConanJob> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ConanJob::getJobType, "interface").orderByDesc(ConanJob::getId);

            if (null != conanJob.getJobId()) {
                queryWrapper.like(ConanJob::getJobId, conanJob.getJobId().toString());
            }
            if (null != conanJob.getPlatform() && !"All".equals(conanJob.getPlatform())) {
                queryWrapper.eq(ConanJob::getPlatform, conanJob.getPlatform());
            }
            if (request.getSwitchStatus()) {
                queryWrapper.ne(ConanJob::getStatus, "1");
            }
            Page<ConanJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public ConanJob selectOne(QueryWrapper queryWrapper) {
        return conanJobMapper.selectOne(queryWrapper);
    }

    @Override
    public void update(ConanJob conanJob, QueryWrapper queryWrapper) {
        conanJobMapper.update(conanJob, queryWrapper);
    }
}
