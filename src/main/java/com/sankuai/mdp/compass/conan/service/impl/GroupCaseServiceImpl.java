package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import com.sankuai.mdp.compass.conan.mapper.GroupCaseMapper;
import com.sankuai.mdp.compass.conan.service.GroupCaseService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by xieyongrui on 2020/4/15.
 */
@Slf4j
@Service
public class GroupCaseServiceImpl extends ServiceImpl<GroupCaseMapper, GroupCase> implements GroupCaseService {
    @Autowired
    GroupCaseMapper groupCaseMapper;
    @Autowired
    GroupCaseService groupCaseService;

    @Override
    public boolean changeType(int id) {
        GroupCase groupCase = groupCaseMapper.selectOne(new QueryWrapper<GroupCase>().eq("id", id));
        try {
            String type = groupCase.getCaseRunningType();
            if (type.equals("regression")) {
                groupCase.setCaseRunningType("smoke");
            } else if (type.equals("smoke")) {
                groupCase.setCaseRunningType("regression");
            } else {
                return false;
            }
            groupCase.setUpdateTime(new Date());
            groupCaseService.update(groupCase);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean changeTypeAmount(int id) {
        GroupCase groupCase = groupCaseMapper.selectOne(new QueryWrapper<GroupCase>().eq("id", id));
        try {
            String type = groupCase.getCaseRunningType();
            if (type.equals("regression") || type.equals("smoke")) {
                groupCase.setCaseRunningType("regression&smoke");
            } else if (type.equals("regression&smoke")) {
                groupCase.setCaseRunningType("regression");
            } else {
//                log.info("Wrong selection");
                return false;
            }
            groupCase.setUpdateTime(new Date());
            groupCaseService.update(groupCase);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean changeStatus(int id) {
        GroupCase groupCase = groupCaseMapper.selectOne(new QueryWrapper<GroupCase>().eq("id", id));
        try {
            if (groupCase.getCaseStatus().equals(0)) {
                groupCase.setCaseStatus(1);
            } else {
                groupCase.setCaseStatus(0);
            }
            groupCase.setUpdateTime(new Date());
            groupCaseService.update(groupCase);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public void add(GroupCase groupCase) {
        groupCase.setUpdateTime(new Date());
        if (groupCase.getCasePlatform().toLowerCase().equals("all")) {
            groupCase.setCasePlatform("Android");
            groupCaseMapper.insert(groupCase);
            groupCase.setCasePlatform("iOS");
            groupCaseMapper.insert(groupCase);
        } else {
            groupCaseMapper.insert(groupCase);
        }
    }

    @Override
    public void delete(GroupCase groupCase) {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", groupCase.getId());
        groupCaseMapper.delete(queryWrapper);
    }

    @Override
    public void update(GroupCase groupCase) {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        groupCaseMapper.updateById(groupCase);
    }


    @Override
    public boolean initialize() {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        //修复case
        List<GroupCase> undifinedList = groupCaseMapper.selectList(new QueryWrapper<GroupCase>().eq("case_rely_on", "undifined"));
        List<GroupCase> firstLaunchList = groupCaseMapper.selectList(new QueryWrapper<GroupCase>().eq("case_rely_on", "undifined"));
        //重置状态
        List<GroupCase> list = groupCaseMapper.selectList(new QueryWrapper<GroupCase>().eq("is_updated", 1));
        try {
            if (undifinedList.size() > 0) {
                for (GroupCase groupCase : list) {
                    groupCase.setCaseRelyOn("");
                    groupCaseService.update(groupCase);
                }
//                log.info("存在未定义依赖项，已修复");
            }
            if (firstLaunchList.size() > 0) {
                for (GroupCase groupCase : list) {
                    groupCase.setCaseRelyOn("");
                    groupCaseService.update(groupCase);
                }
//                log.info("存在依赖项有com.meituan.autotest.group_platform.cases.firstLaunchCase.FirstLaunchAPPTest，已修复");
            }
            if (list.size() > 0) {
                for (GroupCase groupCase : list) {
                    groupCase.setIsUpdated(0);
                    groupCaseService.update(groupCase);
                }
            }
            return true;
        } catch (Exception e) {
//            Log.info("初始化失败：" + e);
            return false;
        }
    }

    @Override
    public String caseStyleInitialize() {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        List<GroupCase> caseList = groupCaseMapper.selectList(queryWrapper);
        int n = 0;
        StringBuilder res = new StringBuilder();
        for (GroupCase groupCase : caseList) {
            if (groupCase.getCaseClass().contains("\t") || groupCase.getCaseClass().contains(" ") || groupCase.getCaseName().contains("\t") || groupCase.getCaseName().contains(" ")) {
                groupCase.setCaseClass(groupCase.getCaseClass().replace("\t", "").replace(" ", ""));
                groupCase.setCaseName(groupCase.getCaseName().replace("\t", "").replace(" ", ""));
                n++;
                res.append(groupCase.getCaseClass()).append(".").append(groupCase.getCaseName()).append(";");
                groupCaseMapper.updateById(groupCase);
            }
        }
        return ("本次初始化共修改" + n + "个用例:" + res);
    }

    @Override
    public List<String> getAllPrincipal() {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<GroupCase> caseOwnerList = groupCaseMapper.selectList(queryWrapper
                .groupBy("case_owner"));
        for (GroupCase groupCase : caseOwnerList) {
            list.add(groupCase.getCaseOwner());
        }
        return list;
    }

    @Override
    public List<GroupCase> getClassList(String platform, String type) {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        List<GroupCase> classList = groupCaseMapper.selectList(queryWrapper
                .eq("is_updated", 0)
                .eq("case_status", 1)
                .like("case_running_type", type)
                .eq("case_platform", platform)
                .eq("case_channel", "platform_group")
                .ne("case_class", "com.meituan.autotest.group_platform.cases.firstLaunchCase.FirstLaunchAPPTest")
                .groupBy("case_class"));
        return classList;
    }

    @Override
    public List<String> getBaseClassList(String platform, String type) {
        List<GroupCase> classList = getClassList(platform, type);
        List<GroupCase> baseClassList = new ArrayList<>();
        List<GroupCase> tempClassList = new ArrayList<>();
        List<GroupCase> resultClassList = new ArrayList<>();
        List<String> baseStringList = new ArrayList<>();
        for (GroupCase groupCase : classList) {
            String tempClassName = groupCase.getCaseRelyOn();
            if (tempClassName.length() > 0 && !baseStringList.contains(tempClassName) && !groupCase.getCaseClass().equals(tempClassName)) {
                baseStringList.add(tempClassName);
                QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
                queryWrapper.like("case_running_type", type)
                        .eq("case_platform", platform)
                        .eq("case_channel", "platform_group")
                        .ne("case_class", "com.meituan.autotest.group_platform.cases.firstLaunchCase.FirstLaunchAPPTest");
                queryWrapper.eq("case_class", tempClassName);
                tempClassList = groupCaseMapper.selectList(queryWrapper);
                int flag = 0;
                for (GroupCase tempGroupCase : tempClassList) {
                    if (tempGroupCase.getCaseRelyOn().length() > 0) {
                        baseClassList.add(tempGroupCase);
                        flag = 1;
                        break;
                    }
                }
                if (flag == 0) {
                    baseClassList.add(tempClassList.get(0));
                }
            }
        }
        //baseclass自己需要排序
        int nextQueueFlag = 0;
        int nextClassFlag = 0;
        while (resultClassList.size() < baseClassList.size()) {
            //取一个起始项
            for (GroupCase baseClass : baseClassList) {
                //第一个
                if ((resultClassList.size() == 0
                        && baseClass.getCaseRelyOn().length() == 0
                        && !resultClassList.contains(baseClass))
                ) {
                    resultClassList.add(baseClass);
                    break;
                }
                //优先取其他queue首项
                else if (nextQueueFlag == 1
                        && baseClass.getCaseRelyOn().length() == 0
                        && !resultClassList.contains(baseClass)) {
                    resultClassList.add(baseClass);
                    nextQueueFlag = 0;
                    break;
                }
                //取一个依赖项不在queue中的queue中项
                else if ((nextQueueFlag == 1
                        && !resultClassList.contains(baseClass))
                        && ifInnerList(baseClass.getCaseClass(), baseClassList)) {
                    resultClassList.add(baseClass);
                    nextQueueFlag = 0;
                    break;
                } else if ((nextQueueFlag == 1
                        && !resultClassList.contains(baseClass))) {
                    resultClassList.add(baseClass);
                    nextQueueFlag = 0;
                    break;
                }
            }
            //取他的下一级
            nextClassFlag = 0;
            String lastName = resultClassList.get(resultClassList.size() - 1).getCaseClass();
            for (GroupCase outerbaseClass : baseClassList) {
                if (outerbaseClass.getCaseRelyOn().equals(lastName) & !resultClassList.contains(outerbaseClass)) {
                    resultClassList.add(outerbaseClass);
                    nextClassFlag = 1;
                }
            }

            if (nextClassFlag == 0) {
                nextQueueFlag = 1;
            }
        }
        baseStringList = new ArrayList<>();
        for (GroupCase resultClass : resultClassList) {
            baseStringList.add(resultClass.getCaseClass());
        }
        return baseStringList;
    }

    @Override
    public Boolean ifInnerList(String element, List<GroupCase> list) {
        for (GroupCase groupCase : list) {
            if (groupCase.getCaseRelyOn().equals(element)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<String> getDetailList(String className, String platform, String type) {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<GroupCase> classList = groupCaseMapper.selectList(queryWrapper
                .groupBy("case_class")
                .eq("is_updated", 0)
                .eq("case_status", 1)
                .like("case_running_type", type)
                .eq("case_platform", platform)
                .eq("case_channel", "platform_group"));
        for (GroupCase groupCase : classList) {
            list.add(groupCase.getCaseDetail());
        }
        return list;
    }

    @Override
    public String getCaseList(String className, String platform, String type) {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        StringBuilder res = new StringBuilder();
        List<GroupCase> classList = groupCaseMapper.selectList(queryWrapper
                .eq("case_class", className)
                .like("case_running_type", type)
                .eq("case_platform", platform));
        // System.err.print(className + ":");
        for (GroupCase groupCase : classList) {
            if (groupCase.getIsUpdated() == 0 & groupCase.getCaseStatus() == 1) {
                res.append(groupCase.getCaseName());
                res.append("<>");
            }
        }
        return res.toString();
    }

    @Override
    public String getRelyOnString(String className, String platform, String type) {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        List<GroupCase> groupCaseList = groupCaseMapper.selectList(queryWrapper
                .eq("case_class", className)
                .eq("case_status", 1)
                .like("case_running_type", type)
                .eq("case_platform", platform)
                .eq("case_channel", "platform_group").ne("case_rely_on", ""));
        GroupCase result = null;
        for (GroupCase groupCase : groupCaseList) {
            result = groupCase;
        }
        if (result != null)
            return result.getCaseRelyOn();
        else
            return "";
    }

    @Override
    public void getposttogroup(int status) {

    }

    @Override
    public void getpost(int status) {

    }

    @Override
    public String createXML(String type, String platform) {
//        String result = "";
//        if (groupCaseService.initialize()) {
//            result += type + "+" + platform + "\n========";
////            log.info("初始化成功,type:" + type + ",platform:" + platform);
//        } else {
////            log.info("初始化失败");
//            return "初始化失败";
//        }
//        if (platform.toLowerCase().equals("ios")) {
//            platform = "iOS";
//        } else if (platform.toLowerCase().equals("android")) {
//            platform = "Android";
//        } else {
////            log.info("platform初始化失败，检查参数");
//            return "platform初始化失败，检查参数";
//        }
//        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<GroupCase>()
//                .eq("is_updated", 0)
//                .eq("case_status", 1)
//                .like("case_running_type", type)
//                .eq("case_platform", platform)
//                .eq("case_channel", "platform_group")
//                .ne("case_class", "com.meituan.autotest.group_platform.cases.firstLaunchCase.FirstLaunchAPPTest");
//        int caseGoal = groupCaseMapper.selectList(queryWrapper).size();
//        int caseCount = 0;
////        log.info("caseGoal:" + caseGoal);
//        int pageCount = 0;
//        int classcount = 0;
//        int loopProtect = 0;
//        List<String> classList = new ArrayList<>();
//        List<GroupCase> list = groupCaseService.getClassList(platform, type);
//        List<String> baselist = groupCaseService.getBaseClassList(platform, type);
//        //System.err.println(list);
//        List<GroupCase> templist;
//        List<GroupCase> specialList = new ArrayList<>();
//        //class的列表排序
//        String currentGroupCase = "";
//        for (String base : baselist) {
//            if (!classList.contains(base)) {
//                classList.add(base);
//                currentGroupCase = base;
//            }
//            while (true) {
//                templist = new ArrayList<>();
//                int tempSignal = 0;
//                for (GroupCase groupCase : list) {
//                    if (!groupCase.getCaseClass().equals(currentGroupCase)) {
//                        //放在末尾
//                        if (groupCase.getCaseClass().equals(groupCase.getCaseRelyOn())
//                                && !specialList.contains(groupCase)
//                                && !groupCase.getCaseRelyOn().equals("")) {
//                            specialList.add(groupCase);
//                        }
//                        //顺序
//                        else if (groupCase.getCaseRelyOn().equals(currentGroupCase)
//                                && !groupCase.getCaseClass().equals(currentGroupCase)
//                                && !specialList.contains(groupCase)
//                                && !classList.contains(groupCase.getCaseClass())) {
//                            classList.add(groupCase.getCaseClass());
//                            currentGroupCase = groupCase.getCaseClass();
//                            tempSignal = 1;
//                        }
//                    }
//                }
//                for (GroupCase groupCase : list) {
//                    if (!specialList.contains(groupCase) && !classList.contains(groupCase.getCaseClass())) {
//                        templist.add(groupCase);
//                    }
//                }
//                list = templist;
//                if (tempSignal == 0) {
//                    break;
//                }
//            }
//        }
//        for (GroupCase groupCase : list) {
//            if (!specialList.contains(groupCase)
//                    && !classList.contains(groupCase.getCaseClass()))
//                classList.add(groupCase.getCaseClass());
//        }
////        log.info("去重前classList:" + classList);
//        List<String> tempClassList = new ArrayList<>();
//        for (String classMember : classList) {
//            if (!tempClassList.contains(classMember)) {
//                tempClassList.add(classMember);
//            }
//        }
//        classList = tempClassList;
////        log.info("去重后classList:" + classList);
//        //排序毕
//        log.info("classGoal:" + classList.size());
//        if (specialList.size() == 0)
//            specialList = null;
//        while (caseCount < caseGoal - 1) {
//            pageCount++;
//            Document document = DocumentHelper.createDocument();
//            document.addDocType("suite", null, "http://testng.org/testng-1.0.dtd");
//            // 2、创建根节点
//            Element suite = document.addElement("suite");
//            // 3、向suite节点添加version属性
//            suite.addAttribute("name", "group" + platform + "TestSuite");
//            suite.addAttribute("parallel", "false");
//            suite.addAttribute("junit", "false");
//            suite.addAttribute("guice-stage", "DEVELOPMENT");
//            suite.addAttribute("configfailurepolicy", "skip");
//            suite.addAttribute("thread-count", "5");
//            suite.addAttribute("skipfailedinvocationcounts", "false");
//            suite.addAttribute("data-provider-thread-count", "10");
//            suite.addAttribute("group-by-instances", "false");
//            suite.addAttribute("preserve-order", "true");
//            suite.addAttribute("allow-return-values", "false");
//            // 4、生成子节点及子节点内容
//            Element test = suite.addElement("test");
//            test.addAttribute("name", type + "Case");
//            test.addAttribute("preserve-order", "true");
//            test.addAttribute("junit", "false");
//            test.addAttribute("skipfailedinvocationcounts", "false");
//            test.addAttribute("group-by-instances", "false");
//            test.addAttribute("allow-return-values", "false");
////            Element groups = test.addElement("groups");
////            Element run = groups.addElement("run");
////            Element includeRun = run.addElement("include");
////            if (type.equals("regression")) {
////                includeRun.addAttribute("name", "all");
////            } else if (type.equals("smoke")) {
////                includeRun.addAttribute("name", "smoke");
////            }
//            Element classes = test.addElement("classes");
//            int n = 0;
//            int signal = 0;
//            Element firstSetClassContent = classes.addElement("class");
//            String relyOn = "com.meituan.autotest.group_platform.cases.firstLaunchCase.FirstLaunchAPPTest";
//            firstSetClassContent.addAttribute("name", relyOn);
//            Element firstMethods = firstSetClassContent.addElement("methods");
//            Element firstInclude = firstMethods.addElement("include");
//            firstInclude.addAttribute("name", "testFirstSetCity");
//            groupCaseService.updateXMLCaseStatus(relyOn, "testFirstSetCity", platform, type);
//            while ((n < 12 || signal == 1) && caseCount < caseGoal && classcount < classList.size()) {
//                String className = classList.get(classcount);
//                String relyOnClass = groupCaseService.getRelyOnString(className, platform, type);
//                if (relyOnClass.equals(relyOn) && !relyOnClass.equals("")) {
//                    signal = 1;
//                } else {
//                    signal = 0;
//                }
//                if (n >= 12 && signal == 0) {
//                    break;
//                }
//                String casenames = groupCaseService.getCaseList(className, platform, type);
//                Element classcontent = classes.addElement("class");
//                classcontent.addAttribute("name", className);
//                Element methods = classcontent.addElement("methods");
//                // System.err.println("class:" + className + "\ncase:" + casenames);
//                for (String casename : casenames.split("<>")) {
//                    Element include = methods.addElement("include");
//                    include.addAttribute("name", casename);
//                    groupCaseService.updateXMLCaseStatus(className, casename, platform, type);
//                    n++;
//                    caseCount++;
//                }
//                relyOn = relyOnClass;
//                classcount++;
//            }
//            if (specialList != null) {
//                Element lastSetClassContent = classes.addElement("class");
//                String lastClassName = specialList.get(0).getCaseClass();
//                lastSetClassContent.addAttribute("name", lastClassName);
//                Element lastMethods = lastSetClassContent.addElement("methods");
//                String casenames = groupCaseService.getCaseList(lastClassName, platform, type);
//                //System.err.println("class:" + lastClassName + "\ncase:" + casenames);
//                for (String casename : casenames.split("<>")) {
//                    Element include = lastMethods.addElement("include");
//                    include.addAttribute("name", casename);
//                    groupCaseService.updateXMLCaseStatus(lastClassName, casename, platform, type);
//                    caseCount++;
//                }
//                if (specialList.size() > 1)
//                    specialList = specialList.subList(1, specialList.size());
//                else
//                    specialList = null;
//            }
//
//            // 5、设置生成xml的格式
//            OutputFormat format = OutputFormat.createPrettyPrint();
//            // 设置编码格式
//            format.setEncoding("UTF-8");
//            // 6、生成xml文件
//            if (0 == n) {
//                break;
//            }
//            try {
//                String XMLStr = document.asXML();//obj.asXML()则为Document对象转换为字符串方法\
//                result += "\n" + XMLStr + "\n========";
////                log.info("生成" + type + pageCount + ".xml成功");
//            } catch (Exception e) {
//                e.printStackTrace();
//                return "文件生成失败";
//            }
//            loopProtect++;
//            log.info("目标：" + caseGoal + ";现状: " + caseCount);
//            if (loopProtect > 14)
//                break;
//        }
      //  return result;
        return null;
    }

    @Override
    public void updateXMLCaseStatus(String className, String caseName, String casePlatform, String caseRunningType) {
        List<GroupCase> list = groupCaseMapper.selectList(new QueryWrapper<GroupCase>()
                .eq("case_name", caseName)
                .eq("case_class", className)
                .eq("case_platform", casePlatform)
                .like("case_running_type", caseRunningType));
        for (GroupCase groupCase : list) {
            groupCase.setIsUpdated(1);
            groupCaseService.update(groupCase);
        }
    }


    @Override
    public List<String> getAllCaseNames() {
        QueryWrapper<GroupCase> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();

        // 使用 or 条件来筛选 case_type 为 smoke 或 regression 的记录
        List<GroupCase> groupCases = groupCaseMapper.selectList(
                queryWrapper.and(wrapper -> wrapper.eq("case_type", "regression")
                                .or()
                                .eq("case_type", "smoke"))
                        .groupBy("case_name")
        );

        for (GroupCase groupCase : groupCases) {
            list.add(groupCase.getCaseName());
        }
        return list;
    }

    @Override
    public IPage<GroupCase> list(QueryRequest request, GroupCase groupCase) {
        try {
            LambdaQueryWrapper<GroupCase> queryWrapper = new LambdaQueryWrapper<>();
            if (null != groupCase.getCaseDetail()) {
                queryWrapper.like(GroupCase::getCaseDetail, groupCase.getCaseDetail());
            }
            if (null != groupCase.getCaseName()) {
                queryWrapper.eq(GroupCase::getCaseName, groupCase.getCaseName());
            }
            if (null != groupCase.getCaseOwner()) {
                queryWrapper.eq(GroupCase::getCaseOwner, groupCase.getCaseOwner());
            }
            if (null != groupCase.getCaseChannel()) {
                queryWrapper.eq(GroupCase::getCaseChannel, groupCase.getCaseChannel());
            }
            if (null != groupCase.getCaseCategory()) {
                if (groupCase.getCaseCategory().contains(",")) {
                    String[] list = groupCase.getCaseCategory().split(",");
                    queryWrapper.in(GroupCase::getCaseCategory, list);
                } else {
                    queryWrapper.eq(GroupCase::getCaseCategory, groupCase.getCaseCategory());
                }
            }
            if (null != groupCase.getCaseRunningType()) {
                queryWrapper.eq(GroupCase::getCaseRunningType, groupCase.getCaseRunningType());
            }
            if (null != groupCase.getCasePlatform()) {
                queryWrapper.eq(GroupCase::getCasePlatform, groupCase.getCasePlatform());
            }
            Page<GroupCase> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }


}
