package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.utils.AquamanUtil;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.JenkinsUtil;
import com.sankuai.mdp.compass.conan.entity.EventTrackingAuto;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.mapper.EventTrackingAutoMapper;
import com.sankuai.mdp.compass.conan.mapper.MgeCaseMapper;
import com.sankuai.mdp.compass.conan.service.ETAutoService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * Created by lizhen on 2020/02/05.
 */
@Slf4j
@Service
public class ETAutoServiceImpl extends ServiceImpl<EventTrackingAutoMapper, EventTrackingAuto> implements ETAutoService {
    @Autowired
    EventTrackingAutoMapper eventTrackingAutoMapper;

    @Autowired
    MgeCaseMapper mgeCaseMapper;

    JenkinsUtil jenkinsUtil = new JenkinsUtil();

    AquamanUtil aquamanUtil = new AquamanUtil();

    DxUtil dxUtil = new DxUtil();

    long groupId = 64974399408L;//埋点测试结果通知大象群

    @Override
    public Integer addEtAuto(EventTrackingAuto etAuto) {
        try {
            eventTrackingAutoMapper.insert(etAuto);
//            jenkinsUtil.newEtAutoJob(etAuto);//调起遍历和UI的Jekins任务
            return etAuto.getJobId();
        }   catch (Exception e){
            log.info(e.getMessage());
            return 0;
        }
    }

    @Override
    public void updateReportId(String jobId,String reportId) {
        synchronized(this){
            QueryWrapper<EventTrackingAuto> queryWrapper = new QueryWrapper<>();
            EventTrackingAuto etAuto = eventTrackingAutoMapper.selectOne(queryWrapper.eq("id", Integer.parseInt(jobId)));
            String id = etAuto.getReportIds()+"-"+reportId;
            etAuto.setReportIds(id);
            eventTrackingAutoMapper.updateById(etAuto);
        }
    }

    @Override
    public void analysisResult(JSONObject body) throws IOException {
        log.info(body.toString());
        JsonParser jsonParser = new JsonParser();
        String notifyMessage = "";
        Integer JobStatus = 1;//标识自动化检测任务成功与否
        //QueryWrapper<MgeCase> queryWrappermge = new QueryWrapper<>();
        QueryWrapper<EventTrackingAuto> queryWrapper = new QueryWrapper<>();
        JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
        String reportUrl = jobData.get("report").getAsString();
        String conanId = jobData.get("id").getAsString();//云测任务id
        EventTrackingAuto etAuto = eventTrackingAutoMapper.selectOne(queryWrapper.like("report_ids", conanId));
        log.info("对应检测任务："+etAuto);
        String reportId = "-" + conanId + etAuto.getConanStatus();
        etAuto.setConanStatus(reportId);
        if(etAuto.getReportIds().length() == reportId.length()){
            JSONArray resultArray  = aquamanUtil.collectResult(etAuto.getVersion(), etAuto.getBuildNumber());
            //etAuto.setOceanResult(resultArray.toString());
            if(resultArray.equals(null)){
                log.info("埋点结果list表为空！");
            }else{
                for(int i=0;i<resultArray.size();i++){
                    JSONObject resultObject = resultArray.getJSONObject(i);
                    if(resultObject != null){
                        if(resultObject.getString("status").equals(("success"))){
                            continue;
                        }else{
                            JobStatus = 0;
                            String bid = resultObject.getString("bid");
                            String cid = resultObject.getString("cid");
                            //根据bid、cid查数据库找到QA埋点负责人，暂时compass先处理，后面给灵犀提需求
                            //MgeCase mgeCase = mgeCaseMapper.selectOne(queryWrappermge.eq("bid", bid));
                            //String qa = mgeCase.getPrincipal();
                            //notifyMessage = notifyMessage + "cid:" + cid + "，" + "bid：" + bid + "，" + "QA：" + qa + "；";
                            notifyMessage = notifyMessage + "cid:" + cid + "，" + "bid：" + bid;
                        }
                    }
                }
                try {
                    if(JobStatus == 1){
                        dxUtil.sendToGroupByCompass("【埋点自动化校验结果通知】埋点检测通过！",groupId);//测试结果大象通知
                        aquamanUtil.sendResult(etAuto.getJobId(),JobStatus,reportUrl);//成功测试结果回调Zero-X
                    }else{
                        dxUtil.sendToGroupByCompass("【埋点自动化校验结果通知】埋点检测失败！以下埋点未上报：" + notifyMessage,groupId);//测试结果大象通知
                        aquamanUtil.sendResult(etAuto.getJobId(),JobStatus,reportUrl);//失败测试结果回调Zero-X，url暂时选一个云测的链接，灵犀调整好后传的链接就是埋点测试结果链接
                    }
              } catch (Exception e) {
                    e.printStackTrace();
                    log.info(e.getMessage());
                }
            }
        }
        eventTrackingAutoMapper.updateById(etAuto);
    }
}
