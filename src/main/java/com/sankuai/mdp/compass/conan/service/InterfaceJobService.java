package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.InterfaceJob;
import net.minidev.json.JSONObject;

public interface InterfaceJobService extends IService<InterfaceJob> {
    IPage<InterfaceJob> list(QueryRequest request, InterfaceJob interfaceJob);

    Resp updateStatusById(InterfaceJob interfaceJob);

    Integer lastestJobId(InterfaceJob interfaceJob);

    Resp updateReportId(InterfaceJob interfaceJob);

    Integer add(InterfaceJob interfaceJob);
}
