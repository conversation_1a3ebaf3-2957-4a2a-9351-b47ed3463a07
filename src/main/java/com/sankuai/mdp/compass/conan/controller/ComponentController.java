package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Component;
import com.sankuai.mdp.compass.conan.service.ComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created by xieyongrui on 2020/6/12.
 */
@RestController
@RequestMapping("/compass/api/component")
public class ComponentController extends BaseController{
    @Autowired
    ComponentService componentService;

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, Component component)  {
        IPage<Component> ComponentIPage = this.componentService.list(request,component);
        if (ComponentIPage != null) {
            return getDataTable(ComponentIPage);
        } else {
            return null;
        }
    }

    @PostMapping("/add")
    public Boolean add(@RequestBody String body)  {
        return this.componentService.add(new JsonParser().parse(body).getAsJsonObject());
    }

    @GetMapping("/searchHpxComp")
    public List<String> hpxComp(String platform) {
        return componentService.hpxComp(platform);
    }

    @GetMapping("/searchCategoryByComponent")
    public String searchCategory(@RequestParam String component) {
        return componentService.searchCategory(component);
    }

    @GetMapping("/delete")
    public void deleteByNameAndPlatform(Component component) {
        componentService.delete(component);
    }
}
