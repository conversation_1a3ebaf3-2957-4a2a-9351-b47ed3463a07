package com.sankuai.mdp.compass.conan.controller;

import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.conan.service.ConanCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by xieyongrui on 2020/4/6.
 */
@RestController
@RequestMapping("/compass/api/autotest/case")
public class ConanCaseController {
    @Autowired
    ConanCaseService conanCaseService;

    public void insertObject(JsonObject jsonObject) {
        conanCaseService.insetObject(jsonObject);
    }
}
