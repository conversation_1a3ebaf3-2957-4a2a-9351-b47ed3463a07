package com.sankuai.mdp.compass.conan.service.impl;

import com.alibaba.fastjson.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.jayway.jsonpath.JsonPath;
import com.saasquatch.jsonschemainferrer.*;

import java.io.IOException;
import java.io.StringReader;
import java.util.*;
public class JsonSchema {
    static final String REGEX = "\\[[0-9]+\\]";


    /**
     *
     * @param checkData: 根据原始数据，直接生成一个仅包含required的jsonSchema
     * @return： 返回一个生成的jsonSchema字符串
     * @throws IOException 文件操作
     */
    public String getSchema(String checkData) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonSchemaInferrer inferrer = JsonSchemaInferrer.newBuilder()
                .setSpecVersion(SpecVersion.DRAFT_06)
                // Requires commons-validator
                .addFormatInferrers(FormatInferrers.email(), FormatInferrers.ip())
                .setAdditionalPropertiesPolicy(AdditionalPropertiesPolicies.allowed())
                .setRequiredPolicy(RequiredPolicies.nonNullCommonFields())
                .addEnumExtractors(EnumExtractors.validEnum(java.time.Month.class),
                        EnumExtractors.validEnum(java.time.DayOfWeek.class))
                .build();
        JsonNode sample1 = mapper.readTree(checkData);
//        JsonNode sample2 = mapper.readTree(
//                "{\"🙈\":1,\"🙉\":{\"🐒\":true,\"🐵\":[2,\"1234:5678::\"],\"🍌\":null},\"🙊\":null,\"months\":[\"JANUARY\",\"FEBRUARY\"]}");
        JsonNode resultForSample1 = inferrer.inferForSample(sample1);
        return resultForSample1.toString();
//            final JsonNode resultForSample1And2 =
//                    inferrer.inferForSamples(Arrays.asList(sample1, sample2));
//            for (JsonNode j : Arrays.asList(sample1, sample2, resultForSample1, resultForSample1And2)) {
//                System.out.println(mapper.writeValueAsString(j));
//            }
    }

    /**
     * 方法会根据两个输入，生成一个符合要求的jsonSchema，包含了required校验和const校验
     * @param userConstValueJson: 用户定义用来标记哪些value是要做正确性校验的JsonString，JsonString中标记了哪些叶子节点需要校验值的正确性。需要校验正确性的value为true
     * @param originData：用户定义的原始校验列表，默认所有值都要生成required校验
     * @throws IOException:用到了文件操作，可能会抛出异常。
     */
    public String setConstValueByDataToSchemaJson(String userConstValueJson, String originData) throws IOException {
        String schemaJson = getSchema(originData);
        JsonObject leafJson = parseJson(userConstValueJson);
        com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(schemaJson);
        Set<String> keySet = leafJson.keySet();
        for (String item:keySet){
            // 为true的叶子结点才需要添加const参数
            if (leafJson.get(item).getAsBoolean()){
                List<String> roadList = Arrays.asList(item.split("\\."));
                StringBuilder jsonPathTemp = new StringBuilder("$");
                if (roadList.size()>1){
                    for (int i=1; i<roadList.size(); ++i){
                        jsonPathTemp.append(".properties" + ".").append(roadList.get(i));
                    }
                    if (JsonPath.parse(originData).read(item) instanceof Integer){
                        JSONPath.set(jsonObject1, jsonPathTemp.append(".").append("const").toString(), ((Integer) JsonPath.parse(originData).read(item)).longValue());
                    }
                    else if (JsonPath.parse(originData).read(item) instanceof Boolean){
                        JSONPath.set(jsonObject1, jsonPathTemp.append(".").append("const").toString(), (Boolean)JsonPath.parse(originData).read(item));
                    }
                    else if (JsonPath.parse(originData).read(item) instanceof Number){
                        JSONPath.set(jsonObject1, jsonPathTemp.append(".").append("const").toString(), ((Number) JsonPath.parse(originData).read(item)).longValue());

                    }
                    else if (JsonPath.parse(originData).read(item) instanceof String){
                        JSONPath.set(jsonObject1, jsonPathTemp.append(".").append("const").toString(), JsonPath.parse(originData).read(item).toString());
                    }
                }
            }
        }
        System.out.println(jsonObject1.toString());
//        osw.write(jsonObject1.toJSONString());
//        osw.flush();
//        osw.close();
        return jsonObject1.toString();
    }

    // 解析一个json路径上的所有叶子节点
    static JsonObject parseJson(String json) throws IOException {
        JsonObject jsonObject = new JsonObject();
        JsonReader reader = new JsonReader(new StringReader(json));
        reader.setLenient(true);
        while (true) {
            JsonToken token = reader.peek();
            switch (token) {
                case BEGIN_ARRAY:
                    reader.beginArray();
                    break;
                case END_ARRAY:
                    reader.endArray();
                    break;
                case BEGIN_OBJECT:
                    reader.beginObject();
                    break;
                case END_OBJECT:
                    reader.endObject();
                    break;
                case NAME:
                    reader.nextName();
                    break;
                case STRING:
                    String s = reader.nextString();
                    jsonObject.addProperty(reader.getPath(), s);
                    break;
                case NUMBER:
                    Long n = reader.nextLong();
                    jsonObject.addProperty(reader.getPath(), n);
                    break;
                case BOOLEAN:
                    boolean b = reader.nextBoolean();
                    jsonObject.addProperty(reader.getPath(), b);
                    break;
                case NULL:
                    reader.nextNull();
                    break;
                case END_DOCUMENT:
                    return jsonObject;
            }
        }
    }
    public static void main(String[] argv) throws IOException {
        JsonSchema jsonTest1 = new JsonSchema();
        String data1="{\"m-shark-traceid\":\"1010000000000000A2BA14A204B7486C92BD84A0C8B52E05A1594778092063745542ca2fd1599248813879c08939\",\"X-CAT-TRACE-MODE\":\"true\",\"mkunionid\":\"0000000000000A2BA14A204B7486C92BD84A0C8B52E05A159477809206374554\",\"request-belong\":\"com.meituan.android.pt.homepage.activity.MainActivity\",\"x-forwarded-for\":\"************\",\"mtgdid\":\"AAAAAAAAAAAAAIFU90N2eJS3LO-Ha39rbYqwI3Dm7kMg0waKm9Lxhjoj2S028neRqvmwXY38yUkyYY6jhyNW81tyK-bBnGmTblI\",\"userid\":\"1589295871\",\"x-real-ip\":\"************\",\"pragma-os\":\"MApi 1.1 com.sankuai.meituan 11.0.200 meituaninternaltest meizu_17_Pro; Android 10\",\"pragma-mtid\":\"a2ba14a204b7486c92bd84a0c8b52e05a159477809206374554\",\"siua\":\"/SrhgI47JotmoI8soXvoRfOcOkPE23StAh33EkMwQ9l1yR6AGJPOchogF7w2rPfmJ4bwCLFXy2gp2TeZwN9URQaiiJiqhi69/JvRb1VGPEUl/Dym0QSrdng+zDzbvWF70nM3iq/KE9+C5pC6FhvvWnOmZOPHRkOjI0HtfWHEhy0dNhDzeHEO7I54X/11IlX9uZrJRYisyOQtq4M3zkWrtQc+iqereI3G3psUiTZaAMH4NbHOPATHHfALG6Wygd9tHPBsPPdImWwZdxPl8i4CrasyA+2Ls54Wj9PzpKn8Y6Oz80Aof0jbqyseZXnBGIeMQMFCqybsl9P15m5nen6ygA5QuJ6CbIFXLeT+zTvzDtpRne1EIvpS5CoVN6EI64tfnitDGZHiB9TUsuyiLI+XFFyUcriirAKj2nkDpgZt87xYdyInxI/uPHH4hRU4QxlaEwO9erR+8/qoFRdBw5uz3sRjqW95NitnSzZiuLQfLlc0F4W4gNqV7M+f2FOo5tuJSs10+94hrX460M0MwriMVUTE+bSEfpdWJj6bep3wJFOKpyGQiFQ+S9Rl0sdQcRF97yaMCxymMqY7IScKD1RS8s0jwizdDv89XnRGcZKe+qXLEbvRLOi0KQ9QMBhhA5q/\",\"host\":\"mop.meituan.com\",\"mkoriginhost\":\"mop.meituan.com\",\"mktunneltype\":\"http\",\"connection\":\"upgrade\",\"accept-encoding\":\"gzip\",\"user-agent\":\"AiMeiTuan /meizu-10-meizu 17 Pro-2250x1080-480-11.0.200-1100000200-a2ba14a204b7486c92bd84a0c8b52e05a159477809206374554-meituaninternaltest\",\"mkscheme\":\"https\"}";

        String originData1="{\"host\":\"mop.meituan.comm\"}'";
        String originData=originData1.replace("-","_");
        String params="$.host";

        Interfacecheck c=new Interfacecheck();
        JsonSchema jsonSchema=new JsonSchema();
//        String jsonCheckValueData = "{\"msid\":true,\"uuid\":true}";
//        String newJson=JsonPath.parse(jsonCheckValueData).set(s, "Paul").jsonString();
        String path=c.jsonCheckValueData(params, originData);//将选定的节点标记true的json字符串
        String inputSchema=jsonSchema.setConstValueByDataToSchemaJson(path, originData);//在标记为true的节点上添加const
//        String result = jsonTest1.setConstValueByDataToSchemaJson(jsonCheckValueData, originData);

        String s=c.checkJson(data1.replace("-","_"),inputSchema);
        System.out.println(s);
    }

}
