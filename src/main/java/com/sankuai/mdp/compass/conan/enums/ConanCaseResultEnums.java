package com.sankuai.mdp.compass.conan.enums;

public enum ConanCaseResultEnums {
    FAIL(0,"失败"),
    PASS(1,"成功"),
    CRASH(2,"crash"),
    SKIP(3,"skip"),
    WAIT(4,"wait");

    private final Integer code;
    private final String msg;

    ConanCaseResultEnums(Integer code, String msg){
        this.code=code;
        this.msg=msg;
    }
    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
