package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Interface;
import com.sankuai.mdp.compass.conan.mapper.InterfaceMapper;
import com.sankuai.mdp.compass.conan.service.InterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class InterfaceServiceImpl extends ServiceImpl<InterfaceMapper, Interface> implements InterfaceService {
    @Autowired
    InterfaceMapper interfaceMapper;

    @Override
    public List<Interface> getListByCaseName(String api) {
        QueryWrapper<Interface> queryWrapper = new QueryWrapper<>();
        List<Interface> interfaceList = interfaceMapper.selectList(queryWrapper.eq("api", api));
        return interfaceList;
    }

    @Override
    public IPage<Interface> list(QueryRequest request, Interface interface1) {
        try {
            LambdaQueryWrapper<Interface> queryWrapper = new LambdaQueryWrapper<>();

            if (null != interface1.getApi()) {
                queryWrapper.like(Interface::getApi, interface1.getApi());
            }
            Page<Interface> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public void add(Interface interface1) throws IOException {
        interfaceMapper.insert(interface1);
    }

    @Override
    public void delete(Interface interface1) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", interface1.getId());
        interfaceMapper.delete(queryWrapper);
    }

    @Override
    public void update(Interface interface1) {
        QueryWrapper<Interface> queryWrapper = new QueryWrapper<>();
        interfaceMapper.update(interface1, queryWrapper
                .eq("id", interface1.getId()));
    }

    @Override
    public List<Interface> select() {
        QueryWrapper queryWrapper = new QueryWrapper();
        return interfaceMapper.selectList(queryWrapper);
    }

    @Override
    public void updateContent() {
        List<Interface> interfaces = interfaceMapper.selectList(new QueryWrapper<Interface>());
        for (Interface interface1 : interfaces) {
            interfaceMapper.updateById(interface1);
        }
    }
    @Override
    public List<String> getAllApi() {
        QueryWrapper<Interface> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<Interface> interfaceList = interfaceMapper.selectList(queryWrapper
                .groupBy("api"));
        for (Interface interface1 : interfaceList) {
            list.add(interface1.getApi());
        }
        return list;
    }


}
