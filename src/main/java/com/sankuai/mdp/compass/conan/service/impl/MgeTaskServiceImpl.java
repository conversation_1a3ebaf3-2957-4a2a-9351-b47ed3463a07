package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.GulfUtil;
import com.sankuai.mdp.compass.common.utils.SigmaUtil;
import com.sankuai.mdp.compass.common.utils.TalosUtil;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.entity.MgeCount;
import com.sankuai.mdp.compass.conan.entity.MgeTask;
import com.sankuai.mdp.compass.conan.mapper.MgeJobMapper;
import com.sankuai.mdp.compass.conan.mapper.MgeTaskMapper;
import com.sankuai.mdp.compass.conan.service.MgeCaseService;
import com.sankuai.mdp.compass.conan.service.MgeJobService;
import com.sankuai.mdp.compass.conan.service.MgeTaskService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by xubangxi on 2020/05/18.
 */
@Slf4j
@Service
public class MgeTaskServiceImpl extends ServiceImpl<MgeTaskMapper, MgeTask> implements MgeTaskService {
    @Autowired
    MgeTaskMapper mgeTaskMapper;
    @Autowired
    MgeJobMapper mgeJobMapper;

    @Autowired
    MgeTaskService mgeTaskService;

    @Autowired
    MgeCaseService mgeCaseService;
    @Autowired
    MgeJobService mgeJobService;

    GulfUtil gulfUtil = new GulfUtil();

    TalosUtil talosUtil = new TalosUtil();

    SigmaUtil sigmaUtil = new SigmaUtil();

    String fileName = "./test.xlsx";
    long pushNameId = 64093934566L;
    long pushNameIdTest = 664974399408L;
    long searchGroupId = 64093934566L;
    DxUtil dxUtil = new DxUtil();

    @Override
    public void add(MgeTask mgeTask) {
        mgeTaskMapper.insert(mgeTask);
    }


    @Override
    public void update(MgeTask mgeTask) {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("event_name", mgeTask.getEventName());
        mgeTaskMapper.update(mgeTask, queryWrapper);
    }


    @Override
    public MgeCount count(String version) {
//        log.info("version>>>"+version);
        MgeCount count = mgeTaskMapper.count(version);
//        log.info(count.toString());
        return count;

    }


    @Override
    public void skip(int id, String operator) {
        MgeTask mgeTask = mgeTaskMapper.selectOne(new QueryWrapper<MgeTask>().eq("id", id));
        String status = mgeTask.getStatus();
        if (status.contains("(set skiped status as succeed)")) {
            mgeTask.setStatus(status.substring(0, status.indexOf("(set skiped status as succeed)")));
            mgeTask.setUpdateTime(new Date());
            mgeTask.setAffirmStatus(-1);
            mgeTask.setOperator(operator);
        } else {
            mgeTask.setStatus(mgeTask.getStatus() + "(set skiped status as succeed)");
            mgeTask.setUpdateTime(new Date());
            mgeTask.setAffirmStatus(1);
            mgeTask.setOperator(operator);
        }
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("event_name", mgeTask.getEventName());
        mgeTaskMapper.update(mgeTask, queryWrapper);
    }

    @Override
    public void reportBug(int id, String operator) {
        MgeTask mgeTask = mgeTaskMapper.selectOne(new QueryWrapper<MgeTask>().eq("id", id));
        int bStatus = mgeTask.getIsBug();
        if (1 == bStatus) {
            //撤销Bug
            mgeTask.setIsBug(0);
            mgeTask.setAffirmStatus(-1);
        } else {
            //确认为Bug
            mgeTask.setIsBug(1);
            mgeTask.setAffirmStatus(0);
        }
        mgeTask.setOperator(operator);
        mgeTask.setUpdateTime(new Date());
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("event_name", mgeTask.getEventName());
        mgeTaskMapper.update(mgeTask, queryWrapper);
    }

    @Override
    public List<MgeTask> select() {
        QueryWrapper queryWrapper = new QueryWrapper();
        return mgeTaskMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<MgeTask> failedTasklist(QueryRequest request, MgeTask mgeTask) {
        try {
            LambdaQueryWrapper<MgeTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MgeTask::getAppVersion, version())
                    .ne(MgeTask::getStatus, "succeed");
            queryWrapper.orderByDesc(MgeTask::getUpdateTime);
            if (null != mgeTask.getPrincipal()) {
                queryWrapper.eq(MgeTask::getPrincipal, mgeTask.getPrincipal());
            }
            if (!request.getSwitchStatus()) {
                queryWrapper.notLike(MgeTask::getStatus, "succeed");
            }
            Page<MgeTask> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public IPage<MgeTask> list(QueryRequest request, MgeTask mgeTask) {
        try {
            LambdaQueryWrapper<MgeTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(MgeTask::getAppVersion);
            if (null != mgeTask.getPrincipal()) {
                queryWrapper.eq(MgeTask::getPrincipal, mgeTask.getPrincipal());
            }
            if (null != mgeTask.getBusinessName()) {
                queryWrapper.like(MgeTask::getBusinessName, mgeTask.getBusinessName());
            }
            if (null != mgeTask.getAppVersion()) {
                queryWrapper.eq(MgeTask::getAppVersion, mgeTask.getAppVersion());
            }

            if (request.getSwitchStatus()) {
                queryWrapper.ne(MgeTask::getStatus, "succeed");
            }
            String version = request.getVersion();
            if (version != null && version != "") {
                queryWrapper.eq(MgeTask::getAppVersion, version);
            }
            if ("android".equals(mgeTask.getDeviceType()) || "iphone".equals(mgeTask.getDeviceType())) {
                queryWrapper.eq(MgeTask::getDeviceType, mgeTask.getDeviceType());
            }
            if ("平台".equals(mgeTask.getMoudle()) || "搜索".equals(mgeTask.getMoudle()) ){
                queryWrapper.eq(MgeTask::getMoudle, mgeTask.getMoudle());
            }
            queryWrapper.orderByAsc(MgeTask::getAffirmStatus);
            Page<MgeTask> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<MgeTask> record = this.page(page, queryWrapper);
            if (version != null && !version.equals("11.9.400") && version.compareTo("11.10.200") < 0) {
                for (int i = 0; i < record.getRecords().size(); i++) {
                    MgeTask item = record.getRecords().get(i);
                    record.getRecords().get(i).setReportTag(item.getTag());
                    record.getRecords().get(i).setReportCid(item.getPageIdentifier());
                    record.getRecords().get(i).setReportLab(item.getEventAttribute());
                }
            }
            return record;

        } catch (Exception e) {

            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public List<String> getAllVersions() {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<MgeTask> mgelist = mgeTaskMapper.selectList(queryWrapper
                .ne("principal", "Job")
                .ne("event_timestamp", "null")
                .groupBy("app_version").orderByDesc("update_time"));
        for (MgeTask mgetask : mgelist) {
            list.add(mgetask.getAppVersion());
        }
        return list;
    }

    @Override
    public void updatePrincipal(String version) {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        List<MgeTask> mgelist = mgeTaskMapper.selectList(queryWrapper
                .eq("app_version",version));
        JSONObject caseMap = new JSONObject();
        for (MgeTask mgetask : mgelist) {
            String bid = mgetask.getEventId();
            String businessName = mgetask.getBusinessName();
            String principal = "";
            if (caseMap.has(bid+"_"+businessName)) {
                principal = caseMap.getString(bid+"_"+businessName);
            } else {
                MgeCase mgeCase = mgeCaseService.getMgeCaseByBid(bid,businessName);
                if (mgeCase != null) {
                    principal = mgeCase.getPrincipal();
                    caseMap.put(bid+"_"+businessName,principal);
                }
            }
            if (principal != null) {
                mgetask.setPrincipal(principal);
                mgeTaskMapper.updateById(mgetask);
            }
        }
    }

    @Override
    public List<String> getAllPrincipal() {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<MgeTask> mgelist = mgeTaskMapper.selectList(queryWrapper
                .ne("principal", "Job")
                .groupBy("principal"));
        for (MgeTask mgetask : mgelist) {
            list.add(mgetask.getPrincipal());
        }
        return list;
    }

    @Override
    public void collectResultByQid(String qid) {
        try {
            talosUtil.download(qid, fileName);
            List<MgeCase> mgeCaseList = mgeCaseService.select();
            mgeTaskService.readXlsNew(mgeCaseList, version(), 0);
        } catch (Exception e) {
            log.error("downloadMgeFromTalos", e);
        }
    }

    @Override
    public int autoCollectUnfinishedResult(String qid, int jobId) {
        int res;
        try {
            res = talosUtil.downloadForMge(qid, fileName);
            List<MgeCase> mgeCaseList = mgeCaseService.select();
            mgeTaskService.readXlsNew(mgeCaseList, version(), jobId);
        } catch (IOException e) {
//            log.error("readXls失败", e);
            res = 3;
        } catch (Exception e) {
//            log.error("unknown error", e);
            res = 4396;
        }
        return res;
    }

    @Override
    public String getVersionFromsql() {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        try {
            return mgeTaskMapper.selectOne(queryWrapper.ne("app_version", "Job").orderByDesc("id").last("limit 1")).getAppVersion();
        } catch (Exception e) {
            return "10.10.400";
        }
    }


    @Override
    public int createASearch(String start, String end, String version, int jobId) {
        String sql = "SELECT event_id,first(tag),page_identifier,\n" +
                "extension['nm'],device_type,first(event_attribute),MAX(event_timestamp) event_timestamp\n" +
                "FROM mart_semantic.fact_log_sdk_platform_mv  \n" +
                "WHERE  partition_log_channel = 'group'  \n" +
                "AND device_type in ('android','iphone')\n" +
                "AND partition_app = 'group'  \n" +
                "AND partition_date >= '" + start + "'  \n" +
                "AND partition_date < '" + end + "' \n" +
                "AND app_version = '" + version + "'\n" +
                "GROUP BY event_id,page_identifier,device_type,extension['nm']";
        String qid = talosUtil.getQid(sql);
        mgeJobService.updateForMge(jobId, qid);
        return talosUtil.waitfinished(qid, fileName);
    }

    @Override
    public String startDate() {
        String pattern = "yyyy-MM-dd";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        Date yesterday = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);

        return simpleDateFormat.format(yesterday);
    }

    @Override
    public String endDate() {
        String pattern = "yyyy-MM-dd";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(new Date());
    }

    @Override
    public String version() {
        String last = mgeTaskService.getVersionFromsql();
        return mgeTaskService.getVersionFromApi(last);
    }

    @Override
    public Boolean ifTestTime() {
        JsonObject obj = gulfUtil.getTestVersion();
        return obj.get("data").toString().length() >= 5;

    }

    @Override
    public String getVersionFromApi(String last) {
        String result = "";
        try {
            //JsonObject obj = gulfUtil.getTestVersion();
            //String[] part = obj.get("data").toString().split("\"");
            //result += part[1];
            String branch = sigmaUtil.getTargetBranches(6,"Android");
            String[] part = branch.split("_");
            result += part[1];
        } catch (Exception e) {
//            log.info("version not found, using version:" + last);
            result = last;
        }
        return result;
    }

    @Override
    public void post(int status) {
        if (ifTestTime() && 1 == status) {
            String[] array = {"lizhen39"};
            String[] platforms = {"iphone", "android"};
            String version = version();
            for (String platform : platforms) {
                List<String> listCase = new ArrayList<>();
                List<String> listStstus = new ArrayList<>();
//            List<String> listPrincipal = new ArrayList<>();
                for (MgeTask mgeTask : selectFailedTask(platform, version,"")) {
                    listCase.add(mgeTask.getBusinessName());
                    listStstus.add(mgeTask.getStatus());
//            listPrincipal.add(mgeTask.getPrincipal());
                }
                String description = "本次平台" + platform + "端" + version + "版本埋点问题：\n";
                int i = listCase.size();
                for (int j = 0; j < i; j++) {
                    description +=
//                    "@"+listPrincipal.get(j)+"\n"+
                            "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j) + "\n";
                }
                for (String name : array) {
                    try {
                        dxUtil.sendToIndividualByCompassAuto(description, name);
                    } catch (Exception e) {
                        log.error("sendToIndividualByCompassAuto err>>"+e.getMessage());
                    }
                }
            }
            for (String name : array) {
                try {
                    dxUtil.sendToIndividualByCompassAuto("Check result from: http://qaassist.sankuai.com/compass/dashboard#/mgeautotest/mgeTask", name);
                } catch (Exception e) {
                    log.error("post err>>"+e.getMessage());
                }
            }
        }
    }

    @Override
    public void postToPersonalMgeResult(int status, String name) {
        if (status == 1) {
            String[] arrayplatform = {"iphone", "android"};
            String version = version();
            for (String platform : arrayplatform) {
                List<String> listCase = new ArrayList<>();
                List<String> listStstus = new ArrayList<>();
                List<String> listPrincipal = new ArrayList<>();
                for (MgeTask mgeTask : selectFailedTask(platform, version, "")) {
                    listCase.add(mgeTask.getBusinessName());
                    listStstus.add(mgeTask.getStatus());
                    listPrincipal.add(mgeTask.getPrincipal());
                }
                String description = "本次" + platform + "端" + version + "版本埋点问题：\n";
                int i = listCase.size();
                for (int j = 0; j < i; j++) {
                    description +=
                            "@" + listPrincipal.get(j) + "\n" +
                                    "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j) + "\n";
                }
                try {
                    dxUtil.sendToIndividualByCompassAuto(description, name);
                } catch (Exception e) {
                    log.error("getRerunPost err>>"+e.getMessage());
                }
            }
        }

    }

    @Override
    public void postToGroup(int status) {
        if (ifTestTime() && 1 == status) {
            String module = "平台";
            String[] platforms = {"iphone", "android"};
            String version = version();
            Boolean needPost = false;
            for (String platform : platforms) {
                if (selectRecordByOs(platform,version,module) <=0 ) {
                    continue;
                }
                needPost = true;
                List<String> listCase = new ArrayList<>();
                List<String> listStstus = new ArrayList<>();
                List<String> listPrincipal = new ArrayList<>();
                for (MgeTask mgeTask : selectFailedTask(platform, version, module)) {
                    listCase.add(mgeTask.getBusinessName());
                    listStstus.add(mgeTask.getStatus());
                    listPrincipal.add(mgeTask.getPrincipal());
                }
                String description = "本次平台" + platform + "端" + version + "版本";
                int i = listCase.size();
                if (i == 0) {
                    description += "埋点验收通过";
                } else {
                    description += "埋点问题如下：\n";
                    for (int j = 0; j < i; j++) {
                        if (j > 0) {
                            if (listPrincipal.get(j).equals(listPrincipal.get(j - 1)))
                                description +=
                                        "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                            else {
                                description +=
                                        "\n@" + listPrincipal.get(j) + "\n" +
                                                "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                            }
                        } else {
                            description +=
                                    "\n@" + listPrincipal.get(j) + "\n" +
                                            "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                        }
                    }
                }
                if (needPost) {
                    try {
                        dxUtil.sendToGroupByCompass(description, pushNameId);
                    } catch (Exception e) {
                        log.error("postToGroup err>>" + e.getMessage());
                    }
                }
            }
            if (needPost) {
                try {
                    dxUtil.sendToGroupByCompass(
                            "For more information, check result from: http://qaassist.sankuai.com/compass/dashboard#/mgeautotest/mgeTask", pushNameId)
                    ;
                } catch (Exception e) {
                    log.error("getposttogroup err>>" + e.getMessage());
                }
            }

            //搜索
            needPost = false;
            module = "搜索";
            for (String platform : platforms) {
                if (selectRecordByOs(platform,version,module) <=0 ) {
                    continue;
                }
                needPost = true;
                List<String> listCase = new ArrayList<>();
                List<String> listStstus = new ArrayList<>();
                List<String> listPrincipal = new ArrayList<>();
                for (MgeTask mgeTask : selectFailedTask(platform, version, module)) {
                    listCase.add(mgeTask.getBusinessName());
                    listStstus.add(mgeTask.getStatus());
                    listPrincipal.add(mgeTask.getPrincipal());
                }
                String description = "本次" + platform + "端" + version + "版本";
                int i = listCase.size();
                if (i == 0) {
                    description += "埋点验收通过";
                } else {
                    description += "埋点问题如下：\n";
                    for (int j = 0; j < i; j++) {
                        if (j > 0) {
                            if (listPrincipal.get(j).equals(listPrincipal.get(j - 1)))
                                description +=
                                        "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                            else {
                                description +=
                                        "\n@" + listPrincipal.get(j) + "\n" +
                                                "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                            }
                        } else {
                            description +=
                                    "\n@" + listPrincipal.get(j) + "\n" +
                                            "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                        }
                    }
                }
                if (needPost) {
                    try {
                        dxUtil.sendToGroupByCompass(description, searchGroupId);
                    } catch (Exception e) {
                        log.error("postToGroup err>>" + e.getMessage());
                    }
                }
            }
            if (needPost) {
                try {
                    dxUtil.sendToGroupByCompass(
                            "For more information, check result from: http://qaassist.sankuai.com/compass/dashboard#/mgeautotest/mgeTask", pushNameId)
                    ;
                } catch (Exception e) {
                    log.error("postToGroup err>>" + e.getMessage());
                }
            }
        }
    }

    @Override
    public void postToGroupMgeResult(int status) {
        if (1 == status) {
            String[] arrayplatform = {"iphone", "android"};
            String version = version();
            Boolean needPost = false;
            String module = "平台";
            for (String platform : arrayplatform) {
                if (selectRecordByOs(platform,version,module) <=0 ) {
                    continue;
                }
                needPost = true;
                List<String> listCase = new ArrayList<>();
                List<String> listStstus = new ArrayList<>();
                List<String> listPrincipal = new ArrayList<>();
                for (MgeTask mgeTask : selectFailedTask(platform, version, module)) {
                    listCase.add(mgeTask.getBusinessName());
                    listStstus.add(mgeTask.getStatus());
                    listPrincipal.add(mgeTask.getPrincipal());
                }
                String description = "本次" + platform + "端" + version + "版本";
                int i = listCase.size();
                if (i == 0) {
                    description += "埋点用例验收通过";
                } else {
                    description += "问题用例如下：\n";
                    for (int j = 0; j < i; j++) {
                        if (j > 0) {
                            if (listPrincipal.get(j).equals(listPrincipal.get(j - 1)))
                                description +=
                                        "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                            else {
                                description +=
                                        "\n@" + listPrincipal.get(j) + "\n" +
                                                "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                            }
                        } else {
                            description +=
                                    "\n@" + listPrincipal.get(j) + "\n" +
                                            "Case:" + listCase.get(j) + " ; Status: " + listStstus.get(j).split(",")[0] + "\n";
                        }
                    }
                }
                if (needPost) {
                    try {
                        dxUtil.sendToGroupByCompass(description, pushNameId);
                    } catch (Exception e) {
//                        log.error("getposttogroup err>>" + e.getMessage());
                    }
                }
            }

            if (needPost) {
                try {
                    dxUtil.sendToGroupByCompass(
                            "For more information, check result from: http://qaassist.sankuai.com/compass/dashboard#/mgeautotest/mgeTask", pushNameId)
                    ;
                } catch (Exception e) {
//                    log.error("getposttogroup err>>" + e.getMessage());
                }
            }
        }
    }

    @Override
    public List<MgeTask> selectFailedTask(String platform, String version, String module) {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        List<MgeTask> list;
        queryWrapper.notLike("status", "succeed").eq("affirm_status", -1).eq("device_type", platform)
                .eq("app_version", version);
        if (module != null && module != "") {
            queryWrapper.eq("module", module);
        }
        queryWrapper.orderByDesc("principal");
        list = mgeTaskMapper.selectList(queryWrapper);
        return list;
    }

    public int selectRecordByOs(String platform, String version, String module) {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        int count = mgeTaskMapper.selectCount(queryWrapper.eq("device_type", platform)
                .eq("app_version", version)
                .eq("module", module));
        return count;
    }


    @Override
    public void readXlsNew(List<MgeCase> mgeCaseList, String version, int jobId) throws IOException {
        JsonParser parser = new JsonParser();

        XSSFWorkbook book = new XSSFWorkbook("./test.xlsx");
        XSSFSheet sheet = book.getSheetAt(0);

        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper();
        List<MgeTask> currentRecords = mgeTaskMapper.selectList(queryWrapper.eq("app_version", version));
        //根据case更新上报记录
        for (int caseIndex = 0; caseIndex < mgeCaseList.size(); caseIndex++) {
            MgeCase mgeCase = mgeCaseList.get(caseIndex);
            String os = mgeCase.getDeviceType();
            String eventId = mgeCase.getBid();
            String pageId = mgeCase.getCid();
            String valLab = mgeCase.getValLab();
            String tag = mgeCase.getTag();
            String principal = mgeCase.getPrincipal();
            String businessName = mgeCase.getBusinessName();
            String moudle = mgeCase.getModule();

            String aEventName = "business_name: " + businessName + "<<>> appversion: " + version + "<<>> device_type: android";
            String iEventName = "business_name: " + businessName + "<<>> appversion: " + version + "<<>> device_type: iphone";

            boolean hasAndroidRecord = false;
            boolean hasIosRecord = false;
            for (int recordIndex = 0; recordIndex < currentRecords.size(); recordIndex++) {
                MgeTask mgeTask = currentRecords.get(recordIndex);

                mgeTask.setEventId(eventId);
                mgeTask.setEventAttribute(valLab);
                mgeTask.setPageIdentifier(pageId);
                mgeTask.setTag(tag);
                mgeTask.setMoudle(moudle);

                String eventName = currentRecords.get(recordIndex).getEventName();
                if (os.equals("android")) {
                    if (eventName.equals(aEventName)) {
                        hasAndroidRecord = true;
                        mgeTaskMapper.updateById(mgeTask);
                        break;
                    }
                } else if (os.equals("iphone")) {
                    if (eventName.equals(iEventName)) {
                        hasIosRecord = true;
                        mgeTaskMapper.updateById(mgeTask);
                        break;
                    }
                } else if (os.equals("all")) {
                    if (eventName.equals(aEventName)) {
                        hasAndroidRecord = true;
                        mgeTaskMapper.updateById(mgeTask);
                    } else if (eventName.equals(iEventName)) {
                        hasIosRecord = true;
                        mgeTaskMapper.updateById(mgeTask);
                    }
                    if (hasAndroidRecord && hasIosRecord) {
                        break;
                    }
                }
            }

            MgeTask mgeTask = new MgeTask();
            mgeTask.setAppVersion(version);
            mgeTask.setEventAttribute(valLab);
            mgeTask.setPageIdentifier(pageId);
            mgeTask.setTag(tag);
            mgeTask.setEventId(eventId);
            mgeTask.setPrincipal(principal);
            mgeTask.setBusinessName(businessName);
            mgeTask.setUpdateTime(new Date());
            mgeTask.setJobId(jobId);
            mgeTask.setAffirmStatus(-1);
            mgeTask.setMoudle(moudle);
            mgeTask.setStatus("bid not found");
            if (!hasAndroidRecord && (os.equals("android") || os.equals("all"))) {
                mgeTask.setDeviceType("android");
                mgeTask.setEventName(aEventName);
                mgeTaskService.addContent(mgeTask);
            }
            if (!hasIosRecord && (os.equals("iphone") || os.equals("all"))) {
                mgeTask.setDeviceType("iphone");
                mgeTask.setEventName(iEventName);
                mgeTaskService.addContent(mgeTask);
            }
        }

        currentRecords = mgeTaskMapper.selectList(queryWrapper.eq("app_version", version));

        //遍历本次捞到的数据，更新上报记录
        for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
            StringBuilder expectValInfo = new StringBuilder();
            StringBuilder expectTagInfo = new StringBuilder();
            XSSFRow row = sheet.getRow(i);
            String reportedBid = row.getCell(0).getStringCellValue();
            String reportedTag = row.getCell(1).getStringCellValue();

            String reportedCid = row.getCell(2).getStringCellValue();
            String reportedType = row.getCell(3).getStringCellValue();
            String reportedDeviceType = row.getCell(4).getStringCellValue();
            String reportedAttr = row.getCell(5).getStringCellValue();
            String reportedTime = row.getCell(6).getStringCellValue();

            int maxNum = 0;
            String principal = "";
            StringBuilder errorInfo = new StringBuilder();
            String businessName = "";
            for (MgeTask mgeTask : currentRecords) {
                int count = 0;
                if (reportedBid.equals(mgeTask.getEventId())) {
                    if (mgeTask.getStatus().contains("succeed")) {
                        continue;
                    }
                    String expectCids = mgeTask.getPageIdentifier().replace(" ", "");
                    String expectLab = mgeTask.getEventAttribute();
                    if (!expectLab.contains("[")) {
                        expectLab = "[" + expectLab + "]";
                    }
                    principal = mgeTask.getPrincipal();
                    businessName = mgeTask.getBusinessName();
                    count += 1;
                    if (maxNum < count) {
                        maxNum = count;
                    }
                    errorInfo = new StringBuilder();


                    for (String cid : expectCids.split(",")) {
                        if (expectCids.equals("") || reportedCid.replace(" ", "").equals(cid)) {
                            count += 2;
                            if (maxNum < count) {
                                int temp = count;
                                maxNum = temp;
                            }

                            //判断val_lab
                            int valCount = 0;
                            if (expectLab.length() > 4 && reportedAttr.length() > 2) {
                                expectValInfo = new StringBuilder();
                                JsonObject reportedAttrJson = parser.parse(reportedAttr).getAsJsonObject();
                                JsonArray expectAttrArr = parser.parse(expectLab).getAsJsonArray();

                                int tempValCount = 0;
                                for (JsonElement expectElement : expectAttrArr) {
                                    if (tempValCount == 1) {
                                        break;
                                    } else if (tempValCount == 2) {
                                        expectValInfo.append("<or>:");
                                    }
                                    JsonObject expectObj = expectElement.getAsJsonObject();
                                    Set<Map.Entry<String, JsonElement>> entries = expectObj.entrySet();
                                    for (Map.Entry<String, JsonElement> entry : entries) {
                                        if (reportedAttrJson.has(entry.getKey())) {
                                            valCount += 1;
                                        } else {
                                            int symbolSecond = 0;
                                            Set<Map.Entry<String, JsonElement>> valLabSet = reportedAttrJson.entrySet();
                                            for (Map.Entry<String, JsonElement> secondValLab : valLabSet) {
                                                String tempstr = secondValLab.getValue().toString().replace("\\", "");
                                                if (tempstr.contains("{") && tempstr.contains("}")) {
                                                    while (tempstr.startsWith("\"")) {
                                                        tempstr = tempstr.substring(1, tempstr.length() - 1);
                                                    }
                                                }
                                                try {
                                                    if (tempstr.contains("{") && tempstr.contains("}")) {
                                                        if (parser.parse(tempstr).isJsonArray()) {
                                                            JsonArray temparr = parser.parse(tempstr).getAsJsonArray();
                                                            for (JsonElement tempele : temparr) {
                                                                JsonObject tempobj = tempele.getAsJsonObject();
                                                                if (tempobj.has(entry.getKey())) {
                                                                    symbolSecond = 1;
                                                                    break;
                                                                }
                                                            }
                                                        } else if (parser.parse(tempstr).isJsonObject()) {
                                                            JsonObject tempobj = parser.parse(tempstr).getAsJsonObject();
                                                            if (tempobj.has(entry.getKey())) {
                                                                symbolSecond = 1;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                } catch (JsonSyntaxException | IllegalStateException e) {
//                                                    log.info("err: " + tempstr);
                                                }
                                            }
                                            if (symbolSecond > 0) {
                                                valCount += 1;
                                            } else {
                                                errorInfo = new StringBuilder(mgeTask.getEventAttribute());
                                                if (!expectValInfo.toString().contains(entry.getKey())) {
                                                    expectValInfo.append(entry.getKey());
                                                    expectValInfo.append(";");
                                                }
                                                valCount = 0;
                                            }
                                        }


                                    }
                                    if (valCount > 0)
                                        tempValCount = 1;
                                    else
                                        tempValCount = 2;
                                }
                                if (tempValCount == 1) {
                                    count += 4;
                                    if (maxNum < count) {
                                        int temp = count;
                                        maxNum = temp;
                                    }
                                }
                            } else if (expectLab.length() == 4) {
                                count += 4;
                                if (maxNum < count) {
                                    int temp = count;
                                    maxNum = temp;
                                }
                            } else if (reportedAttr.length() == 2 && expectLab.length() > 4) {
                                JsonArray jsonArray = parser.parse(expectLab).getAsJsonArray();
                                for (JsonElement element : jsonArray) {
                                    JsonObject obj = element.getAsJsonObject();
                                    Set<Map.Entry<String, JsonElement>> entries = obj.entrySet();
                                    for (Map.Entry<String, JsonElement> entry : entries) {
                                        expectValInfo.append(entry.getKey()).append(";");

                                    }
                                }
                            }
                            int tagCount = 1;

                            int len_tag = mgeTask.getTag().length();
                            if (len_tag > 0 && parser.parse(reportedTag).isJsonObject() && reportedTag.length() > 0) {
                                JsonObject jsonTag = parser.parse(reportedTag).getAsJsonObject();

                                String firstTagele = mgeTask.getTag();
                                String secondTagele = new String();
                                String thirdtagele = new String();
//                                log.info(firstTagele);
                                JsonObject tempsecond = parser.parse(firstTagele).getAsJsonObject();

                                Set<Map.Entry<String, JsonElement>> tempsecondtag = tempsecond.entrySet();
                                for (Map.Entry<String, JsonElement> secondele : tempsecondtag) {
                                    secondTagele = tempsecond.get(secondele.getKey()).toString();
                                }
                                try {
                                    JsonObject tempthird = parser.parse(secondTagele).getAsJsonObject();
                                    Set<Map.Entry<String, JsonElement>> tempthirdtag = tempthird.entrySet();
                                    for (Map.Entry<String, JsonElement> thirdele : tempthirdtag) {
                                        thirdtagele = tempthird.get(thirdele.getKey()).toString();
                                    }
                                } catch (Exception e) {
//                                    log.error(e.getMessage());
//                                    log.info(secondTagele);
                                }

                                //1
                                JsonObject secondtag = new JsonObject();
                                JsonObject thirdtag = new JsonObject();
                                int len_first = firstTagele.length();
                                if (len_first > 0) {
                                    int tempCountSecondtag = 0;
                                    JsonObject firstobj = parser.parse(firstTagele).getAsJsonObject();
                                    Set<Map.Entry<String, JsonElement>> firsttagentries = firstobj.entrySet();
                                    for (Map.Entry<String, JsonElement> tagelement : firsttagentries) {
                                        if (jsonTag.has(tagelement.getKey()) || tagelement.getKey().equals("skipThisEle")) {
                                            tagCount += 1;
                                            if (tempCountSecondtag == 0) {
                                                String secondtagKey = tagelement.getKey();
                                                secondtag = firstobj.get(secondtagKey).getAsJsonObject();
                                                //System.err.println(secondtag);
                                                //    System.err.println(firsttagele);
                                                tempCountSecondtag = 1;
                                            }
                                        } else {
                                            tagCount = -255;
                                            expectTagInfo.append(tagelement.getKey());
                                            expectTagInfo.append(";");
                                        }
                                    }
                                }

                                //2
                                int len_second = secondTagele.length();
                                if (len_second > 0) {
                                    int tempCountThirdtag = 0;
                                    Set<Map.Entry<String, JsonElement>> secondtagEntry = jsonTag.entrySet();
                                    //JsonObject second_level_tag = parser.parse(jsonTag.toString()).getAsJsonObject();
                                    if (tagCount > 0) {
                                        int tagtmp = 0;
                                        JsonObject secondobj = parser.parse(secondTagele).getAsJsonObject();
                                        Set<Map.Entry<String, JsonElement>> secondtagentries = secondobj.entrySet();
                                        for (Map.Entry<String, JsonElement> tagelement : secondtagentries) {
                                            if (secondtag.has(tagelement.getKey())
                                                    || tagelement.getKey().equals("skipThisEle")) {
                                                tagtmp = 1;
                                                if (tempCountThirdtag == 0) {
                                                    String secondtagKey = tagelement.getKey();
                                                    thirdtag = secondobj.get(secondtagKey).getAsJsonObject();
                                                    tempCountThirdtag = 1;
                                                }
                                            } else {
                                                expectTagInfo.append(tagelement.getKey());
                                                expectTagInfo.append(";");
                                            }
                                        }
                                        if (tagtmp == 1)
                                            tagCount++;
                                        else {
                                            tagCount = -255;
                                        }
                                    }


                                }
                                int len_third = thirdtagele.length();
                                //3
                                if (len_third > 0) {
                                    //JsonObject second_level_tag = parser.parse(jsonTag.toString()).getAsJsonObject();
                                    if (tagCount > 0) {
                                        int tagtmp = 0;
                                        JsonObject thirdobj = parser.parse(thirdtagele).getAsJsonObject();
                                        Set<Map.Entry<String, JsonElement>> thirdtagentries = thirdobj.entrySet();
                                        for (Map.Entry<String, JsonElement> tagelement : thirdtagentries) {
                                            if (thirdtag.has(tagelement.getKey())) {
                                                tagtmp = 1;
                                            } else {
                                                expectTagInfo.append(tagelement.getKey());
                                                expectTagInfo.append(";");
                                            }
                                        }
                                        if (tagtmp == 1)
                                            tagCount++;
                                        else {
                                            tagCount = -255;
                                        }
                                    }


                                }
                            }
                            if (tagCount > 0) {
                                count += 8;
                                if (maxNum < count) {
                                    int temp = count;
                                    maxNum = temp;
                                }
                                break;
                            }
                        } else {
                            errorInfo.append(cid).append(";");
                        }
                    }
                } else {
                    if (maxNum == 0) {
                        errorInfo = new StringBuilder(mgeTask.getEventId());
                    }
                }
                if (maxNum == 15) {
                    MgeTask record = new MgeTask();
                    record.setAppVersion(version);
                    record.setEventId(reportedBid);
                    record.setEventTimestamp(reportedTime);
                    record.setEventType(reportedType);
                    record.setDeviceType(reportedDeviceType);
                    record.setPrincipal(principal);
                    record.setBusinessName(businessName);
                    record.setUpdateTime(new Date());
                    record.setJobId(jobId);
                    record.setEventName("business_name: " + businessName + "<<>> appversion: " + version + "<<>> device_type: " + reportedDeviceType);
                    record.setStatus("succeed");
                    record.setAffirmStatus(-1);
                    record.setReportCid(reportedCid);
                    record.setReportLab(reportedAttr);
                    record.setReportTag(reportedTag);


                    mgeTaskService.addContent(mgeTask);
                    break;
                }
            }
            if (reportedBid.equals("b_group_rfuv2b2j_mv")) {
//                log.info("sss");
            }

            MgeTask mgeTask = new MgeTask();
            mgeTask.setAppVersion(version);
            mgeTask.setEventId(reportedBid);
            mgeTask.setEventTimestamp(reportedTime);
            mgeTask.setEventType(reportedType);
            mgeTask.setDeviceType(reportedDeviceType);
            mgeTask.setPrincipal(principal);
            mgeTask.setBusinessName(businessName);
            mgeTask.setUpdateTime(new Date());
            mgeTask.setJobId(jobId);
            mgeTask.setAffirmStatus(-1);
            mgeTask.setEventName("business_name: " + businessName + "<<>> appversion: " + version + "<<>> device_type: " + reportedDeviceType);
            mgeTask.setReportCid(reportedCid);
            mgeTask.setReportLab(reportedAttr);
            mgeTask.setReportTag(reportedTag);
            if (15 == maxNum) {
                mgeTask.setStatus("succeed");
                mgeTaskService.addContent(mgeTask);
            } else if (11 == maxNum) {
                mgeTask.setStatus("val_lab error, expect: " + expectValInfo.toString());
                mgeTaskService.addContent(mgeTask);
            } else if (7 == maxNum) {
                mgeTask.setStatus("tag error, expect: " + expectTagInfo.toString());
                mgeTaskService.addContent(mgeTask);
            } else if (3 == maxNum) {
                mgeTask.setStatus("val_lab & tag error, expect val_lab: " + expectValInfo.toString() + "===expect tag: " + expectTagInfo.toString());
                mgeTaskService.addContent(mgeTask);
            } else if (1 == maxNum) {
                mgeTask.setStatus("cid error, expect: " + errorInfo);
                mgeTaskService.addContent(mgeTask);
            }
        }
    }


    @Override
    public void delete() {
        try {
            File file = new File("./test.xlsx");
            if (file.delete()) {
//                log.info(file.getName() + " 文件已被删除！");
            } else {
//                log.info("文件删除失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void addContent(MgeTask mgeTask) {
        QueryWrapper<MgeTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("event_name", mgeTask.getEventName());
        if (mgeTaskMapper.selectCount(queryWrapper) == 0) {
            mgeTaskService.add(mgeTask);
//            log.info("新增埋点结果: " + mgeTask.getBusinessName() + "-" + mgeTask.getDeviceType() + "端", mgeTask);
        } else {
            String statusSql = mgeTaskMapper.selectOne(queryWrapper).getStatus();
            if (!statusSql.contains("succeed")) {
                if (statusSql.equals(mgeTask.getStatus())) {
//                    log.info("原：" + statusSql);
                    mgeTaskService.update(mgeTask);
//                    log.info("更新时间: " + mgeTask.getBusinessName() + "-" + mgeTask.getDeviceType() + "端");
                } else {
//                    log.info("原：" + statusSql + ";现：" + mgeTask.getStatus());
                    mgeTaskService.update(mgeTask);
//                    log.info("更新埋点结果: " + mgeTask.getBusinessName() + "-" + mgeTask.getDeviceType() + "端");
                }
            }
        }

    }

//    public static void main(String[] argv) {
//        TalosUtil talosUtil = new TalosUtil();
//        String sql = "SELECT event_id,first(tag),page_identifier,\n" +
//                "extension['nm'],device_type,first(event_attribute),MAX(event_timestamp) event_timestamp\n" +
//                "FROM mart_semantic.fact_log_sdk_platform_mv  \n" +
//                "WHERE  partition_log_channel = 'group'  \n" +
//                "AND device_type in ('android','iphone')\n" +
//                "AND partition_app = 'group'  \n" +
//                "AND partition_date >= '2021-05-18'  \n" +
//                "AND partition_date < '2021-05-19' \n" +
//                "AND app_version = '10.5.400'\n" +
//                "GROUP BY event_id,page_identifier,device_type,extension['nm']";
//        String qid = talosUtil.getQid(sql);
//        talosUtil.waitfinished("b09154ee461e4b63a57052fe7be607a0","file.xlsx");
//    }


}
