package com.sankuai.mdp.compass.conan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.Interface;
import com.sankuai.mdp.compass.conan.entity.InterfaceCase;

import java.io.IOException;
import java.util.List;

public interface InterfaceService {

    List<Interface> getListByCaseName(String api);

    IPage<Interface> list(QueryRequest request, Interface interface1);

    void add(Interface interface1) throws IOException;

    void delete(Interface interface1);

    void update(Interface interface1);

    List<Interface> select();
    List<String> getAllApi();
    void updateContent();
}
