package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.InterfaceCase;
import com.sankuai.mdp.compass.conan.service.InterfaceCaseService;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by xubangxi on 2020/6/21.
 */
@RestController
@RequestMapping("/compass/api/autotest/interfaceCase")
public class InterfaceCaseController extends BaseController {
    @Autowired
    InterfaceCaseService interfaceCaseService;

    @GetMapping("/get")
    public String get() {
        return "true";
    }

    @GetMapping("/getCase")
    public String getCase(String caseName) {
        return interfaceCaseService.gethost(caseName).toString();
    }

    @PostMapping("/update")
    public void update(InterfaceCase interfaceCase) {
        interfaceCaseService.update(interfaceCase);
    }

    @PostMapping("/add")
    public void add(InterfaceCase interfaceCase) throws IOException {
        interfaceCaseService.add(interfaceCase);
    }

    @PostMapping("/delete")
    public void delete(InterfaceCase interfaceCase) {
        interfaceCaseService.delete(interfaceCase);
    }

    @GetMapping("/select")
    public List<InterfaceCase> select() {
        return interfaceCaseService.select();
    }

    @GetMapping("/getApiByCaseName")
    public String getApi(InterfaceCase interfaceCase) {
        return interfaceCaseService.getApiByCaseName(interfaceCase.getCaseName());
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, InterfaceCase interfaceCase) {
        interfaceCaseService.updateContent();
        IPage<InterfaceCase> CompatilityJobIPage = this.interfaceCaseService.list(request, interfaceCase);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }
    @GetMapping("/getAllPrincipal")
    public List<String> getAllPrincipal() {
        return interfaceCaseService.getAllPrincipal();
    }
    @GetMapping("/getStatus")
    public String getStatus(String caseName,String platform,String principal) {
        return interfaceCaseService.getStatus(caseName,platform,principal);
    }
}
