package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("interface_job")
public class InterfaceJob implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value="id",type= IdType.AUTO)
    private Integer id;

    private Integer buildId;

    private  String platform;

    private String buildUrl;
    /** 测试状态码说明
     * -1   排队
     * 0    测试中
     * 1    完成
     * 2    取消
     */
    private Integer status;

    private  String statusStr;

    private String type;

    private String apkUrl;

    private String creator;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    private String reportId;

    private String finishedId;

    @TableField(exist = false)
    private String operator;


}
