package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import com.sankuai.mdp.compass.conan.entity.InterfaceCase;
import com.sankuai.mdp.compass.conan.mapper.GroupCaseMapper;
import com.sankuai.mdp.compass.conan.mapper.InterfaceCaseMapper;
import com.sankuai.mdp.compass.conan.mapper.InterfaceMapper;
import com.sankuai.mdp.compass.conan.service.InterfaceCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class InterfaceCaseServiceImpl extends ServiceImpl<InterfaceCaseMapper, InterfaceCase> implements InterfaceCaseService {
    @Autowired
    InterfaceCaseMapper interfaceCaseMapper;
    @Autowired
    GroupCaseMapper groupCaseMapper;

    @Autowired
    InterfaceMapper interfaceMapper;
    @Override
    public String gethost(String caseName) {
        QueryWrapper<InterfaceCase> queryWrapper = new QueryWrapper<>();
        InterfaceCase interfaceCase = interfaceCaseMapper.selectOne(queryWrapper.eq("case_name", caseName));
        try {
            return interfaceCase.getApi();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<String> getAllPrincipal() {
        QueryWrapper<InterfaceCase> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<InterfaceCase> interfaceCaseList = interfaceCaseMapper.selectList(queryWrapper
                .groupBy("principal"));
        for (InterfaceCase interfaceCase : interfaceCaseList) {
            list.add(interfaceCase.getPrincipal());
        }
        return list;
    }


    @Override
    public List<InterfaceCase> getListByCaseName(String caseName) {
        QueryWrapper<InterfaceCase> queryWrapper = new QueryWrapper<>();
        List<InterfaceCase> interfaceCaseList = interfaceCaseMapper.selectList(queryWrapper.eq("case_name", caseName));
        return interfaceCaseList;
    }

    public List<InterfaceCase> getApiByCaseNameAndApi(String caseName, String api) {
        QueryWrapper<InterfaceCase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("case_name", caseName);
        queryWrapper.eq("api", api);
        List<InterfaceCase> result = interfaceCaseMapper.selectList(queryWrapper);
        if(result == null || result.isEmpty()){
            throw new IllegalArgumentException("No data found with given case name and api");
        }
        return result;
    }


    @Override
    public String getApiByCaseName(String caseName) {
        String result = "";
        List<InterfaceCase> interfaceCaseList = getListByCaseName(caseName);
        for (int i = 0; i < interfaceCaseList.size(); i++) {
            if ("" == result) {
                result = result + interfaceCaseList.get(i).getApi();
            } else {
                result = result + "," + interfaceCaseList.get(i).getApi();
            }
        }
        return result;
    }
    @Override
    public IPage<InterfaceCase> list(QueryRequest request, InterfaceCase interfaceCase) {
        try {
            LambdaQueryWrapper<InterfaceCase> queryWrapper = new LambdaQueryWrapper<>();

            if (null != interfaceCase.getApi()) {
                queryWrapper.like(InterfaceCase::getApi, interfaceCase.getApi());
            }
            if (null != interfaceCase.getPrincipal()) {
                queryWrapper.eq(InterfaceCase::getPrincipal, interfaceCase.getPrincipal());
            }
            Page<InterfaceCase> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public void add(InterfaceCase interfaceCase) throws IOException {
        Interfacecheck check=new Interfacecheck();
        if(!interfaceCase.getHeader().equals("{}")){
            JsonSchema jsonSchema=new JsonSchema();
            String inputSchema="";
            if(!interfaceCase.getHeaderValue().isEmpty()){
                inputSchema=jsonSchema.setConstValueByDataToSchemaJson(check.jsonCheckValueData(interfaceCase.getHeaderValue(), interfaceCase.getHeader()).replace("-","_"), interfaceCase.getHeader().replace("-","_"));
            }else{
                inputSchema=jsonSchema.getSchema(interfaceCase.getHeader());
            }
            interfaceCase.setHeaderSchema(inputSchema);
        }
        if(!interfaceCase.getBody().equals("{}")){
            JsonSchema jsonSchema=new JsonSchema();
            String inputSchema="";
            if(!interfaceCase.getBodyValue().isEmpty()){
                inputSchema=jsonSchema.setConstValueByDataToSchemaJson(check.jsonCheckValueData(interfaceCase.getBodyValue(), interfaceCase.getBody()).replace("-","_"), interfaceCase.getBody().replace("-","_"));
            }else{
                inputSchema=jsonSchema.getSchema(interfaceCase.getBody());
            }
            interfaceCase.setBodySchema(inputSchema);
        }
        if(!interfaceCase.getParams().equals("{}")){
            JsonSchema jsonSchema=new JsonSchema();
            String inputSchema="";
            if(!interfaceCase.getParamsValue().isEmpty()){
                inputSchema=jsonSchema.setConstValueByDataToSchemaJson(check.jsonCheckValueData(interfaceCase.getParamsValue(), interfaceCase.getParams()).replace("-","_"), interfaceCase.getParams().replace("-","_"));
            }else{
                inputSchema=jsonSchema.getSchema(interfaceCase.getParams());
            }
            interfaceCase.setParamSchema(inputSchema);
        }
        interfaceCaseMapper.insert(interfaceCase);
    }

    @Override
    public void delete(InterfaceCase interfaceCase) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", interfaceCase.getId());
        interfaceCaseMapper.delete(queryWrapper);
    }

    @Override
    public void update(InterfaceCase interfaceCase) {
        QueryWrapper<InterfaceCase> queryWrapper = new QueryWrapper<>();
        interfaceCaseMapper.update(interfaceCase, queryWrapper
                .eq("id", interfaceCase.getId()));
    }

    @Override
    public List<InterfaceCase> select() {
        QueryWrapper queryWrapper = new QueryWrapper();
        return interfaceCaseMapper.selectList(queryWrapper);
    }

    @Override
    public void updateContent() {
        List<InterfaceCase> interfaceCases = interfaceCaseMapper.selectList(new QueryWrapper<InterfaceCase>());
        for (InterfaceCase interfaceCase : interfaceCases) {
//            if (null != interfaceCase.getHost() && interfaceCase.getHost().equals("undefined"))
//                interfaceCase.setHost("");
//            if (interfaceCase.getMethod().equals("undefined"))
//                interfaceCase.setMethod("GET");
            if (interfaceCase.getMinVersion().equals("undefined"))
                interfaceCase.setMinVersion("");
            if (interfaceCase.getMaxVersion().equals("undefined"))
                interfaceCase.setMaxVersion("");
            interfaceCaseMapper.updateById(interfaceCase);
        }
    }

    @Override
    public String getStatus(String caseName, String platform, String principal) {
        StringBuilder result = new StringBuilder();
        int flag = 0;
        List<GroupCase> list = groupCaseMapper.selectList(new QueryWrapper<GroupCase>().eq("case_name", caseName).eq("case_owner", principal));
        if (list.size() > 2) {
            result = new StringBuilder("error");
        } else if (platform.equalsIgnoreCase("all")) {
            result.append("安卓:");
            for (GroupCase groupCase : list) {
                if (groupCase.getCasePlatform().equalsIgnoreCase("android")
                        && groupCase.getCaseStatus().equals(1)) {
                    result.append("在线");
                    flag = 1;
                } else if (groupCase.getCasePlatform().equalsIgnoreCase("android")
                        && groupCase.getCaseStatus().equals(0)) {
                    result.append("离线");
                    flag = 1;
                }
            }
            if (0 == flag) {
                result.append("不存在");
            }
            result.append("/ios:");
            for (GroupCase groupCase : list) {
                if (groupCase.getCasePlatform().equalsIgnoreCase("ios")
                        && groupCase.getCaseStatus().equals(1)) {
                    result.append("在线");
                    flag = 1;
                } else if (groupCase.getCasePlatform().equalsIgnoreCase("ios")
                        && groupCase.getCaseStatus().equals(0)) {
                    result.append("离线");
                    flag = 1;
                }
            }
            if (0 == flag) {
                result.append("不存在");
            }
        } else if (platform.equalsIgnoreCase("android")) {
            result.append("安卓:");
            for (GroupCase groupCase : list) {
                if (groupCase.getCasePlatform().equalsIgnoreCase("android")
                        && groupCase.getCaseStatus().equals(1)) {
                    result.append("在线");
                    flag = 1;
                } else if (groupCase.getCasePlatform().equalsIgnoreCase("android")
                        && groupCase.getCaseStatus().equals(0)) {
                    result.append("离线");
                    flag = 1;
                }
            }
            if (0 == flag) {
                result.append("不存在");
            }
        } else if (platform.equalsIgnoreCase("ios")) {
            result.append("ios:");
            for (GroupCase groupCase : list) {
                if (groupCase.getCasePlatform().equalsIgnoreCase("ios")
                        && groupCase.getCaseStatus().equals(1)) {
                    result.append("在线");
                    flag = 1;
                } else if (groupCase.getCasePlatform().equalsIgnoreCase("ios")
                        && groupCase.getCaseStatus().equals(0)) {
                    result.append("离线");
                    flag = 1;
                }
            }
            if (0 == flag) {
                result.append("不存在");
            }
        }
        return result.toString();
    }

}
