package com.sankuai.mdp.compass.conan.controller;

import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.conan.service.MgeJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

/**
 * Created by xubangxi on 2020/07/10.
 */
@RestController
@RequestMapping("/compass/api/autotest/mgeJob")

public class MgeJobController extends BaseController {
    @Autowired
    MgeJobService mgeJobService;

    @GetMapping("/date")
    public Boolean select() throws ParseException {
        return mgeJobService.checkRerun();
    }

    @GetMapping("/toStart")
    public void toStart() throws ParseException {
        mgeJobService.updateToStart();
    }
}
