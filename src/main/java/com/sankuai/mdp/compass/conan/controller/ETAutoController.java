package com.sankuai.mdp.compass.conan.controller;

import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.EventTrackingAuto;
import com.sankuai.mdp.compass.conan.service.ETAutoService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * Created by lizhen on 2020/02/05.
 */
@RestController
@RequestMapping("/compass/api/autotest/eventTrack")
public class ETAutoController extends BaseController {
    @Autowired
    ETAutoService etAutoService;

    @PostMapping("/add")
    public Resp addEtAuto(@RequestBody EventTrackingAuto etAuto) {

        try {
            return Resp.success(etAutoService.addEtAuto(etAuto));
        }
        catch (Exception e){
            return Resp.error();
        }
    }

    @PostMapping("/update")
    public void updateReportId(@RequestParam String jobId, @RequestParam String reportId) {
        etAutoService.updateReportId(jobId,reportId);
    }

    //云测回调接口
    @PostMapping("/result")
    public void analysisResult(@RequestBody JSONObject body) throws IOException {
        etAutoService.analysisResult(body);
    }
}
