package com.sankuai.mdp.compass.conan.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.inf.xmdlog.remote.util.StringUtils;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.utils.ConanUtil;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.common.utils.FileUtil;
import com.sankuai.mdp.compass.conan.entity.*;
import com.sankuai.mdp.compass.conan.enums.ConanCaseResultEnums;
import com.sankuai.mdp.compass.conan.enums.InterfaceCheckErrorCode;
import com.sankuai.mdp.compass.conan.mapper.ConanJobMapper;
import com.sankuai.mdp.compass.conan.mapper.InterfaceJobMapper;
import com.sankuai.mdp.compass.conan.mapper.InterfaceMapper;
import com.sankuai.mdp.compass.conan.mapper.InterfaceTaskMapper;
import com.sankuai.mdp.compass.conan.service.*;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InterfaceTaskServiceImpl extends ServiceImpl<InterfaceTaskMapper, InterfaceTask> implements InterfaceTaskService {
    private static final Logger logger = LoggerFactory.getLogger(InterfaceTaskServiceImpl.class);
    private final static String DASH = "-";
    @Autowired
    InterfaceTaskMapper interfaceTaskMapper;

    @Autowired
    InterfaceJobMapper interfaceJobMapper;

    @Autowired
    InterfaceCaseService interfaceCaseService;
    @Autowired
    InterfaceService interfaceService;
    @Autowired
    InterfaceTaskService interfaceTaskService;
    @Autowired
    ConanCaseService conanCaseService;

    @Autowired
    ConanJobService conanJobService;

    @Autowired
    ConanTaskService conanTaskService;

    @Autowired
    ConanJobMapper conanJobMapper;
    @Autowired
    InterfaceMapper interfaceMapper;

    ConanUtil conanUtil = new ConanUtil();

    DxUtil dxUtil = new DxUtil();

    FileUtil fileUtil = new FileUtil();

    ComConstant comConstant = new ComConstant();

//    QueryWrapper<InterfaceTask> queryWrapper = new QueryWrapper<>();
//        interfaceTaskMapper.update(interfaceTask, queryWrapper.eq("event_name", interfaceTask.getEventName()));
//}public void update(InterfaceTask interfaceTask) {


    @Override
    public void add(InterfaceTask interfaceTask) {
        interfaceTaskMapper.insert(interfaceTask);
    }

    @Override
    public List<String> getAllPrincipal() {
        QueryWrapper<InterfaceTask> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<InterfaceTask> inlist = interfaceTaskMapper.selectList(queryWrapper
                .groupBy("principal"));
        for (InterfaceTask interfaceTask : inlist) {
            list.add(interfaceTask.getPrincipal());
        }
        return list;
    }

    @Override
    public List<String> getAllVersions() {
        QueryWrapper<InterfaceTask> queryWrapper = new QueryWrapper<>();
        List<String> list = new ArrayList<String>();
        List<InterfaceTask> interfaceTasks =
                interfaceTaskMapper.selectList(queryWrapper.groupBy("app_version")
                        .orderByDesc("app_version"));
        for (InterfaceTask interfaceTask : interfaceTasks) {
            list.add(interfaceTask.getAppVersion());
        }
        return list;
    }

    @Override
    public IPage<InterfaceCaseAndTask> getCaseAndTask(String jobId, QueryRequest request, InterfaceTask interfaceTask) {
        try {
            Page<InterfaceCaseAndTask> page = new Page<>(request.getPageNum(), request.getPageSize());
            // 兼容jobId可能是多个id，如"1_2"
            List<InterfaceCaseAndTask> interfaceCaseAndTask = new ArrayList<>();
            String[] jobIds = jobId.split("_");
            for (String id : jobIds) {
                List<InterfaceCaseAndTask> taskList = interfaceTaskMapper.getCaseAndTask(page, id);
                if (taskList == null || taskList.isEmpty()) {
                    logger.warn("No result found for jobId: {}", id);
                } else {
                    interfaceCaseAndTask.addAll(taskList);
                }
            }
            if (interfaceTask.getPrincipal() != null) {
                interfaceCaseAndTask = interfaceCaseAndTask.stream()
                        .filter(t -> t.getPrincipal().equals(interfaceTask.getPrincipal()))
                        .collect(Collectors.toList());
            }
            if (request != null && request.getSwitchStatus()) {
                interfaceCaseAndTask = interfaceCaseAndTask.stream().filter(t -> t != null && t.getStatus() == 1)
                        .collect(Collectors.toList());
            }
            return page.setRecords(interfaceCaseAndTask);
    } catch (Exception e) {
        logger.error("Error occurred while getting case and task: ", e);
        return null;
    }
}


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateResult(JSONObject body) throws ParseException {
        Interfacecheck check = new Interfacecheck();
        JsonParser jsonParser = new JsonParser();
        HashMap<String, Integer> apiCountMap = new HashMap<>();
        List<InterfaceCase> updatedCases = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JsonObject timeData = jsonParser.parse(body.get("timedata").toString()).getAsJsonObject();
        Date startTime = sdf.parse(timeData.get("startTime").getAsString());
        Date endTime = sdf.parse(timeData.get("endTime").getAsString());
        JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
        String jobId = jobData.get("id").getAsString();
        JsonArray taskArray = conanUtil.getTaskInfo(jobId);
        try {
            for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
                JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
                String id = task.get("id").getAsString();
                String downloadURL = conanUtil.getDownloadURL(id);
                if (null != downloadURL) {
                    FileUtil.downloadAndReadFile(downloadURL, ComConstant.OUT_PUT, id);
                    ArrayList<String> fileList = new ArrayList<>();
                    String path = ComConstant.OUT_PUT + "/" + id + "/interface/";
                    FileUtil.getFileList(path, "", fileList);
                    for (String item : fileList) {
                        String output = FileUtil.read(path + item);
                        if (!output.isEmpty()) {
                            processOutputData(jobId, jsonParser, check, task, output);
                            JsonObject jsonObject = jsonParser.parse(output).getAsJsonObject();
                            JsonArray resultsArray = jsonObject.getAsJsonArray("results");
                            for (int index = 0; index < resultsArray.size(); index++) {
                                JsonObject caseData = resultsArray.get(index).getAsJsonObject();
                                for (String caseName : caseData.keySet()) {
                                    JsonArray apiRequests = caseData.getAsJsonArray(caseName);
                                    for (int apiIndex = 0; apiIndex < apiRequests.size(); apiIndex++) {
                                        JsonObject apiRequestData = apiRequests.get(apiIndex).getAsJsonObject();
                                        String api = "";
                                        if (apiRequestData.get("reqUrl") != null && !apiRequestData.get("reqUrl").isJsonNull() && !apiRequestData.get("reqUrl").getAsString().isEmpty()) {
                                            api = apiRequestData.get("reqUrl").getAsString().split("\\?")[0];
                                        }
                                        JsonObject reqParams = null;
                                        if (apiRequestData.get("reqParams") != null
                                                && !apiRequestData.get("reqParams").isJsonNull()) {
                                            reqParams = apiRequestData.getAsJsonObject("reqParams");
                                        }
                                        JsonObject reqBody = null;
                                        if (apiRequestData.get("reqBody") != null
                                                && !apiRequestData.get("reqBody").isJsonNull()) {
                                            reqBody = apiRequestData.getAsJsonObject("reqBody");
                                        }
                                        JsonObject reqHeaders = null;
                                        if (apiRequestData.get("reqHeaders") != null
                                                && !apiRequestData.get("reqHeaders").isJsonNull()) {
                                            reqHeaders = apiRequestData.getAsJsonObject("reqHeaders");
                                        }
                                        String method = String.valueOf(apiRequestData.get("method"));
                                        List<Interface> methods = interfaceService.getListByCaseName(api);
                                        if (!methods.isEmpty()) {
                                            Interface interface1 = methods.get(0);
                                            String originalMethod = interface1.getMethod();
                                            if (!method.equals(originalMethod)) {
                                                interface1.setMethod(originalMethod);
                                                interfaceService.update(interface1);
                                            }
                                        }
                                        String mapKey = caseName + "_" + api;
                                        apiCountMap.put(mapKey, apiCountMap.getOrDefault(mapKey, 0) + 1);
                                        List<InterfaceCase> record = interfaceCaseService
                                                .getApiByCaseNameAndApi(caseName, api);
                                        InterfaceCase recordCase = record.get(0);
                                        JsonObject oriReqParams = new JsonParser().parse(recordCase.getParams())
                                                .getAsJsonObject();
                                        JsonObject oriReqBody = new JsonParser().parse(recordCase.getBody())
                                                .getAsJsonObject();
                                        JsonObject oriReqHeaders = new JsonParser().parse(recordCase.getHeader())
                                                .getAsJsonObject();
                                        boolean isUpdated = false;
                                        if (reqParams != null) {
                                            isUpdated |= compareAndUpdate(getAllKeys(reqParams),
                                                    getAllKeys(oriReqParams), reqParams, recordCase, "params");
                                        }
                                        if (reqBody != null) {
                                            isUpdated |= compareAndUpdate(getAllKeys(reqBody), getAllKeys(oriReqBody),
                                                    reqBody, recordCase, "body");
                                        }
                                        if (reqHeaders != null) {
                                            isUpdated |= compareAndUpdate(getAllKeys(reqHeaders),
                                                    getAllKeys(oriReqHeaders), reqHeaders, recordCase, "header");
                                        }
                                        if (recordCase.getRequestCount() != null) {
                                            int newRequestCount = apiCountMap.get(mapKey);
                                            int oldRequestCount = recordCase.getRequestCount();
                                            if (newRequestCount != oldRequestCount) {
                                                recordCase.setRequestCount(newRequestCount);
                                                isUpdated = true;
                                            }
                                        }
                                        if (isUpdated) {
                                            JsonSchema jsonSchema = new JsonSchema();
                                            if (!recordCase.getParamsValue().isEmpty()) {
                                                String inputSchema = jsonSchema.setConstValueByDataToSchemaJson(
                                                        check.jsonCheckValueData(recordCase.getParamsValue(),
                                                                recordCase.getParams()).replace(DASH, "_"),
                                                        recordCase.getParams().replace(DASH, "_"));
                                                recordCase.setParamSchema(inputSchema);
                                            }
                                            if (!recordCase.getBodyValue().isEmpty()) {
                                                String inputSchema = jsonSchema.setConstValueByDataToSchemaJson(
                                                        check.jsonCheckValueData(recordCase.getBodyValue(),
                                                                recordCase.getBody()).replace(DASH, "_"),
                                                        recordCase.getBody().replace(DASH, "_"));
                                                recordCase.setBodySchema(inputSchema);
                                            }
                                            if (!recordCase.getHeaderValue().isEmpty()) {
                                                String inputSchema = jsonSchema.setConstValueByDataToSchemaJson(
                                                        check.jsonCheckValueData(recordCase.getHeaderValue(),
                                                                recordCase.getHeader()).replace(DASH, "_"),
                                                        recordCase.getHeader().replace(DASH, "_"));
                                                recordCase.setHeaderSchema(inputSchema);
                                            }
                                            recordCase.setRequestCount(apiCountMap.get(mapKey));
                                            if (!updatedCases.contains(recordCase)) {
                                                updatedCases.add(recordCase);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            // 更新 InterfaceCase 对象
            for (InterfaceCase updatedCase : updatedCases) {
                interfaceCaseService.update(updatedCase);
            }
        } catch (Exception e) {
            logger.error("接口测试回调" + e);
            JsonArray userList = new JsonArray();
            userList.add("fengenci");
            if (EnvUtil.isOnline()) {
                for (JsonElement u : userList) {
                    String user = u.getAsString();
                    dxUtil.sendToPersionByCompass("接口数据录制失败，请重新录制", user);
                }
            }
        } finally {
            finishJob(timeData, jobId, startTime, sdf);
        }
    }


    public Set<String> getAllKeys(JsonObject jsonObject) {
        Set<String> keys = new HashSet<>();
        for (String key : jsonObject.keySet()) {
            keys.add(key);
            JsonElement value = jsonObject.get(key);
            if (value.isJsonObject()) {
                keys.addAll(getAllKeys(value.getAsJsonObject()));
            } else if (value.isJsonArray()) {
                for (JsonElement element : value.getAsJsonArray()) {
                    if (element.isJsonObject()) {
                        keys.addAll(getAllKeys(element.getAsJsonObject()));
                    }
                }
            }
        }
        return keys;
    }

    public boolean compareAndUpdate(Set<String> newKeys, Set<String> oldKeys, JsonObject newJson,
            InterfaceCase recordCase, String type) {
        // 如果新的Json为 null 或 {}，则直接返回
        if (newJson == null || newJson.toString().trim().equals("{}")) {
            return false;
        }
        // 如果旧的Json为 null 或 {}，则直接返回
        JsonObject oldJson = null;
        switch (type) {
            case "params":
                oldJson = new JsonParser().parse(recordCase.getParams()).getAsJsonObject();
                break;
            case "body":
                oldJson = new JsonParser().parse(recordCase.getBody()).getAsJsonObject();
                break;
            case "header":
                oldJson = new JsonParser().parse(recordCase.getHeader()).getAsJsonObject();
                break;
        }
        if (oldJson == null || oldJson.toString().trim().equals("{}")) {
            return false;
        }
        // 比较新旧Json的键是否一致
        if (!newKeys.equals(oldKeys)) {
            // 如果不一致，更新对应的字段
            switch (type) {
                case "params":
                    recordCase.setParams(newJson.toString());
                    String oldParamsValue = recordCase.getParamsValue();
                    recordCase.setParamsValue(updateValue(oldParamsValue, newKeys));
                    break;
                case "body":
                    recordCase.setBody(newJson.toString());
                    String oldBodyValue = recordCase.getBodyValue();
                    recordCase.setBodyValue(updateValue(oldBodyValue, newKeys));
                    break;
                case "header":
                    recordCase.setHeader(newJson.toString());
                    String oldHeaderValue = recordCase.getHeaderValue();
                    recordCase.setHeaderValue(updateValue(oldHeaderValue, newKeys));
                    break;
            }
            return true;
        }
        return false;
    }

    public String updateValue(String oldValue, Set<String> newKeys) {
        if (oldValue == null || newKeys == null) {
            throw new IllegalArgumentException("oldValue and newKeys must not be null");
        }
        // 将旧的值按照逗号分割，得到一个字符串数组，然后转换为列表
        String[] oldValues = oldValue.split(",");
        List<String> newValue = new ArrayList<>(Arrays.asList(oldValues));
        // 遍历新的值列表，如果列表中的键在新的键集合中不存在，就从列表中移除这个键
        newValue.removeIf(key -> key.length() < 2 || !newKeys.contains(key.substring(2)));
        // 返回更新后的值
        return String.join(",", newValue);
    }

    @Override
    public IPage<InterfaceTask> list(String jobId, QueryRequest request, InterfaceTask interfaceTask) {
        try {
            LambdaQueryWrapper<InterfaceTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(InterfaceTask::getJobId, jobId);
            if (null != interfaceTask.getPrincipal()) {
                queryWrapper.eq(InterfaceTask::getPrincipal, interfaceTask.getPrincipal());
            }
            if (request.getSwitchStatus()) {
                queryWrapper.ne(InterfaceTask::getStatus, 1);
            }
            Page<InterfaceTask> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);

        } catch (Exception e) {

//            log.error("获取列表失败", e);
            return null;
        }
    }

    @Override
    public void collectResult(JSONObject body) throws ParseException {
        Interfacecheck check = new Interfacecheck();
        JsonParser jsonParser = new JsonParser();
        String jobId = null;
//        log.info(body.toString());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject timeData = jsonParser.parse(body.get("timedata").toString()).getAsJsonObject();
            JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
            jobId = jobData.get("id").getAsString();
            Integer jobResult = jobData.get("jobResult").getAsInt();
            Date startTime = sdf.parse(timeData.get("startTime").getAsString());
//            Date endTime = sdf.parse(timeData.get("endTime").getAsString());
            Float totalTime = timeData.get("totalTime").getAsFloat();
            String reportUrl = jobData.get("report").getAsString();
            QueryWrapper<ConanJob> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("job_id", jobId);

            ConanJob conanJob = conanJobMapper.selectOne(queryWrapper);
//           log.info(conanJob.getJobId());
            try {
                if (null != conanJob) {
                conanJob.setStatus(1);
                conanJob.setStatusStr("已完成");
                conanJob.setJobResult(jobResult);
                conanJob.setStartTime(startTime);
//                conanJob.setEndTime(endTime);
                conanJob.setTotalTime(totalTime);
                conanJob.setReportUrl(reportUrl);
                conanJobMapper.update(conanJob, queryWrapper);
//                log.info(conanJob.toString());
                conanJobService.collectTask(jobId);
                conanJobService.collectCase(jobId);
                }
            }catch(Exception e) {
                log.error("更新状态失败，未找到ID为 {} 的InterfaceJob", jobId);
            }

        try {
            // 接口部分
            // 根据jobId获取task列表
            JsonArray taskArray = conanUtil.getTaskInfo(jobId);
//            遍历task列表，下载输出文件
            for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
                /*
                 * 1.获取文件下载链接
                 */
                JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
                String id = task.get("id").getAsString();
                String deviceModel = task.get("deviceModel").getAsString();
                String deviceVersion = task.get("deviceVersion").getAsString();
//                Double totalTime = task.get("totalTime").getAsDouble();
                String appVersion = task.get("appVersion").getAsString();
                String platform = task.get("platform").getAsString();
                String downloadURL = conanUtil.getDownloadURL(id);
                logger.info("接口测试的conan执行下载地址" + downloadURL);
                if (null != downloadURL) {
                    /*
                     * 2.下载解压文件
                     */
                    FileUtil.downloadAndReadFile(downloadURL, ComConstant.OUT_PUT, id);
                    ArrayList<String> fileList = new ArrayList<>();
                    String path = ComConstant.OUT_PUT + "/" + id + "/interface/";
                    FileUtil.getFileList(path, "", fileList);
                    if (fileList.isEmpty()) {
                        logger.info("接口测试未找到数据，无接口数据,跳出本次循环");
                        continue;
                    } else {
                        if (conanJob != null) {
                            conanJob.setExtra("InterfaceTask");
                        }
                        conanJobMapper.updateById(conanJob);
                    }
                    for (String item : fileList) {
                        String output = FileUtil.read(path + item);
                        if (!output.isEmpty()) {
                            logger.info("开始处理接口数据...");
                            JsonObject jsonObject = jsonParser.parse(output).getAsJsonObject();
                            JsonArray jsonElements = jsonObject.getAsJsonArray("results");
                            for (int index = 0; index < jsonElements.size(); index++) {
                                JsonObject ele = jsonElements.get(index).getAsJsonObject();
                                for (String key : ele.keySet()) {
                                    // key=caseName
                                    JsonArray value = ele.getAsJsonArray(key);
                                    // apiJson:{"api1":[{},{}],"api2":[{}]}
                                    JsonObject apiJson = new JsonObject();
                                    for (int apiIndex = 0; apiIndex < value.size(); apiIndex++) {
                                        JsonObject itemApiData = value.get(apiIndex).getAsJsonObject();
                                        String api = itemApiData.get("reqUrl").getAsString().split("\\?")[0];
                                        if (!apiJson.has(api)) {
                                            apiJson.add(api, new JsonArray());
                                        }
                                        apiJson.get(api).getAsJsonArray().add(itemApiData);
                                    }
                                    // 查找caseName绑定的api，与实际数据（apiJson）对比
                                    List<InterfaceCase> caseList = interfaceCaseService.getListByCaseName(key);
                                    for (InterfaceCase interfaceCase : caseList) {
//                                      int case_id=interfaceCase.getId();
                                        String api = interfaceCase.getApi();
                                        String casePlatform = interfaceCase.getPlatform();
                                        String principal = interfaceCase.getPrincipal();

                                        if (casePlatform.equals(platform) || casePlatform.equalsIgnoreCase("all")) {
                                            InterfaceTask interfaceTask = new InterfaceTask();
                                            interfaceTask.setAppVersion(appVersion);
                                            interfaceTask.setPlatform(platform);
                                            interfaceTask.setUpdateTime(new Date());
                                            interfaceTask.setDeviceVersion(deviceVersion);
                                            interfaceTask.setDeviceModel(deviceModel);

                                            // case执行失败了，接口自然是失败的
                                            if (isCaseNameExtFail(key, jobId)) {
                                                logger.info("case执行失败，进入兜底的task判断");
                                                interfaceTask
                                                        .setStatus(InterfaceCheckErrorCode.CONAN_EX_FAIL.getCode());
                                                interfaceTask.setResult(InterfaceCheckErrorCode.CONAN_EX_FAIL.getMsg());
                                                interfaceTaskService.add(interfaceTask);
                                            } else {
                                                // 实际请求数据中包含该api
                                                if (apiJson.has(api)) {
                                                    JsonArray jsonArray = apiJson.getAsJsonArray(api);
                                                    Integer api_count = jsonArray.size();
                                                    String extra = "";
                                                    HashMap<String, String> results = new HashMap<>();
                                                    // 校验请求次数
                                                    int check_c = 0;
                                                    try {
                                                        if (interfaceCase.getRequestCount() != null
                                                                && interfaceCase.getRequestCount() != 0) {
                                                            check_c = check
                                                                    .checkRequestCount(interfaceCase, api_count)
                                                                    .getCode();
                                                        }
                                                    } catch (NullPointerException e) {
                                                        check_c = InterfaceCheckErrorCode.SUCCESS.getCode();

                                                    }

                                                    int check_url = 0;
                                                    int check_method = 0;
                                                    int check_param = 0;
                                                    int check_header = 0;
                                                    int check_body = 0;
                                                    String[] resultAll = new String[jsonArray.size()];
                                                    for (int apiIndex = 0; apiIndex < jsonArray.size(); apiIndex++) {
                                                        // 校验url和method
                                                        String result_url = "";
                                                        String result_method = "";
                                                        try {
                                                            QueryWrapper<Interface> queryWrapper1 = new QueryWrapper<>();
                                                            queryWrapper1.eq("api", api);
                                                            Interface interface1 = interfaceMapper
                                                                    .selectOne(queryWrapper1);

                                                            int tmp1 = check
                                                                    .checkURL(interface1,
                                                                            jsonArray.get(apiIndex).getAsJsonObject()
                                                                                    .get("reqHeaders").getAsJsonObject()
                                                                                    .get("host").getAsString(),
                                                                            jsonArray.get(apiIndex).getAsJsonObject()
                                                                                    .get("reqUrl").getAsString(),
                                                                            api)
                                                                    .getCode();
                                                            int tmp2 = check
                                                                    .checkMethod(interface1,
                                                                            jsonArray.get(apiIndex).getAsJsonObject()
                                                                                    .get("method").getAsString())
                                                                    .getCode();
                                                            if (tmp1 != 0)
                                                                result_url = InterfaceCheckErrorCode.URLFAIL.getMsg();
                                                            if (tmp2 != 0)
                                                                result_method = InterfaceCheckErrorCode.METHODFAIL
                                                                        .getMsg();
                                                            check_url += tmp1;
                                                            check_method += tmp2;
                                                        } catch (Exception e) {
                                                            logger.error("校验url和method失败");
                                                        }

                                                        // 请求参数校验
                                                        String result_p = "";
                                                        try {
                                                            if (interfaceCase.getParamSchema() != null) {
                                                                String paramsJson = check.paramToJson(
                                                                        jsonArray.get(apiIndex).getAsJsonObject()
                                                                                .get("reqUrl").getAsString());
                                                                // 平台校验
                                                                if (checkPlatform(platform, paramsJson)) {
                                                                    // 平台不匹配，设置失败状态和结果
                                                                    interfaceTask.setStatus(
                                                                            InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                                    .getCode());
                                                                    interfaceTask.setResult(
                                                                            InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                                    .getMsg());
                                                                } else {
                                                                    // 平台匹配，进行参数比对
                                                                    result_p = check.checkJson(paramsJson,
                                                                            interfaceCase.getParamSchema());
                                                                    if (result_p.isEmpty())
                                                                        check_param += InterfaceCheckErrorCode.SUCCESS
                                                                                .getCode();
                                                                    else
                                                                        check_param += InterfaceCheckErrorCode.PARAMFAIL
                                                                                .getCode();
                                                                }
                                                            }
                                                        } catch (Exception e) {
                                                            logger.error("校验参数失败");
                                                        }

// header校验
                                                        String result_hv = "";
                                                        try {
                                                            if (interfaceCase.getHeaderSchema() != null) {
                                                                String headersJson = jsonArray.get(apiIndex)
                                                                        .getAsJsonObject().get("reqHeaders")
                                                                        .getAsJsonObject().getAsString();
                                                                // 平台校验
                                                                if (checkPlatform(platform, headersJson)) {
                                                                    // 平台不匹配，设置失败状态和结果
                                                                    interfaceTask.setStatus(
                                                                            InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                                    .getCode());
                                                                    interfaceTask.setResult(
                                                                            InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                                    .getMsg());
                                                                } else {
                                                                    // 平台匹配，进行头部比对
                                                                    result_hv = check.checkJson(headersJson,
                                                                            interfaceCase.getHeaderSchema());
                                                                    if (result_hv.isEmpty())
                                                                        check_header += InterfaceCheckErrorCode.SUCCESS
                                                                                .getCode();
                                                                    else
                                                                        check_header += InterfaceCheckErrorCode.HEADERFAIL
                                                                                .getCode();
                                                                }
                                                            }
                                                        } catch (Exception e) {
                                                            logger.error("校验header失败");
                                                        }

// body校验
                                                        String result_b = "";
                                                        try {
                                                            if (interfaceCase.getBodySchema() != null) {
                                                                String bodyJson = jsonArray.get(apiIndex)
                                                                        .getAsJsonObject().get("reqBody")
                                                                        .getAsJsonObject().toString();
                                                                // 平台校验
                                                                if (checkPlatform(platform, bodyJson)) {
                                                                    // 平台不匹配，设置失败状态和结果
                                                                    interfaceTask.setStatus(
                                                                            InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                                    .getCode());
                                                                    interfaceTask.setResult(
                                                                            InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                                    .getMsg());
                                                                } else {
                                                                    // 平台匹配，进行主体比对
                                                                    result_b = check.checkJson(bodyJson,
                                                                            interfaceCase.getBodySchema());
                                                                    if (result_b.isEmpty())
                                                                        check_body += InterfaceCheckErrorCode.SUCCESS
                                                                                .getCode();
                                                                    else
                                                                        check_body += InterfaceCheckErrorCode.BODYFAIL
                                                                                .getCode();
                                                                }
                                                            }
                                                        } catch (Exception e) {
                                                            logger.error("校验body失败");
                                                        }

                                                        results.put("request_count", check
                                                                .checkRequestCount(interfaceCase, api_count).getMsg());
                                                        results.put("url", result_url);
                                                        results.put("method", result_method);
                                                        results.put("params", result_p);
                                                        results.put("body", result_b);
                                                        results.put("header", result_hv);
                                                        resultAll[apiIndex] = JSON.toJSONString(results);
                                                    }
                                                    if (check_url + check_method + check_c + check_param + check_param
                                                            + check_header + check_body == 0) {
                                                        interfaceTask.setStatus(1);
                                                        interfaceTask.setResult("succeed");
                                                    } else {
                                                        interfaceTask.setStatus(0);
                                                        interfaceTask.setResult(Arrays.toString(resultAll));
                                                    }

                                                } else {
//                                                interfaceTask.setVariable("");
                                                    interfaceTask.setStatus(0);
                                                    interfaceTask.setResult("api not found");
                                                }
                                                try {
                                                    interfaceTask.setCaseId(interfaceCase.getId());
                                                    interfaceTask.setPrincipal(principal);
                                                    interfaceTask.setJobId(jobId);
                                                    if (apiJson.has(api))
                                                        interfaceTask
                                                                .setRequestData(apiJson.getAsJsonArray(api).toString());
                                                    else
                                                        interfaceTask.setRequestData("[{}]");
                                                    interfaceTaskService.add(interfaceTask);
                                                } catch (Exception e) {
                                                    logger.error("校验结果存储失败" + e.toString());
                                                }
                                                logger.info("本次测试：");
                                            }

                                        } else {
//                                                log.info("本次测试：" + platform + ", api为单平台：" + casePlatform);
                                        }
                                    }
                                }

                            }
                        } else
                            logger.info("output not found..");
                    }
                } else
                    logger.info("URL not found..");
            }
            logger.info("interfacetask done.");
            Date endTime = sdf.parse(timeData.get("endTime").getAsString());
            if (conanJob != null) {
                conanJob.setEndTime(endTime);
            }
            conanJobMapper.update(conanJob, queryWrapper);

        } catch (Exception e) {
            logger.error("接口测试回调"+e.toString());
            JsonArray userList =  new JsonArray();
            userList.add("fengenci");
            String repUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/interfaceautotest/interfacetask?jobId=";
            if (EnvUtil.isOnline()) {
                for (JsonElement u : userList) {
                    String user = u.getAsString();
                    dxUtil.sendToPersionByCompass("※ 请求自动化测试执行失败 ※：\n 【任务ID】："  + jobId + "\n" +
                            "【查看报告】：[查看报告|" + repUrl + jobId + "]\n" +
                            "如有问题请联系fengenci", user);
                }
            }
        } finally {
            // 使用 jobId 作为查询条件，从数据库中查询出对应的 InterfaceJob 对象
            QueryWrapper<InterfaceJob> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.like("report_id", jobId);
            InterfaceJob interfaceJob = interfaceJobMapper.selectOne(queryWrapper1);

            boolean flag = false; // 标记任务是否完成

            if (interfaceJob != null) {
                // 更新 finishedId 字段
                String finishedId = interfaceJob.getFinishedId();
                finishedId = finishedId == null ? jobId : finishedId + "_" + jobId;
                interfaceJob.setFinishedId(finishedId);
                interfaceJob.setStatus(0);
                interfaceJob.setStatusStr("进行中");
                Date endTime = sdf.parse(timeData.get("endTime").getAsString());

                // 检查 InterfaceJob 对象的 reportId 和 finishedId 字段，如果这两个字段的值相等，那么表示任务已经完成
                if (interfaceJob.getReportId().length() == interfaceJob.getFinishedId().length()) {
                    // 任务已完成，更新状态
                    interfaceJob.setStatus(1);
                    interfaceJob.setStatusStr("已完成");
                    interfaceJob.setStartTime(startTime);
                    interfaceJob.setFinishTime(endTime);
                    flag = true; // 标记任务已完成
                }

                // 更新 InterfaceJob 对象
                interfaceJobMapper.update(interfaceJob, queryWrapper1);
            }

            if (flag) {
                JsonArray userList =  new JsonArray();
                userList.add("fengenci");
                String repUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/interfaceautotest/interfacetask?jobId=";
                if (EnvUtil.isOnline()) {
                    for (JsonElement u : userList) {
                        String user = u.getAsString();
                        dxUtil.sendToPersionByCompass("※ 请求自动化测试测试成功 ※：\n 【任务ID】："  + jobId+ "\n" +
                                "【测试进度】：✅ 已完成\n" +
                                "【开始时间】：" + startTime + "\n" +
                                "【完成时间】：" + sdf.format(new Date()) + "\n" +
                                "【查看报告】：[查看报告|" + repUrl + interfaceJob.getFinishedId() + "]\n" +
                                "如有问题请联系fengenci", user);
                    }
                }
            }
            FileUtil.delFolder(ComConstant.OUT_PUT);
        }
    }

    @Override
    public void collectResultNew(JSONObject body) throws ParseException {
        Interfacecheck check = new Interfacecheck();
        JsonParser jsonParser = new JsonParser();
        String jobId = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JsonObject timeData = jsonParser.parse(body.get("timedata").toString()).getAsJsonObject();
        JsonObject jobData = jsonParser.parse(body.get("jobdata").toString()).getAsJsonObject();
        jobId = jobData.get("id").getAsString();
        Integer jobResult = jobData.get("jobResult").getAsInt();
        Date startTime = sdf.parse(timeData.get("startTime").getAsString());
        Date endTime = sdf.parse(timeData.get("endTime").getAsString());
        Float totalTime = timeData.get("totalTime").getAsFloat();
        String reportUrl = jobData.get("report").getAsString();
        QueryWrapper<ConanJob> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("job_id", jobId);

        ConanJob conanJob = conanJobMapper.selectOne(queryWrapper);
        try {
            if (null != conanJob) {
                updateConanJob(conanJob, jobResult, startTime, totalTime, reportUrl, queryWrapper);
                conanJobService.collectTask(jobId);
                conanJobService.collectCase(jobId);
            }
        } catch (Exception e) {
            log.error("更新状态失败，未找到ID为 {} 的InterfaceJob", jobId);
        }

        try {
            processInterfacePart(jobId, conanJob, queryWrapper, jsonParser, check, startTime, endTime, sdf);
        } catch (Exception e) {
            handleException(jobId, e);
        } finally {
            finishJob(timeData, jobId, startTime, sdf);
        }
    }

    private void updateConanJob(ConanJob conanJob, Integer jobResult, Date startTime, Float totalTime, String reportUrl,
            QueryWrapper<ConanJob> queryWrapper) {
        conanJob.setStatus(1);
        conanJob.setStatusStr("已完成");
        conanJob.setJobResult(jobResult);
        conanJob.setStartTime(startTime);
        conanJob.setTotalTime(totalTime);
        conanJob.setReportUrl(reportUrl);
        conanJobMapper.update(conanJob, queryWrapper);
    }

    private void processInterfacePart(String jobId, ConanJob conanJob, QueryWrapper<ConanJob> queryWrapper,
            JsonParser jsonParser, Interfacecheck check, Date startTime,Date endTime ,SimpleDateFormat sdf) {
        JsonArray taskArray = conanUtil.getTaskInfo(jobId);
        for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
            JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
            String downloadURL = conanUtil.getDownloadURL(task.get("id").getAsString());
            if (null != downloadURL) {
                processDownloadedFile(jobId, conanJob, queryWrapper, jsonParser, check, task, downloadURL);
            } else {
                logger.info("URL not found..");
            }
        }
        logger.info("interfacetask done.");
        if (conanJob != null) {
            conanJob.setEndTime(endTime);
        }
        conanJobMapper.update(conanJob, queryWrapper);
    }

    private void processDownloadedFile(String jobId, ConanJob conanJob, QueryWrapper<ConanJob> queryWrapper,
            JsonParser jsonParser, Interfacecheck check, JsonObject task, String downloadURL) {
        FileUtil.downloadAndReadFile(downloadURL, ComConstant.OUT_PUT, task.get("id").getAsString());
        ArrayList<String> fileList = new ArrayList<>();
        String path = ComConstant.OUT_PUT + "/" + task.get("id").getAsString() + "/interface/";
        FileUtil.getFileList(path, "", fileList);
        if (!fileList.isEmpty()) {
            conanJob.setExtra("InterfaceTask");
            conanJobMapper.updateById(conanJob);
            processFileList(jobId, jsonParser, check, task, fileList, path);
        } else {
            logger.info("接口测试未找到数据，无接口数据,跳出本次循环");
        }
    }

    private void processFileList(String jobId,
                                 JsonParser jsonParser, Interfacecheck check, JsonObject task, ArrayList<String> fileList, String path) {
        for (String item : fileList) {
            String output = FileUtil.read(path + item);
            if (!output.isEmpty()) {
                processOutputData(jobId, jsonParser, check, task, output);
            } else {
                logger.info("output not found..");
            }
        }
    }

    private void processOutputData(String jobId,
                                   JsonParser jsonParser, Interfacecheck check, JsonObject task, String output) {

        String id = task.get("id").getAsString();
        String deviceModel = task.get("deviceModel").getAsString();
        String deviceVersion = task.get("deviceVersion").getAsString();
        String appVersion = task.get("appVersion").getAsString();
        String platform = task.get("platform").getAsString();
        String downloadURL = conanUtil.getDownloadURL(id);

        logger.info("开始处理接口数据...");
        JsonObject jsonObject = jsonParser.parse(output).getAsJsonObject();
        JsonArray jsonElements = jsonObject.getAsJsonArray("results");
        for (int index = 0; index < jsonElements.size(); index++) {
            JsonObject ele = jsonElements.get(index).getAsJsonObject();
            for (String key : ele.keySet()) {
                // key=caseName
                JsonArray value = ele.getAsJsonArray(key);
                // apiJson:{"api1":[{},{}],"api2":[{}]}
                JsonObject apiJson = new JsonObject();
                for (int apiIndex = 0; apiIndex < value.size(); apiIndex++) {
                    JsonObject itemApiData = value.get(apiIndex).getAsJsonObject();
                    String api = itemApiData.get("reqUrl").getAsString().split("\\?")[0];
                    if (!apiJson.has(api)) {
                        apiJson.add(api, new JsonArray());
                    }
                    apiJson.get(api).getAsJsonArray().add(itemApiData);
                }
                // 查找caseName绑定的api，与实际数据（apiJson）对比
                List<InterfaceCase> caseList = interfaceCaseService.getListByCaseName(key);
                for (InterfaceCase interfaceCase : caseList) {
//                  int case_id=interfaceCase.getId();
                    String api = interfaceCase.getApi();
                    String casePlatform = interfaceCase.getPlatform();
                    String principal = interfaceCase.getPrincipal();

                    if (casePlatform.equals(platform) || casePlatform.equalsIgnoreCase("all")) {
                        InterfaceTask interfaceTask = new InterfaceTask();
                        interfaceTask.setAppVersion(appVersion);
                        interfaceTask.setPlatform(platform);
                        interfaceTask.setUpdateTime(new Date());
                        interfaceTask.setDeviceVersion(deviceVersion);
                        interfaceTask.setDeviceModel(deviceModel);

                        // case执行失败了，接口自然是失败的
                        if (isCaseNameExtFail(key, jobId)) {
                            logger.info("case执行失败，进入兜底的task判断");
                            interfaceTask
                                    .setStatus(InterfaceCheckErrorCode.CONAN_EX_FAIL.getCode());
                            interfaceTask.setResult(InterfaceCheckErrorCode.CONAN_EX_FAIL.getMsg());
                            interfaceTaskService.add(interfaceTask);
                        } else {
                            // 实际请求数据中包含该api
                            if (apiJson.has(api)) {
                                JsonArray jsonArray = apiJson.getAsJsonArray(api);
                                Integer api_count = jsonArray.size();
                                String extra = "";
                                HashMap<String, String> results = new HashMap<>();
                                // 校验请求次数
                                int check_c = 0;
                                try {
                                    if (check != null) {
                                        if (interfaceCase.getRequestCount() != null
                                                && interfaceCase.getRequestCount() != 0) {
                                            check_c = check
                                                    .checkRequestCount(interfaceCase, api_count)
                                                    .getCode();
                                        }
                                    }
                                } catch (NullPointerException e) {
                                    check_c = InterfaceCheckErrorCode.SUCCESS.getCode();

                                }

                                int check_url = 0;
                                int check_method = 0;
                                int check_param = 0;
                                int check_header = 0;
                                int check_body = 0;
                                String[] resultAll = new String[jsonArray.size()];
                                for (int apiIndex = 0; apiIndex < jsonArray.size(); apiIndex++) {
                                    // 校验url和method
                                    String result_url = "";
                                    String result_method = "";
                                    try {
                                        QueryWrapper<Interface> queryWrapper1 = new QueryWrapper<>();
                                        queryWrapper1.eq("api", api);
                                        Interface interface1 = interfaceMapper
                                                .selectOne(queryWrapper1);

                                        int tmp1 = 0;
                                        if (check != null) {
                                            tmp1 = check
                                                    .checkURL(interface1,
                                                            jsonArray.get(apiIndex).getAsJsonObject()
                                                                    .get("reqHeaders").getAsJsonObject()
                                                                    .get("host").getAsString(),
                                                            jsonArray.get(apiIndex).getAsJsonObject()
                                                                    .get("reqUrl").getAsString(),
                                                            api)
                                                    .getCode();
                                        }
                                        int tmp2 = 0;
                                        if (check != null) {
                                            tmp2 = check
                                                    .checkMethod(interface1,
                                                            jsonArray.get(apiIndex).getAsJsonObject()
                                                                    .get("method").getAsString())
                                                    .getCode();
                                        }
                                        if (tmp1 != 0)
                                            result_url = InterfaceCheckErrorCode.URLFAIL.getMsg();
                                        if (tmp2 != 0)
                                            result_method = InterfaceCheckErrorCode.METHODFAIL
                                                    .getMsg();
                                        check_url += tmp1;
                                        check_method += tmp2;
                                    } catch (Exception e) {
                                        logger.error("校验url和method失败");
                                    }

                                    // 请求参数校验
                                    String result_p = "";
                                    try {
                                        if (interfaceCase.getParamSchema() != null) {
                                            String paramsJson =jsonArray.get(apiIndex)
                                                    .getAsJsonObject().get("reqParams")
                                                    .getAsJsonObject().toString();
                                            // 平台校验
                                            if (checkPlatform(platform, paramsJson)) {
                                                // 平台不匹配，设置失败状态和结果
                                                interfaceTask.setStatus(
                                                        InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                .getCode());
                                                interfaceTask.setResult(
                                                        InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                .getMsg());
                                            } else {
                                                // 平台匹配，进行参数比对
                                                if (check != null) {
                                                    result_p = check.checkJson(paramsJson,
                                                            interfaceCase.getParamSchema());
                                                }
                                                if (result_p.isEmpty())
                                                    check_param += InterfaceCheckErrorCode.SUCCESS
                                                            .getCode();
                                                else
                                                    check_param += InterfaceCheckErrorCode.PARAMFAIL
                                                            .getCode();
                                            }
                                        }
                                    } catch (Exception e) {
                                        logger.error("校验参数失败");
                                    }

// header校验
                                    String result_hv = "";
                                    try {
                                        if (interfaceCase.getHeaderSchema() != null) {
                                            String headersJson = jsonArray.get(apiIndex)
                                                    .getAsJsonObject().get("reqHeaders")
                                                    .getAsJsonObject().getAsString();
                                            // 平台校验
                                            if (checkPlatform(platform, headersJson)) {
                                                // 平台不匹配，设置失败状态和结果
                                                interfaceTask.setStatus(
                                                        InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                .getCode());
                                                interfaceTask.setResult(
                                                        InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                .getMsg());
                                            } else {
                                                // 平台匹配，进行头部比对
                                                if (check != null) {
                                                    result_hv = check.checkJson(headersJson,
                                                            interfaceCase.getHeaderSchema());
                                                }
                                                if (result_hv.isEmpty())
                                                    check_header += InterfaceCheckErrorCode.SUCCESS
                                                            .getCode();
                                                else
                                                    check_header += InterfaceCheckErrorCode.HEADERFAIL
                                                            .getCode();
                                            }
                                        }
                                    } catch (Exception e) {
                                        logger.error("校验header失败");
                                    }

// body校验
                                    String result_b = "";
                                    try {
                                        if (interfaceCase.getBodySchema() != null) {
                                            String bodyJson = jsonArray.get(apiIndex)
                                                    .getAsJsonObject().get("reqBody")
                                                    .getAsJsonObject().getAsString();
                                            // 平台校验
                                            if (checkPlatform(platform, bodyJson)) {
                                                // 平台不匹配，设置失败状态和结果
                                                interfaceTask.setStatus(
                                                        InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                .getCode());
                                                interfaceTask.setResult(
                                                        InterfaceCheckErrorCode.PLATFORM_MISMATCH
                                                                .getMsg());
                                            } else {
                                                // 平台匹配，进行主体比对
                                                if (check != null) {
                                                    result_b = check.checkJson(bodyJson,
                                                            interfaceCase.getBodySchema());
                                                }
                                                if (result_b.isEmpty())
                                                    check_body += InterfaceCheckErrorCode.SUCCESS
                                                            .getCode();
                                                else
                                                    check_body += InterfaceCheckErrorCode.BODYFAIL
                                                            .getCode();
                                            }
                                        }
                                    } catch (Exception e) {
                                        logger.error("校验body失败");
                                    }

                                    if (check != null) {
                                        results.put("request_count", check
                                                .checkRequestCount(interfaceCase, api_count).getMsg());
                                    }
                                    results.put("url", result_url);
                                    results.put("method", result_method);
                                    results.put("params", result_p);
                                    results.put("body", result_b);
                                    results.put("header", result_hv);
                                    resultAll[apiIndex] = JSON.toJSONString(results);
                                }
                                if (check_url + check_method + check_c + check_param + check_param
                                        + check_header + check_body == 0) {
                                    interfaceTask.setStatus(1);
                                    interfaceTask.setResult("succeed");
                                } else {
                                    interfaceTask.setStatus(0);
                                    interfaceTask.setResult(Arrays.toString(resultAll));
                                }

                            } else {
//                                                interfaceTask.setVariable("");
                                interfaceTask.setStatus(0);
                                interfaceTask.setResult("api not found");
                            }
                            try {
                                // 查询是否已经存在相同的 jobId 和 api 的记录
                                QueryWrapper<InterfaceTask> queryWrapper2 = new QueryWrapper<>();
                                queryWrapper2.eq("job_id", jobId);
                                queryWrapper2.eq("case_id", interfaceCase.getId());
                                InterfaceTask existingTask = interfaceTaskMapper.selectOne(queryWrapper2);

                                if (existingTask != null) {
                                    // 如果存在，那么更新这条记录
                                    existingTask.setAppVersion(appVersion);
                                    existingTask.setPlatform(platform);
                                    existingTask.setUpdateTime(new Date());
                                    existingTask.setDeviceVersion(deviceVersion);
                                    existingTask.setDeviceModel(deviceModel);
                                    existingTask.setStatus(interfaceTask.getStatus());
                                    existingTask.setResult(interfaceTask.getResult());
                                    existingTask.setRequestData(interfaceTask.getRequestData());
                                    interfaceTaskMapper.updateById(existingTask);
                                } else {
                                    // 如果不存在，那么添加新的记录
                                    interfaceTask.setCaseId(interfaceCase.getId());
                                    interfaceTask.setPrincipal(principal);
                                    interfaceTask.setJobId(jobId);
                                    if (apiJson.has(api))
                                        interfaceTask.setRequestData(apiJson.getAsJsonArray(api).toString());
                                    else
                                        interfaceTask.setRequestData("[{}]");
                                    interfaceTaskService.add(interfaceTask);
                                }
                            } catch (Exception e) {
                                logger.error("校验结果存储失败" + e.toString());
                            }
                            logger.info("本次测试执行完毕："+jobId);
                        }

                    } else {
                        logger.info("本次测试：" + platform + ", api为单平台：" + casePlatform);
                    }
                }
            }
        }
    }

    private void handleException(String jobId, Exception e) {
        logger.error("接口测试回调" + e.toString());
        JsonArray userList = new JsonArray();
        userList.add("fengenci");
        String repUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/interfaceautotest/interfacetask?jobId=";
        if (EnvUtil.isOnline()) {
            for (JsonElement u : userList) {
                String user = u.getAsString();
                dxUtil.sendToPersionByCompass("※ 请求自动化测试执行失败 ※：\n 【任务ID】：" + jobId + "\n" + "【查看报告】：[查看报告|" + repUrl + jobId
                        + "]\n" + "如有问题请联系fengenci", user);
            }
        }
    }

    private void finishJob(JsonObject timeData, String jobId, Date startTime, SimpleDateFormat sdf) throws ParseException {
        QueryWrapper<InterfaceJob> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.like("report_id", jobId);
        InterfaceJob interfaceJob = interfaceJobMapper.selectOne(queryWrapper1);

        boolean flag = false; // 标记任务是否完成

        if (interfaceJob != null) {
            flag = updateInterfaceJob(timeData, jobId, startTime, sdf, queryWrapper1, interfaceJob);
        }

        if (flag) {
            sendCompletionMessage(jobId, startTime, sdf, interfaceJob);
        }
        FileUtil.delFolder(ComConstant.OUT_PUT);
    }

    private boolean updateInterfaceJob(JsonObject timeData, String jobId, Date startTime, SimpleDateFormat sdf,
            QueryWrapper<InterfaceJob> queryWrapper1, InterfaceJob interfaceJob) throws ParseException {
        boolean flag;
        String finishedId = interfaceJob.getFinishedId();
        if (finishedId == null) {
            finishedId = jobId;
        } else {
            // 检查数据库中的finishedId是否已经包含了本次的jobId
            List<String> finishedIds = Arrays.asList(finishedId.split("_"));
            if (!finishedIds.contains(jobId)) {
                // 如果不包含，那么进行拼接
                finishedId += "_" + jobId;
            }
        }
        interfaceJob.setFinishedId(finishedId);
        interfaceJob.setStatus(0);
        interfaceJob.setStatusStr("进行中");
        Date endTime = sdf.parse(timeData.get("endTime").getAsString());

        if (interfaceJob.getReportId().length() == interfaceJob.getFinishedId().length()) {
            interfaceJob.setStatus(1);
            interfaceJob.setStatusStr("已完成");
            interfaceJob.setStartTime(startTime);
            interfaceJob.setFinishTime(endTime);
        flag = true; // 标记任务已完成
    } else {
        flag = false;
    }

    interfaceJobMapper.update(interfaceJob, queryWrapper1);
    return flag;
}


    private void sendCompletionMessage(String jobId, Date startTime, SimpleDateFormat sdf, InterfaceJob interfaceJob) {
    JsonArray userList = new JsonArray();
    userList.add("fengenci");
    String repUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/interfaceautotest/interfacetask?jobId=";
    if (EnvUtil.isOnline()) {
        for (JsonElement u : userList) {
            String user = u.getAsString();
            if ("interfaceTest".equals(interfaceJob.getType())) {
                dxUtil.sendToPersionByCompass("※ 请求自动化测试成功 ※：\n 【任务ID】：" + jobId+ "\n" + "【测试进度】：✅ 已完成\n" + "【开始时间】："
                        + startTime + "\n" + "【完成时间】：" + sdf.format(new Date()) + "\n" + "【查看报告】：[查看报告|" + repUrl
                        + interfaceJob.getFinishedId() + "]\n" + "如有问题请联系fengenci", user);
            } else {
                dxUtil.sendToPersionByCompass("※ 请求自动化数据录制成功 ※：\n 【任务ID】：" + jobId+ "\n" + "【录制进度】：✅ 已完成\n" + "【开始时间】："
                        + startTime + "\n" + "【完成时间】：" + sdf.format(new Date()) + "\n" + "【查看报告】：[查看报告|" + repUrl
                        + interfaceJob.getFinishedId() + "]\n" + "如有问题请联系fengenci", user);
            }
        }
    }
}


    boolean isCaseNameExtFail(String caseName, String jobId) {
        JsonArray caseJsonInfo = conanUtil.getAllCaseInfo(jobId);
        if (caseJsonInfo == null) {
            logger.error("getAllCaseInfo返回null");
            return true;
        }
        for (JsonElement caseItem : caseJsonInfo) {
            JsonObject caseJson = caseItem.getAsJsonObject();
            if (caseJson == null) {
                logger.error("getAsJsonObject返回null");
                continue;
            }
            if (caseJson.get("methodName").getAsString().equals(caseName)) {
                if (caseJson.get("status").getAsInt() == ConanCaseResultEnums.PASS.getCode()) {
                    logger.info("task中包含指定用例:"+caseName+caseJson.get("taskId").getAsString());
                return false;
            }
        }
    }
    logger.info("task中不包含指定用例:"+caseName+ jobId);
    return true;
}

// 平台校验方法
private boolean checkPlatform(String currentPlatform, String json) {
    // 如果当前平台是android，检查JSON中是否包含"ios"
    if ("android".equalsIgnoreCase(currentPlatform) && StringUtils.containsIgnoreCase(json, "ios")) {
        return true;
    }
    // 如果当前平台是ios，检查JSON中是否包含"android"
    return "ios".equalsIgnoreCase(currentPlatform) && StringUtils.containsIgnoreCase(json, "android");
    // 平台匹配
}

}
