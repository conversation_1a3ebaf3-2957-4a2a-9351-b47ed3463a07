package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * Created by xubangxi on 2020/05/18.
 */
@Data
@TableName("autotest_mge_case")
public class MgeCase{
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    //bid
    private String bid;
    //cid
    private String cid;
    //business_name(业务模块)
    private String businessName;
    //val_lab
    private String valLab;
    //principal(负责人)
    private String principal;
    //private int isSecondVallab;
    private String tag;
    private String deviceType;
    private String module;
    private String principalCn;
}
