package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("autotest_mge_task")
public class MgeTask {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    //输入表:
private String id;
    private String tag;
    private String appVersion;
    private String pageIdentifier;
    private String eventId;
    private String eventTimestamp;
    private String eventType;
    private String deviceType;
    private String eventAttribute;
    private String status;
    //business_name(业务模块)
    private String businessName;
    //principal(负责人)
    private String principal;
    private String eventName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private int jobId;
    private int isBug;
    private int affirmStatus;
    private String operator;
    private String reportCid;
    private String reportLab;
    private String reportTag;
    private String moudle;

}
