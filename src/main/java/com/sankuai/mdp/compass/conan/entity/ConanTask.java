package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by xieyongrui on 2020/4/2.
 */
@Data
@TableName("autotest_conan_task")
public class ConanTask {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer taskId;

    private String jobId;

    private Integer status;

    private String statusStr;

    private String deviceModel;

    private String deviceVersion;

    private String serialNumber;

    private String deviceResolution;

    private String taskResult;

    private Integer taskMsgCode;

    private String crashUrl;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private float totalTime;

    private String issueKey;
    private String issueStatus;
    private String issueAssignee;

}
