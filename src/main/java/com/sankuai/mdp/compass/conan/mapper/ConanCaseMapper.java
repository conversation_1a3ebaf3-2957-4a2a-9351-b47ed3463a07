package com.sankuai.mdp.compass.conan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.conan.entity.ConanCase;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created by xieyongrui on 2020/4/2.
 */
@Mapper
public interface ConanCaseMapper extends BaseMapper<ConanCase> {
    @Select("select method_name as caseName,class_name as caseClass,case_owner as caseOwner,T.success_count/T.count as passRate " +
            "from " +
            "(SELECT method_name,class_name,count(*) as count,sum(case when status=0 then 1 else 0 end) as fail_count,sum(case when status=1 then 1 else 0 end) as success_count " +
            "from autotest_conan_case " +
            "where job_id in (select job_id from autotest_conan_job where job_type in ('regression','smoke') and platform=#{platform} and status=1 and start_time >= #{startTime}) " +
            "group by method_name) T left join group_cases on T.method_name = group_cases.case_name and T.class_name = group_cases.case_class and group_cases.case_platform = #{platform} " +
            "where T.success_count/T.count < #{passRate}")
    List<GroupCase> selectPassRateLowerCaseName(@Param("startTime")String startTime,@Param("passRate")Double passRate,@Param("platform")String platform);

    @Select("SELECT task_id\n" +
            "from autotest_conan_case\n" +
            "where job_id in (select job_id from autotest_conan_job where job_type in ('regression','smoke') and platform=#{platform} and status=1 and start_time>=#{startTime}) \n" +
            "and method_name=#{caseName}\n" +
            "and class_name=#{className}\n" +
            "and status in (0,2)")
    List<Integer> selectFailedCasesJobIdList(@Param("startTime")String startTime,@Param("platform")String platform,@Param("caseName")String caseName,@Param("className")String className);
}
