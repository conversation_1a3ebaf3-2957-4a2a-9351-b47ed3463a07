package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by <PERSON>ieyongrui on 2020/4/15.
 */
@Data
@TableName("group_cases")
public class GroupCase {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String caseName;

    private String caseClass;

    private String caseDetail;

    private String caseType;

    private String casePlatform;

    private String caseOwner;

    private Integer caseState;

    private String casePriority;

    private String adenId;

    private String caseModule;

    private String caseChannel;

    private String caseCategory;

    @TableField(exist = false)
    private double passRate;

    private Integer isUpdated;
    private Integer caseStatus;
    private String caseRunningType;
//    private String specificCaseName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private String caseRelyOn;
}
