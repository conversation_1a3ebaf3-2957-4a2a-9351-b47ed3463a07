package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.ConanJob;
import com.sankuai.mdp.compass.conan.entity.InterfaceJob;
import com.sankuai.mdp.compass.conan.mapper.InterfaceJobMapper;
import com.sankuai.mdp.compass.conan.service.InterfaceJobService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class InterfaceJobServiceImpl extends ServiceImpl<InterfaceJobMapper,InterfaceJob> implements InterfaceJobService {

    @Autowired
    InterfaceJobMapper interfaceJobMapper;

    @Override
    public IPage<InterfaceJob> list(QueryRequest request, InterfaceJob interfaceJob) {
        try {
            LambdaQueryWrapper<InterfaceJob> queryWrapper = new LambdaQueryWrapper<>();
            if (interfaceJob.getStatus() != null) {
                queryWrapper.eq(InterfaceJob::getStatus, interfaceJob.getStatus());
            }
            if (interfaceJob.getType() != null) {
                queryWrapper.eq(InterfaceJob::getType, interfaceJob.getType());
            }
            queryWrapper.orderByDesc(InterfaceJob::getId);
            Page<InterfaceJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            throw new RuntimeException(e);
    }
}


    @Override
    public Resp updateStatusById(InterfaceJob interfaceJob) {
        Integer id = interfaceJob.getId();
        InterfaceJob interfaceJob1 = interfaceJobMapper.selectById(id);
        if(interfaceJob1 != null) {
            interfaceJob1.setStatus(interfaceJob.getStatus());
            interfaceJobMapper.updateById(interfaceJob1);
            return Resp.success();
        } else {
            log.error("更新状态失败，未找到ID为 {} 的InterfaceJob", id);
            return Resp.error();
        }
    }

    @Override
    public Integer lastestJobId(InterfaceJob interfaceJob) {
        LambdaQueryWrapper<InterfaceJob> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InterfaceJob::getPlatform, interfaceJob.getPlatform())
                .eq(InterfaceJob::getType, interfaceJob.getType())
                .orderByDesc(InterfaceJob::getId)
                .last("limit 1");
        List<InterfaceJob> interfaceJobs = interfaceJobMapper.selectList(queryWrapper);
        return Optional.ofNullable(interfaceJobs)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0).getId())
                .orElse(-1);
    }

    @Override
    public Resp updateReportId(InterfaceJob interfaceJob) {
        Integer id = interfaceJob.getId();
        InterfaceJob interfaceJob2 = interfaceJobMapper.selectById(id);
        String oldReportId = interfaceJob2.getReportId();
        String newReportId = interfaceJob.getReportId();
        if (oldReportId != null && !oldReportId.isEmpty()) {
            newReportId = oldReportId + "_" + newReportId;
        }
        // 如果新的reportId以"_"结尾，去掉末尾的"_"
        if (newReportId.endsWith("_")) {
            newReportId = newReportId.substring(0, newReportId.length() - 1);
        }
        interfaceJob2.setReportId(newReportId);
    interfaceJob2.setBuildUrl(interfaceJob.getBuildUrl());
    int result = interfaceJobMapper.updateById(interfaceJob2);
    if (result <= 0) {
        log.error("更新InterfaceJob失败");
        throw new RuntimeException("更新InterfaceJob失败");
    }
    return Resp.success();
}




    @Override
    public Integer add(InterfaceJob interfaceJob) {
        int result = interfaceJobMapper.insert(interfaceJob);
        if (result <= 0) {
            log.error("添加InterfaceJob失败");
            throw new RuntimeException("添加InterfaceJob失败");
        }
        return interfaceJob.getId();
    }
}
