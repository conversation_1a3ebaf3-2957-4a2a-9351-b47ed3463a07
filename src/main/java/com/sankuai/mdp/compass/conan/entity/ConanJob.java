package com.sankuai.mdp.compass.conan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by xieyongrui on 2020/4/1.
 */
@Data
@TableName("autotest_conan_job")
public class ConanJob implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer buildId;

    private String jobId;

    private Integer status;

    private String statusStr;

    private String jobType;

    private String alias;

    private String platform;

    private Integer jobResult;

    private String reportUrl;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private Float totalTime;

    private String extra;
}
