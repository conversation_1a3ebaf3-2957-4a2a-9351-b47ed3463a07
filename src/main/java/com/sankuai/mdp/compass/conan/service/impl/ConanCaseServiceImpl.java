package com.sankuai.mdp.compass.conan.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.sankuai.mdp.compass.conan.entity.ConanCase;
import com.sankuai.mdp.compass.conan.entity.GroupCase;
import com.sankuai.mdp.compass.conan.mapper.ConanCaseMapper;
import com.sankuai.mdp.compass.conan.service.ConanCaseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Created by xieyongrui on 2020/4/2.
 */
@Slf4j
@Service
public class ConanCaseServiceImpl extends ServiceImpl<ConanCaseMapper,ConanCase> implements ConanCaseService{
    @Autowired
    ConanCaseMapper conanCaseMapper;

    @Override
    public void add(ConanCase conanCase) {

    }

    @Override
    public void insetObject(JsonObject jsonObject) {
        SimpleDateFormat sdf =   new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ConanCase conanCase = new ConanCase();
        try {
            conanCase.setId(jsonObject.get("id").getAsInt());
            conanCase.setJobId(jsonObject.get("jobId").getAsInt());
            conanCase.setTaskId(jsonObject.get("taskId").getAsInt());
            conanCase.setClassName(jsonObject.get("className").getAsString());
            conanCase.setMethodName(jsonObject.get("methodName").getAsString());
            if (null != jsonObject.get("startTime") && jsonObject.get("startTime").getAsString().length() > 4) {
                conanCase.setStartTime(sdf.parse(jsonObject.get("startTime").getAsString()));
            }
            if (null != jsonObject.get("endTime") && jsonObject.get("endTime").getAsString().length() > 4) {
                conanCase.setEndTime(sdf.parse(jsonObject.get("endTime").getAsString()));
            }
            conanCase.setStatus(jsonObject.get("status").getAsInt());
            conanCase.setTotalTime(jsonObject.get("duration").getAsFloat());
            if (null != jsonObject.get("exceptionName") && !jsonObject.get("exceptionName").isJsonNull()) {
                conanCase.setExceptionName(jsonObject.get("exceptionName").getAsString());
            }
            if (null != jsonObject.get("exceptionMessage") && !jsonObject.get("exceptionMessage").isJsonNull()) {
                conanCase.setExceptionMessage(jsonObject.get("exceptionMessage").getAsString());
            }
            if (null != jsonObject.get("failReason") && !jsonObject.get("failReason").isJsonNull()) {
                conanCase.setFailReason(jsonObject.get("failReason").getAsString());
            }
            if (null != jsonObject.get("failReasonDesc") && !jsonObject.get("failReasonDesc").isJsonNull()) {
                conanCase.setFailReasonDesc(jsonObject.get("failReasonDesc").getAsString());
            }
            if (null != jsonObject.get("runCount") && !jsonObject.get("runCount").isJsonNull()) {
                conanCase.setRunCount(jsonObject.get("runCount").getAsInt());
            }
            if (null != jsonObject.get("validCode") && !jsonObject.get("validCode").isJsonNull()) {
                conanCase.setValidCode(jsonObject.get("validCode").getAsString());
            }
            if (null != jsonObject.get("issueKey") && !jsonObject.get("issueKey").isJsonNull()) {
                conanCase.setIssueKey(jsonObject.get("issueKey").getAsString());
            }
            if (null != jsonObject.get("issueStatus") && !jsonObject.get("issueStatus").isJsonNull()) {
                conanCase.setIssueStatus(jsonObject.get("issueStatus").getAsString());
            }
            if (null != jsonObject.get("issueAssignee") && !jsonObject.get("issueAssignee").isJsonNull()) {
                conanCase.setIssueAssignee(jsonObject.get("issueAssignee").getAsString());
            }

        } catch (Exception e) {
//            log.error("插入conanCase遇到问题",e);
        } finally {
            conanCaseMapper.insert(conanCase);
        }
    }

    /**
     * 获取全量UI自动化通过率低于60%的case
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<GroupCase> getPassRateLowerCase(String startTime,String endTime,String platform) {
        List<GroupCase> list = conanCaseMapper.selectPassRateLowerCaseName(startTime,0.6,platform);
        return list;
    }
    @Override
    public List<Integer> getFailedCasesTaskIdList(String startTime,String platform,String caseName,String className) {
        return conanCaseMapper.selectFailedCasesJobIdList(startTime,platform,caseName,className);
    }

    public List<GroupCase> test(String startTime,String endTime,String platform) {
        return conanCaseMapper.selectPassRateLowerCaseName(startTime,0.6,platform);
    }

//    @Test
    public void myTest(){
        List<GroupCase> failedCase = test("2020-04-17","2020-04-19","iOS");
        System.out.print(failedCase);
    }

    @Override
    public ConanCase selectOneCase(String caseId) {
        return conanCaseMapper.selectById(caseId);
    }

}
