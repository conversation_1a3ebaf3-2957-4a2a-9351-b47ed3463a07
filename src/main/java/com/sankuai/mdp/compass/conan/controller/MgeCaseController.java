package com.sankuai.mdp.compass.conan.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.conan.entity.MgeCase;
import com.sankuai.mdp.compass.conan.service.MgeCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Created by xubangxi on 2020/05/18.
 */
@RestController
@RequestMapping("/compass/api/autotest/mgecase")

public class MgeCaseController extends BaseController {
    @Autowired
    MgeCaseService mgeCaseService;

    @PostMapping("/update")
    public Resp update(MgeCase mgeCase) {
        return mgeCaseService.update(mgeCase);
    }

    @PostMapping("/add")
    public Resp add(MgeCase mgeCase) {
        return mgeCaseService.add(mgeCaseService.updateContent(mgeCase));

    }

    @PostMapping("updatePrincipalCn")
    public Resp updatePrincipalCn(MgeCase mgeCase) {
        return mgeCaseService.updatePrincipalCn(mgeCase);
    }

    @PostMapping("updateModuleByPrincipal")
    public Resp updateModuleByPrincipal(MgeCase mgeCase) {
        return mgeCaseService.updateModuleByPrincipal(mgeCase);
    }

    @PostMapping("/delete")
    public void delete(MgeCase mgeCase) {
        mgeCaseService.delete(mgeCase);
    }

    @GetMapping("/select")
    public List<MgeCase> select() {
        return mgeCaseService.select();
    }

    @GetMapping("/list")
    public Map<String, Object> list(QueryRequest request, MgeCase mgeCase) {
        IPage<MgeCase> CompatilityJobIPage = this.mgeCaseService.list(request, mgeCase);
        if (CompatilityJobIPage != null) {
            return getDataTable(CompatilityJobIPage);
        } else {
            return null;
        }
    }

    @GetMapping("/getAllPrincipal")
    public List<String> getAllPrincipal() {
        return mgeCaseService.getAllPrincipal();
    }
}
