package com.sankuai.mdp.compass.track.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.track.entity.TrackOceanUser;
import net.minidev.json.JSONObject;

public interface TrackJobService  extends IService<TrackOceanUser> {

    Resp result(JSONObject conanBody);

    Resp queryIdleUser();

    Resp addUser(TrackOceanUser trackOceanUser);

    Resp releaseUser(TrackOceanUser trackOceanUser);

    Resp occupyIdleUser(TrackOceanUser trackOceanUser);

    Resp deleteUser(TrackOceanUser trackOceanUser);

    Resp accountResult(JSONObject conanBody);
}
