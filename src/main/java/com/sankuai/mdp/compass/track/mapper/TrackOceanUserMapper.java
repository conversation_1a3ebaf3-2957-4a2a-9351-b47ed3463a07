package com.sankuai.mdp.compass.track.mapper;

import com.sankuai.mdp.compass.track.entity.TrackOceanUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface TrackOceanUserMapper extends BaseMapper<TrackOceanUser> {
    @Update("UPDATE autotest_track_ocean_user SET in_use = #{inUse}, version = version + 1 WHERE id = #{id} AND version = #{version}")
    int updateByIdWithOptimisticLock(TrackOceanUser user);
}
