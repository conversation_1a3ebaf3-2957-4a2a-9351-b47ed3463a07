package com.sankuai.mdp.compass.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;

@Data
@TableName("autotest_track_ocean_user")
public class TrackOceanUser implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer oceanUserid;

    private String oceanUserName;

    private Boolean inUse;

    private Long version;
}
