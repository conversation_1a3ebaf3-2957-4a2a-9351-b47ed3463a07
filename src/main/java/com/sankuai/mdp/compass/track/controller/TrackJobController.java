package com.sankuai.mdp.compass.track.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.*;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.oreo.entity.OreoJob;
import com.sankuai.mdp.compass.oreo.entity.OreoWhitelist;
import com.sankuai.mdp.compass.oreo.service.OreoJobService;
import com.sankuai.mdp.compass.oreo.service.OreoWhitelistService;
import com.sankuai.mdp.compass.oreo.utils.Common;
import com.sankuai.mdp.compass.track.entity.TrackOceanUser;
import com.sankuai.mdp.compass.track.service.TrackJobService;
import net.minidev.json.JSONObject;
import org.mortbay.log.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@RestController
@RequestMapping("/compass/api/trackJob")
public class TrackJobController extends BaseController {
    @Autowired
    TrackJobService trackJobService;

    @PostMapping("/result")
    public Resp result(@RequestBody JSONObject conanBody) throws Exception {
        return trackJobService.result(conanBody);
    }

    @PostMapping("/accountResult")
    public Resp accountResult(@RequestBody JSONObject conanBody) throws Exception {
        return trackJobService.accountResult(conanBody);
    }
    @GetMapping("/queryUser")
    public Resp queryIdleUser() throws Exception {
        return trackJobService.queryIdleUser();
    }

    @PostMapping("/occupyUser")
    public Resp occupyIdleUser(@RequestBody TrackOceanUser trackOceanUser) throws Exception {
        return trackJobService.occupyIdleUser(trackOceanUser);
    }

    @PostMapping("/releaseUser")
    public Resp releaseUser(@RequestBody TrackOceanUser trackOceanUser) throws Exception {
        return trackJobService.releaseUser(trackOceanUser);
    }

    @PostMapping("/addUser")
    public Resp addUser(@RequestBody TrackOceanUser trackOceanUser) throws Exception {
        return trackJobService.addUser(trackOceanUser);
    }
    //根据用户名删除用户
    @PostMapping("/deleteUser")
    public Resp deleteUser(@RequestBody TrackOceanUser trackOceanUser) throws Exception {
        return trackJobService.deleteUser(trackOceanUser);
    }
}
