package com.sankuai.mdp.compass.track.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.ConanUtil;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.track.entity.TrackOceanUser;
import com.sankuai.mdp.compass.oreo.enums.JobStatusEnum;
import com.sankuai.mdp.compass.oreo.service.OreoJobService;
import com.sankuai.mdp.compass.track.mapper.TrackOceanUserMapper;
import com.sankuai.mdp.compass.track.service.TrackJobService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;

@Slf4j
@Service
public class TrackJobServiceImpl extends ServiceImpl<TrackOceanUserMapper, TrackOceanUser> implements TrackJobService  {
    @Autowired
    TrackOceanUserMapper trackOceanUserMapper;
    DxUtil dxUtil = new DxUtil();

    ConanUtil conanUtil = new ConanUtil();

    public Resp result(JSONObject conanBody) {
        Resp resp = new Resp();
        try {
            JsonParser jsonParser = new JsonParser();
            log.info("conanBody: " + conanBody);
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            String reportUrl = jobData.get("report").getAsString();
            log.info("report is: " + reportUrl);
            JsonArray userList = new JsonArray();
            userList.add("zhangyuchi02");
            userList.add("lizhen39");
            if (EnvUtil.isOnline()) {
                for (JsonElement u : userList) {
                    String user = u.getAsString();
                    dxUtil.sendToPersionByCompass("※ 埋点自动化测试完成  ※：\n" +
                            "【查看报告】：[查看报告|" + reportUrl
                            + "]\n"+ "如有问题请联系zhangyuchi02", user);
                }
                dxUtil.sendToGroupByCompass("※ 埋点自动化测试完成 ※：\n" +
                        "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68300993308L);
            }

        }catch (Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
        return Resp.success();
    }

    public Resp queryIdleUser() {
        QueryWrapper queryWrapper = new QueryWrapper();
        Resp resp = new Resp();
        try{
            log.info("start try");
            queryWrapper.eq("in_use", 0);
            queryWrapper.orderByAsc("id"); // 假设根据id升序排列可以得到第一条数据
            queryWrapper.last("limit 1");
            TrackOceanUser trackOceanUser = trackOceanUserMapper.selectOne(queryWrapper);
            log.info("trackOceanUser is " + trackOceanUser);
            if(trackOceanUser != null) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("oceanUserid", trackOceanUser.getOceanUserid());
                jsonObject.put("oceanUsername", trackOceanUser.getOceanUserName());
                return Resp.success(jsonObject);
            }else {
                resp.setMsg("No idel user found");
                resp.setCode(200);
            }
        }catch (Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
        }
        return resp;
    }

    @Override
    public Resp addUser(TrackOceanUser trackOceanUser) {
        Resp resp = new Resp();
        try{
            trackOceanUserMapper.insert(trackOceanUser);
            resp.setMsg("success");
            resp.setCode(200);
        }catch(Exception e) {
            log.info(e.toString());
            resp.setMsg("error");
            resp.setCode(-1);
        }
        return resp;
    }

    @Override
    public Resp releaseUser(TrackOceanUser trackOceanUser) {
        Resp resp = new Resp();
        try {
            // 创建查询条件，根据userid查询
            QueryWrapper<TrackOceanUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ocean_userid", trackOceanUser.getOceanUserid());
            // 查询结果
            TrackOceanUser trackOceanUserOne = trackOceanUserMapper.selectOne(queryWrapper);
            trackOceanUserOne.setInUse(false);
            // 执行更新
//            trackOceanUserMapper.updateById(trackOceanUserOne);
            int updatedRows = trackOceanUserMapper.updateByIdWithOptimisticLock(trackOceanUserOne);
            if (updatedRows == 0) {
                // 没有更新成功，可能是因为版本号不匹配，可以抛出异常或进行重试
                throw new OptimisticLockingFailureException("更新失败，可能是因为版本号冲突");
            }
            resp.setMsg("success");
        } catch (Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
        }
        return resp;
    }

    @Override
    public Resp occupyIdleUser(TrackOceanUser trackOceanUser) {
        Resp resp = new Resp();
        try {
            // 创建查询条件，根据userid查询
            QueryWrapper<TrackOceanUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ocean_userid", trackOceanUser.getOceanUserid());
            // 查询结果
            TrackOceanUser trackOceanUserOne = trackOceanUserMapper.selectOne(queryWrapper);
            trackOceanUserOne.setInUse(true);
            // 执行更新
            int updatedRows = trackOceanUserMapper.updateByIdWithOptimisticLock(trackOceanUserOne);
            if (updatedRows == 0) {
                // 没有更新成功，可能是因为版本号不匹配，可以抛出异常或进行重试
                throw new OptimisticLockingFailureException("更新失败，可能是因为版本号冲突");
            }
//            trackOceanUserMapper.updateById(trackOceanUserOne);
            resp.setMsg("success");
        } catch (Exception e) {
            resp.setMsg(e.toString());
            resp.setCode(-1);
        }
        return resp;
    }

    @Override
    public Resp deleteUser(TrackOceanUser trackOceanUser) {
        Resp resp = new Resp();
        try{
            trackOceanUserMapper.delete(new QueryWrapper<TrackOceanUser>().eq("ocean_user_name", trackOceanUser.getOceanUserName()));
            resp.setMsg("success");
            resp.setCode(200);
        }catch(Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
        }
        return resp;
    }

    @Override
    public Resp accountResult(JSONObject conanBody) {
        log.info("conanBody: " + conanBody);
        Resp resp = new Resp();
        log.info(String.valueOf(conanBody));
        ArrayList<String> deviceList = new ArrayList<>();
        deviceList.add("PPA-AL20");
        deviceList.add("JSC-AL50");
        try {
            JsonParser jsonParser = new JsonParser();
            // 解析云测回调参数
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            JsonArray devicesList = jsonParser.parse(conanBody.get("devicelist").toString()).getAsJsonArray();
            String deviceModel = devicesList.get(0).getAsJsonObject().get("deviceModel").getAsString();
            String conanId = jobData.get("id").getAsString();
            String reportUrl = jobData.get("report").getAsString();
            JsonArray userList = new JsonArray();
            userList.add("zhangyuchi02");
            if(conanUtil.getPassrate(conanId).get("pass").getAsFloat() == 1){
                resp.setCode(200);
            }
            else {
//                EnvUtil.isOnline()
                if (true) {
                    if(deviceList.contains(deviceModel)){
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("※ android账号自动化测试不通过  ※：\n" +
                                    "【查看报告】：[查看报告|" + reportUrl
                                    + "]\n"+ "如有问题请联系zhangyuchi02", user);
                        }
                        dxUtil.sendToGroupByCompass("※ android账号自动化测试不通过 ※：\n" +
                                "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68714749072L);
                    }
                    else {
                        for (JsonElement u : userList) {
                            String user = u.getAsString();
                            dxUtil.sendToPersionByCompass("※ ios账号自动化测试不通过  ※：\n" +
                                    "【查看报告】：[查看报告|" + reportUrl
                                    + "]\n"+ "如有问题请联系zhangyuchi02", user);
                        }
                        dxUtil.sendToGroupByCompass("※ ios账号自动化测试不通过 ※：\n" +
                                "【查看报告】：[查看报告|" + reportUrl + "]\n" + "如有问题请联系zhangyuchi02",68714749072L);
                    }
                }
            }
        }catch (Exception e) {
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
        return Resp.success();
    }
}
