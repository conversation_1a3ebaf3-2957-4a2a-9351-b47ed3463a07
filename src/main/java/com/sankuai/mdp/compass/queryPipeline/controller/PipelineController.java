package com.sankuai.mdp.compass.queryPipeline.controller;

import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.queryPipeline.entity.Pipeline;
import com.sankuai.mdp.compass.queryPipeline.service.PipelineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

/**
 * Created by liutianqi07 on 2023/07/19.
 */
@RestController
@RequestMapping("/compass/api/pipeline")
public class PipelineController {
    @Autowired
    PipelineService pipelineService;

    @PostMapping("fetchData/{dt}")
    public Resp fetchData(@PathVariable(value = "dt") String dt) {
        LocalDate date = LocalDate.now();
        if(null != dt){
            //若有指定日期，则获取改日期前一天的数据
            DateTimeFormatter formatterInput = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter formatterOutput = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date1 = LocalDate.parse(dt, formatterInput);
            String formattedDate = date1.format(formatterOutput);

            date = LocalDate.parse(formattedDate);

        }


        ArrayList<Pipeline> dataList= pipelineService.fetchPipeWeekData(date);

        boolean flag = pipelineService.insertPipeWeekData(dataList);
        if(flag){
            return Resp.success(dataList);
        }else{
            return Resp.error();
        }
    }
}
