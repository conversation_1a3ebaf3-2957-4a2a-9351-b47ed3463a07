package com.sankuai.mdp.compass.queryPipeline.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.mdp.compass.queryPipeline.entity.Params;
import com.sankuai.mdp.compass.queryPipeline.entity.Pipeline;
import com.sankuai.mdp.compass.queryPipeline.entity.PipelineInteRepoBu;
import com.sankuai.mdp.compass.queryPipeline.entity.PipelineOnesRepoBu;
import com.sankuai.mdp.compass.queryPipeline.mapper.PipelineInteRepoBuMapper;
import com.sankuai.mdp.compass.queryPipeline.mapper.PipelineMapper;
import com.sankuai.mdp.compass.queryPipeline.mapper.PipelineOnesRepoBuMapper;

import com.sankuai.mdp.compass.queryPipeline.service.PipelineService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;


import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

import static org.reflections.Reflections.log;


@Service
@Slf4j
public class PipelineServiceImpl extends ServiceImpl<PipelineMapper, Pipeline> implements PipelineService {
    @Autowired
    PipelineOnesRepoBuMapper pipelineOnesRepoBuMapper;

    @Autowired
    PipelineInteRepoBuMapper pipelineInteRepoBuMapper;

    /**
     *每天定时获取数据
     * @param date
     * @return
     */
    @SneakyThrows
    @Override
    public ArrayList<Pipeline> fetchPipeWeekData(LocalDate date) {
        //Integer appId = 1 或者 2，表示andriod 或 ios
        //pDescId = 438 表示ones流水线android  441是ios
        //这两个参数是绑定的

        int pipeline_num = 6;
        Params[] params = new Params[pipeline_num];

        params[0] = new Params(1, 438L);//android ones流水线    output paramsMAP branchWithIssue  repository
        params[1] = new Params(2, 441L);//ios ones流水线        output paramsMAP branchWithIssue  repository
        params[2] = new Params(1, 1038L);//android push流水线   output paramsMAP ssh
        params[3] = new Params(2, 1039L);//ios push流水线        output paramsMAP ssh
        params[4] = new Params(1, 1042L);//android 组件pr流水线      output paramsMAP ssh
        params[5] = new Params(2, 1043L);//ios 组件pr流水线


        //开始时间
        LocalDateTime start = date.minusDays(1).atTime(LocalTime.MIN);
        //结束时间
        LocalDateTime end = date.minusDays(0).atTime(LocalTime.MIN);

        String startStr = start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endStr = end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        //结果数组
        ArrayList<Pipeline> resultList = new ArrayList<>();

        //遍历ones pr push流水线
        log.info("开始扫描提测、push、pr流水线");
        for (Params param : params) {
            URIBuilder builder = new URIBuilder();
            builder.setHost("sigma.sankuai.com")
                    .setScheme("http")
                    .setPath("/api/v1/open/integration/pipeline/list")
                    .setParameter("dateFrom", startStr)
                    .setParameter("dateTo", endStr)
                    .setParameter("appId", String.valueOf(param.getAppId()))
                    .setParameter("pDescId", String.valueOf(param.getPDescId()))
                    .setParameter("pageSize" , String.valueOf(500));//一周大概数量就是几十条，这里暂时设置500条

            HttpGet httpGet = new HttpGet(builder.build());

            CloseableHttpClient httpClient = null;
            try {
                httpClient = HttpClients.createDefault();
            } catch (Exception e) {
                e.printStackTrace();
            }
            log.info("向Sigma发送请求获取数据");
            CloseableHttpResponse response = httpClient.execute(httpGet);//发送请求
            log.info("请求返回结果："+response.toString());
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity);
            JSONObject jsonObject = JSON.parseObject(responseBody);

            //responseBody格式
            /**
             * records[{
             *     id : 每一条记录，即运行id;
             *     taskName : Android流水线/ios流水线;
             *     customContent : {
             *         repository :
             *     }
             *     startTime : 记录的开始时间;
             *     endTime : 记录的结束时间;
             *     createBy : 创建人;
             *     statusText : 状态（执行失败/成功）
             *     output :
             *         paramsMap ：
             *             taskId ：Hpx任务id,唯一标识;
             *             testSubmitTitle : 记录的名称
             *         ...
             *         jobList : [{
             *             id : jobId;
             *             alias : job名称;（失败步骤）
             *             statusText : job状态（执行成功/失败）;
             *             jobOutput :
             *                 statusMark : 失败原因
             *         },{},{},...
             *         ]
             * },{},{},...]
             * total :
             */
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray resultArray = data.getJSONArray("records");
            if(resultArray != null && !resultArray.isEmpty()){
                for(int i = 0; i < resultArray.size(); i++){
                    JSONObject recordObject = resultArray.getJSONObject(i);

                    //--  ---获取每条记录的id,唯一标识
                    String runTaskId = recordObject.getString("id");
                    JSONObject output = recordObject.getJSONObject("output");
                    JSONObject paramsMap = output.getJSONObject("paramsMap");

                    //-------获取流水线名称
                    String pipeName = recordObject.getString("taskName");//流水线名称


                    //-------获取唯一标识（仅流水线和pr需要）
                    String HpxTaskId = "";//Hpx任务id,用来判断是否相等，若相等，取最新的结果，主要是除重用

                    if(pipeName.contains("Ones")){
                        HpxTaskId = paramsMap.getString("taskId");
                    }
                    if(pipeName.contains("组件PR")){
                        //以Pr地址为唯一标识
                        HpxTaskId = paramsMap.getString("prUrl");
                    }
                    if(pipeName.contains("Push")){
                        HpxTaskId = paramsMap.getString("head_commit").replaceAll("\"", "");
                    }


                    //-------获取OS
                    String appId = "Android";
                    if(pipeName.contains("iOS"))
                        appId = "iOS";

                    //------获取创建人
                    String createBy = recordObject.getString("createBy");//创建人

                    //--------每一条流水线的标题
                    String testSubmitTitle = "";
                    if(pipeName.contains("Ones")){
                        testSubmitTitle = paramsMap.getString("testSubmitTitle");
                    }else if (pipeName.contains("组件PR")){
                        testSubmitTitle = paramsMap.getString("title");
                    }else if(pipeName.contains("Push")){
                        testSubmitTitle = paramsMap.getString("head_commit").replaceAll("\"", "");//去掉转义符
                    }

                    String statusText = recordObject.getString("statusText");//状态（执行失败/成功,已终止）

                    //---------获取失败的job和原因
                    StringBuilder jobAlias = new StringBuilder();
                    StringBuilder jobStatusMark = new StringBuilder();//失败的原因，如果成功为空
//                    System.out.println(param.getPDescId());
//                    System.out.println(runTaskId);

                    if (!"执行成功".equals(statusText)){
                        //计算失败原因
                        JSONArray jobList = output.getJSONArray("jobList");
                        for (int j = 0; j < jobList.size(); j++) {
                            JSONObject job = jobList.getJSONObject(j);
                            String jobStatus = job.getString("statusText");//job状态
                            if(!"执行成功".equals(jobStatus)){
                                jobAlias.append(job.getString("alias"));//失败的步骤，如果成功为空
                                jobAlias.append("*");

                                if("未启动".equals(jobStatus)){
                                    jobStatusMark.append(jobStatus);
                                }

                                //失败的原因
                                //只有执行失败才写详细的原因
                                if("执行失败".equals(jobStatus) && job.containsKey("jobOutput")){
                                    JSONObject jobOutput = job.getJSONObject("jobOutput");
                                    String outputString = jobOutput.getString("output");
                                    ObjectMapper mapper = new ObjectMapper();

                                    // 解析JSON字符串
                                    JsonNode rootNode = mapper.readTree(outputString);

                                    // 提取包含失败的原因的数组
                                    if (rootNode.get("data").has("taskJsonArray")) {
                                        // 如果"data"字段中存在"branchArray"字段，则添加更加详细的错误信息
                                        String taskJsonArrayString = rootNode.get("data").get("taskJsonArray").asText();
                                        JSONArray taskJsonArray = JSON.parseArray(taskJsonArrayString);

                                        // 遍历数组taskJsonArray中的每个元素，并提取"status"字段
                                        for (int k = 0; k < taskJsonArray.size(); k++) {
                                            JSONObject taskJsonObject = taskJsonArray.getJSONObject(k);
                                            String status = taskJsonObject.getString("status");
                                            String failMsg = taskJsonObject.getString("msg");
                                            if("FAIL".equals(status)){
                                                //执行失败，向jobStatusMark中追加，用｜隔开
                                                if (jobStatusMark.length() > 0) {
                                                    jobStatusMark.append("|");
                                                }
                                                jobStatusMark.append(failMsg);
                                            }
                                        }
                                    }
                                }
                                jobStatusMark.append("*");
                            }
                        }
                    }
                    //存入数据库的失败的原因，如果成功为空
                    String jobStatusMarkString = jobStatusMark.toString();


                    //------获取流水线开始和结束时间
                    String startTime = recordObject.getString("startTime");
                    String endTime = recordObject.getString("endTime");


                    //流水线详情链接，通过拼接字符串实现
                    //https://sigma.sankuai.com/#/flows/pipeline/pipelineDetail?appId=2&taskId=43305
                    StringBuilder pipeDetail = new StringBuilder();
                    String Path = "https://sigma.sankuai.com/#/flows/pipeline/pipelineDetail?";
                    pipeDetail.append(Path)
                            .append("appId=").append(param.getAppId()).append("&")
                            .append("taskId=").append(runTaskId);


                    //------获取仓库名称和buname
                    String repository = "";
                    String buName = "";

                    if(pipeName.contains("Ones")){
                        //ones流水线
                        ArrayList<String> repList = new ArrayList<>();//ones有多个分支,用于存放repository数组
                        ArrayList<String> repBuNameList = new ArrayList<>();
                        ArrayList<String> branchList = new ArrayList<>();

                        JSONObject jobOne = output.getJSONArray("jobList").getJSONObject(0);
                        if(jobOne.containsKey("jobOutput")){
                            JSONObject jobOutput = jobOne.getJSONObject("jobOutput");
                            // 提取包含branch的数组
                            if(jobOutput.containsKey("output")){
                                ObjectMapper mapper = new ObjectMapper();
                                // 解析JSON字符串
                                JsonNode rootNode = mapper.readTree(jobOutput.getString("output"));
                                if (rootNode.get("data").has("branchArray")){
                                    //存在branchArray数组
                                    String branchArrayString = rootNode.get("data").get("branchArray").asText();
                                    JSONArray branchArray = JSON.parseArray(branchArrayString);

                                    //遍历branchArray,有的分支查buName查不到，所以要遍历查一遍
                                    //初始默认查不到buName
                                    buName = "该仓库名查不到buName";
                                    for (int branchNum = 0; branchNum < branchArray.size(); branchNum++) {
                                        JSONObject repJsonObject = branchArray.getJSONObject(branchNum);
                                        // 提取repository字段的值
                                        String rep = repJsonObject.getString("ssh");

                                        if(!this.queryBuName(rep).equals("没查到buName，可能是提测未绑定分支")){
                                            //如果查到了buName
                                            repository = rep;
                                            buName = this.queryBuName(repository);
                                            break;
                                        }
                                    }

                                    //获取多个repository和buname
                                    for (int branchNum = 0; branchNum < branchArray.size(); branchNum++) {
                                        JSONObject repJsonObject = branchArray.getJSONObject(branchNum);
                                        // 提取repository字段的值
                                        String rep = repJsonObject.getString("ssh");
                                        String repBuName = this.queryBuName(rep);
                                        repList.add(rep);
                                        repBuNameList.add(repBuName);
                                        branchList.add(repJsonObject.getString("branch"));
                                    }
                                }
                            }
                        }

                        //将多个分支落库
                        ArrayList<PipelineOnesRepoBu> pipelineOnesRepoBuArrayList = new ArrayList<>();
                        for(int j = 0; j < repList.size(); j++){
                            PipelineOnesRepoBu pipelineOnesRepoBu = new PipelineOnesRepoBu();
                            pipelineOnesRepoBu.setTaskId(Long.valueOf(runTaskId));
                            pipelineOnesRepoBu.setUniqueId(HpxTaskId);
                            pipelineOnesRepoBu.setPipeType(pipeName);
                            pipelineOnesRepoBu.setAppId(appId);
                            pipelineOnesRepoBu.setCreateBy(createBy);
                            pipelineOnesRepoBu.setPipelineTitle(testSubmitTitle);
                            pipelineOnesRepoBu.setStatus(statusText);
                            pipelineOnesRepoBu.setRepository(repList.get(j));
                            pipelineOnesRepoBu.setBuName(repBuNameList.get(j));
                            pipelineOnesRepoBu.setPipeDetail(pipeDetail.toString());
                            pipelineOnesRepoBu.setBranch(branchList.get(j));
                            pipelineOnesRepoBuArrayList.add(pipelineOnesRepoBu);

                            pipelineOnesRepoBuMapper.insert(pipelineOnesRepoBu);

                        }


                    }else{
                        //pr push流水线
                        repository = paramsMap.getString("ssh");//仓库名称
                        buName = this.queryBuName(repository);
                    }



                    Pipeline pipeline = new Pipeline();
                    pipeline.setTaskId(Long.valueOf(runTaskId));
                    pipeline.setHpxId(HpxTaskId);
                    pipeline.setPipeName(pipeName);
                    pipeline.setAppId(appId);
                    pipeline.setCreateBy(createBy);
                    pipeline.setTestSubmitTitle(testSubmitTitle);
                    pipeline.setStatus(statusText);
                    pipeline.setJobAlias(jobAlias.toString());
                    pipeline.setJobStatusMark(jobStatusMarkString);

                    pipeline.setStartTime(startTime);
                    pipeline.setEndTime(endTime);

                    pipeline.setRepository(repository);
                    pipeline.setBuName(buName);

                    pipeline.setPipeDetail(pipeDetail.toString());

                    resultList.add(pipeline);
                }

            }
            response.close();
        }

        //遍历集成流水线
        log.info("开始扫描集成流水线....");
        for(Integer integrationAppId = 1; integrationAppId <= 2; integrationAppId++){
            URIBuilder builder = new URIBuilder();
            builder.setHost("sigma.sankuai.com")
                    .setScheme("http")
                    .setPath("/api/v1/open/integration/listIntegrationOrderPipeline")
                    .setParameter("dateFrom", startStr)
                    .setParameter("dateTo", endStr)
                    .setParameter("appId", String.valueOf(integrationAppId))
                    .setParameter("pageSize" , String.valueOf(500))//暂时设置500条
                    .setParameter("needDetail", String.valueOf(true));

            HttpGet httpGet = new HttpGet(builder.build());

            CloseableHttpClient httpClient = null;
            try {
                httpClient = HttpClients.createDefault();
            } catch (Exception e) {
                e.printStackTrace();
            }
            log.info("请求Sigma获取数据");
            CloseableHttpResponse response = httpClient.execute(httpGet);//发送请求
            log.info("请求返回结果（集成）："+response.toString());
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity);
            JSONObject jsonObject = JSON.parseObject(responseBody);

            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray resultArray = data.getJSONArray("records");

            if(resultArray != null && !resultArray.isEmpty()){
                for(int i = 0; i < resultArray.size(); i++){
                    JSONObject recordObject = resultArray.getJSONObject(i);
                    JSONArray pipelineArray = recordObject.getJSONArray("pipelineTaskList");
                    JSONObject pipelineOne = pipelineArray.getJSONObject(0);
                    JSONArray pipelineJobArray = pipelineOne.getJSONArray("pipelineJobList");

                    //--  ---获取每条集成的id,唯一标识
                    String runTaskId = recordObject.getString("id");

                    //-------获取流水线类型
                    String pipeName = pipelineOne.getString("taskName");

                    //-------获取唯一标识（仅流水线和pr需要）
                    String HpxTaskId = pipelineOne.getString("id");//集成流水线的id

                    //-------获取OS
                    String appId = recordObject.getString("platform");

                    //------获取创建人
                    String createBy = recordObject.getString("createBy");//创建人

                    //--------每一条流水线的标题
                    String testSubmitTitle = recordObject.getString("department");

                    //--------执行状态
                    Integer statusCode = recordObject.getInteger("status");//状态码
                    String statusText = "";
                    if(statusCode == 0){
                        statusText = "待修改";
                    }else if(statusCode == 1){
                        statusText = "打包中";
                    }else if(statusCode == 3){
                        statusText = "审批中";
                    }else if(statusCode == 4){
                        statusText = "集成中";
                    }else if(statusCode == 5){
                        statusText = "执行成功";
                    }else if(statusCode == 6){
                        statusText = "执行失败";
                    }else if(statusCode == 7){
                        statusText = "集成取消";
                    }else{
                        statusText = "未知状态";
                    }

                    //---------获取失败的job和原因
                    StringBuilder jobAlias = new StringBuilder();//失败的步骤，可能有多个
                    StringBuilder jobStatusMark = new StringBuilder();//失败的原因，如果成功为空

                    //只要状态不是成功都统计原因
                    if (statusCode != 5){
                        //计算失败原因
                        for (int j = 0; j < pipelineJobArray.size(); j++) {
                            JSONObject job = pipelineJobArray.getJSONObject(j);
                            if(!job.containsKey("jobOutput")){
                                break;
                            }
                            String jobStatus = job.getJSONObject("jobOutput").getString("msg");//job状态
                            //这里有可能状态不是失败，但是总体流水线是失败了，所以都要统计上
                            if(!"执行成功".equals(jobStatus)){

                                jobAlias.append(job.getString("alias"));
                                jobAlias.append("*");//失败的步骤，如果成功为空


                                if("执行失败".equals(jobStatus) && job.containsKey("jobOutput")){
                                    JSONObject jobOutput = job.getJSONObject("jobOutput");
                                    jobStatusMark.append(jobOutput.getString("msg"));
                                }
                                jobStatusMark.append("*");
                            }
                        }
                    }
                    //存入数据库的失败的原因，如果成功为空
                    String jobStatusMarkString = jobStatusMark.toString();


                    //------获取流水线开始和结束时间
                    String startTime = recordObject.getString("createTime");
                    String endTime = recordObject.getString("updateTime");

                    //流水线详情链接
                    String pipelineDetail = recordObject.getString("sigmaUrl");

                    //------获取仓库名称和buname
                    String repository = "";
                    String buName = "";

                    //集成有多个组件，在这个表里先只存第一个
                    JSONArray integrationList = recordObject.getJSONArray("integrationList");

                    buName = "该组件名查不到buName";
                    for(int interNum = 0; interNum < integrationList.size(); interNum++) {
                        JSONObject integration = integrationList.getJSONObject(interNum);
                        String comp = integration.getString("name");

                        //根据组件名查buName,默认查不到
                        if(!this.queryBuNameThroughComponent(appId, comp).equals("该组件没查到buName")) {
                            //查到了buName
                            repository = this.queryRepoUrlThroughComponent(appId, comp);//存查到的那个仓库名称
                            buName = this.queryBuNameThroughComponent(appId, comp);
                            break;
                        }
                    }

                    //将多个组件和buName存到表里
                    ArrayList<String> componentList = new ArrayList<>();
                    ArrayList<String> repositoryList = new ArrayList<>();
                    ArrayList<String> buNameList = new ArrayList<>();
                    ArrayList<PipelineInteRepoBu> pipelineInteRepoBuArrayList = new ArrayList<>();
                    for(int interNum = 0; interNum < integrationList.size(); interNum++) {
                        JSONObject integration = integrationList.getJSONObject(interNum);
                        String comp = integration.getString("name");
                        String compRepo = this.queryRepoUrlThroughComponent(appId, comp);
                        String compBuName = this.queryBuNameThroughComponent(appId, comp);
                        componentList.add(comp);
                        repositoryList.add(compRepo);
                        buNameList.add(compBuName);

                        //另存一个表
                        PipelineInteRepoBu pipelineInteRepoBu = new PipelineInteRepoBu();
                        pipelineInteRepoBu.setTaskId(Long.valueOf(runTaskId));
                        pipelineInteRepoBu.setUniqueId(HpxTaskId);
                        pipelineInteRepoBu.setPipeType(pipeName);
                        pipelineInteRepoBu.setAppId(appId);
                        pipelineInteRepoBu.setCreateBy(createBy);
                        pipelineInteRepoBu.setPipelineTitle(testSubmitTitle);
                        pipelineInteRepoBu.setStatus(statusText);
                        pipelineInteRepoBu.setComponent(comp);
                        pipelineInteRepoBu.setRepository(compRepo);
                        pipelineInteRepoBu.setBuName(compBuName);
                        pipelineInteRepoBu.setPipeDetail(pipelineDetail);

                        pipelineInteRepoBuArrayList.add(pipelineInteRepoBu);

                        pipelineInteRepoBuMapper.insert(pipelineInteRepoBu);

                    }



                    Pipeline pipeline = new Pipeline();
                    pipeline.setTaskId(Long.valueOf(runTaskId));
                    pipeline.setHpxId(HpxTaskId);
                    pipeline.setPipeName(pipeName);
                    pipeline.setAppId(appId);
                    pipeline.setCreateBy(createBy);
                    pipeline.setTestSubmitTitle(testSubmitTitle);
                    pipeline.setStatus(statusText);
                    pipeline.setJobAlias(jobAlias.toString());
                    pipeline.setJobStatusMark(jobStatusMarkString);

                    pipeline.setStartTime(startTime);
                    pipeline.setEndTime(endTime);

                    pipeline.setRepository(repository);
                    pipeline.setBuName(buName);

                    pipeline.setPipeDetail(pipelineDetail);

                    resultList.add(pipeline);
                }

            }
            response.close();
        }

        return resultList;
    }

    @Override
    public boolean insertPipeWeekData(ArrayList<Pipeline> pipeWeekData) {
        if (pipeWeekData.isEmpty()) {
            return false;
        } else {
            //为了防止重复插入数据，新建一个列表保存不重复的数据
            ArrayList<Pipeline> noRepeatPipeWeekData = new ArrayList<>();

            //遍历pipeWeekData的每一条，检查是否重复
            for (Pipeline data : pipeWeekData) {

                // 查询数据库中是否已存在相同数据
                LambdaQueryWrapper<Pipeline> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Pipeline::getTaskId, data.getTaskId())
                        .eq(Pipeline::getHpxId, data.getHpxId())
                        .eq(Pipeline::getPipeName, data.getPipeName())
                        .eq(Pipeline::getAppId, data.getAppId())
                        .eq(Pipeline::getCreateBy, data.getCreateBy());

                Pipeline existingData = getOne(queryWrapper);
                // 如果不存在相同数据，则将当前数据添加到新数据列表中
                if (existingData == null) {
                    noRepeatPipeWeekData.add(data);
                }
            }
            if(noRepeatPipeWeekData.isEmpty()){
                System.out.println("该数据表中已存在！");
                //即使没有上报失败，也要返回false
                return false;
            }
            saveOrUpdateBatch(noRepeatPipeWeekData, noRepeatPipeWeekData.size());
            return true;

        }

    }

    @Override
    @SneakyThrows
    /**
     * 根据仓库名称，查询bu名称
     */
    public String queryBuName(String repository){
        String Authorization = "A77C449F1B0788AB610FCF87D31E279B";

        URIBuilder builder2 = new URIBuilder();
        builder2.setHost("hpx.sankuai.com")
                .setScheme("https")
                .setPath("/api/open/getProjectUrlInfo")
                .setParameter("repoUrl", repository);
        HttpGet httpGet2 = new HttpGet(builder2.build());
        httpGet2.setHeader("Authorization", Authorization);


        CloseableHttpClient httpClient2 = null;
        try {
            httpClient2 = HttpClients.createDefault();
        } catch (Exception e) {
            e.printStackTrace();
        }

        CloseableHttpResponse response2 = httpClient2.execute(httpGet2);//发送请求

        HttpEntity entity2 = response2.getEntity();
        String responseBody2 = EntityUtils.toString(entity2);
        JSONObject jsonObject2 = JSON.parseObject(responseBody2);
        if(jsonObject2.getInteger("status")==0){
            return "没查到buName，可能是提测未绑定分支";
        }

        JSONObject projectObject = jsonObject2.getJSONArray("data").getJSONObject(0).getJSONObject("project");
        String buName = projectObject.getJSONObject("buInfo").getString("buName");

        response2.close();
        return buName;
    }



    @Override
    @SneakyThrows
    /**
     * 根据组件名称，查询bu名称
     */
    public String queryBuNameThroughComponent(String os, String component){

        URIBuilder builder2 = new URIBuilder();
        builder2.setHost("hpx.sankuai.com")
                .setScheme("https")
                .setPath("/api/open/getProjectList")
                .setParameter("os", os)
                .addParameter("componentName", component);
        HttpGet httpGet2 = new HttpGet(builder2.build());

        CloseableHttpClient httpClient2 = null;
        try {
            httpClient2 = HttpClients.createDefault();
        } catch (Exception e) {
            e.printStackTrace();
        }

        CloseableHttpResponse response2 = httpClient2.execute(httpGet2);//发送请求

        HttpEntity entity2 = response2.getEntity();
        String responseBody2 = EntityUtils.toString(entity2);
        JSONObject jsonObject2 = JSON.parseObject(responseBody2);

        if(jsonObject2.getInteger("status")==0){
            return "该组件没查到buName";
        }

        JSONObject projectObject = jsonObject2.getJSONArray("data").getJSONObject(0);
        String buName = projectObject.getJSONObject("buInfo").getString("buName");

        response2.close();
        return buName;
    }

    @Override
    @SneakyThrows
    /**
     * 根据组件名称，查询bu名称
     */
    public String queryRepoUrlThroughComponent(String os, String component){

        URIBuilder builder2 = new URIBuilder();
        builder2.setHost("hpx.sankuai.com")
                .setScheme("https")
                .setPath("/api/open/getProjectList")
                .setParameter("os", os)
                .addParameter("componentName", component);
        HttpGet httpGet2 = new HttpGet(builder2.build());

        CloseableHttpClient httpClient2 = null;
        try {
            httpClient2 = HttpClients.createDefault();
        } catch (Exception e) {
            e.printStackTrace();
        }

        CloseableHttpResponse response2 = httpClient2.execute(httpGet2);//发送请求

        HttpEntity entity2 = response2.getEntity();
        String responseBody2 = EntityUtils.toString(entity2);
        JSONObject jsonObject2 = JSON.parseObject(responseBody2);

        if(jsonObject2.getInteger("status")==0){
            return "该组件没查到repository";
        }

        JSONObject projectObject = jsonObject2.getJSONArray("data").getJSONObject(0);
        String repository = projectObject.getString("repoUrl");

        response2.close();
        return repository;
    }

}



