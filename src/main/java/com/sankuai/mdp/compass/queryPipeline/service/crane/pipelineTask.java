package com.sankuai.mdp.compass.queryPipeline.service.crane;

import com.cip.crane.client.spring.annotation.Crane;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.queryPipeline.entity.Pipeline;
import com.sankuai.mdp.compass.queryPipeline.service.PipelineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

@Service
public class pipelineTask {
    @Resource
    PipelineService pipelineService;

    @Crane("query_pipeline")
    public void fetchData(){
        LocalDate date = LocalDate.now();
        ArrayList<Pipeline> dataList= pipelineService.fetchPipeWeekData(date);
        if(!dataList.isEmpty()){
            pipelineService.insertPipeWeekData(dataList);
        }
    }
}

