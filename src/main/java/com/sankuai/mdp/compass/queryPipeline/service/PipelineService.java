package com.sankuai.mdp.compass.queryPipeline.service;

import com.sankuai.mdp.compass.queryPipeline.entity.Pipeline;

import java.time.LocalDate;
import java.util.ArrayList;

public interface PipelineService {
    public ArrayList<Pipeline> fetchPipeWeekData(LocalDate date);
    public boolean insertPipeWeekData(ArrayList<Pipeline> pipeWeekData);
    public String queryBuName(String repository);
    public String queryBuNameThroughComponent(String os, String Component);
    public String queryRepoUrlThroughComponent(String os, String Component);
}
