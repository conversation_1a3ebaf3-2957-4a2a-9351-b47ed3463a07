package com.sankuai.mdp.compass.queryPipeline.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by liutianqi07 on 2023/7/18.
 */
@Data
@TableName("pipeline")
public class Pipeline {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "task_id")
    private Long taskId;//任务id,唯一标识
    @TableField(value = "hpx_id")
    private String hpxId;//hpxid，若相同，表示重复运行
    @TableField(value = "pipe_name")
    private String pipeName;//流水线名称
    @TableField(value = "app_id")
    private String appId;//android or os
    @TableField(value = "create_by")
    private String createBy;//创建人
    @TableField(value = "test_submit_title")
    private String testSubmitTitle;//提测标题
    @TableField(value = "status")
    private String Status;//状态，如"执行成功"
    @TableField(value = "job_alias")
    private String jobAlias;//如果失败，失败的步骤
    @TableField(value = "job_status_mark")
    private String jobStatusMark;//如果失败，失败的原因


    @TableField(value = "start_time")
    private String startTime;
    @TableField(value = "end_time")
    private String endTime;

    @TableField(fill = FieldFill.INSERT)
    private String create_time;

    @TableField(fill = FieldFill.INSERT)
    private String update_time;

    @TableField(value = "repository")
    private String repository;//仓库名
    @TableField(value = "bu_name")
    private String buName;//bu的名称
    @TableField(value = "pipe_detail")
    private String pipeDetail;//流水线链接


}

