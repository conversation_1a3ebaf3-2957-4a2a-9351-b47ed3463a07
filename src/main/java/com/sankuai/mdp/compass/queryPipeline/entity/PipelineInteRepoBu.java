package com.sankuai.mdp.compass.queryPipeline.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@Data
@TableName("pipeline_inte_repo_bu")
public class PipelineInteRepoBu {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "task_id")
    private Long taskId;//任务id,唯一标识
    @TableField(value = "unique_id")
    private String uniqueId;//唯一标识，若相同，表示重复运行
    @TableField(value = "pipe_type")
    private String pipeType;//流水线类型
    @TableField(value = "app_id")
    private String appId;//android or os
    @TableField(value = "create_by")
    private String createBy;//创建人
    @TableField(value = "pipeline_title")
    private String pipelineTitle;//提测标题
    @TableField(value = "status")
    private String Status;//状态，如"执行成功"
    @TableField(value = "component")
    private String component;//组件名
    @TableField(value = "repository")
    private String repository;//仓库名
    @TableField(value = "bu_name")
    private String buName;//bu的名称

    @TableField(fill = FieldFill.INSERT)
    private String create_time;

    @TableField(fill = FieldFill.INSERT)
    private String update_time;

    @TableField(value = "pipe_detail")
    private String pipeDetail;//流水线链接
}
