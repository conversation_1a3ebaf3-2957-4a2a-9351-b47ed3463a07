package com.sankuai.mdp.compass.queryPipeline.entity;

import com.baomidou.mybatisplus.annotation.*;
import jnr.ffi.annotations.In;
import lombok.Data;

/**
 * Created by liutianqi07 on 2023/7/19.
 */
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
@Data
@Setter
@Getter
public class Params {
    Integer appId;
    Long pDescId;

    public Params(Integer appId, Long pDescId) {
        this.appId = appId;
        this.pDescId = pDescId;
    }

    public Params(Integer appId) {
        this.appId = appId;
    }
}

