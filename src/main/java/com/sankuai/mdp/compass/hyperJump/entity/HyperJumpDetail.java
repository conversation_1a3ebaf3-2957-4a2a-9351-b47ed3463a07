package com.sankuai.mdp.compass.hyperJump.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("hyper_jump_detail")
public class HyperJumpDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer buildId;

    private String platform;

    private String picName;

    private String caseName;

    private String basePic;

    private String baseResolution;

    private String testPic;

    private String exceptionType;

    private String exceptionImage;

    private String testResolution;

    private Double similarity;

    private String diffUrl;

    private String conanTask;

    /**
     * 0：基准
     * 1：测试
     */
    @TableField(exist = false)
    private Integer type;

    @TableField(exist = false)
    private Object testPicObject;

    @TableField(exist = false)
    private Object basePicObject;
}
