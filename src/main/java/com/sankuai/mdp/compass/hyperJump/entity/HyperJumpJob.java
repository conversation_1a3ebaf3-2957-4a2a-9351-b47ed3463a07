package com.sankuai.mdp.compass.hyperJump.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("hyper_jump_job")
public class HyperJumpJob implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer jenkinsId;

    private String platform;

    private String bg;

    private String caseType;

    private String bu;

    private String biz;

    private String pageType;

    private String pageDescription;

    private String creator;

    private String testReportId;

    private String baseApkUrl;

    private String testApkUrl;

    private Integer baseStatus;

    private Integer testStatus;

    private String baseReportId;

    private String pageId;

    private String zipDownloadUrl;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String baseFinishId;

    private String testFinishId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date baseStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date testStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date baseFinishTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date testFinishTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;



}
