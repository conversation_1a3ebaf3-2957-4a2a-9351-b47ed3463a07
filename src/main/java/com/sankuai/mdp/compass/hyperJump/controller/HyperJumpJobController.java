package com.sankuai.mdp.compass.hyperJump.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.*;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.DxUtil;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpJob;
import com.sankuai.mdp.compass.hyperJump.service.HyperJumpJobService;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.jump.entity.JumpJob;
import lombok.SneakyThrows;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/compass/api/hyperJump")
public class HyperJumpJobController extends BaseController {

    DxUtil dxUtil = new DxUtil();

    @Autowired
    HyperJumpJobService hyperJumpJobService;

    @GetMapping("/list")
    public Map<String, Object> hyperJumpList(QueryRequest request, HyperJumpJob hyperJumpJob) {
        IPage<HyperJumpJob> HyperJumpJobIPage = this.hyperJumpJobService.list2(request, hyperJumpJob);
        if (HyperJumpJobIPage!=null){
            return getDataTable(HyperJumpJobIPage);
        }
        else {
            return null;
        }
    }

    @PostMapping("/new")
    public Resp newBuild(@RequestBody String body) throws Exception {
        Gson gson = new Gson();
        JsonObject responseBody = new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload = responseBody.get("payload").getAsJsonObject();

        // 遍历 payload，将其值为数组的字段用英文“,”拼接
        for (Map.Entry<String, JsonElement> entry : payload.entrySet()) {
            if (entry.getValue().isJsonArray()) {
                JsonArray jsonArray = entry.getValue().getAsJsonArray();
            // 如果数组为空，则跳过处理
            if (jsonArray.size() == 0) {
                continue;
            }
            List<String> elements = new ArrayList<>();
            for (JsonElement element : jsonArray) {
                elements.add(element.getAsString());
            }
            String joinedString = String.join(",", elements);
            payload.addProperty(entry.getKey(), joinedString);
        }
    }

    // 直接将 payload 转化为 HyperJumpJob 类
    HyperJumpJob hyperJumpJob = gson.fromJson(payload, HyperJumpJob.class);
    return hyperJumpJobService.newBuild(hyperJumpJob);
}


    @PostMapping("/add")
    public Integer addJob(HyperJumpJob hyperJumpJob) throws Exception {
        return hyperJumpJobService.add(hyperJumpJob);
    }

    @PostMapping("/update")
    public Resp updateJob(HyperJumpJob hyperJumpJob) {
        return hyperJumpJobService.updateJob(hyperJumpJob);
    }

    @PutMapping("/updateTestConanId")
    public Resp updateTestConanId(HyperJumpJob hyperJumpJob) throws Exception {
        return hyperJumpJobService.updateTestConanId(hyperJumpJob);
    }

    @PutMapping("/updateBaseConanId")
    public Resp updateBaseConanId(HyperJumpJob hyperJumpJob) throws Exception {
        return hyperJumpJobService.updateBaseConanId(hyperJumpJob);
    }

    @PostMapping("/result")
    public Resp result(@RequestBody JSONObject conanBody) throws Exception {
        return hyperJumpJobService.result(conanBody);
    }

    @GetMapping("/getAllCreator")
    public List<String> getAllCreator() {
        return hyperJumpJobService.getAllCreator();
    }

    @SneakyThrows
    @PostMapping("retryJob")
    public Resp retryJob(@RequestBody String body) {
        Resp result = hyperJumpJobService.retryJob(body);
        JsonObject responseBody =  new JsonParser().parse(body).getAsJsonObject();
        JsonObject payload =  responseBody.get("payload").getAsJsonObject();
        String creator = payload.get("creator").getAsString();
        String code = result.getCode();
        dxUtil.sendToPersionByCompass("遍历截屏触发结果："+result.getMsg(), creator);
        return  result;
    }



}
