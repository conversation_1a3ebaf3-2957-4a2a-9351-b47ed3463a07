package com.sankuai.mdp.compass.hyperJump.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dianping.zebra.util.StringUtils;
import com.google.gson.*;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.common.domain.ComConstant;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.common.utils.*;
import com.sankuai.mdp.compass.conan.service.ConanJobService;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpDetail;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpJob;
import com.sankuai.mdp.compass.hyperJump.mapper.HyperJumpDetailMapper;
import com.sankuai.mdp.compass.hyperJump.mapper.HyperJumpJobMapper;
import com.sankuai.mdp.compass.hyperJump.service.HyperJumpDetailService;
import com.sankuai.mdp.compass.hyperJump.service.HyperJumpJobService;
import com.sankuai.mdp.compass.service.CommonPageService;
import com.sankuai.mdp.compass.service.OcrService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import static org.yaml.snakeyaml.util.UriEncoder.encode;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import org.apache.commons.io.FileUtils;

@Slf4j
@Service
public class HyperJumpJobServiceImpl extends ServiceImpl<HyperJumpJobMapper, HyperJumpJob>
        implements HyperJumpJobService {
    @Autowired
    HyperJumpJobMapper hyperJumpJobMapper;

    @Autowired
    HyperJumpDetailMapper hyperJumpDetailMapper;

    @Autowired
    HyperJumpDetailService hyperDetailService;

    @Autowired
    OcrService ocrService;

    @Autowired
    CommonPageService commonPageService;

    @Autowired
    ConanJobService conanJobService;

    ComConstant comConstant = new ComConstant();

    ConanUtil conanUtil = new ConanUtil();

    JenkinsUtil jenkinsUtil = new JenkinsUtil();

    private String fileDir = "hyperJump";

    FileUtil fileUtil = new FileUtil();

    VenusUtil venusUtil = new VenusUtil();

    DxUtil dxUtil = new DxUtil();

    @Override
    public Resp newBuild(HyperJumpJob hyperJumpJob) throws Exception {
        Resp resp = new Resp();
        String creator = hyperJumpJob.getCreator();
        // EnvUtil.isOnline()
        if (EnvUtil.isOnline()) {
            jenkinsUtil.newHyperJumpJob(hyperJumpJob);
            JsonArray users = new JsonArray();
            users.add(creator);
            users.add("zhoujing40");
            users.add("fengenci");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                for (JsonElement user : users) {
                    String u = user.getAsString();
                    dxUtil.sendToPersionByCompass("※ 遍历截屏 ※：\n" + "【生成进度】： 开始生成\n" + "【开始时间】：" + sdf.format(new Date())
                            + "\n" + "如有问题请联系fengenci", u);
                }
            } catch (Exception e) {
                log.error("发送消息失败：" + e.getMessage());
            }
            resp.setMsg("success");
            resp.setCode(1);
        } else {
            resp.setCode(0);
            resp.setMsg("非线上环境");
        }
        resp.setData(hyperJumpJob);
        return resp;
    }

    @Override
    public Integer add(HyperJumpJob hyperJumpJob) {
        Resp resp = new Resp();
        Date date = new Date();
        hyperJumpJob.setCreateTime(date);
        hyperJumpJobMapper.insert(hyperJumpJob);
        return hyperJumpJob.getId();
    }

    @Override
    public Resp updateJob(HyperJumpJob hyperJumpJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", hyperJumpJob.getId());
        HyperJumpJob hyperJumpJob1 = hyperJumpJobMapper.selectOne(queryWrapper);
        String testReportId = hyperJumpJob1.getTestReportId();
        String baseReportId = hyperJumpJob1.getBaseReportId();
        String addtestReportId = hyperJumpJob.getTestReportId();
        String addbaseReportId = hyperJumpJob.getBaseReportId();
        if (addbaseReportId != null) {
            if (baseReportId == null) {
                baseReportId = "";
            }
            if (baseReportId.equals("")) {
                baseReportId = addbaseReportId;
            } else if (!baseReportId.contains(addbaseReportId)) {
                baseReportId = baseReportId + "_" + addbaseReportId;
            }
            hyperJumpJob.setBaseReportId(baseReportId);
        } else if (addtestReportId != null) {
            if (testReportId == null) {
                testReportId = "";
            }
            if (testReportId.equals("")) {
                testReportId = addtestReportId;
            } else if (!testReportId.contains(addtestReportId)) {
                testReportId = testReportId + "_" + addtestReportId;
            }
            hyperJumpJob.setTestReportId(testReportId);
        }
        hyperJumpJobMapper.updateById(hyperJumpJob);

        return null;
    }

    @Override
    public Resp updateTestConanId(HyperJumpJob hyperJumpJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", hyperJumpJob.getId());
        HyperJumpJob hyperJumpJob1 = hyperJumpJobMapper.selectOne(queryWrapper);
        if (null != hyperJumpJob1) {
            hyperJumpJob1.setTestReportId(hyperJumpJob.getTestReportId());
            hyperJumpJobMapper.updateById(hyperJumpJob1);
        }

        return null;
    }

    @Override
    public Resp updateBaseConanId(HyperJumpJob hyperJumpJob) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", hyperJumpJob.getId());
        HyperJumpJob hyperJumpJob1 = hyperJumpJobMapper.selectOne(queryWrapper);
        if (null != hyperJumpJob1) {
            hyperJumpJob1.setTestReportId(hyperJumpJob.getBaseReportId());
            hyperJumpJobMapper.updateById(hyperJumpJob1);
        }

        return null;
    }

    @Override
    public IPage<HyperJumpJob> list2(QueryRequest request, HyperJumpJob hyperJumpJob) {
        try {
            LambdaQueryWrapper<HyperJumpJob> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.last("order by create_time DESC");
            Page<HyperJumpJob> page = new Page<>(request.getPageNum(), request.getPageSize());
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取列表失败", e);
            return null;
        }
    }

    /**
     * 更新基准组进度
     *
     * @param hyperJumpJob
     * @param conanId
     * @return
     */
    private Boolean handleBaseProcess(HyperJumpJob hyperJumpJob, String conanId) {
        Boolean result = false;
        String baseFinishedId = hyperJumpJob.getBaseFinishId();
        String baseConanId = hyperJumpJob.getBaseReportId();
        // 判断测试组是否完成
        if (baseFinishedId == null) {
            baseFinishedId = "";
        }
        if (baseFinishedId.isEmpty()) {
            baseFinishedId = conanId;
        } else if (!baseFinishedId.contains(conanId)) {
            baseFinishedId = baseFinishedId + "_" + conanId;
        }
        if (baseFinishedId.length() == baseConanId.length()) {
            hyperJumpJob.setBaseStatus(1);
            hyperJumpJob.setBaseFinishTime(new Date());

            if (hyperJumpJob.getTestStatus() == 1) {
                result = true;
            }
        }

        hyperJumpJob.setBaseFinishId(baseFinishedId);
        hyperJumpJobMapper.updateById(hyperJumpJob);
        return result;

    }

    /**
     * 更新测试组进度
     *
     * @param hyperJumpJob
     * @param conanId
     * @return
     */
    private Boolean handleTestProcess(HyperJumpJob hyperJumpJob, String conanId) {
        boolean result = false;
        String testFinishedId = hyperJumpJob.getTestFinishId();
        String testConanId = hyperJumpJob.getTestReportId();
        // 判断测试组是否完成
        if (testFinishedId == null) {
            testFinishedId = "";
        }
        if (testFinishedId.isEmpty()) {
            testFinishedId = conanId;
        } else if (!testFinishedId.contains(conanId)) {
            testFinishedId = testFinishedId + "_" + conanId;
        }
        if (testFinishedId.length() == testConanId.length()) {
            hyperJumpJob.setTestStatus(1);
            hyperJumpJob.setTestFinishTime(new Date());

            // 所有测试完成，打包并上传临时文件夹
            try {
                // 获取复制过来的文件夹路径
                String sourceDirName = "hyperJumpPic";
                String hyperJumpPicPath = Paths.get(System.getProperty("java.io.tmpdir"), sourceDirName).toString();
                File dir = new File(hyperJumpPicPath);
                if (dir.exists()) {
                    try {
                        // 打包并上传复制过来的文件夹
                        String zipFilePath = System.getProperty("java.io.tmpdir") + "/picDownload.zip";
                        FileUtil.zip(hyperJumpPicPath, zipFilePath);
                        log.info("保存图片链接");
                        String zipUrl = MSSUtil.uploadZip(zipFilePath);
                        hyperJumpJob.setZipDownloadUrl(zipUrl);
                    } catch (IOException e) {
                        log.error("打包或上传文件时出错", e);
                    }
                    try {
                        // 上传完成后删除文件夹
                        FileUtils.deleteDirectory(new File(hyperJumpPicPath));
                    } catch (IOException e) {
                        log.error("删除文件夹时出错", e);
                    }
                } else {
                    log.error("文件夹不存在: " + hyperJumpPicPath);
                }
            } catch (Exception e) {
                log.error("处理测试进度时出错", e);
            }

            if (hyperJumpJob.getBaseStatus() == 1) {
                result = true;
            }
        }
        hyperJumpJob.setTestFinishId(testFinishedId);
        hyperJumpJobMapper.updateById(hyperJumpJob);
        return result;
    }

    @Override
    public Resp result(JSONObject conanBody) {
        conanJobService.collectResult(conanBody);
        Resp resp = new Resp();
        try {
            log.info(conanBody.toString());
            JsonParser jsonParser = new JsonParser();

            // 解析云测回调参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JsonObject timeData = jsonParser.parse(conanBody.get("timedata").toString()).getAsJsonObject();
            JsonObject jobData = jsonParser.parse(conanBody.get("jobdata").toString()).getAsJsonObject();
            String conanId = jobData.get("id").getAsString();
            log.info("云测id：" + conanId);

            // 根据云测id找到对应的测试job
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper
                    .last("where test_report_id like '%" + conanId + "%' or base_report_id like '%" + conanId + "%'");
            HyperJumpJob hyperJumpJob = hyperJumpJobMapper.selectOne(queryWrapper);
            if (hyperJumpJob == null) {
                log.info("没有找到关联job");
                return null;
            } else {
                log.info("对应job：" + hyperJumpJob);
            }

            Integer id = hyperJumpJob.getId();
            String baseReportId = hyperJumpJob.getBaseReportId();
            String testReportId = hyperJumpJob.getTestReportId();

            // 文件下载完成后存放的目录
            String dirPath = ComConstant.OUT_PUT + fileDir + id;

            try {
                JsonObject conanJobInfo = conanUtil.getJobInfo(conanId);
                if (conanJobInfo != null) {
                    JsonArray taskArray = conanJobInfo.getAsJsonArray("taskList");
                    String appVersion = conanJobInfo.get("appVersion").getAsString();
                    String platform = conanJobInfo.get("platform").getAsString();

                    for (int taskIndex = 0; taskIndex < taskArray.size(); taskIndex++) {
                        JsonObject task = taskArray.get(taskIndex).getAsJsonObject();
                        String taskId = task.get("id").getAsString();
                        QueryWrapper<HyperJumpDetail> detailQueryWrapper = new QueryWrapper<HyperJumpDetail>()
                                .eq("conan_task", taskId);
                        int detailUpdateFlag = hyperJumpDetailMapper.selectCount(detailQueryWrapper);
                        if (detailUpdateFlag > 0) {
                            log.info("id为 " + taskId + " 的任务已校验下载入库完成过了，跳过");
                            continue;
                        }
                        /**
                         * 1.获取文件下载链接
                         */
                        String deviceModel = task.get("deviceModel").getAsString();
                        String deviceVersion = task.get("deviceVersion").getAsString();
                        String deviceResolution = task.get("resolution").getAsString();
                        Double totalTime = task.get("totalTime").getAsDouble();
                        String downloadURL = conanUtil.getDownloadURL(taskId);

                        if (null != downloadURL) {
                            downloadURL = downloadURL.replace("s3-img", "s3");
                            /**
                             * 2.下载解压文件
                             */
                            FileUtil.downloadAndReadFile(downloadURL, dirPath, taskId);

                            /**
                             * 4.获取图片文件路径
                             * 将本地图片上传到服务器
                             * 将图片链接存入数据库
                             */
                            ArrayList<String> fileList = new ArrayList<>();
                            String picPath = dirPath + "/" + taskId + "/autoScreenshot/";
                            String hyperJumpPicPath = dirPath + "/" + taskId + "/hyperJumpPic";// test-output截图存储
                            FileUtil.getFileList(picPath, "", fileList);
                            String zipFilePath = null;
                            for (String fileName : fileList) {
                                if (!fileName.contains(".txt")) {
                                    continue;
                                }
                                String caseName = fileName.split(".txt")[0];
                                List<List<Object>> casePics = FileUtil.readFileByLines(picPath + fileName, "\\|");

                                for (List<Object> casePic : casePics) {
                                    try {
                                        if (casePic.size() < 2) {
                                            log.error("数据格式错误，跳过此行数据: " + casePic);
                                            continue;
                                        }
                                        String picName = casePic.get(0).toString();
                                        String picUrl = casePic.get(1).toString();
                                        HyperJumpDetail hyperJumpDetail = new HyperJumpDetail();
                                        hyperJumpDetail.setPlatform(platform);
                                        hyperJumpDetail.setConanTask(taskId);
                                        hyperJumpDetail.setBuildId(id);
                                        hyperJumpDetail.setCaseName(caseName);
                                        hyperJumpDetail.setPicName(picName);
                                        if (baseReportId.contains(conanId)) {
                                            hyperJumpDetail.setBasePic(picUrl);
                                            hyperJumpDetail.setBaseResolution(deviceResolution);
                                            hyperJumpDetail.setType(0);
                                        } else if (testReportId.contains(conanId)) {
                                            String regInfo = ocrService.recognizeUIBugByAI(picUrl);

                                            // 设置测试图片、分辨率和类型
                                            hyperJumpDetail.setTestPic(picUrl);
                                            hyperJumpDetail.setTestResolution(deviceResolution);
                                            hyperJumpDetail.setType(1);

                                            try {
                                                // 使用 Gson 解析 regInfo JSON 字符串
                                                Gson gson = new Gson();
                                                JsonObject regInfoJson = gson.fromJson(regInfo, JsonObject.class);
                                                String resultImageUrl = regInfoJson.get("resultImageUrl").getAsString();
                                                JsonArray exceptions = regInfoJson.getAsJsonArray("exceptions");
                                                Set<String> exceptionTypesSet = new HashSet<>(); // 使用 HashSet
                                                                                                 // 来存储异常类型，自动去重

                                                for (JsonElement element : exceptions) {
                                                    JsonObject exception = element.getAsJsonObject();
                                                    String exceptionType = exception.get("exceptionType").getAsString();
                                                    exceptionTypesSet.add(exceptionType); // 添加到 Set 中，重复的异常类型不会被添加
                                                }

// 将所有异常类型以逗号分隔连接
                                                String exceptionTypes = String.join(", ", exceptionTypesSet);

                                                // 设置异常类型（所有异常类型以逗号分隔）和异常图片URL
                                                hyperJumpDetail.setExceptionType(exceptionTypes);
                                                hyperJumpDetail.setExceptionImage(resultImageUrl);
                                            } catch (Exception e) {
                                                // 异常处理逻辑，确保即使异常图片解析失败，也不影响其他数据的插入
                                                log.error("解析异常图片信息失败", e);
                                            }

                                            storeHyperJumpPic(hyperJumpPicPath);
                                        }

                                        hyperDetailService.insert(hyperJumpDetail);
                                    } catch (Exception e) {
                                        log.error("处理casePic时出错: " + casePic, e);
                                    }
                                }

                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("get output err", e);
            } finally {
                synchronized (this) {
                    QueryWrapper queryWrapper2 = new QueryWrapper();
                    queryWrapper2.last(
                            "where base_report_id like '%" + conanId + "%' or test_report_id like '%" + conanId + "%'");

                    HyperJumpJob hyperJumpJob1 = hyperJumpJobMapper.selectOne(queryWrapper2);
                    String baseConanId = hyperJumpJob1.getBaseReportId();
                    String testConanId = hyperJumpJob1.getTestReportId();
                    Boolean finishFlag = false;
                    if (baseConanId != null && baseConanId.contains(conanId)) {
                        finishFlag = handleBaseProcess(hyperJumpJob1, conanId);
                    } else if (testConanId != null && testConanId.contains(conanId)) {
                        finishFlag = handleTestProcess(hyperJumpJob1, conanId);
                    }

                    String reportUrl = "http://w.sankuai.com/ptqa/compass_new_web/index.html#/hyperJump/hyperReport?id="
                            + id;
                    JsonArray userList = new JsonArray();
                    userList.add("zhoujing40");
                    userList.add("fengenci");

                    if (finishFlag) {
                        FileUtil.delFolder(dirPath);

                        if (EnvUtil.isOnline()) {
                            for (JsonElement u : userList) {
                                String user = u.getAsString();
                                dxUtil.sendToPersionByCompass(
                                        "※ 遍历截屏 ※：\n 【任务ID】：" + id + "\n" + "【生成进度】：✅ 已完成，请及时对结果进行确认\n" + "【开始时间】："
                                                + sdf.format(hyperJumpJob.getCreateTime()) + "\n" + "【完成时间】："
                                                + sdf.format(new Date()) + "\n" + "【查看报告】：[查看报告|" + reportUrl + "]\n",
                                        user);
                            }
                        }

                    }

                    resp.setMsg("success");
                    resp.setData(id);
                    resp.setCode(200);
                }
            }
        } catch (Exception e) {
            log.info(e.toString());
            resp.setMsg("error");
            resp.setCode(-1);
            return resp;
        }
        return resp;
    }

    private static void storeHyperJumpPic(String hyperJumpPicPath) {
        // 获取系统的临时文件夹路径
        String serverPath = System.getProperty("java.io.tmpdir");

        // 创建源文件夹和目标文件夹的File对象
        File sourceDir = new File(hyperJumpPicPath);
        File targetDir = new File(serverPath);

        try {
            // 将源文件夹的内容复制到目标文件夹
            FileUtils.copyDirectoryToDirectory(sourceDir, targetDir);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<String> getAllCreator() {
        QueryWrapper<HyperJumpJob> queryWrapper = new QueryWrapper();
        List<String> list = new ArrayList<String>();
        List<HyperJumpJob> hyperJumpJobs = hyperJumpJobMapper.selectList(queryWrapper.groupBy("creator"));
        for (HyperJumpJob hyperJumpJob : hyperJumpJobs) {
            list.add(hyperJumpJob.getCreator());
        }
        return list;
    }

    @Override
    public Resp retryJob(String body) {
        Resp resp = new Resp();

        try {
            // 解析body获取任务id
            JsonObject responseBody = new JsonParser().parse(body).getAsJsonObject();
            JsonObject payload = responseBody.get("payload").getAsJsonObject();
            if (payload == null) {
                resp.setMsg("payload cannot be null");
                resp.setCode(-1);
                return resp;
            }
            int buildId = payload.get("buildId").getAsInt();
            String creator = "";
            String testApkUrl = "";
            if (payload.has("creator")) {
                creator = payload.get("creator").getAsString();
            } else {
                creator = "unKnowUser";
            }
            if (payload.has("testApkUrl")) {
                testApkUrl = payload.get("testApkUrl").getAsString();
            }

            // 查询对应的hyperJumpJob记录
            HyperJumpJob hyperJumpJob = hyperJumpJobMapper.selectById(buildId);
            if (hyperJumpJob == null) {
                resp.setMsg("未找到对应的任务");
                resp.setCode(-1);
                return resp;
            }
            hyperJumpJob.setCreator(creator);
            if (!testApkUrl.isEmpty()) {
                hyperJumpJob.setTestApkUrl(testApkUrl);
            }

            // 重新触发自动化
            Resp newBuildResp = newBuild(hyperJumpJob);
            int code = Integer.parseInt(newBuildResp.getCode());
            if (code == 1) {
                resp.setMsg("重试成功");
                resp.setCode(200);
            } else {
                resp.setMsg("重试失败");
                resp.setCode(-1);
            }

        } catch (Exception e) {
            log.error("重试任务出错", e);
            resp.setMsg("重试任务出错");
            resp.setCode(-1);
        }
        return resp;
    }

}
