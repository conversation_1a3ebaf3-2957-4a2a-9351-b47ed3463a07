package com.sankuai.mdp.compass.hyperJump.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.common.enums.Resp;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpJob;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.jump.entity.JumpJob;
import net.minidev.json.JSONObject;

import java.util.HashMap;
import java.util.List;

public interface HyperJumpJobService extends IService<HyperJumpJob> {
    Resp newBuild(HyperJumpJob hyperJumpJob) throws Exception;

    Integer add(HyperJumpJob hyperJumpJob);

    Resp updateJob(HyperJumpJob hyperJumpJob);

    Resp updateTestConanId(HyperJumpJob hyperJumpJob);

    Resp updateBaseConanId(HyperJumpJob hyperJumpJob);

    IPage<HyperJumpJob> list2(QueryRe<PERSON> request, HyperJumpJob hyperJumpJob);

    Resp result(JSONObject conanBody);

    List<String> getAllCreator();

    Resp retryJob(String body);
}
