package com.sankuai.mdp.compass.hyperJump.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.service.mobile.mtthrift.netty.exception.RequestTimeoutException;
import com.sankuai.mdp.compass.common.QueryRequest;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpDetail;
import com.sankuai.mdp.compass.hyperJump.mapper.HyperJumpDetailMapper;
import com.sankuai.mdp.compass.hyperJump.service.HyperJumpDetailService;
import com.sankuai.mdp.compass.jump.entity.JumpDetail;
import com.sankuai.mdp.compass.service.OcrService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Service
public class HyperJumpDetailServiceImpl extends ServiceImpl<HyperJumpDetailMapper, HyperJumpDetail>
        implements HyperJumpDetailService {
    @Autowired
    HyperJumpDetailMapper hyperJumpDetailMapper;

    @Autowired
    OcrService ocrService;

    private static String compareModel = "1";
    private static String cutModel = "Complete";
    private static String lineConfidence = "0.2";
    private static String ignoreArea = "2";

    @Override
    public void insert(HyperJumpDetail hyperJumpDetail) throws Exception {
        Integer type = hyperJumpDetail.getType();
        String picName = hyperJumpDetail.getPicName();
        String caseName = hyperJumpDetail.getCaseName();
        String platform = hyperJumpDetail.getPlatform();
        Integer buildId = hyperJumpDetail.getBuildId();
        String testPic = hyperJumpDetail.getTestPic();
        String basePic = hyperJumpDetail.getBasePic();

        if (type == 1) {
            // 基准组先结束
            QueryWrapper baseWrapper = new QueryWrapper();
            baseWrapper.eq("build_id", buildId);
            baseWrapper.eq("case_name", caseName);
            baseWrapper.eq("pic_name", picName);
            baseWrapper.eq("platform", platform);
            baseWrapper.isNotNull("base_pic");
            baseWrapper.last(" order by id asc limit 1");
            HyperJumpDetail baseDetail = null;
            try {
                baseDetail = hyperJumpDetailMapper.selectOne(baseWrapper);
            } catch (Exception e) {
                log.error("Database operation timeout", e);
            }
            if (baseDetail != null) {
                basePic = baseDetail.getBasePic();
                try {
                    String res = ocrService.diff(basePic, testPic, compareModel, cutModel, lineConfidence, ignoreArea);
                    JsonObject jsonObject = new JsonParser().parse(res).getAsJsonObject();
                    if (jsonObject.has("resultrurl") && jsonObject.has("similarresult")) {
                        try {
                            String diffUrl = jsonObject.get("resultrurl").getAsString();
                            Double similar = jsonObject.get("similarresult").getAsDouble();
                            hyperJumpDetail.setDiffUrl(diffUrl);
                            hyperJumpDetail.setSimilarity(similar);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            log.error("diffUrl获取失败，jsonObject：" + jsonObject.toString());
                        }
                    }
                } catch (RequestTimeoutException e) {
                    log.error("ocrService.diff调用超时", e);
                }
            }
            try {
                hyperJumpDetailMapper.insert(hyperJumpDetail);
            } catch (Exception e) {
                log.error("Database operation timeout", e);
            }

        } else if (type == 0) {
            try {
                hyperJumpDetailMapper.insert(hyperJumpDetail);
            } catch (Exception e) {
                log.error("Database operation timeout", e);
            }

            // 测试组先结束
            QueryWrapper testWrapper = new QueryWrapper();
            testWrapper.eq("build_id", buildId);
            testWrapper.eq("case_name", caseName);
            testWrapper.eq("pic_name", picName);
            testWrapper.eq("platform", platform);
            testWrapper.isNotNull("test_pic");
            testWrapper.isNull("diff_url");
            List<HyperJumpDetail> testDetailList = null;
            try {
                testDetailList = hyperJumpDetailMapper.selectList(testWrapper);
            } catch (Exception e) {
                log.error("Database operation timeout", e);
            }
            if (testDetailList != null) {
                for (int i = 0; i < testDetailList.size(); i++) {
                    HyperJumpDetail testDetail = testDetailList.get(i);
                    testPic = testDetail.getTestPic();
                    try {
                        String res = ocrService.diff(basePic, testPic, compareModel, cutModel, lineConfidence,
                                ignoreArea);
                        JsonObject jsonObject = new JsonParser().parse(res).getAsJsonObject();
                        if (jsonObject.has("resultrurl") && jsonObject.has("similarresult")) {
                            try {
                                String diffUrl = jsonObject.get("resultrurl").getAsString();
                                Double similar = jsonObject.get("similarresult").getAsDouble();
                                testDetail.setSimilarity(similar);
                                testDetail.setDiffUrl(diffUrl);
                            } catch (Exception e) {
                                log.error(e.getMessage());
                                log.info("diff error >> " + res);
                            }
                        }
                    } catch (RequestTimeoutException e) {
                        log.error("ocrService.diff调用超时", e);
                    }
                    try {
                        hyperJumpDetailMapper.updateById(testDetail);
                    } catch (Exception e) {
                        log.error("Database operation timeout", e);
                    }
                }
            }
        }
    }

    @Override
    public IPage<HyperJumpDetail> list(QueryRequest request, HyperJumpDetail hyperJumpDetail, String id) {
        try {
            return testPicList(id);
        } catch (Exception e) {
            log.error("获取列表失败");
            return null;
        }
    }

    private Page<HyperJumpDetail> testPicList(String id) {
        Page<HyperJumpDetail> result = new Page<>();
        LambdaQueryWrapper<HyperJumpDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HyperJumpDetail::getBuildId, id);
        queryWrapper.isNotNull(HyperJumpDetail::getTestPic);
        queryWrapper.last(" order by pic_name");
        Page<HyperJumpDetail> page = new Page<>(1, 500);
        IPage<HyperJumpDetail> iPage = this.page(page, queryWrapper);
        List<HyperJumpDetail> list = new ArrayList<>();
        JSONObject flagObject = new JSONObject();
        JSONObject baseObject = new JSONObject();
        for (int i = 0; i < iPage.getRecords().size(); i++) {
            HyperJumpDetail record = iPage.getRecords().get(i);
            String picName = record.getPicName();
            String testPic = record.getTestPic();
            String diffPic = record.getDiffUrl();
            String exceptionType = record.getExceptionType();
            String exceptionImage = record.getExceptionImage();
            String resolution = record.getTestResolution();
            Double similarity = record.getSimilarity();
            if (!flagObject.has(picName)) {
                flagObject.put(picName, new JSONObject());
            }
            String platform = record.getPlatform();
            String basePic = null;
            String baseResolution = null;
            if (baseObject.has(picName + "-" + platform)) {
                basePic = baseObject.getJSONObject(picName + "-" + platform).getString("url");
                baseResolution = baseObject.getJSONObject(picName + "-" + platform).getString("resolution");
            } else {
                HyperJumpDetail hyperJumpDetail = getBaseOne(id, picName, platform);
                basePic = hyperJumpDetail.getBasePic();
                baseResolution = hyperJumpDetail.getBaseResolution();
                baseObject.put(picName + "-" + platform, new JSONObject());
                baseObject.getJSONObject(picName + "-" + platform).put("url", basePic);
                baseObject.getJSONObject(picName + "-" + platform).put("resolution", baseResolution);

            }
            if (!flagObject.getJSONObject(picName).has(platform)) {
                flagObject.getJSONObject(picName).put(platform, new JSONObject());
            }
            if (!flagObject.getJSONObject(picName).getJSONObject(platform).has("basePic")) {
                flagObject.getJSONObject(picName).getJSONObject(platform).put("basePic", new JSONObject());
                flagObject.getJSONObject(picName).getJSONObject(platform).getJSONObject("basePic").put("url", basePic);
                flagObject.getJSONObject(picName).getJSONObject(platform).getJSONObject("basePic").put("resolution",
                        baseResolution);
            }
            if (!flagObject.getJSONObject(picName).getJSONObject(platform).has("testPic")) {
                flagObject.getJSONObject(picName).getJSONObject(platform).put("testPic", new JSONArray());
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("rurl", testPic);
            jsonObject.put("durl", diffPic);
            jsonObject.put("exceptionType", exceptionType);
            jsonObject.put("exceptionImage", exceptionImage);
            jsonObject.put("resolution", resolution);
            jsonObject.put("similarity", similarity);
            flagObject.getJSONObject(picName).getJSONObject(platform).getJSONArray("testPic").put(jsonObject);
        }
        Iterator iterator = flagObject.keys();
        while (iterator.hasNext()) {
            String key = iterator.next().toString();
            JSONObject jsonObject = flagObject.getJSONObject(key);
            String baseResolution = "";
            Iterator pIt = jsonObject.keys();
            while (pIt.hasNext()) {
                String platform = pIt.next().toString();

                HyperJumpDetail hyperJumpDetail = new HyperJumpDetail();
                hyperJumpDetail.setPicName(key);
                hyperJumpDetail.setPlatform(platform);
                baseObject = jsonObject.getJSONObject(platform).getJSONObject("basePic");
                baseResolution = baseObject.getString("resolution");
                String basePic = baseObject.getString("url");
                hyperJumpDetail.setBaseResolution(baseResolution);
                hyperJumpDetail.setBasePic(basePic);

                List<Object> testPicList = jsonObject.getJSONObject(platform).getJSONArray("testPic").toList();

                hyperJumpDetail.setTestPicObject(testPicList);

                list.add(hyperJumpDetail);
            }
        }
        result.setRecords(list);
        result.setTotal((long)list.size());
        return result;
    }

    public HyperJumpDetail getBaseOne(String buildId, String picName, String platform) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("build_id", buildId);
        queryWrapper.eq("pic_name", picName);
        queryWrapper.eq("platform", platform);
        queryWrapper.isNotNull("base_pic");
        queryWrapper.last(" limit 1");
        HyperJumpDetail hyperJumpDetail = hyperJumpDetailMapper.selectOne(queryWrapper);
        if (hyperJumpDetail == null) {
            hyperJumpDetail = new HyperJumpDetail();
            hyperJumpDetail.setBaseResolution("");
            hyperJumpDetail.setBasePic("");
        }
        return hyperJumpDetail;
    }
}
