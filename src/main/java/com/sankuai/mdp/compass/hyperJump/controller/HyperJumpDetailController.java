package com.sankuai.mdp.compass.hyperJump.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpDetail;
import com.sankuai.mdp.compass.hyperJump.service.HyperJumpDetailService;
import com.sankuai.mdp.compass.common.BaseController;
import com.sankuai.mdp.compass.common.QueryRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/compass/api/hyperJumpDetail")
public class HyperJumpDetailController extends BaseController {
    @Autowired
    HyperJumpDetailService hyperJumpDetailService;

    /**
     * 获取自动截图详情列表
     * @param request 查询请求
     * @param hyperJumpDetail 自动截图详情
     * @param processId 流程ID
     * @return 自动截图详情列表
     */
    @GetMapping("/list")
    public Map<String, Object> jobList(QueryRequest request, HyperJumpDetail hyperJumpDetail, @RequestParam("id") String processId)  {
        IPage<HyperJumpDetail> autoScreenshotDetailIPage = this.hyperJumpDetailService.list(request, hyperJumpDetail, processId);
        if (autoScreenshotDetailIPage != null) {
            return getDataTable(autoScreenshotDetailIPage);
        } else {
            return null;
        }
    }
}
