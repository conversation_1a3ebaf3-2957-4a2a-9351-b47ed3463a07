package com.sankuai.mdp.compass.hyperJump.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.mdp.compass.hyperJump.entity.HyperJumpDetail;
import com.sankuai.mdp.compass.common.QueryRequest;

public interface HyperJumpDetailService extends IService<HyperJumpDetail> {
    void insert(HyperJumpDetail hyperJumpDetail) throws Exception;
    IPage<HyperJumpDetail> list(QueryRequest request, HyperJumpDetail hyperJumpDetail, String id);
}
