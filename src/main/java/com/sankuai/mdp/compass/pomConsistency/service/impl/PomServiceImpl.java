package com.sankuai.mdp.compass.pomConsistency.service.impl;

import com.sankuai.mdp.compass.pomConsistency.entity.dto.PomMessageDTO;
import com.sankuai.mdp.compass.pomConsistency.entity.po.PomPO;
import com.sankuai.mdp.compass.pomConsistency.mapper.PomMapper;
import com.sankuai.mdp.compass.pomConsistency.service.PomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
@Service
public class PomServiceImpl implements PomService {

    @Autowired
    private PomMapper pomMapper;

    @Override
    public void setPomMessage(PomMessageDTO pomMessage) {
        PomPO pomPO = new PomPO();
        pomPO.setConsistencyFlag(pomMessage.getConsistencyFlag());
        pomPO.setCreateTime(new Date());
        pomPO.setRepositoryName(pomMessage.getRepositoryName());
        pomPO.setVersionDifferences(pomMessage.getVersionDifferences());
        pomMapper.insert(pomPO);
    }
}
