package com.sankuai.mdp.compass.pomConsistency.service.impl;

import com.sankuai.mdp.compass.caseCompleter.utils.CodeUtils;
import com.sankuai.mdp.compass.common.utils.GsonUtil;
import com.sankuai.mdp.compass.pomConsistency.entity.dto.CodePrInfo;
import com.sankuai.mdp.compass.pomConsistency.service.CodeService;
import org.springframework.stereotype.Service;

@Service
public class CodeServiceImpl implements CodeService {

    @Override
    public void sendPrComment(CodePrInfo codePrInfo) {
        String url = "http://git.sankuai.com/rest/api/2.0/projects/"+ codePrInfo.getProject()+"/repos/"+ codePrInfo.getRepo()+"/pull-requests/"+ codePrInfo.getPrId()+"/comments" ;
        try {
            CodeUtils.doCodePost(url, GsonUtil.toJson(codePrInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void sendCodeCallBack(CodePrInfo codePrInfo) {
        String url = "http://git.sankuai.com/rest/api/2.0/projects/"+ codePrInfo.getProject()+"/repos/"+ codePrInfo.getRepo()+"/pull-requests/"+ codePrInfo.getPrId()+"/status-check-callback/"+ codePrInfo.getCiId();
        try {
            CodeUtils.doCodePost(url, GsonUtil.toJson(codePrInfo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
