package com.sankuai.mdp.compass.pomConsistency.entity.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
@Data
public class PullRequestNotification {
    @SerializedName("pr_global_id")
    private int prGlobalId;
    @SerializedName("pr_id")
    private String prId;
    private int version;
    private String title;
    private String description;

    @SerializedName("source_ref")
    private RefInfo sourceRef;
    @SerializedName("target_ref")
    private RefInfo targetRef;
    @SerializedName("tmp_merge_ref")
    private RefInfo tmpMergeRef;
    private User author;
    private String operator;
    private List<User> reviewers;
    @SerializedName("message_id")
    private String messageId;
    @SerializedName("event_id")
    private String eventId;
    private String action;
    private String state;
    @SerializedName("ci_id")
    private String ciId;

}