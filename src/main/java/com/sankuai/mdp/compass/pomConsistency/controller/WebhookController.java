package com.sankuai.mdp.compass.pomConsistency.controller;

import com.google.gson.Gson;
import com.sankuai.mdp.compass.pomConsistency.entity.dto.CodePrInfo;
import com.sankuai.mdp.compass.pomConsistency.entity.dto.JenkinsParams;
import com.sankuai.mdp.compass.pomConsistency.entity.dto.PullRequestNotification;
import com.sankuai.mdp.compass.pomConsistency.service.CodeService;
import com.sankuai.mdp.compass.pomConsistency.service.JenkinsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/compass/api/pom/webhook")
public class WebhookController {
    @Autowired
    JenkinsService jenkinsService;

    @Autowired
    CodeService codeService;

    @PostMapping("/pr")
    public void handlePullRequestWebhook(@RequestBody String body) {
        try {
            PullRequestNotification notification = new Gson().fromJson(body, PullRequestNotification.class);
            processPullRequestNotification(notification);
        } catch (Exception e) {
            // 记录错误日志或者采取其他适当的错误处理措施
            e.printStackTrace();
        }
    }

    @PostMapping("/prComment")
    public void  sendPrComment(@RequestBody String body) {
        try {
            CodePrInfo codePrInfo = new Gson().fromJson(body, CodePrInfo.class);
            codeService.sendPrComment(codePrInfo);
        } catch (Exception e) {
            // 记录错误日志或者采取其他适当的错误处理措施
            e.printStackTrace();
        }
    }
    @PostMapping("/sendCodeCallBack")
    public void  sendCodeCallBack(@RequestBody String body) {
        try {
            CodePrInfo codePrInfo = new Gson().fromJson(body, CodePrInfo.class);
            codeService.sendCodeCallBack(codePrInfo);
        } catch (Exception e) {
            // 记录错误日志或者采取其他适当的错误处理措施
            e.printStackTrace();
        }
    }


    private void processPullRequestNotification(PullRequestNotification notification) {
        JenkinsParams jenkinsConfig = JenkinsParams.builder()
                .gitUrl(notification.getSourceRef().getSshUrl())
                .branch(notification.getSourceRef().getBranch())
                .repoName(notification.getSourceRef().getRepo())
                .prId(notification.getPrId())
                .eventId(notification.getEventId())
                .action(notification.getAction())
                .ciId(notification.getCiId())
                .projectName(notification.getSourceRef().getProject())
                .build();
        jenkinsService.triggerJenkinsJob(jenkinsConfig);
    }
}