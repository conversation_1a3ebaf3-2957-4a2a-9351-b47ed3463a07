package com.sankuai.mdp.compass.pomConsistency.entity.po;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("pom_message")
public class PomPO {
    @TableId(value = "id",type =  IdType.AUTO)
    Long id;

    @TableField(value = "is_success")
    private int consistencyFlag;

    @TableField(value = "repository_name")
    private String repositoryName;

    @TableField(value = "version_differences")
    private String versionDifferences;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
}
