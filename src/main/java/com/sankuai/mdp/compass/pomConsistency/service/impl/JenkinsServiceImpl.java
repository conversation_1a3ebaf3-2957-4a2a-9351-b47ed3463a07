package com.sankuai.mdp.compass.pomConsistency.service.impl;

import com.sankuai.mdp.compass.common.utils.JenkinsUtil;
import com.sankuai.mdp.compass.pomConsistency.entity.dto.JenkinsParams;
import com.sankuai.mdp.compass.pomConsistency.service.JenkinsService;
import com.sankuai.mdp.compass.common.utils.LionUtil;
import org.springframework.stereotype.Service;

@Service
public class JenkinsServiceImpl implements JenkinsService {


    public void triggerJenkinsJob(JenkinsParams jenkinsParams) {
        LionUtil lionUtil = new LionUtil();
        String jenkinsBaseUrlWithToken = lionUtil.getValue("jenkins.base.url.with.token");
        jenkinsParams.setToken(jenkinsBaseUrlWithToken);
        JenkinsUtil jenkinsUtil = new JenkinsUtil();
        jenkinsUtil.dependencyCheckJob(jenkinsParams);
         }
}