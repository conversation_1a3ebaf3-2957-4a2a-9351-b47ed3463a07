package com.sankuai.mdp.compass.pomConsistency.controller;

import com.sankuai.mdp.compass.pomConsistency.entity.dto.PomMessageDTO;
import com.sankuai.mdp.compass.pomConsistency.service.PomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/compass/api/pom/data")
public class PomController {
    @Autowired
    private PomService pomService;

    @RequestMapping("/setPomMessage")
    public void setPomMessage(@RequestBody PomMessageDTO pomMessage) {
        pomService.setPomMessage(pomMessage);
    }
}
