package com.sankuai.mdp.compass.horus.service;

import com.meituan.horus.proxy.HorusProxyResponse;

public interface horusAgentService {
    
    /**
     * 通过图片URL调用Horus服务
     * @param imageUrl 图片URL
     * @param sceneId 业务场景ID
     * @param abilityId 能力ID
     * @param prompt 提示词，可选
     * @return 服务响应结果
     */
    HorusProxyResponse getHorusResultByUrl(String imageUrl, long sceneId, long abilityId, String prompt);
    
    /**
     * 从响应中提取结果
     * @param response Horus服务响应
     * @return 处理后的结果
     */
    String processHorusResponse(HorusProxyResponse response);
}
