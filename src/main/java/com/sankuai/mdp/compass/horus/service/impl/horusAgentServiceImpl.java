package com.sankuai.mdp.compass.horus.service.impl;

import com.meituan.horus.proxy.HorusProxyRequest;
import com.meituan.horus.proxy.HorusProxyResponse;
import com.meituan.horus.proxy.HorusProxyService;
import com.meituan.horus.proxy.common.Image;
import com.sankuai.mdp.compass.horus.service.horusAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;

@Service
public class horusAgentServiceImpl implements horusAgentService {

    private static final Logger log = LoggerFactory.getLogger(horusAgentServiceImpl.class);

    @Autowired
    private HorusProxyService.Iface horus;

    /**
     * 通过图片URL调用Horus服务
     * @param imageUrl 图片URL
     * @param sceneId 业务场景ID
     * @param abilityId 能力ID
     * @param prompt 提示词，可选
     * @return 服务响应结果
     */
    public HorusProxyResponse getHorusResultByUrl(String imageUrl, long sceneId, long abilityId, String prompt) {
        try {
            // 构建请求
            HorusProxyRequest request = new HorusProxyRequest();
            // 设置业务场景ID
            request.setSceneId(sceneId);
            // 设置能力ID
            request.setAbilityId(abilityId);
            // 生成唯一请求token
            request.setRequestToken(UUID.randomUUID().toString());
            
            // 构建图片信息
            Image image = new Image();
            image.setUrl(imageUrl);
            
            List<Image> images = new ArrayList<>();
            images.add(image);
            request.setImportImages(images);
            
            // 添加请求参数
            Map<String, String> requestData = new HashMap<>();
            // 如果有prompt，添加到请求数据中
            if (StringUtils.isNotBlank(prompt)) {
                requestData.put("prompt", prompt);
                log.info("Adding prompt to request: {}", prompt);
            }
            request.setRequestData(requestData);
            
            // 调用Horus服务
            HorusProxyResponse response = horus.getResult(request);
            log.info("Horus service call completed, requestToken: {}, code: {}", 
                    request.getRequestToken(), response.getCode());
            
            return response;
        } catch (Exception e) {
            log.error("Failed to call Horus service with imageUrl: {}", imageUrl, e);
            throw new RuntimeException("调用Horus服务失败", e);
        }
    }
    
    /**
     * 从响应中提取结果
     * @param response Horus服务响应
     * @return 处理后的结果
     */
    public String processHorusResponse(HorusProxyResponse response) {
        if (response == null) {
            return null;
        }
        
        if (response.getCode() != 0) {
            log.error("Horus service returned error, code: {}, message: {}", 
                    response.getCode(), response.getMessage());
            return null;
        }
        
        // 从响应中提取结果数据
        Map<String, String> responseData = response.getResponseData();
        if (responseData != null && !responseData.isEmpty()) {
            // 这里可以根据具体业务需求处理响应数据
            return responseData.toString();
        }
        
        // 处理输出图片信息
        List<Image> outputImages = response.getOutputImages();
        if (outputImages != null && !outputImages.isEmpty()) {
            // 处理输出图片
            return "图片处理成功，输出图片数量：" + outputImages.size();
        }
        
        return "请求成功，但无返回数据";
    }
}
