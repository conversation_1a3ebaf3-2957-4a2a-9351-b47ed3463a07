package com.sankuai.mdp.compass.horus.controller;

import com.meituan.horus.proxy.HorusProxyResponse;
import com.sankuai.mdp.compass.horus.service.horusAgentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Horus服务测试控制器
 */
@RestController
@RequestMapping("compass/api/horus")
public class HorusController {

    private static final Logger log = LoggerFactory.getLogger(HorusController.class);

    @Autowired
    private horusAgentService horusAgentService;

    /**
     * 通过图片URL调用Horus服务
     * @param imageUrl 图片URL
     * @param sceneId 业务场景ID，默认为1
     * @param abilityId 能力ID，默认为1
     * @param prompt 提示词，可选
     * @return 处理结果
     */
    @PostMapping("/process-image")
    public Map<String, Object> processImage(
            @RequestParam("imageUrl") String imageUrl,
            @RequestParam(value = "sceneId", defaultValue = "1") long sceneId,
            @RequestParam(value = "abilityId", defaultValue = "30000574") long abilityId,
            @RequestParam(value = "prompt", required = false) String prompt) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("Processing image with URL: {}, sceneId: {}, abilityId: {}, prompt: {}", 
                    imageUrl, sceneId, abilityId, prompt);
            
            // 调用Horus服务
            HorusProxyResponse response = horusAgentService.getHorusResultByUrl(imageUrl, sceneId, abilityId, prompt);
            
            // 处理响应
            String processedResult = horusAgentService.processHorusResponse(response);
            
            result.put("success", true);
            result.put("code", response.getCode());
            result.put("message", response.getMessage());
            result.put("result", processedResult);
            result.put("requestToken", response.getRequestToken());
            
            // 如果有其他需要返回的数据，可以添加到result中
            if (response.getResponseData() != null) {
                result.put("responseData", response.getResponseData());
            }
            
        } catch (Exception e) {
            log.error("Error processing image", e);
            result.put("success", false);
            result.put("code", -1);
            result.put("message", "处理图片时发生错误: " + e.getMessage());
        }
        
        return result;
    }
} 