package com.sankuai.mdp.compass.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.entity.DynamicCase;
import com.sankuai.mdp.compass.entity.DynamicData;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;



/**
 * Created by xieyongrui on 2019/11/21.
 */
@Mapper
public interface DynamicCaseMapper extends BaseMapper<DynamicCase> {
}
