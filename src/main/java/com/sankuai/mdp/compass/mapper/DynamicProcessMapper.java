package com.sankuai.mdp.compass.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sankuai.mdp.compass.entity.DynamicCase;
import com.sankuai.mdp.compass.entity.DynamicProcess;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * Created by sunkangtong on 2021/3/9.
 */
@Mapper
public interface DynamicProcessMapper extends BaseMapper<DynamicProcess> {
}
