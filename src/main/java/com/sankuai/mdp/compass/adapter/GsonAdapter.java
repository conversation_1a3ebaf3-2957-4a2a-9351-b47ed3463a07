package com.sankuai.mdp.compass.adapter;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.internal.bind.ObjectTypeAdapter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Slf4j
public class GsonAdapter {
    public Gson getGson() {
        Gson gson = new GsonBuilder().create();
        try {
            Field factories = Gson.class.getDeclaredField("factories");
            factories.setAccessible(true);
            Object o = factories.get(gson);
            List<TypeAdapterFactory> factoriesList = extractFactoriesList(o);
            replaceFactory(factoriesList, MapTypeAdapter.FACTORY, ObjectTypeAdapter.class);
        } catch (Exception e) {
            log.error("getGson error: {}", e.getMessage(), e);
        }
        return gson;
    }
    private List<TypeAdapterFactory> extractFactoriesList(Object factoriesObject) throws IllegalAccessException {
        if (factoriesObject instanceof List) {
            return (List<TypeAdapterFactory>) factoriesObject;
        }

        for (Field field : factoriesObject.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            Object value = field.get(factoriesObject);
            if (value instanceof List) {
                return (List<TypeAdapterFactory>) value;
            }
        }

        throw new IllegalStateException("Unable to find factories list");
    }


    private void replaceFactory(List<TypeAdapterFactory> factories, TypeAdapterFactory newFactory, Class<?> targetType) {
        for (int i = 0; i < factories.size(); i++) {
            TypeAdapterFactory factory = factories.get(i);
            if (targetType.isAssignableFrom(factory.getClass())) {
                factories.set(i, newFactory);
                break;
            }
        }
    }
}
