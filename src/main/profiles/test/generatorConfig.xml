<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 引入配置文件 -->
    <!--<properties resource="application-test.properties"/>-->
    <!--<classPathEntry-->
    <classPathEntry  location="/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.11/mysql-connector-java-8.0.11.jar"/>
    <context id="DB2Tables"  targetRuntime="MyBatis3">
    <commentGenerator>
        <property name="suppressDate" value="true"/>
        <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
        <property name="suppressAllComments" value="true"/>
    </commentGenerator>
    <!--数据库链接URL，用户名、密码 -->
    <jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="***********************************************************" userId="sankuai" password="sankuaiest123">
    </jdbcConnection>
    <javaTypeResolver>
        <property name="forceBigDecimals" value="false"/>
    </javaTypeResolver>
    <!-- 生成模型的包名和位置-->
    <javaModelGenerator targetPackage="com.sankuai.mdp.compass.entity" targetProject="src/main/java">
        <property name="enableSubPackages" value="true"/>
        <property name="trimStrings" value="true"/>
    </javaModelGenerator>
    <!-- 生成映射文件的包名和位置-->
    <sqlMapGenerator targetPackage="mapping" targetProject="src/main/resources">
        <property name="enableSubPackages" value="true"/>
    </sqlMapGenerator>
    <!-- 生成DAO的包名和位置-->
    <!-- XMLMAPPER生成xml映射文件, ANNOTATEDMAPPER生成的dao采用注解来写sql -->
    <javaClientGenerator type="ANNOTATEDMAPPER" targetPackage="com.sankuai.mdp.compass.mapper" targetProject="src/main/java">
        <property name="enableSubPackages" value="true"/>
    </javaClientGenerator>
    <!-- 要生成的表 tableName是数据库中的表名或视图名 domainObjectName是实体类名-->
    <table tableName="dynamic_sdk_diff_report" domainObjectName="DynamicSdkReport" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
    </context>
</generatorConfiguration>