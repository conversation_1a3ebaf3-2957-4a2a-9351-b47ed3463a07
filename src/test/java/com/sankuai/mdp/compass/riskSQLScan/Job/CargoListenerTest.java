package com.sankuai.mdp.compass.riskSQLScan.Job;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @auther bock
 * @date 2024-10-18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class CargoListenerTest {
    @Autowired
    private CargoListener cargoListener;

    @Test
    public void fun(){
        cargoListener.getSwimlaneInfo();
    }
}