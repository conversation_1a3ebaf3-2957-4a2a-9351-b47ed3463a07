package com.sankuai.mdp.compass.caseCompleter.job;

import com.google.gson.JsonParser;
import com.sankuai.mdp.compass.ApplicationLoader;
import com.sankuai.mdp.compass.caseCompleter.utils.CodeUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @auther bock
 * @date 2024-10-17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationLoader.class)
public class TestXListenerTest {

    @Autowired
    private CaseCompleterListener caseCompleterListener;

    @Test
    public void scanTest(){
        caseCompleterListener.scanSwimlane();
    }

    @Test
    public void createTask() throws Exception {
        caseCompleterListener.createTask();
    }

    @Test
    public void generateTest() throws Exception {
        caseCompleterListener.generate();
    }

    public static void main(String[] args) throws Exception {
        String res = CodeUtils.getMergeStatus("~yangyang298", "caseCompleter", "9");
        String canMerge = new JsonParser().parse(res).getAsJsonObject().get("canMerge").getAsString();
        System.out.println("true".equals(canMerge));
    }

    @Test
    public void manageTask() throws Exception {
        caseCompleterListener.manageTask();
    }
    @Test
    public void merge() throws Exception {
        caseCompleterListener.merge();
    }





}