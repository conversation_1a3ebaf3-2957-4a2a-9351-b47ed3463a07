package com.sankuai.mdp.compass.caseCompleter.utils;

import com.sankuai.mdp.compass.caseCompleter.entity.PO.CaseCompleterBranchInfoPO;
import org.junit.Test;


import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.*;

/**
 * @auther bock
 * @date 2024-10-17
 */
public class TestXUtilTest {

    @Test
    public void createTaskBySwimLane() {
    }

    @Test
    public void restartTask() {
    }


    public static void main(String[] args) throws Exception {

    }

    @Test
    public void generate() {
    }

    @Test
    public void stopTaskBySwimLane() {
    }

    @Test
    public void getTaskBySwimlane() {

    }

    @Test
    public void stopTaskByIdAndUserName() {
    }

    private static Date getFormatDate(){
        LocalDateTime now = LocalDateTime.now();
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }
}