package com.sankuai.ptqa.upload;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Jsoup;

import java.io.*;
import java.net.URL;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;

public class UploadPictureService {


    private static String accessKey = "894a8fbe96b64894ab526523ea318aee";
    private static String endpoint = "s3plus.vip.sankuai.com";
    private static String bucketName = "autotest";
    private static AWSCredentials credentials = new BasicAWSCredentials(accessKey, "9177528ff9c74730bfb2c07652e4d5f6");
    private static AmazonS3Client mssClient = new AmazonS3Client(credentials);
    private static Gson GSON = new Gson();


    //运行脚本
    // java -cp uploadpicture-0.0.1.jar com.sankuai.ptqa.upload.UploadPictureService
    public static void main(String[] args) throws IOException {
//        args = new String[2];
//        args[0]="13";
//        args[1]="IOS";
        Integer jenkinsId = Integer.valueOf(args[0]);
        String platform = (String) args[1];
        String str = traversalFileListForPicDiff(jenkinsId, platform);
//        traversalFileListForFileDiff();
        if ("success".equals(str)) {
            System.exit(1);
        } else {
            System.exit(0);
        }
    }


    public static void traversalFileListForFileDiff() throws IOException {
        long startTime = System.currentTimeMillis();
        String fileDirPath = "/Users/<USER>/Downloads/ele_diff/";
        String basicFileDirPath = "/Users/<USER>/Downloads/basic_ele_diff/";
        CreateAmazonS3Conn(endpoint);
        createBucketIfNotExist(bucketName);

        Map<String, String> testFileMap = new HashMap<>();
        Map<String, String> basicFileMap = new HashMap<>();
        ArrayList<String> testLineResultArray = new ArrayList<>();
//        String url = "http://qaassist.sankuai.com/compass/api/diff/PicUrlDiff";

//      获取文件夹下的所有文件名称
        File fileDir = new File(fileDirPath);
        if (fileDir.exists()) {
            File[] fileList = fileDir.listFiles();
            //遍历被测文件
            System.out.println("==========Process Start===========");
            for (int i = 0; i < fileList.length; i++) {


                String testFileName = fileList[i].getName();
                String testFilePath = fileDirPath + testFileName;
                String xmlName = StringUtils.substringBefore(testFileName, ".txt");
                File file = new File(testFilePath);
                // S3下载基础文件
                String basicXmlName = "basic_" + xmlName;
                File basicFile = new File(basicFileDirPath + basicXmlName + ".txt");
                getObjectToLocalFile(bucketName, basicXmlName);

                BufferedReader testFileReader = null;
                BufferedReader basicFileReader = null;

                try {
                    if (file.isFile() && file.exists()) {
//                        System.out.print("===========上传基准文件：===========" + basicXmlName + "\n");
//                        putObjectFile(bucketName, basicXmlName, testFilePath);
//                        String testFileUrl = presignUrl(bucketName, basicXmlName);
//                        System.out.println("testFileUrl:" + testFileUrl);

                        if (basicFile.isFile() && basicFile.exists()) {

                            testFileReader = new BufferedReader(new FileReader(file));
                            basicFileReader = new BufferedReader(new FileReader(basicFile));

                            ArrayList<String> testFileLineArray = new ArrayList<>();
                            ArrayList<String> basicFileLineArray = new ArrayList<>();

                            String lineText = null;
                            while ((lineText = testFileReader.readLine()) != null) {
                                if (lineText == null || lineText == "") {
                                    continue;
                                }
                                testFileLineArray.add(lineText.trim());
                            }
                            while ((lineText = basicFileReader.readLine()) != null) {
                                if (lineText == null || lineText == "") {
                                    continue;
                                }
                                basicFileLineArray.add(lineText.trim());
                            }

                            // 文本对比
                            int size = basicFileLineArray.size() >= testFileLineArray.size() ? testFileLineArray.size() : basicFileLineArray.size();
                            int j;
                            for (j = 0; j < size; j++) {
                                if (!testFileLineArray.get(j).equals(basicFileLineArray.get(j))) {
                                    testLineResultArray.add("======测试文件：" + testFileName + "，第" + j + "行不相等，测试文件当前行内容：" + testFileLineArray.get(j));
                                }
                            }

                            // 判断文本是否缺失
                            int lineNumber = basicFileLineArray.size() - testFileLineArray.size();
                            if (lineNumber > 0) {
                                testLineResultArray.add("===测试文件：" + testFileName + "，有缺失内容：" + basicFileLineArray.get(j) + "…………………");
                            } else if (lineNumber < 0) {
                                testLineResultArray.add("===测试文件：" + testFileName + "，有多余内容："  + testFileLineArray.get(j) + "…………………");
                            }
                            //上传测试文件到S3
                            putObjectFile(bucketName, xmlName, testFilePath);
                            String testFileUrl = presignUrl(bucketName, xmlName);
                            String basicFileUrl = presignUrl(bucketName, basicXmlName);
                            //上传测试结果
                            testFileMap.put(xmlName, testFileUrl);
                            basicFileMap.put(xmlName, basicFileUrl);


                        } else {
                            // 如果基准文件不存在，不对比，上传基准文件，上传会替换历史同名文件
                            try {
                                System.out.print("===========基准文件缺失，文件名：===========" + basicXmlName + "\n");
                                putObjectFile(bucketName, basicXmlName, testFilePath);
                            } catch (Exception e) {
                                System.out.print("===========上传文件失败==========\n");
                            }
                        }
                    } else {
                        System.out.println("===========找不到指定的文件===========\n");
                    }
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    testFileReader.close();
                    basicFileReader.close();
                }
            }

            System.out.println("=======basicFileMap====" + basicFileMap.toString());
            System.out.println("=======testFileMap====" + testFileMap.toString());
            System.out.println(StringUtils.join(testLineResultArray, "\n"));

            long endTime = System.currentTimeMillis();
            System.out.println("======Process Over!=======");
            System.out.println("Time Spending:" + ((endTime - startTime) / 1000D) + "s");
        }
    }


    public static String traversalFileListForPicDiff(Integer jenkinsId, String platform) throws IOException {
        ArrayList<String> fileList = new ArrayList<>();
        Map<String, String> testPicMap = new HashMap<>();
//        String picPath = "../dynamic_diff_test/";
        String picPath = "/Users/<USER>/Downloads/pic_diff/Documents/PicDiff/";
//        String url = "http://127.0.0.1:80/compass/api/diff/PicUrlDiff";
        String url = "http://qaassist.sankuai.com/compass/api/diff/PicUrlDiff";

//      获取1个文件夹下的所有文件名称
        getFileList(picPath, "", fileList);
        for (int i = 0; i < fileList.size(); i++) {
            String fileName = fileList.get(i);
            String xmlName = StringUtils.substringBefore(fileName, ".png");
//          上传Venus
            String testPicUrl = uploadPicture(picPath + fileName);
            testPicMap.put(xmlName, testPicUrl);
        }

        String testPicMapStr = GSON.toJson(testPicMap);
        Connection con = Jsoup.connect(url);
        con.header("User-Agent", "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.48 Safari/537.36");
        con.header("Content-Type", "application/json;charset=utf-8");

        Map<String,Object> body = Maps.newHashMap();
        body.put("platform", platform);
        body.put("jenkinsId", jenkinsId);
        body.put("testPicMapStr", testPicMapStr);
        con.requestBody(GSON.toJson(body));

//      发送请求，图像对比
        try {
            Connection.Response response = con.timeout(36000000).method(Connection.Method.POST).execute();

            if (response.statusCode() == 200) {
                Map<String, String> jsonObject = GSON.fromJson(response.body(), Map.class);
                System.out.println(response.body());
                if (jsonObject != null && "success".equals(jsonObject.get("status"))) {
                    return "success";
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String uploadPicture(String path) {
        try {
            String bucket = "ptautotest";
            String client_id = "ptautotest";
            String client_secret = "1292ee16e73a9d36aa52cbd45b3d0bcc";
            ImageUploadClient client = new ImageUploadClientImpl(bucket, client_id, client_secret);

            File file = new File(path);
            ImageRequest request = new ImageRequest(1, file, false);
            ImageResult res = client.postImage(request);
            return res.getOriginalLink();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.print("图片上传失败");
            return null;
        }
    }

    public static void getFileList(String path, String dirName, ArrayList<String> fileNameList) {
        File file = new File(path);
        if (file.exists()) {
            File[] fileList = file.listFiles();
            for (int i = 0; i < fileList.length; i++) {
                if (fileList[i].isFile()) {
                    fileNameList.add(dirName + fileList[i].getName());

                } else if (fileList[i].isDirectory()) {
                    String dirPath = fileList[i].getPath();
                    String[] dirNameList = dirPath.split("/");
                    getFileList(dirPath, dirName + dirNameList[dirNameList.length - 1] + "/", fileNameList);
                }
            }
        }
    }


    public static AmazonS3 CreateAmazonS3Conn(String hostname) {

        //配置云存储服务地址
        mssClient.setEndpoint(hostname);

        //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        mssClient.setS3ClientOptions(s3ClientOptions);
        return mssClient;
    }

    public static void createBucketIfNotExist(String bucketName) {
        try {
            //判断待创建的bucket是否存在，如果存在不用重复创建，重复创建同名bucket服务器端会返回错误
            if (mssClient.doesBucketExist(bucketName) == false) {
                mssClient.createBucket(bucketName);
            }
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3创建桶失败：Caught an ServiceException.");
        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3创建桶失败：Caught an ClientException.");
        }
    }

    public static void putObjectFile(String bucketName, String objectName, String uploadFileName) {
        try {
            FileInputStream inputStream = new FileInputStream(uploadFileName);
            mssClient.putObject(bucketName, objectName, inputStream, null);

        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3上传失败：Caught an AmazonServiceException.");

        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3上传失败：Caught an ClientException.");

        } catch (IOException e) {
            System.out.println("s3上传失败：IOException.");

        }

    }

    public static void getObjectToLocalFile(String bucketName, String objectName) {
        try {
            mssClient.getObject(new GetObjectRequest(bucketName, objectName), new File("/Users/<USER>/Downloads/basic_ele_diff/" + objectName + ".txt"));
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3下载失败：Caught an ServiceException.");
            System.out.print(ase);
        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("Caught an ClientException.");
            System.out.println("Error Message: " + ace.getMessage());
        } catch (Exception e) {
            System.out.println("Error Message: " + e.toString());
        }
    }


    public static void getObject(String bucketName, String objectName) {
        try {
            S3Object s3object = mssClient.getObject(new GetObjectRequest(
                    bucketName, objectName));
            InputStream content = s3object.getObjectContent();
            BufferedReader reader = new BufferedReader(new InputStreamReader(content));
            if (content != null) {
                while (true) {
                    String line = reader.readLine();
                    if (line == null) break;
                    System.out.println("\n" + line);
                }
                //获取object后需要close(),释放连接
                s3object.close();
            }
        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3下载失败：Caught an ServiceException.");
            System.out.print(ase);
        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3下载失败：Caught an ClientException.");
        } catch (Exception e) {
            System.out.println("s3下载失败：Exception");
        }
    }

    public static String presignUrl(String bucketName, String objectName) {
        String resultUrl = "";
        try {
            //设定url的有效时间
            Date expiration = new Date();
            long milliSeconds = getAfterMonth(3);
            expiration.setTime(milliSeconds);
            //指定授权的bucket和object
            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName, objectName);
            //指定授权的请求类型
            generatePresignedUrlRequest.setMethod(HttpMethod.GET);
            generatePresignedUrlRequest.setExpiration(expiration);
            //生成授权的url
            URL url = mssClient.generatePresignedUrl(generatePresignedUrlRequest);
            return url.toString();

        } catch (AmazonServiceException ase) {
            //存储服务端处理异常
            System.out.println("s3生成url失败：Caught an ServiceException.");

        } catch (AmazonClientException ace) {
            //客户端处理异常
            System.out.println("s3生成url失败：Caught an ClientException.");
        }
        return resultUrl;
    }

    public static long getAfterMonth(int number) {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String inputDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        Date date = null;
        try {
            date = sdf.parse(inputDate);
        } catch (Exception e) {

        }
        c.setTime(date);
        c.add(Calendar.MONTH, number);
        String strDate = sdf.format(c.getTime());

        long milliSeconds = sdf.parse(strDate, new ParsePosition(0)).getTime();
//        System.out.print(milliSeconds);
        return milliSeconds;
    }


}
