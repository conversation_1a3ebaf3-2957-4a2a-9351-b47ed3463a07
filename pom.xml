<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sankuai.ptqa</groupId>
    <artifactId>compass</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>compass</name>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-parent</artifactId>
        <version>1.8.6-GM12</version>
    </parent>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!--<mybatis-plus.version>3.1.0</mybatis-plus.version>-->

        <javacv.version>1.5.8</javacv.version>
        <javacv.opencv.version>4.6.0-1.5.8</javacv.opencv.version>
        <javacv.ffmpeg.version>5.1.2-1.5.8</javacv.ffmpeg.version>
        <javacv.openblas.version>0.3.21-1.5.8</javacv.openblas.version>
        <javacpp.platform.macosx-x86_64>macosx-x86_64</javacpp.platform.macosx-x86_64>
        <javacpp.platform.macosx-arm64>macosx-arm64</javacpp.platform.macosx-arm64>
        <javacpp.platform.linux-armhf>linux-armhf</javacpp.platform.linux-armhf>
        <javacpp.platform.linux-arm64>linux-arm64</javacpp.platform.linux-arm64>
        <javacpp.platform.linux-ppc64le>linux-ppc64le</javacpp.platform.linux-ppc64le>
        <javacpp.platform.linux-x86>linux-x86</javacpp.platform.linux-x86>
        <javacpp.platform.linux-x86_64>linux-x86_64</javacpp.platform.linux-x86_64>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>inf-bom</artifactId>
                <version>1.4.13</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--    <repositories>-->
    <!--        <repository>-->
    <!--            <id>jitpack.io</id>-->
    <!--            <url>https://jitpack.io</url>-->
    <!--        </repository>-->
    <!--    </repositories>-->

    <dependencies>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-java-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>sts-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.saasquatch</groupId>
            <artifactId>json-schema-inferrer</artifactId>
            <version>0.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.horus</groupId>
            <artifactId>horus-proxy-sdk</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.erosb</groupId>
            <artifactId>everit-json-schema</artifactId>
            <version>1.14.1</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.erosb</groupId>
            <artifactId>everit-json-schema-jdk6</artifactId>
            <version>1.9.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>2.5.6</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>3.2</version> <!-- 选择一个兼容您Java版本的版本 -->
        </dependency>
        <!--mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <!--<scope>runtime</scope>-->
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
            <version>1.8.6-GM12</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-spring</artifactId>
                </exclusion>

                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>

                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>-->
        <!--    <groupId>org.mybatis</groupId>-->
        <!--    <artifactId>mybatis</artifactId>-->
        <!--    <version>3.5.0</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--    <groupId>org.mybatis</groupId>-->
        <!--    <artifactId>mybatis-spring</artifactId>-->
        <!--    <version>2.0.0</version>-->
        <!--</dependency>-->

        <!--&lt;!&ndash; mybatis-plus &ndash;&gt;-->
        <!--<dependency>-->
        <!--    <groupId>com.baomidou</groupId>-->
        <!--    <artifactId>mybatis-plus-boot-starter</artifactId>-->
        <!--    <version>${mybatis-plus.version}</version>-->
        <!--</dependency>-->
        <!--&lt;!&ndash; 代码生成器 &ndash;&gt;-->
        <!--<dependency>-->
        <!--    <groupId>com.baomidou</groupId>-->
        <!--    <artifactId>mybatis-plus-generator</artifactId>-->
        <!--    <version>${mybatis-plus.version}</version>-->
        <!--</dependency>-->
        <!--        &lt;!&ndash; mybatis &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.mybatis.spring.boot</groupId>-->
        <!--            <artifactId>mybatis-spring-boot-starter</artifactId>-->
        <!--            <version>1.2.1</version>-->
        <!--        </dependency>-->


        <dependency>
            <groupId>org.python</groupId>
            <artifactId>jython-standalone</artifactId>
            <version>2.7.0</version>
        </dependency>




        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-jdbc</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.14.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-autoconfigure</artifactId>
            <version>1.3.2</version>
        </dependency>


        <!--zebra-dao依赖，其内部依赖了mybatis、mybatis-spring-->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
            <version>4.0.4.5</version>
        </dependency>
        <!--Zebra数据源相关依赖-->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
            <version>4.0.4.5</version>
        </dependency>
        <!--zebra监控-->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
            <version>4.0.4.5</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.talostwo</groupId>
            <artifactId>talostwo-sdk-java</artifactId>
            <version>1.1.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <!-- Excel Xls格式解析 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>

        <!-- Excel Xlsx格式解析 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>

        <!--  解析Excel相关依赖 -->
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
            <classifier>jdk15</classifier>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20200518</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>2.6.0</version>
        </dependency>



        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.bi.ocean</groupId>
            <artifactId>config-api</artifactId>
            <version>2.9.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--大象通知-->
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-sdk</artifactId>
            <version>2.1.2</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
        <!--临时添加：原因为fusion检测xm的包中有1.2.67版本的fastjson-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83_noneautotype</version>
        </dependency>

        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacpp</artifactId>
            <version>${javacv.version}</version>
            <classifier>${javacpp.platform.linux-x86_64}</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>openblas</artifactId>
            <version>${javacv.openblas.version}</version>
            <classifier>${javacpp.platform.linux-x86_64}</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>opencv</artifactId>
            <version>${javacv.opencv.version}</version>
            <classifier>${javacpp.platform.linux-x86_64}</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <version>${javacv.ffmpeg.version}</version>
            <classifier>${javacpp.platform.linux-x86_64}</classifier>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv</artifactId>
            <version>${javacv.version}</version>
        </dependency>


        <dependency>
            <groupId>com.meituan.horus</groupId>
            <artifactId>horus-sdk</artifactId>
            <version>1.4.38</version>
        </dependency>



        <!--  玲珑，获取资源位投放的资源 -->
        <dependency>
            <groupId>com.meituan.travel.mop.ad</groupId>
            <artifactId>res-ops</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.java-json-tools</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>2.2.14</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
            <version>1.3.9</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.3</version>
        </dependency>



        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>sms-api</artifactId>
            <version>2.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.iam</groupId>
            <artifactId>iam-common-base</artifactId>
            <version>1.5.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-bean-copy</artifactId>
            <version>1.8.6-GM12</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>2.5.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
            <version>1.4.12</version>
        </dependency>
        <dependency>
            <groupId>org.mnode.ical4j</groupId>
            <artifactId>ical4j</artifactId>
            <version>3.0.21</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>activation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.offbytwo.jenkins</groupId>
            <artifactId>jenkins-client</artifactId>
            <version>0.3.7</version>
            <exclusions>
                    <exclusion>
                        <groupId>dom4j</groupId>
                        <artifactId>dom4j</artifactId>
                    </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
            <version>1.8.6-GM12</version>
        </dependency>

        <!-- STS: https://km.sankuai.com/page/850133309 -->


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>

            </plugin>

            <!-- mybatis generator 自动生成代码插件 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <configurationFile>src/main/profiles/test/generatorConfig.xml
                    </configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>
