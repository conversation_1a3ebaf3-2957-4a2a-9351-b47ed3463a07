version: v1
common:
  tools:
    oracle-jdk: 8
  env:
    CURRENT_ENV: prod
build: 
  tools:
    maven: 3.9.5
  run:
    workDir: ./
    cmd: 
      - sh deploy/compile.sh
  target: 
    distDir: ./target/
    files: 
      - ./*.jar
      - ../deploy
autodeploy:
    targetDir: /opt/meituan/com.sankuai.sigma.compass/
    hulkos: centos7
    tools:
      python_with_virtualenv_and_buildout3.12:
    env:
    run: sh deploy/run.sh
    check: sh deploy/check.sh
    checkRetry: 5 #后面下掉，加默认值为1
    CheckInterval: 30s #后面下掉
