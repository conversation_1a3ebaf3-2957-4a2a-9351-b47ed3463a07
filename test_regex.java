import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class test_regex {
    public static void main(String[] args) {
        // 测试数据 - vivo_V2243A 设备的日志
        String remarks = "设备轮次通知: 【MBP-VTD9VTHM3G-2250】 | 设备轮次通知 | 时间：2025-07-28 11:43:26 | 设备：vivo_V2243A (ANDROID) | UDID：10AD5F1KJ4002B6 | 状态：部分完成第 760 轮测试（历史轮次: 5931） |  | 测试问题信息： | - 检测到测试流程问题（如连接失败、设备断开等） |  | 测试统计信息： | 当前轮次：760 | 历史轮次：5931 | 总测试时长：72小时16分钟35秒 | 完成轮次：760 (100.0%) | 总图标数：5549 | 完成图标总数：4460 | 应用问题率：4.3% | 测试问题率：0.0% | 平均图标耗时：58.3秒 | 图标完成率：80.4% |  | 最近 1 小时问题记录： | 1. 应用问题 2025-07-28 11:43:26 |    轮次：5931 |    页面：首页检查 |    详情：固定图标测试：无法回到美团首页，已达到最大重试次数 |    图片URL：http://p0.meituan.net/ptautotest/9e755764d338ddb9c782080bed8de5fe162582.png | 2. 应用问题 2025-07-28 11:18:14 |    轮次：5929 |    页面：首页检查 |    详情：推荐测试：无法回到美团首页，已达到最大重试次数 |    图片URL：http://p0.meituan.net/ptautotest/43882962b41d75f920d5c851ecbeb42a162295.png | 3. 应用问题 2025-07-28 11:00:00 |    轮次：5925 |    页面：首页检查 |    详情：固定图标测试：无法回到美团首页，已达到最大重试次数 |    图片URL：http://p1.meituan.net/ptautotest/f3ea2588def8e199208941135896b7a7164029.png |";
        
        // 测试总轮次提取 - 修复后的正则
        String totalRoundsRegex = "(?:已完成|部分完成)第\\s*(\\d+)\\s*轮测试";
        String totalRounds = extractByRegex(remarks, totalRoundsRegex);
        System.out.println("总轮次: " + totalRounds);
        
        // 测试完成率提取
        String testCompletionRateRegex = "完成轮次[:：]\\s*\\d+\\s*\\((\\d+\\.?\\d*%)\\)";
        String testCompletionRate = extractByRegex(remarks, testCompletionRateRegex);
        System.out.println("测试完成率: " + testCompletionRate);
        
        // 测试平均图标耗时提取
        String avgTimeRegex = "平均图标耗时[:：]\\s*([\\u4e00-\\u9fa5\\d\\.]+秒)";
        String avgTime = extractByRegex(remarks, avgTimeRegex);
        System.out.println("平均图标耗时: " + avgTime);
        
        // 测试应用问题率
        String appIssueRateRegex = "应用问题率[:：]\\s*([\\u4e00-\\u9fa5\\d\\.]+%)";
        String appIssueRate = extractByRegex(remarks, appIssueRateRegex);
        System.out.println("应用问题率: " + appIssueRate);
        
        // 测试问题率
        String testIssueRateRegex = "测试问题率[:：]\\s*([\\u4e00-\\u9fa5\\d\\.]+%)";
        String testIssueRate = extractByRegex(remarks, testIssueRateRegex);
        System.out.println("测试问题率: " + testIssueRate);
        
        // 统计最近一小时问题数
        int totalErrorCount = 0;
        String keyword = "详情";
        int index = 0;
        while (index < remarks.length()) {
            index = remarks.indexOf(keyword, index);
            if (index == -1) break;
            totalErrorCount++;
            index += keyword.length();
        }
        System.out.println("最近一小时问题数: " + totalErrorCount);
    }
    
    private static String extractByRegex(String text, String regex) {
        if (text == null) return null;
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
