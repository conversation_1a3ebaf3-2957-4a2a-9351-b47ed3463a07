#!/usr/bin/env bash
WAIT_SECONDS=30
k=1
TEST_URL="127.0.0.1:8080/monitor/alive"

echo "check service......"
if [ -z $TEST_URL ]; then
    echo "Warning:Need a test_url!"
    exit 0
fi

for k in $(seq 1 $WAIT_SECONDS)
do
    sleep 1
    STATUS_CODE=`curl -o /dev/null -s -w %{http_code} $TEST_URL`
    if [ "$STATUS_CODE" = "200" ];then
        echo "request test_url:$TEST_URL succeeded!"
        echo "response code:$STATUS_CODE"
        break
    else
        echo "request test_url:$TEST_URL failed!"
        echo "response code: $STATUS_CODE"
        echo "try one more time:the $k time....."
    fi
    if [ $k -eq $WAIT_SECONDS ];then
        echo "have tried 30 times, no more try"
        echo "failed"
        exit -1
    fi
done

#测试环境安装arthas
function getEnv(){
    FILE_NAME="/data/webapps/appenv"
    PROP_KEY="env"
    PROP_VALUE=""
    if [[ -f "$FILE_NAME" ]]; then
        PROP_VALUE=`cat ${FILE_NAME} | grep -w ${PROP_KEY} | cut -d'=' -f2`
    fi
    echo $PROP_VALUE
}

function installArthas() {
    if [ ! -x as.sh ]; then
        echo "need download and install arthas"
        curl -L https://alibaba.github.io/arthas/install.sh | sh
    else
        echo "found arthas.no need install"
    fi
    pid=`jps|grep jar|awk '{print $1}'`
    if [ -x as.sh ]; then
        ./as.sh --attach-only $pid@127.0.0.1:44397:44398
    fi
}

ENV=`getEnv`
if [ "$ENV" == "test" ]; then
    installArthas
fi
exit 0