# 初次运行项目时可能遇到的问题



## 项目依赖大量报错
初次从仓库中下载项目到本地后，如果com.baomidou、com.google等很多依赖全都报错，很可能是maven导致的问题

解决方案：

###第一步
见[这篇回答](https://stackoverflow.com/questions/70214833/intellij-2021-3-maven-not-able-to-resolve-dependencies-after-update-to-intellij)

省流版：
> for me just change Settings (Preferences on macOS) | Build, Execution, Deployment | Build Tools | Maven | Maven home path to User Maven Wrapper

###第二步

就在上一步的“Maven home path”下面，User settings file选项，勾选override，然后选择本地的settings.xml文件（此文件可以找项目负责人询问）

###第三步

IDEA中，进入Project Structure，将SDK设置为1.8版本



----
